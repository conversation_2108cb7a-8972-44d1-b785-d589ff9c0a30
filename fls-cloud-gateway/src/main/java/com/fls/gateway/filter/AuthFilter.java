package com.fls.gateway.filter;

import cn.dev33.satoken.exception.NotLoginException;
import cn.dev33.satoken.reactor.filter.SaReactorFilter;
import cn.dev33.satoken.router.SaRouter;
import cn.dev33.satoken.stp.StpUtil;
import cn.dev33.satoken.util.SaResult;
import com.fls.gateway.config.properties.IgnoreWhiteProperties;
import com.fls.common.core.constant.HttpStatus;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * [Sa-Token 权限认证] 拦截器
 *
 * <AUTHOR>
 * @date 2022/04/15
 */
@Configuration
public class AuthFilter {

    /**
     * 注册 Sa-Token 全局过滤器
     */
    @Bean
    public SaReactorFilter getSaReactorFilter(IgnoreWhiteProperties ignoreWhite) {
        return new SaReactorFilter()
            // 拦截地址
            .addInclude("/**")
            .addExclude("/favicon.ico", "/actuator/**")
            // 鉴权方法：每次访问进入
            .setAuth(obj -> {
                // 登录校验 -- 拦截所有路由
                SaRouter.match("/**")
                    .notMatch(ignoreWhite.getWhites())
                    .check(r -> {
                        // 检查是否登录 是否有token
                        StpUtil.checkLogin();

                    });
            }).setError(e ->{
                NotLoginException exception = (NotLoginException)e;
                String message = null;
                if ("-1".equals(exception.getType())) {
                    message = "未能读取到有效Token";
                } else if ("-2".equals(exception.getType())) {
                    message = "Token无效";
                } else if ("-3".equals(exception.getType())) {
                    message = "Token已过期";
                } else if ("-4".equals(exception.getType())) {
                    message = "Token已被顶下线";
                } else if ("-5".equals(exception.getType())) {
                    message = "Token已被踢下线";
                } else {
                    message = "当前会话未登录";
                }
               return SaResult.error("认证失败，无法访问系统资源，"+message).setCode(HttpStatus.UNAUTHORIZED);
            } );
    }
}
