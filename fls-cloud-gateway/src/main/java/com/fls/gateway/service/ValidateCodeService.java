package com.fls.gateway.service;

import com.fls.common.core.domain.ResponseData;
import com.fls.common.core.exception.CaptchaException;

import java.io.IOException;
import java.util.Map;

/**
 * 验证码处理
 *
 * <AUTHOR>
 * @date 2022/04/11
 */
public interface ValidateCodeService {
    /**
     * 生成验证码
     */
    ResponseData<Map<String, Object>> createCaptcha() throws IOException, CaptchaException;

    /**
     * 校验验证码
     */
    void checkCaptcha(String key, String value) throws CaptchaException;
}
