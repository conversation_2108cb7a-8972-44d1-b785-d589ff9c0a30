package com.fls.gateway;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.context.metrics.buffering.BufferingApplicationStartup;

/**
 * 网关启动程序
 *
 * <AUTHOR>
 * @date 2022/03/24
 */
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
public class FlsGatewayApplication {
    public static void main(String[] args) {
        SpringApplication application = new SpringApplication(FlsGatewayApplication.class);
        application.setApplicationStartup(new BufferingApplicationStartup(2048));
        application.run(args);
    }
}
