<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <LINK rel="Bookmark" href="../images/favicon.ico" >
    <LINK rel="Shortcut Icon" href="../images/favicon.ico" />
    <title>欢迎登录</title>
    <style>
        body {
            background: url('../images/background.jpg') no-repeat;
            background-size: cover;
        }

        #login_box {
            width: 20%;
            height: 300px;
            background-color: rgba(0, 0, 0, 0.21);
            margin: 10% auto auto;
            text-align: center;
            border-radius: 10px;
            padding: 50px 50px;
        }

        h1 {
            color: rgba(25, 54, 241, 0.79);
            margin-top: 5px;
        }


        span {
            color: #fff;
        }

        input {
            border: 0;
            width: 65%;
            font-size: 16px;
            margin-top: 30px;
            height: 36px;
            border-radius: 4px;
            padding-left: 10px;
            outline:none;
        }

        input:focus {
            outline:1px solid #9bc5fa
        }

        button {
            margin-top: 30px;
            width: 68%;
            height: 39px;
            border-radius: 4px;
            border: 0;
            outline: none;
            color: #fff;
            text-align: center;
            font-size: 16px;
            background-image: linear-gradient(to right, #30cfd0, #330867);
        }

        #sign_up {
            margin-top: 45%;
            margin-left: 60%;
        }

        a {
            color: #b94648;
        }
    </style>
</head>

<body>
<script src="../js/jquery.min.js">
</script>

<div id="login_box">
    <h1>欢迎登录</h1>
    <div id="input_box">
        <input id="username" type="text" placeholder="请输入用户名">
    </div>
    <div class="input_box">
        <input id="password" type="password" placeholder="请输入密码">
    </div>
    <button id="loginBtn">登录</button><br>
</div>

<script>
    var redirectUrl="${redirectUrl}";
    document.addEventListener('keydown', function(event) {
        if (event.key === "Enter") {
            var button = document.getElementById("loginBtn");
            button.click();
        }
    });
    $(document).ready(function(){
        $("#loginBtn").click(function(){
          var password = $("#password").val();
          var username = $("#username").val();
            if(username==null||username==''){
                alert("请输入账号！");
                return;
            }
            if(password==null||password==''){
                alert("请输入密码！");
                return;
            }

            $.ajax({
                type: "POST",
                url: "/auth/sso/do-login",
                dataType:"json",
                contentType:'application/json',
                data:JSON.stringify({username:username,password:password}),
                success: function(res){
                    console.log(res);
                    if(res.success){
                        //登录成功，获取返回的url进行重新跳转
                     window.location.href=redirectUrl+"?ticket="+res.data;
                    }else{
                        alert("登录失败，"+res.msg)
                    }
                },
            });
        });
    });
</script>
</body>
</html>
