package com.fls.auth.controller;

import cn.dev33.satoken.SaManager;
import cn.dev33.satoken.stp.StpUtil;
import cn.dev33.satoken.util.SaFoxUtil;
import cn.hutool.core.util.ObjectUtil;
import com.fls.auth.form.LoginBody;
import com.fls.auth.form.TokenDto;
import com.fls.auth.service.SysLoginService;
import com.fls.common.core.constant.HttpStatus;
import com.fls.common.core.domain.ResponseData;
import com.fls.common.redis.utils.RedisUtils;
import com.fls.common.satoken.utils.LoginHelper;
import com.fls.upms.api.model.LoginUser;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

/**
 * 单点登录
 *
 * <AUTHOR>
 * @date 2022/09/01
 **/
@RequiredArgsConstructor
@Controller
@RequestMapping("sso")
@Slf4j
public class SsoController {

    private final SysLoginService sysLoginService;

    /**
     * sso授权地址
     * @param redirectUrl 授权成功转发地址
     * @param mode 授权模式
     * @return
     */
    @GetMapping("auth")
    public String ssoAuth(@RequestParam("redirectUrl")String redirectUrl, String mode, ModelMap modelMap){
        modelMap.addAttribute("redirectUrl",redirectUrl);
        return "login";
    }

    /**
     * sso登录,登录成功带上ticket重定向到
     * @param loginBody
     * @return
     */
    @PostMapping("do-login")
    @ResponseBody
    public ResponseData doLogin(@Validated @RequestBody LoginBody loginBody){
        //登录成功返回token
        String accessToken = sysLoginService.login(loginBody.getUsername(), loginBody.getPassword());
        //把ticket放到跳转url中返回
        //改为使用随机生成ticket，固定值会缓存覆盖造成注销所有会话
        //String ticket = DigestUtils.md5DigestAsHex(loginBody.getUsername().getBytes());
        String ticket = SaFoxUtil.getRandomString(64);
        //设置ticket有效期，一天
        //更改为获取全局有效期
        long tokenTimeout = SaManager.getConfig().getTimeout();
        RedisUtils.setCacheObject(ticket,accessToken, Duration.ofSeconds(tokenTimeout));
        return ResponseData.ok("登录成功",ticket);
    }

    /**
     * 校验ticket
     * @return
     */
    @GetMapping("check-ticket-Login")
    @ResponseBody
    public ResponseData checkTicketLogin(@RequestParam("ticket") String ticket) {
        //获取token
        String token = RedisUtils.getCacheObject(ticket);
        if(ObjectUtil.isEmpty(token)){
            return ResponseData.fail(HttpStatus.FORBIDDEN,"ticket已过期，请重新申请！");
        }
        //获取用户信息
        LoginUser loginUser = LoginHelper.getLoginUser(token);
        if(ObjectUtil.isEmpty(loginUser)){
            return ResponseData.fail(HttpStatus.FORBIDDEN,"ticket已过期，请重新申请！");
        }
        //免密登录
        String accessToken = sysLoginService.login(loginUser.getUsername());
        //把ticket放到跳转url中返回
        String newTicket = SaFoxUtil.getRandomString(64);
        //设置ticket有效期，一天
        //更改为获取全局有效期
        long tokenTimeout = SaManager.getConfig().getTimeout();
        RedisUtils.setCacheObject(newTicket,accessToken, Duration.ofSeconds(tokenTimeout));
        return ResponseData.ok("登录成功",newTicket);
    }
    /**
     * 校验ticket
     * @return
     */
    @GetMapping("check-ticket")
    @ResponseBody
    public ResponseData checkTicket(@RequestParam("ticket") String ticket) {
        //获取token
        String token = RedisUtils.getCacheObject(ticket);
        if(ObjectUtil.isEmpty(token)){
            return ResponseData.fail(HttpStatus.FORBIDDEN,"ticket已过期，请重新申请！");
        }
        //获取用户信息
        LoginUser loginUser = LoginHelper.getLoginUser(token);
        if(ObjectUtil.isEmpty(loginUser)){
            return ResponseData.fail(HttpStatus.FORBIDDEN,"ticket已过期，请重新申请！");
        }
        Map<String,Object> resultMap = new HashMap<>(16);
        resultMap.put("loginUser",loginUser);
        resultMap.put("token",token);
        return ResponseData.ok(resultMap);
    }

    /**
     * 校验token
     * @return
     */
    @GetMapping("check-token")
    @ResponseBody
    public ResponseData checkToken(@RequestParam("token") String token) {
        //获取用户信息
        LoginUser loginUser = LoginHelper.getLoginUser(token);
        if(ObjectUtil.isEmpty(loginUser)){
            return ResponseData.fail(HttpStatus.FORBIDDEN,"token无效，请确认！");
        }
        Map<String,Object> resultMap = new HashMap<>(16);
        resultMap.put("loginUser",loginUser);
        return ResponseData.ok(resultMap);
    }

    /**
     * 注销用户
     * @param tokenDto token接收请求体
     * @return
     */
    @PostMapping("logout")
    @ResponseBody
    public Object logout(@Validated @RequestBody TokenDto tokenDto) {
        Object loginId = StpUtil.getLoginIdByToken(tokenDto.getUserToken());
        if(ObjectUtil.isEmpty(loginId)){
            return ResponseData.fail("注销失败，token无效");
        }
        StpUtil.logoutByTokenValue(tokenDto.getUserToken());
        //改为只注销当token会话
        //StpUtil.logout(loginId);
        return ResponseData.ok("注销成功");
    }
}
