package com.fls.auth.controller;

import cn.dev33.satoken.session.SaSession;
import cn.dev33.satoken.session.TokenSign;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.nacos.api.common.Constants;
import com.fls.auth.form.*;
import com.fls.auth.service.SysLoginService;
import com.fls.common.core.constant.CacheConstants;
import com.fls.common.core.domain.ResponseData;
import com.fls.common.redis.utils.RedisUtils;
import com.fls.common.satoken.utils.LoginHelper;
import com.fls.upms.api.model.LoginUser;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * token 控制
 *
 * <AUTHOR>
 * @date 2022/06/07
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("token")
public class TokenController {

    private final SysLoginService sysLoginService;

    /**
     * PC登录
     */
    @PostMapping("login")
    public ResponseData<Map<String, Object>> login(@Validated @RequestBody LoginBody form) {
        String accessToken = sysLoginService.login(form.getUsername(), form.getPassword());
        Map<String, Object> rspMap = new HashMap<String, Object>(16);
        rspMap.put("accessToken", accessToken);
        return ResponseData.ok(rspMap);
    }

    /**
     * 短信登录
     *
     * @param smsLoginBody 登录信息
     * @return 结果
     */
    @PostMapping("smsLogin")
    public ResponseData<Map<String, Object>> smsLogin(@Validated @RequestBody SmsLoginBody smsLoginBody) {
        Map<String, Object> ajax = new HashMap<>(16);
        String token = sysLoginService.smsLogin(smsLoginBody.getPhonenumber(), smsLoginBody.getSmsCode());
        ajax.put(Constants.TOKEN, token);
        //移除对应的验证码缓存
        RedisUtils.deleteObject(CacheConstants.CAPTCHA_CODE_KEY+smsLoginBody.getPhonenumber());
        return ResponseData.ok(ajax);
    }

    /**
     * 小程序登录
     *
     * @param xcxCode 小程序code
     * @return 结果
     */
    @PostMapping("xcxLogin")
    public ResponseData<Map<String, Object>> xcxLogin(@RequestParam("xcxCode") String xcxCode) {
        Map<String, Object> ajax = new HashMap<>();
        String token = sysLoginService.xcxLogin(xcxCode);
        ajax.put(Constants.TOKEN, token);
        return ResponseData.ok(ajax);
    }

    /**
     * 登出方法
     */
    @GetMapping("logout")
    public ResponseData<Void> logout() {
        sysLoginService.logout();
        return ResponseData.ok();
    }


    /**
     * 刷新token
     */
    @GetMapping("refresh-token")
    public ResponseData<Map<String, Object>> refreshToken() {
        //续签，两个小时过期，需要不断刷新
        StpUtil.renewTimeout(7200L);
        Map<String, Object> result = new HashMap<>(16);
        result.put("token", StpUtil.getTokenValue());
        Calendar dar = Calendar.getInstance();
        dar.setTime(new Date());
        dar.add(java.util.Calendar.HOUR_OF_DAY, 2);
        result.put("expiredTime", dar.getTime());
        result.put("spanSecond", 7200L);
        return ResponseData.ok(result);
    }


    /**
     * 查询token的有效性
     */
    @PostMapping("check-token")
    public ResponseData checkToken(@Validated @RequestBody TokenDto checkTokenDto) {

        Map<String, Object> result = new HashMap<>(16);
        result.put("token", checkTokenDto.getUserToken());
        long time = StpUtil.stpLogic.getTokenSessionTimeoutByTokenValue(checkTokenDto.getUserToken());
        //-2为token不存在，-1为永久时间，大于的则会有过期时间
        result.put("isExist", time > -2);
        if (time == -1) {
            result.put("spanSecond", time);
        } else if (time > 0) {
            result.put("spanSecond", time);
            Calendar dar = Calendar.getInstance();
            dar.setTime(new Date());
            dar.add(Calendar.SECOND, new Long(time).intValue());
            result.put("expiredTime", dar.getTime());
        }
        return ResponseData.ok(result);
    }
    @PostMapping("token-list")
    public ResponseData tokenList(@RequestBody Dict dict){
        Integer start = dict.getInt("start");
        Integer size = dict.getInt("size");
        String keyword = dict.getStr("keyword");
        if(ObjectUtil.isEmpty(keyword)){
            keyword="";
        }
        // 创建集合
        List<UserTokenListDto> sessionList = new ArrayList<>();
        // 分页查询数据
        List<String> sessionIdList = StpUtil.searchSessionId(keyword, start, size);
        for (String sessionId: sessionIdList) {
            SaSession session = StpUtil.getSessionBySessionId(sessionId);
            LoginUser user = BeanUtil.toBean(session.getDataMap().get("loginUser"), LoginUser.class);
            UserTokenListDto userToken = BeanUtil.copyProperties(user, UserTokenListDto.class);
            userToken.setExpireTime(DateUtil.offsetSecond(new Date(), (int)session.getTimeout()));
            List<TokenSign> tokenSignList = session.getTokenSignList();
            List<TokenListDto> tokens=new ArrayList<>();
            for (TokenSign sign : tokenSignList) {
                LoginUser loginUser = LoginHelper.getLoginUser(sign.getValue());
                TokenListDto tokenListDto = BeanUtil.copyProperties(loginUser, TokenListDto.class);
                tokenListDto.setToken(sign.getValue());
                SaSession sessionByToken = StpUtil.getTokenSessionByToken(sign.getValue());
                tokenListDto.setExpireTime(DateUtil.offsetSecond(new Date(), (int)sessionByToken.getTimeout()));
                tokens.add(tokenListDto);
            }
            userToken.setToken(tokens);
            sessionList.add(userToken);
        }
        return ResponseData.ok(sessionList);
    }
}
