package com.fls.auth.form;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fls.upms.api.model.LoginUser;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class UserTokenListDto {
    /**
     * 用户ID
     */
    private String idUser;

    /**
     * 身份id
     */
    private String idIdentity;

    /**
     * 用户名
     */
    private String username;

    /**
     * 昵称
     */
    private String nickName;

    /**
     * 姓名
     */
    private String name;


    /**
     * 手机
     */
    private String phone;

    /**
     * 用户类型
     */
    private String userType;

    /**
     * 登录时间
     */
    private Date loginTime;

    /**
     * 过期时间
     */
    private Date expireTime;


    private List<TokenListDto> token;
}
