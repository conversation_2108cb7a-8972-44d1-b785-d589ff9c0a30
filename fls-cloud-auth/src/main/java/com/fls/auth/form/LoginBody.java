package com.fls.auth.form;

import com.fls.common.core.constant.UserConstants;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

/**
 * 用户登录对象
 *
 * <AUTHOR>
 * @date 2022/06/07
 */
@Data
@NoArgsConstructor
public class LoginBody {

    /**
     * 用户名
     */
    @NotBlank(message = "用户名不能为空！")
    @Length(min = UserConstants.USERNAME_MIN_LENGTH, max = UserConstants.USERNAME_MAX_LENGTH, message = "用户名长度为2-20个字符")
    private String username;

    /**
     * 用户密码
     */
    @NotBlank(message = "用户密码不能为空！")
    @Length(min = UserConstants.PASSWORD_MIN_LENGTH, max = UserConstants.PASSWORD_MAX_LENGTH, message = "密码长度为1-30个字符")
    private String password;

}
