package com.fls.auth.service;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.HexUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson.JSON;
import com.fls.auth.properties.UserPasswordProperties;
import com.fls.common.core.constant.CacheConstants;
import com.fls.common.core.constant.CommonConstants;
import com.fls.common.core.enums.DeviceType;
import com.fls.common.core.enums.LoginType;
import com.fls.common.core.enums.UserType;
import com.fls.common.core.exception.user.CaptchaExpireException;
import com.fls.common.core.exception.user.UserException;
import com.fls.common.core.utils.StringUtils;
import com.fls.common.redis.utils.RedisUtils;
import com.fls.common.satoken.utils.LoginHelper;
import com.fls.upms.api.RemoteUserService;
import com.fls.upms.api.model.LoginUser;
import com.fls.upms.api.model.XcxLoginUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Supplier;

/**
 * 登录校验方法
 *
 * <AUTHOR>
 * @date 2022/06/07
 */
@Service
@Slf4j
public class SysLoginService {


    @DubboReference
    private RemoteUserService remoteUserService;

    @Autowired
    private UserPasswordProperties userPasswordProperties;

    /**
     * 免密登录
     */
    public String login(String username){
        return login(username,null);
    }

    /**
     * 登录
     */
    public String login(String username, String password) {
        //保证调用方获取到请求信息
        LoginUser userInfo = remoteUserService.getUserInfo(username);
        userInfo.setUserType(UserType.SYS_USER.getUserType());
        Map<String,String> user=new HashMap<>();
        user.put("idUser",userInfo.getIdUser());
        user.put("code",userInfo.getUsername());
        user.put("password",password);
        userInfo.setUser(HexUtil.encodeHexStr(JSON.toJSONString(user), StandardCharsets.UTF_8));
        //如果密码为空进行免密登录
        if(ObjectUtil.isNotEmpty(password)){
            checkLogin(LoginType.PASSWORD, username, () -> !userInfo.getPassword().equals(SecureUtil.md5(userInfo.getSalt()+password)));
        }
        // 获取登录token
        LoginHelper.loginByDevice(userInfo, DeviceType.PC);

        return StpUtil.getTokenValue();
    }
    public String smsLogin(String phonenumber, String smsCode) {
        // 通过手机号查找用户
        LoginUser userInfo = remoteUserService.getUserInfoByPhoneNumber(phonenumber);
        userInfo.setUserType(UserType.SYS_USER.getUserType());
        checkLogin(LoginType.SMS, userInfo.getUsername(), () -> !validateSmsCode(phonenumber, smsCode));
        // 生成token
        LoginHelper.loginByDevice(userInfo, DeviceType.APP);

        return StpUtil.getTokenValue();
    }

    public String xcxLogin(String xcxCode) {
        // xcxCode 为 小程序调用 wx.login 授权后获取
        //  自行实现 校验 appid + appsrcret + xcxCode 调用登录凭证校验接口 获取 session_key 与 openid
        String openid = "";
        XcxLoginUser userInfo = remoteUserService.getUserInfoByOpenid(openid);
        // 生成token
        LoginHelper.loginByDevice(userInfo, DeviceType.XCX);

        return StpUtil.getTokenValue();
    }

    /**
     * 退出登录
     */
    public void logout() {
        StpUtil.logout();
    }





    /**
     * 校验短信验证码
     */
    private boolean validateSmsCode(String phonenumber, String smsCode) {
        String code = RedisUtils.getCacheObject(CacheConstants.CAPTCHA_CODE_KEY + phonenumber);
        if (StringUtils.isBlank(code)) {
            throw new CaptchaExpireException();
        }
        return code.equals(smsCode);
    }

    /**
     * 登录校验
     */
    private void checkLogin(LoginType loginType, String username, Supplier<Boolean> supplier) {
        String errorKey = CacheConstants.PWD_ERR_CNT_KEY + username;
        String loginFail = CommonConstants.LOGIN_FAIL;
        Integer maxRetryCount = userPasswordProperties.getMaxRetryCount();
        Integer lockTime = userPasswordProperties.getLockTime();

        // 获取用户登录错误次数(可自定义限制策略 例如: key + username + ip)
        Integer errorNumber = RedisUtils.getCacheObject(errorKey);
        // 锁定时间内登录 则踢出
        if (ObjectUtil.isNotNull(errorNumber) && errorNumber.equals(maxRetryCount)) {
            throw new UserException(loginType.getRetryLimitExceed(), maxRetryCount, lockTime);
        }
         //密码错误
        if (supplier.get()) {
            // 是否第一次
            errorNumber = ObjectUtil.isNull(errorNumber) ? 1 : errorNumber + 1;
            // 达到规定错误次数 则锁定登录
            if (errorNumber.equals(maxRetryCount)) {
                RedisUtils.setCacheObject(errorKey, errorNumber, Duration.ofMinutes(lockTime));
                throw new UserException(loginType.getRetryLimitExceed(), maxRetryCount, lockTime);
            } else {
                // 未达到规定错误次数 则递增
                RedisUtils.setCacheObject(errorKey, errorNumber);
                throw new UserException(loginType.getRetryLimitCount(), errorNumber);
            }
        }
        // 登录成功 清空错误次数
        RedisUtils.deleteObject(errorKey);
    }
}
