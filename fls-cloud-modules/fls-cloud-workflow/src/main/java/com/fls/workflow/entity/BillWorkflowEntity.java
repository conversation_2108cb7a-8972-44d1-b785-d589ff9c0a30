package com.fls.workflow.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fls.workflow.entity.base.BaseEntityForProcess;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 单据流程关系扩展
 *
 * <AUTHOR>
 * @since 2024-08-10
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("t_bill_workflow")
public class BillWorkflowEntity extends BaseEntityForProcess {
    private String billCode;

    private String billName;

    private String transTypeCode;

    private String transTypeName;

    private String version;

    private String flowableModelId;

    private String flowableModelKey;

    private String status;
}
