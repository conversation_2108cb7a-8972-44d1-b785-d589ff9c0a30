package com.fls.workflow.entity.base;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 树形实体基类
 *
 * <AUTHOR>
 * @since 2024-08-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
public abstract class TreeEntityForProcess<T> extends BaseEntityForProcess {

    private static final long serialVersionUID = 1L;

    /**
     * 父级编号
     */
    protected String parentId;

    /**
     * 所有父级编号
     */
    protected String parentIds;

    /**
     * 名称
     */
    protected String name;

    /**
     * 排序
     */
    protected Integer sort;

    /**
     * 子元素集合
     */
    @TableField(exist = false)
    protected List <T> children;

    /**
     * 构造函数
     */
    public TreeEntityForProcess() {
        super ( );
    }

    /**
     * 构造函数
     *
     * @param id
     */
    public TreeEntityForProcess(String id) {
        super ( id );
    }


}
