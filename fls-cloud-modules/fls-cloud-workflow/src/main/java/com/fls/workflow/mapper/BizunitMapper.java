package com.fls.workflow.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.fls.master.api.model.BizunitNameInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;


@Mapper
@DS("fls_db")
public interface BizunitMapper {
    /**
     * 查询经营主体列表
     * @param idsBizunit 经营主体集合
     * @return 经营主体列表
     */
    List<BizunitNameInfo> getBizunitList(@Param("idsBizunit") Set<String> idsBizunit);
}
