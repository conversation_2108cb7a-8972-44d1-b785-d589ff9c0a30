package com.fls.workflow.controller;

import cn.hutool.core.lang.Dict;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fls.common.core.domain.ResponseData;
import com.fls.common.core.exception.ServiceException;
import com.fls.common.core.utils.JsonUtils;
import com.fls.common.mybatis.core.page.PageResult;
import com.fls.workflow.engine.CustomProcessDiagramGenerator;
import com.fls.workflow.entity.BillWorkflowEntity;
import com.fls.workflow.enums.ActionType;
import com.fls.workflow.enums.ApproveCodeEnum;
import com.fls.workflow.enums.ProcessStatus;
import com.fls.workflow.model.Flow;
import com.fls.workflow.model.TaskComment;
import com.fls.workflow.model.UserTaskFormData;
import com.fls.workflow.model.request.ApproveProcessReq;
import com.fls.workflow.model.request.ProcessApproveReq;
import com.fls.workflow.model.request.ProcessTodoReq;
import com.fls.workflow.model.request.ProcessTransferReq;
import com.fls.workflow.model.request.StartProcessReq;
import com.fls.workflow.model.request.TodoTaskReq;
import com.fls.workflow.model.response.ExecuteProcessResp;
import com.fls.workflow.model.vo.FlowNodeVo;
import com.fls.workflow.model.vo.FlowVo;
import com.fls.workflow.model.vo.HisTaskVo;
import com.fls.workflow.model.vo.ProcessVo;
import com.fls.workflow.model.vo.TaskVo;
import com.fls.workflow.model.vo.TodoTaskVo;
import com.fls.workflow.service.BillWorkflowService;
import com.fls.workflow.service.FlowTaskService;
import com.fls.workflow.service.ProcessService;
import com.fls.workflow.service.TodoTaskService;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.flowable.bpmn.model.BpmnModel;
import org.flowable.engine.HistoryService;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.history.HistoricActivityInstance;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.image.impl.DefaultProcessDiagramGenerator;
import org.flowable.task.api.Task;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.ResponseEntity;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

/**
 * 流程中心通用能力接口
 *
 * <AUTHOR>
 * @since 2024-08-10
 */
@RestController
@RequestMapping("/workflow")
public class ProcessController {

    @Autowired
    @Qualifier("customProcessService")
    private ProcessService processService;

    @Resource
    private HistoryService historyService;

    @Resource
    private FlowTaskService flowTaskService;

    @Resource
    private BillWorkflowService billWorkflowService;

    @Resource
    private RepositoryService repositoryService;

    @Resource
    private RuntimeService runtimeService;

    @Resource
    private TodoTaskService todoTaskService;

    /**
     * 发起流程
     *
     * @param req 流程发起请求，带用户id
     * @return 流程启动响应
     */
    @PostMapping("/start")
    public ResponseData<ExecuteProcessResp> startProcess(@RequestBody @Valid StartProcessReq req) {
        return ResponseData.ok(processService.startProcess(req));
    }

    /**
     * 审批流程
     *
     * @param req 流程审批请求req
     * @return 流程执行结果响应
     */
    @PostMapping("/approve")
    public ResponseData<ExecuteProcessResp> approveProcess(@RequestBody @Valid ApproveProcessReq req) {
        // 查询任务
        String userId = req.getUserId();
        Task task = processService.queryTaskByProcessInsId(req.getProcessInstanceId(), userId);
        if (ObjectUtils.isEmpty(task)) {
            throw new ServiceException("当前用户任务不存在");
        }
        // todo 更新为参数校验响应
        Integer result = req.getResult();
        if (result == null) {
            throw new ServiceException("审批结果不能为空");
        }
        String remarks = req.getRemarks();
        Flow flow = new Flow();
        flow.setProcInsId(req.getProcessInstanceId());
        flow.setTaskId(task.getId());
        flow.setComment(new TaskComment());
        flow.getComment().setMessage(remarks);
        flow.setUserId(userId);
        if (!ObjectUtils.isEmpty(req.getUserIds())) {
            flow.setAssignee(String.join(",", req.getUserIds()));
        }
        ApproveCodeEnum approveCodeEnum = ApproveCodeEnum.getEnumByCode(result);
        switch (approveCodeEnum) {
            case SUBMITTED:
                HistoricProcessInstance hi = historyService.createHistoricProcessInstanceQuery()
                    .processInstanceId(req.getProcessInstanceId())
                    .singleResult();
                String startUserId = hi.getStartUserId();
                if (userId.equalsIgnoreCase(startUserId) && "提交申请".equals(task.getName())) {
                    flow.getComment().setCommentType(TaskComment.prefix + ActionType.RECOMMIT.getType());
                    flow.getComment().setStatus(ActionType.RECOMMIT.getStatus());
                } else {
                    flow.getComment().setCommentType(TaskComment.prefix + ActionType.AGREE.getType());
                    flow.getComment().setStatus(ActionType.AGREE.getStatus());
                }
                processService
                    .submitTaskFormData(new UserTaskFormData(flow, JsonUtils.toJsonString(req.getData())));
                break;
            case REJECTED:
                // 驳回
                flow.getComment().setCommentType(TaskComment.prefix + ActionType.REJECT.getType());
                flow.getComment().setStatus(ActionType.REJECT.getStatus());
                flowTaskService.rejected(req.getProcessInstanceId(), task.getId(), flow.getComment(), userId);
                break;
            default:
                processService
                    .submitTaskFormData(new UserTaskFormData(flow, JsonUtils.toJsonString(req.getData())));
        }
        return ResponseData.ok(processService.getBillFlowResp(req.getProcessInstanceId()));
    }

    @PostMapping("/approve/disagree")
    public ResponseData<ExecuteProcessResp> approveDisagree(@RequestBody @Valid ApproveProcessReq req) {
        String userId = req.getUserId();
        Task task = processService.queryTaskByProcessInsId(req.getProcessInstanceId(), userId);
        if (ObjectUtils.isEmpty(task)) {
            throw new ServiceException("当前用户,任务不存在");
        }
        processService.stopProcessInstanceById(req.getProcessInstanceId(), ProcessStatus.STOP,
            StringUtils.isEmpty(req.getRemarks()) ? "审批不同意" : req.getRemarks(), userId);
        todoTaskService.handleTodo(task, userId);
        return ResponseData.ok(processService.getBillFlowResp(req.getProcessInstanceId()));
    }

    /**
     * 待办任务查询
     *
     * @param req 待办任务查询条件
     * @return
     */
    @PostMapping("/todo")
    public ResponseData<Page<ProcessVo>> todo(@RequestBody ProcessTodoReq req) {
        return ResponseData.ok(processService.todo(req));
    }


    @PostMapping("/todo/tasks")
    public ResponseData<PageResult<TodoTaskVo>> queryTodoTasks(@Valid @RequestBody TodoTaskReq req) {
        return ResponseData.ok(processService.queryTodoTasks(req));
    }

    /**
     * 流程审批历史任务
     *
     * @param params 流程实例id
     * @return 流程审批历史信息
     */
    @PostMapping("/detail")
    public ResponseData<FlowVo> historicTaskList(@RequestBody Dict params) {
        String processInstanceId = params.getStr("processInstanceId");
        // todo 改为参数校验+统一异常处理
        if (ObjectUtils.isEmpty(processInstanceId)) {
            return ResponseData.fail("processInstanceId不能为空");
        }
        List<Flow> historicTaskList = flowTaskService.historicTaskList(processInstanceId);
        FlowVo flowVo = new FlowVo();
        flowVo.setHistoryFlowList(historicTaskList);
        List<FlowNodeVo> flowNodeVos = new ArrayList<>(0);
        for (Flow flow : historicTaskList) {
            FlowNodeVo flowNodeVo = new FlowNodeVo();
            flowNodeVo.setNodeName(flow.getHistIns().getActivityName());
            flowNodeVo.setCreateTime(flow.getHistIns().getTime());
            flowNodeVo.setEndTime(flow.getHistIns().getEndTime());
            flowNodeVo.setAssigneeName(flow.getAssigneeName());
            flowNodeVos.add(flowNodeVo);

        }
        flowVo.setFlowNodeList(flowNodeVos);
        return ResponseData.ok(flowVo);
    }

    /**
     * 流程撤回
     *
     * @param dict 流程实例id
     * @return 流程执行结果响应
     */
    @PostMapping("/revoke")
    public ResponseData<ExecuteProcessResp> revoke(@RequestBody Dict dict) {
        String processInstanceId = dict.getStr("processInstanceId");
        String userId = dict.getStr("userId");
        if (ObjectUtils.isEmpty(processInstanceId)) {
            return ResponseData.fail("processInstanceId不能为空");
        }
        flowTaskService.revoke(processInstanceId, userId);
        return ResponseData.ok(processService.getBillFlowResp(processInstanceId));
    }

    /**
     * 流程作废
     *
     * @param req 流程作废请求
     * @return 流程执行结果响应
     */
    @PostMapping("/voided")
    public ResponseData<ExecuteProcessResp> voided(@RequestBody @Valid ProcessApproveReq req) {
        String userId = req.getUserId();
        Task task = processService.queryTaskByProcessInsId(req.getProcessInstanceId(), userId);
        if (ObjectUtils.isEmpty(task)) {
            throw new ServiceException("当前用户,任务不存在");
        }
        HistoricProcessInstance hi = historyService.createHistoricProcessInstanceQuery()
            .processInstanceId(req.getProcessInstanceId())
            .singleResult();
        String startUserId = hi.getStartUserId();
        if (!userId.equalsIgnoreCase(startUserId)) {
            throw new ServiceException("当前审批人不是发起人，不能作废该流程");
        }
        processService.stopProcessInstanceById(req.getProcessInstanceId(), ProcessStatus.DELETED, req.getRemarks(),
            userId);
        return ResponseData.ok(processService.getBillFlowResp(req.getProcessInstanceId()));
    }

    /**
     * 驳回上一审批人
     *
     * @param req 驳回请求
     * @return 流程执行结果响应
     */
    @PostMapping("/reject/back")
    public ResponseData<ExecuteProcessResp> rejectback(@RequestBody @Valid ProcessApproveReq req) {
        String userId = req.getUserId();
        Task task = processService.queryTaskByProcessInsId(req.getProcessInstanceId(), userId);
        if (ObjectUtils.isEmpty(task)) {
            throw new ServiceException("当前用户,任务不存在");
        }
        List<Flow> nodes = flowTaskService.getBackNodes(task.getId());
        if (ObjectUtils.isEmpty(nodes)) {
            throw new ServiceException("没有可以驳回的节点");
        }
        Flow backFlow = nodes.get(nodes.size() - 1);
        TaskComment comment = new TaskComment();
        comment.setMessage(req.getRemarks());
        comment.setCommentType(TaskComment.prefix + ActionType.REJECT.getType());
        comment.setStatus(ActionType.REJECT.getStatus());
        flowTaskService.backTask(backFlow.getTaskDefKey(), task.getId(), comment, userId);
        todoTaskService.handleTodo(task, userId);
        return ResponseData.ok(processService.getBillFlowResp(req.getProcessInstanceId()));
    }

    /**
     * 驳回至申请人节点
     *
     * @param req 驳回请求
     * @return 流程执行结果响应
     */
    @PostMapping("/reject/applicant")
    public ResponseData<ExecuteProcessResp> rejectApplicant(@RequestBody @Valid ProcessApproveReq req) {
        String userId = req.getUserId();
        Task task = processService.queryTaskByProcessInsId(req.getProcessInstanceId(), userId);
        if (ObjectUtils.isEmpty(task)) {
            throw new ServiceException("当前用户,任务不存在");
        }
        TaskComment comment = new TaskComment();
        comment.setMessage(StringUtils.isEmpty(req.getRemarks()) ? "退回制单人" : req.getRemarks());
        comment.setCommentType(TaskComment.prefix + ActionType.BACK.getType());
        comment.setStatus(ActionType.BACK.getStatus());
        flowTaskService.rejected(req.getProcessInstanceId(), task.getId(), comment, "REJECT_CREATOR");
        todoTaskService.handleTodo(task, userId);
        return ResponseData.ok(processService.getBillFlowResp(req.getProcessInstanceId()));
    }

    /**
     * 我的已办列表
     *
     * @param req
     * @return
     */
    @PostMapping("/list")
    public ResponseData<Page<HisTaskVo>> historicListData(@RequestBody ProcessTodoReq req) {
        return ResponseData.ok(processService.list(req));
    }

    /**
     * 获取流程实例状态
     *
     * @param processInstanceId 流程实例id
     * @return 流程实例状态
     */
    @GetMapping("/status")
    public ResponseData<Integer> getProcessStatus(@RequestParam(required = true) String processInstanceId) {
        return ResponseData.ok(processService.getProcessStatus(processInstanceId));
    }

    /**
     * 流程转办
     *
     * @param transferReq 转办请求
     * @return 流程执行结果响应
     */
    @PostMapping("/transfer")
    public ResponseData<String> transferTask(@RequestBody @Valid ProcessTransferReq transferReq) {
        // 查询任务
        String userId = transferReq.getUserId();
        Task task = processService.queryTaskByProcessInsId(transferReq.getProcessInstanceId(), userId);
        if (ObjectUtils.isEmpty(task)) {
            throw new ServiceException("当前用户,任务不存在");
        }
        processService.transferTask(task.getId(), transferReq.getTransUserId());
        todoTaskService.transferTodo(task, userId, transferReq.getTransUserId());
        return ResponseData.ok("转派成功");
    }

    /**
     * 获取流程审批人
     *
     * @param processInstanceId 流程实例id
     * @return 流程审批人
     */
    @GetMapping("/assignees")
    public ResponseData<List<String>> getAssignees(@RequestParam(required = true) String processInstanceId) {
        return ResponseData.ok(processService.getAssignees(processInstanceId));
    }

    /**
     * 获取流程待审任务列表
     *
     * @param dict 流程实例id
     * @return 流程执行结果响应
     */
    @PostMapping("/tasks")
    public ResponseData<List<TaskVo>> getTasks(@RequestBody Dict dict) {
        String processInstanceId = dict.getStr("processInstanceId");
        if (ObjectUtils.isEmpty(processInstanceId)) {
            return ResponseData.fail("processInstanceId不能为空");
        }
        return ResponseData.ok(processService.getTasks(processInstanceId));
    }

    /**
     * 挂起、激活流程实例
     */
    @PutMapping("update/{state}")
    public ResponseEntity<String> updateState(@PathVariable("state") String state, String procDefId, String id) {
        if ("active".equals(state)) {
            // 增加对单据流程扩展信息的停用逻辑
            BillWorkflowEntity bill = billWorkflowService.lambdaQuery().eq(BillWorkflowEntity::getFlowableModelId, id)
                .one();
            if (bill != null) {
                List<BillWorkflowEntity> billWorkflowList = billWorkflowService.lambdaQuery()
                    .eq(BillWorkflowEntity::getBillCode, bill.getBillCode())
                    .eq(BillWorkflowEntity::getTransTypeCode, bill.getTransTypeCode())
                    .list();
                for (BillWorkflowEntity flow : billWorkflowList) {
                    // 停用其它流程
                    if (!flow.getId().equals(bill.getId()) && !"3".equals(flow.getStatus())) {
                        billWorkflowService.lambdaUpdate().set(BillWorkflowEntity::getStatus, "3")
                            .eq(BillWorkflowEntity::getId, flow.getId()).update();
                        ProcessDefinition processDefinition = processService
                            .getProcessDefinitionByKey(flow.getFlowableModelKey());
                        if (processDefinition != null) {
                            processService.updateState("suspend", processDefinition.getId());
                        }
                    }
                }
                // 启用
                billWorkflowService.lambdaUpdate().set(BillWorkflowEntity::getStatus, "2")
                    .eq(BillWorkflowEntity::getId, bill.getId()).update();
            }
        } else if ("suspend".equals(state)) {
            billWorkflowService.lambdaUpdate().set(BillWorkflowEntity::getStatus, "3")
                .eq(BillWorkflowEntity::getFlowableModelId, id).update();
        }
        String message = processService.updateState(state, procDefId);
        return ResponseEntity.ok(message);
    }

    /**
     * 查询流程图
     *
     * @param procInsId 流程实例id
     * @param response  响应对象
     */
    @RequestMapping(value = "flowChart")
    public void getJpgActivityDiagram(@RequestParam String procInsId, HttpServletResponse response) {
        // 获得已经办理的历史节点
        List<HistoricActivityInstance> activityInstances = historyService.createHistoricActivityInstanceQuery()
            .processInstanceId(procInsId).orderByHistoricActivityInstanceStartTime().asc().list();
        List<String> activties = new ArrayList<>();
        List<String> flows = new ArrayList<>();
        for (HistoricActivityInstance activityInstance : activityInstances) {
            if ("sequenceFlow".equals(activityInstance.getActivityType())) {
                // 需要高亮显示的连接线
                flows.add(activityInstance.getActivityId());
            } else {
                // 需要高亮显示的节点
                activties.add(activityInstance.getActivityId());
            }
        }

        HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery()
            .processInstanceId(procInsId).singleResult();
        if (ObjectUtils.isEmpty(historicProcessInstance)) {
            throw new ServiceException("流程实例不存在");
        }
        String procDefId = historicProcessInstance.getProcessDefinitionId();

        try {
            // 根据modelId或者BpmnModel
            BpmnModel bpmnModel = repositoryService.getBpmnModel(procDefId);
            // 获得图片流
            DefaultProcessDiagramGenerator diagramGenerator = new CustomProcessDiagramGenerator();
            InputStream inputStream = diagramGenerator.generateDiagram(
                bpmnModel,
                "png",
                activties,
                flows,
                "宋体",
                "宋体",
                "宋体",
                null,
                1.0,
                false);
            // 输出图片
            IOUtils.copy(inputStream, response.getOutputStream());
            response.setHeader("Content-Disposition",
                "attachment; filename=" + bpmnModel.getMainProcess().getId() + ".png");
            response.flushBuffer();
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @PostMapping("/task/next")
    public ResponseData<?> getNextStepInfo(@RequestBody @Valid ProcessApproveReq req) {
        String processInstanceId = req.getProcessInstanceId();
        Task task = flowTaskService.isNextDirectlyEnd(processInstanceId);
        JSONObject result = new JSONObject();
        if (!ObjectUtils.isEmpty(task)) {
            result.set("id", task.getId());
            result.set("name", task.getName());
            result.set("processInstanceId", processInstanceId);
        }
        return ResponseData.ok(result);
    }
}
