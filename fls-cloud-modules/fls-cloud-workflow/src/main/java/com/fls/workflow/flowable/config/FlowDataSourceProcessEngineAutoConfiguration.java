package com.fls.workflow.flowable.config;

import com.fls.workflow.service.FlowIdentityServiceImpl;
import org.flowable.idm.engine.IdmEngineConfiguration;
import org.flowable.idm.engine.configurator.IdmEngineConfigurator;
import org.flowable.spring.SpringProcessEngineConfiguration;
import org.flowable.spring.boot.EngineConfigurationConfigurer;
import org.flowable.spring.boot.FlowableProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.sql.DataSource;

@Configuration
@EnableConfigurationProperties(FlowableProperties.class)
public class FlowDataSourceProcessEngineAutoConfiguration implements EngineConfigurationConfigurer<SpringProcessEngineConfiguration> {


    @Bean
    public IdmEngineConfigurator idmEngineConfigurator(DataSource dataSource) {
        IdmEngineConfiguration idmEngineConfiguration = new IdmEngineConfiguration();
        idmEngineConfiguration.setDataSource(dataSource);
        idmEngineConfiguration.setIdmIdentityService(new FlowIdentityServiceImpl(idmEngineConfiguration));

        IdmEngineConfigurator idmEngineConfigurator = new IdmEngineConfigurator();
        idmEngineConfigurator.setIdmEngineConfiguration(idmEngineConfiguration);
        return idmEngineConfigurator;
    }

    @Override
    public void configure(SpringProcessEngineConfiguration springProcessEngineConfiguration) {
    }
}
