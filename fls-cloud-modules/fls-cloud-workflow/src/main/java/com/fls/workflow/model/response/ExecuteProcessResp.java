package com.fls.workflow.model.response;

import com.fls.workflow.model.vo.TaskVo;
import lombok.Data;

import java.util.List;

/**
 * 启动流程响应对象
 *
 * <AUTHOR>
 * @since 2024-08-10
 */
@Data
public class ExecuteProcessResp
{
    /**
     * 流程实例ID
     */
    private String processInstanceId;

    /**
     * 当前审批人信息
     */
    private List<String> userIds;

    /**
     * 当前流程状态 1-执行中   2-已完成
     */
    private int status;

    /**
     * 当前审批节点信息
     */
    private TaskVo task;
}
