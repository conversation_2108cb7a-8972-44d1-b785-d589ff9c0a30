package com.fls.workflow.model.request;

import cn.hutool.json.JSONObject;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

@Data
public class ProcessApproveReq {
    @NotBlank
    private String processInstanceId;

    @NotBlank
    private String userId;

    private JSONObject data;

    private String remarks;

    private Integer result;

    private List<String> userIds;
}
