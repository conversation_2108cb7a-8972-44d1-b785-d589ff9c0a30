package com.fls.workflow.model.request;

import cn.hutool.json.JSONObject;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

@Data
public class ApproveProcessReq {

    @NotBlank
    private String userId;

    @NotBlank
    private String processInstanceId;

    private JSONObject data;

    private String remarks;

    private Integer result;

    private List<String> userIds;
}
