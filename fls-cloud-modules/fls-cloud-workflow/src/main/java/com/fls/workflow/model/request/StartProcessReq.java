package com.fls.workflow.model.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 单据流程请求参数对象
 *
 * <AUTHOR>
 *
 */
@Data
public class StartProcessReq
{
    @NotBlank(message = "用户id不能为空")
    private String userId;

    @NotBlank(message = "单据编码不能为空")
	private String billCode;

	private String transTypeCode;

    private String title;

    @NotNull(message = "表单入参不能为空")
    private Object data;

    private boolean findHead;
}
