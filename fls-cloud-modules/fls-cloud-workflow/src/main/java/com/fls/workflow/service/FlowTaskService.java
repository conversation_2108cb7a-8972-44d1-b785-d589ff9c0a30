package com.fls.workflow.service;

import cn.hutool.core.util.StrUtil;
import com.fls.common.core.exception.ServiceException;
import com.fls.workflow.config.WorkflowCenterProperties;
import com.fls.workflow.constant.CommonConstants;
import com.fls.workflow.constant.FlowableConstant;
import com.fls.workflow.enums.ActionType;
import com.fls.workflow.model.Flow;
import com.fls.workflow.model.TaskComment;
import com.fls.workflow.model.request.BackTaskReq;
import com.fls.workflow.utils.FlowableUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.flowable.bpmn.constants.BpmnXMLConstants;
import org.flowable.bpmn.model.BpmnModel;
import org.flowable.bpmn.model.EndEvent;
import org.flowable.bpmn.model.FlowNode;
import org.flowable.bpmn.model.Process;
import org.flowable.bpmn.model.SequenceFlow;
import org.flowable.engine.HistoryService;
import org.flowable.engine.ManagementService;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.TaskService;
import org.flowable.engine.history.HistoricActivityInstance;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.runtime.ActivityInstance;
import org.flowable.engine.runtime.Execution;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.task.api.DelegationState;
import org.flowable.task.api.Task;
import org.flowable.task.api.TaskQuery;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.flowable.variable.api.history.HistoricVariableInstanceQuery;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 流程定义相关Service
 *
 * <AUTHOR>
 * @since 2024-08-10
 */
@Slf4j
@Service
public class FlowTaskService {

    @Autowired
    private TaskService taskService;

    @Autowired
    private RuntimeService runtimeService;

    @Autowired
    private RepositoryService repositoryService;

    @Autowired
    private HistoryService historyService;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private FlowableBpmnModelService flowableBpmnModelService;

    @Autowired
    private ManagementService managementService;

    @Autowired
    @Qualifier("customProcessService")
    private ProcessService processService;

    @Autowired
    private MasterDataService masterDataService;

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private WorkflowCenterProperties workflowCenterProperties;

    @Resource
    private TodoTaskService todoTaskService;

    /**
     * 提交任务, 并保存意见
     *
     * @param vars 任务变量
     */
    @Transactional
    public void complete(Flow flow, Map<String, Object> vars) {
        // 添加意见
        if (StrUtil.isNotBlank(flow.getProcInsId())) {
            taskService.addComment(flow.getTaskId(), flow.getProcInsId(), flow.getComment().getCommentType(), flow.getComment().getFullMessage());
        }

        // 设置流程变量
        if (vars == null) {
            vars = Maps.newHashMap();
        }

        // 设置流程标题
        if (StrUtil.isNotBlank(flow.getTitle())) {
            vars.put(FlowableConstant.TITLE, flow.getTitle());
        }

        Task task = taskService.createTaskQuery().taskId(flow.getTaskId()).singleResult();
        // owner不为空说明可能存在委托任务
        if (StrUtil.isNotBlank(task.getOwner())) {
            DelegationState delegationState = task.getDelegationState();
            switch (delegationState) {
                case PENDING:
                    taskService.resolveTask(flow.getTaskId());
                    taskService.complete(flow.getTaskId(), vars);
                    break;

                case RESOLVED:
                    // 委托任务已经完成
                    break;

                default:
                    // 不是委托任务
                    taskService.complete(flow.getTaskId(), vars);
                    break;
            }
        } else if (StrUtil.isBlank(task.getAssignee())) { // 未签收任务
            // 签收任务
            taskService.claim(flow.getTaskId(), String.valueOf(vars.get("userId")));
            // 提交任务
            taskService.complete(flow.getTaskId(), vars);
        } else {
            // 提交任务
            taskService.complete(flow.getTaskId(), vars);
        }
    }

    /*
     * 驳回任务
     */
    @Transactional
    public String backTask(String backTaskDefKey, String taskId, TaskComment comment, String userId) {
        //调用业务系统接口，让业务系统更新其退款单号状态
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        BackTaskReq backTaskReq = new BackTaskReq(backTaskDefKey, taskId, comment, Collections.emptyList());
        HttpEntity<BackTaskReq> formEntity = new HttpEntity<>(backTaskReq, headers); //允许传递body中的null值
        ResponseEntity<String> rep = restTemplate.postForEntity(workflowCenterProperties.getBackTaskPath(), formEntity, String.class);
        if (rep.getStatusCode() != HttpStatus.OK) {
            log.error("调用业务系统接口失败：{}", rep);
            throw new ServiceException("流程回退失败：" + rep.getBody());
        }
        return rep.getBody();
    }

    /**
     * 是否可以取回任务
     */
    public boolean isBack(HistoricTaskInstance hisTask) {
        ProcessInstance pi = runtimeService.createProcessInstanceQuery()
            .processInstanceId(hisTask.getProcessInstanceId()).singleResult();
        if (pi != null) {
            if (pi.isSuspended()) {
                return false;
            } else {
                Task currentTask = taskService.createTaskQuery().processInstanceId(hisTask.getProcessInstanceId()).list().get(0);
                HistoricTaskInstance lastHisTask = historyService.createHistoricTaskInstanceQuery().processInstanceId(hisTask.getProcessInstanceId()).finished()
                    .includeProcessVariables().orderByHistoricTaskInstanceEndTime().desc().list().get(0);

                if (currentTask.getClaimTime() != null) {//用户已签收
                    return false;
                }
                if (hisTask.getId().equals(lastHisTask.getId())) {
                    return true;
                }
                return false;
            }

        } else {
            return false;
        }
    }

    /**
     * 获取可驳回节点
     *
     * @param taskId
     * @return
     */
    public List<Flow> getBackNodes(String taskId) {
        Task taskEntity = taskService.createTaskQuery().taskId(taskId).singleResult();
        String processInstanceId = taskEntity.getProcessInstanceId();
        String currActId = taskEntity.getTaskDefinitionKey();
        String processDefinitionId = taskEntity.getProcessDefinitionId();
        Process process = repositoryService.getBpmnModel(processDefinitionId).getMainProcess();
        FlowNode currentFlowElement = (FlowNode) process.getFlowElement(currActId, true);
        List<ActivityInstance> activitys =
            runtimeService.createActivityInstanceQuery().processInstanceId(processInstanceId).finished().orderByActivityInstanceStartTime().asc().list();
        List<String> activityIds =
            activitys.stream().filter(activity -> activity.getActivityType().equals(BpmnXMLConstants.ELEMENT_TASK_USER) ||
                    activity.getActivityType().equals(BpmnXMLConstants.ELEMENT_EVENT_START)).filter(activity -> !activity.getActivityId().equals(currActId))
                .map(ActivityInstance::getActivityId).distinct().collect(Collectors.toList());
        List<Flow> result = new ArrayList<>();
        for (String activityId : activityIds) {
            FlowNode toBackFlowElement = (FlowNode) process.getFlowElement(activityId, true);
            if (FlowableUtils.isReachable(process, toBackFlowElement, currentFlowElement)) {
                Flow vo = new Flow();
                vo.setTaskDefKey(activityId);
                vo.setTaskName(toBackFlowElement.getName());
                vo.setTaskId(activityId);
                result.add(vo);
            }
        }
        return result;
    }

    /**
     * 驳回任务
     *
     * @param processInstanceId
     * @return
     */
    public ResponseEntity rejected(String processInstanceId, String taskId, TaskComment comment, String userId) {
        ProcessInstance pi = runtimeService.createProcessInstanceQuery().processInstanceId(processInstanceId)
            .singleResult();
        if (pi == null) {
            throw new ServiceException("审批流程不存在！");
        }
        if (pi.isSuspended()) {
            throw new ServiceException("任务已终止！");
        }
        List<Flow> flowList = this.getBackNodes(taskId);
        if (ObjectUtils.isEmpty(flowList)) {
            throw new ServiceException("没有可以驳回的节点");
        }
        // 优先驳回至申请人  如果没有就驳回至上一层
        for (Flow node : flowList) {
            if (CommonConstants.SUBMIT.equals(node.getTaskName())) {
                String procInsId = this.backTask(node.getTaskDefKey(), taskId, comment, userId);
                return ResponseEntity.ok(procInsId);
            }
        }
        Flow backFlow = flowList.get(flowList.size() - 1);
        String procInsId = this.backTask(backFlow.getTaskDefKey(), taskId, comment, userId);
        return ResponseEntity.ok(procInsId);
    }

    /**
     * 获取流转历史任务列表
     *
     * @param procInsId 流程实例
     */
    public List<Flow> historicTaskList(String procInsId) {
        List<Flow> actList = Lists.newArrayList();
        List<HistoricActivityInstance> list = Lists.newArrayList();
        List<HistoricActivityInstance> historicActivityInstances2 = historyService.createHistoricActivityInstanceQuery().processInstanceId(procInsId)
            .orderByHistoricActivityInstanceStartTime().asc().orderByHistoricActivityInstanceEndTime().asc().list();
        for (HistoricActivityInstance historicActivityInstance : historicActivityInstances2) {
            if (historicActivityInstance.getEndTime() != null) {
                list.add(historicActivityInstance);
            }
        }

        for (HistoricActivityInstance historicActivityInstance : historicActivityInstances2) {
            if (historicActivityInstance.getEndTime() == null) {
                list.add(historicActivityInstance);
            }
        }

        for (int i = 0; i < list.size(); i++) {
            HistoricActivityInstance histIns = list.get(i);
            // 只显示开始节点和结束节点，并且执行人不为空的任务
            if (StrUtil.isNotBlank(histIns.getAssignee())
                && historyService.createHistoricTaskInstanceQuery().taskId(histIns.getTaskId()).count() != 0
                || BpmnXMLConstants.ELEMENT_TASK_USER.equals(histIns.getActivityType()) && histIns.getEndTime() == null
                || BpmnXMLConstants.ELEMENT_EVENT_START.equals(histIns.getActivityType())
                || BpmnXMLConstants.ELEMENT_EVENT_END.equals(histIns.getActivityType())) {
                // 获取流程发起人名称
                Flow e = queryTaskState(histIns);

                actList.add(e);
            }
        }
        return actList;
    }

    /**
     * 查询任务节点的状态
     */
    public Flow queryTaskState(HistoricActivityInstance histIns) {
        Flow e = new Flow();
        e.setHistIns(histIns);
        // 获取流程发起人名称
        if (BpmnXMLConstants.ELEMENT_EVENT_START.equals(histIns.getActivityType())) {
            List<HistoricProcessInstance> il =
                historyService.createHistoricProcessInstanceQuery().processInstanceId(histIns.getProcessInstanceId()).orderByProcessInstanceStartTime().asc().list();
            if (il.size() > 0) {
                if (StrUtil.isNotBlank(il.get(0).getStartUserId())) {
                    String userNameByUserId = masterDataService.getUserNameByUserId(il.get(0).getStartUserId());
                    e.setAssignee(histIns.getAssignee());
                    e.setAssigneeName(userNameByUserId);
                }
            }
            TaskComment taskComment = new TaskComment();
            taskComment.setStatus(FlowableConstant.START_EVENT_LABEL);
            taskComment.setMessage(FlowableConstant.START_EVENT_COMMENT);
            e.setComment(taskComment);
            return e;
        }
        if (BpmnXMLConstants.ELEMENT_EVENT_END.equals(histIns.getActivityType())) {
            TaskComment taskComment = new TaskComment();
            taskComment.setStatus(FlowableConstant.END_EVENT_LABEL);
            taskComment.setMessage(FlowableConstant.END_EVENT_COMMENT);
            e.setAssigneeName(FlowableConstant.SYSTEM_EVENT_COMMENT);
            e.setComment(taskComment);
            return e;
        }
        // 获取任务执行人名称
        if (StrUtil.isNotEmpty(histIns.getAssignee())) {
            e.setAssignee(histIns.getAssignee());
            String userNameByUserId = masterDataService.getUserNameByUserId(histIns.getAssignee());
            e.setAssignee(histIns.getAssignee());
            e.setAssigneeName(userNameByUserId);
        }
        // 获取意见评论内容
        if (StrUtil.isNotBlank(histIns.getTaskId())) {
            List<TaskComment> commentList = this.getTaskComments(histIns.getTaskId());
            HistoricVariableInstanceQuery action =
                historyService.createHistoricVariableInstanceQuery().processInstanceId(histIns.getProcessInstanceId()).taskId(histIns.getTaskId())
                    .variableName("_flow_button_name");
            if (commentList.size() > 0) {
                TaskComment comment = commentList.get(commentList.size() - 1);
                e.setComment(comment);
            } else {
                e.setComment(new TaskComment());
            }
        }
        //等待执行的任务
        if (histIns.getEndTime() == null) {
            Set<String> assignList = new HashSet<>();
            taskService.getIdentityLinksForTask(histIns.getTaskId()).forEach(identityLink -> {
                assignList.add(masterDataService.getUserNameByUserId(identityLink.getUserId()));
            });
            String assignName = String.join(", ", assignList);
            TaskComment taskComment = new TaskComment();
            taskComment.setStatus(ActionType.WAITING.getStatus());
//            taskComment.setMessage (FlowableConstant.WAITING_EVENT_COMMENT);
            e.setComment(taskComment);
            e.setAssigneeName(assignName);
        }
        return e;
    }

    public List<TaskComment> getTaskComments(String taskId) {
        return jdbcTemplate.query("select * from ACT_HI_COMMENT where TYPE_ like '" + TaskComment.prefix + "%' and TASK_ID_ = '" + taskId + "' order by TIME_ desc",
            new RowMapper<TaskComment>() {
                @Override
                public TaskComment mapRow(ResultSet rs, int rowNum) throws SQLException {
                    TaskComment taskComment = new TaskComment();
                    taskComment.setCommentType(rs.getString("TYPE_"));
                    taskComment.setFullMessage(new String(rs.getBytes("FULL_MSG_")));
                    return taskComment;
                }
            });
    }

    /**
     * 撤回任务
     */
    public ResponseEntity revoke(String processInstanceId, String userId) {
        ProcessInstance pi = runtimeService.createProcessInstanceQuery().processInstanceId(processInstanceId)
            .singleResult();
        if (pi == null) {
            throw new ServiceException("审批流程不存在！");
        }
        if (pi.isSuspended()) {
            throw new ServiceException("任务已终止！");
        }
        String startUserId = pi.getStartUserId();
        if (!userId.equalsIgnoreCase(startUserId)) {
            throw new ServiceException("当前审批人不是发起人，不能撤回该流程");
        }
        Task currentTask = taskService.createTaskQuery().processInstanceId(processInstanceId).list().get(0);
        if (currentTask.getClaimTime() != null) {// 用户已签收
            throw new ServiceException("任务已被签领，无法撤回！");
        }
        FlowNode node = this.getSubmitNode(pi.getProcessDefinitionId());
        if (node == null) {
            throw new ServiceException("未找到【提交申请】流程节点");
        }

        TaskComment taskComment = new TaskComment();
        taskComment.setType(ActionType.REVOKE.getType());
        taskComment.setStatus(ActionType.REVOKE.getStatus());

        taskService.addComment(currentTask.getId(), processInstanceId, taskComment.getCommentType(),
            ActionType.REVOKE.name());
        // 2、执行终止
        List<Execution> executions = runtimeService.createExecutionQuery().parentId(processInstanceId).list();
        List<String> executionIds = new ArrayList<>();
        executions.forEach(execution -> executionIds.add(execution.getId()));
        runtimeService.createChangeActivityStateBuilder()
            .moveExecutionsToSingleActivityId(executionIds, node.getId())
            .changeState();
        Task task = taskService.createTaskQuery().processInstanceId(processInstanceId).singleResult();
        taskService.setAssignee(task.getId(), userId);
        todoTaskService.cancelTodo(task, userId);
        return ResponseEntity.ok("");
    }

    private FlowNode getSubmitNode(String processDefinitionId) {
        List<FlowNode> nodes = flowableBpmnModelService.findFlowNodes(processDefinitionId);
        if (ObjectUtils.isEmpty(nodes)) {
            return null;
        }
        for (FlowNode node : nodes) {
            if (CommonConstants.SUBMIT.equals(node.getName())) {
                return node;
            }
        }
        return null;
    }

    public Task getTaskByProcessInsId(String insId, String userId) {
        // =============== 查询任务  ===============
        TaskQuery todoTaskQuery = taskService.createTaskQuery()
            .includeProcessVariables()
            .processInstanceId(insId)
            .orderByTaskCreateTime().desc();
        List<Task> taskList = todoTaskQuery.list();
        if (org.springframework.util.ObjectUtils.isEmpty(taskList)) {
            return null;
        }
        if (taskList.size() > 1) {
            for (Task task : taskList) {
                String assignee = task.getAssignee();
                if (userId.equals(assignee)) {
                    return task;
                }
            }
        }
        return taskList.get(0);
    }

    /**
     * 判断当前任务的下一个任务是否是终止任务
     *
     * @param insId 任务id
     * @return 任务信息
     */
    public Task isNextDirectlyEnd(String insId) {
        TaskQuery todoTaskQuery = taskService.createTaskQuery()
            .processInstanceId(insId)
            .orderByTaskCreateTime().desc();
        List<Task> taskList = todoTaskQuery.list();
        Task task = taskList.get(0);
        if (task == null) {
            return null;
        }
        BpmnModel model = repositoryService.getBpmnModel(task.getProcessDefinitionId());
        FlowNode currentNode = (FlowNode) model.getMainProcess().getFlowElement(task.getTaskDefinitionKey());
        boolean isFinal = currentNode.getOutgoingFlows().stream()
            .map(SequenceFlow::getTargetFlowElement)
            .allMatch(el -> el instanceof EndEvent);
        return isFinal ? null : task;
    }
}
