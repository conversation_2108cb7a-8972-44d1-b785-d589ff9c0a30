package com.fls.workflow.model.request;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fls.workflow.model.vo.ProcessVo;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Date;

@Data
public class ProcessTodoReq extends Page<ProcessVo>{
	private static final long serialVersionUID = 1L;

    @NotBlank
    private String userId;

	private String billCode;

	private String transTypeCode;

	private Date beginDate;

    private Date endDate;

	private String title;
}
