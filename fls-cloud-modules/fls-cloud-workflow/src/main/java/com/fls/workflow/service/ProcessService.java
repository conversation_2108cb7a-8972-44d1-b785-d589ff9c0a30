package com.fls.workflow.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fls.common.core.exception.ServiceException;
import com.fls.common.core.utils.DateUtils;
import com.fls.common.mybatis.core.page.PageResult;
import com.fls.master.api.model.BizunitNameInfo;
import com.fls.workflow.config.WorkflowCenterProperties;
import com.fls.workflow.constant.CommonConstants;
import com.fls.workflow.constant.FlowableConstant;
import com.fls.workflow.entity.BillMetadataEntity;
import com.fls.workflow.entity.BillWorkflowEntity;
import com.fls.workflow.enums.ActionType;
import com.fls.workflow.enums.FlowableProcessCodeEnum;
import com.fls.workflow.enums.ProcessStatus;
import com.fls.workflow.model.TaskComment;
import com.fls.workflow.model.UserStartFormData;
import com.fls.workflow.model.UserTaskFormData;
import com.fls.workflow.model.request.ProcessTodoReq;
import com.fls.workflow.model.request.StartProcessReq;
import com.fls.workflow.model.request.TodoTaskReq;
import com.fls.workflow.model.response.ExecuteProcessResp;
import com.fls.workflow.model.vo.HisTaskVo;
import com.fls.workflow.model.vo.ProcessVo;
import com.fls.workflow.model.vo.TaskVo;
import com.fls.workflow.model.vo.TodoTaskVo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.flowable.bpmn.model.EndEvent;
import org.flowable.bpmn.model.Process;
import org.flowable.common.engine.impl.identity.Authentication;
import org.flowable.engine.FormService;
import org.flowable.engine.HistoryService;
import org.flowable.engine.IdentityService;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.TaskService;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.engine.repository.ProcessDefinitionQuery;
import org.flowable.engine.runtime.Execution;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.identitylink.api.IdentityLink;
import org.flowable.identitylink.api.IdentityLinkInfo;
import org.flowable.identitylink.api.IdentityLinkType;
import org.flowable.task.api.Task;
import org.flowable.task.api.TaskInfo;
import org.flowable.task.api.TaskInfoQuery;
import org.flowable.task.api.TaskQuery;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.flowable.task.api.history.HistoricTaskInstanceQuery;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service(value = "customProcessService")
@Slf4j
public class ProcessService {
    @Autowired
    private BillWorkflowService billWorkflowService;

    @Autowired
    private FlowableBpmnModelService flowableBpmnModelService;

    @Autowired
    private TaskService taskService;

    @Autowired
    private HistoryService historyService;

    @Autowired
    private FlowTaskService flowTaskService;

    @Autowired
    private RuntimeService runtimeService;

    @Autowired
    private RepositoryService repositoryService;

    @Autowired
    private FormService formService;

    @Autowired
    private IdentityService identityService;

    @Autowired
    private MasterDataService masterDataService;

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private WorkflowCenterProperties workflowCenterProperties;

    @Resource
    private BillMetaService billMetaService;

    public ExecuteProcessResp startProcess(StartProcessReq req) {
        // 1、根据单据编码和交易类型查询单据
        // 2、如果1不存在 则根据单据编码查询单据
        BillWorkflowEntity billWorkflowEntity = billWorkflowService.lambdaQuery()
            .eq(BillWorkflowEntity::getBillCode, req.getBillCode())
            .eq(BillWorkflowEntity::getTransTypeCode,
                ObjectUtils.isEmpty(req.getTransTypeCode()) ? FlowableConstant.NULL_STR
                    : req.getTransTypeCode())
            .eq(BillWorkflowEntity::getDelFlag, CommonConstants.NO).eq(BillWorkflowEntity::getStatus, "2").one();
        if (req.isFindHead() && billWorkflowEntity == null) {
            billWorkflowEntity = billWorkflowService.lambdaQuery()
                .eq(BillWorkflowEntity::getBillCode, req.getBillCode())
                .eq(BillWorkflowEntity::getTransTypeCode, FlowableConstant.NULL_STR)
                .eq(BillWorkflowEntity::getDelFlag, CommonConstants.NO).eq(BillWorkflowEntity::getStatus, "2")
                .one();
        }
        // 3、如果都不存在 则提示单据流程还未注册
        if (ObjectUtils.isEmpty(billWorkflowEntity)) {
            throw new ServiceException("单据流程还未注册");
        }
        JSONObject jsonObject = JSONUtil.parseObj(req.getData());
        BillMetadataEntity metadata = billMetaService.lambdaQuery()
            .eq(BillMetadataEntity::getBillCode, req.getBillCode())
            .eq(BillMetadataEntity::getDelFlag, CommonConstants.NO)
            .one();
        if (metadata != null) {
            String primaryKey = metadata.getPrimaryKey();
            if (jsonObject.containsKey(primaryKey)) {
                jsonObject.set("idBill", jsonObject.getStr(primaryKey));
            }
            String bizunitKey = metadata.getBizunitKey();
            if (jsonObject.containsKey(bizunitKey)) {
                jsonObject.set("idBizunit", jsonObject.getStr(bizunitKey));
            }
        }
        String userName = masterDataService.getUserNameByUserId(req.getUserId());
        ProcessDefinition processDefinition = getProcessDefinitionByKey(billWorkflowEntity.getFlowableModelKey());
        String processInstanceId = submitStartFormData(new UserStartFormData(processDefinition.getId(), req.getTitle(),
            jsonObject.toString(), userName, req.getUserId()));
        return this.getBillFlowResp(processInstanceId);
    }

    public boolean hasPermission(Task task, String operator) {
        List<IdentityLink> identityLinks = taskService.getIdentityLinksForTask(task.getId());
        boolean isCandidate = identityLinks.stream().anyMatch(
            link -> IdentityLinkType.CANDIDATE.equals(link.getType()) && (operator.equals(link.getUserId())));
        return task.getAssignee().equals(operator) || isCandidate;
    }

    public Page<ProcessVo> todo(ProcessTodoReq req) {
        String idUser = req.getUserId();
        Page<ProcessVo> page = new Page<ProcessVo>();
        page.setSize(req.getSize());
        page.setCurrent(req.getCurrent());
        // =============== 已经签收或者等待签收的任务 ===============
        TaskQuery todoTaskQuery = taskService.createTaskQuery().taskCandidateOrAssigned(idUser).active()
            .includeProcessVariables().orderByTaskCreateTime().desc();
        List<String> processDefinitionKeys = null;
        if (!ObjectUtils.isEmpty(req.getBillCode())) {
            List<BillWorkflowEntity> billWorkflowList = billWorkflowService.lambdaQuery()
                .eq(BillWorkflowEntity::getBillCode, req.getBillCode())
                .eq(!ObjectUtils.isEmpty(req.getTransTypeCode()), BillWorkflowEntity::getTransTypeCode,
                    req.getTransTypeCode())
                .eq(BillWorkflowEntity::getDelFlag, CommonConstants.NO).list();
            if (!ObjectUtils.isEmpty(billWorkflowList)) {
                processDefinitionKeys = billWorkflowList.stream().map(BillWorkflowEntity::getFlowableModelKey)
                    .collect(Collectors.toList());
            } else {
                return page;
            }
        }
        // 设置查询条件
        if (!ObjectUtils.isEmpty(processDefinitionKeys)) {
            todoTaskQuery.processDefinitionKeyIn(processDefinitionKeys);
        }

        long total = todoTaskQuery.count();
        page.setTotal(total);
        List<Task> todoList;
        // 查询列表
        if (page.getSize() == -1) {// 不分页
            todoList = todoTaskQuery.list();
        } else {
            int start = (int) ((page.getCurrent() - 1) * page.getSize());
            int size = (int) (page.getSize());
            todoList = todoTaskQuery.listPage(start, size);
        }
        List<ProcessVo> records = Lists.newArrayList();
        for (Task task : todoList) {
            Process process = SpringUtil.getBean(RepositoryService.class).getBpmnModel(task.getProcessDefinitionId())
                .getMainProcess();
            ProcessDefinition pd = repositoryService.createProcessDefinitionQuery()
                .processDefinitionId(task.getProcessDefinitionId()).singleResult();
            ProcessVo processVo = new ProcessVo();
            TaskVo taskVo = new TaskVo(task);
            taskVo.setProcessDefKey(process.getId());
            processVo.setTask(taskVo);
            processVo.setVars(task.getProcessVariables());
            processVo.setProcessDefinitionName(pd.getName());
            processVo.setVersion(pd.getVersion());
            processVo.setStatus("todo");
            records.add(processVo);
        }
        page.setRecords(records);
        return page;
    }

    /**
     * 新的待办任务查询
     *
     * @param req 待办任务查询条件
     * @return 分页结果
     */
    public PageResult<TodoTaskVo> queryTodoTasks(TodoTaskReq req) {
        int isActive = req.getIsActive() == null ? 1 : req.getIsActive();
        if (isActive == 1) {
            // 待办任务查询
            TaskQuery query = taskService.createTaskQuery().active()
                .includeProcessVariables()
                .taskDefinitionKeyLike("UserTask%")
                .orderByTaskCreateTime().desc();
            return executeQuery(req, query);
        } else if (isActive == 0) {
            // 已办任务查询
            HistoricTaskInstanceQuery query = historyService.createHistoricTaskInstanceQuery()
                .finished()
                .includeProcessVariables()
                .taskDefinitionKeyLike("UserTask%")
                .orderByHistoricTaskInstanceEndTime().desc();
            return executeQuery(req, query);
        } else {
            throw new ServiceException("不支持的任务状态: " + isActive + "，请使用1(待办)或0(已办)");
        }
    }

    /**
     * 执行查询（通用方法）
     */
    private PageResult<TodoTaskVo> executeQuery(TodoTaskReq req, TaskInfoQuery query) {
        // 构建查询条件
        buildQueryConditions(req, query);

        // 分页查询
        int start = (req.getPageNo() - 1) * req.getPageSize();
        List<TodoTaskVo> records = Lists.newArrayList();
        long total = query.count();

        if (query instanceof TaskQuery) {
            // 待办任务查询
            List<Task> tasks = ((TaskQuery) query).listPage(start, req.getPageSize());
            for (Task task : tasks) {
                records.add(convertToTodoTaskVo(task));
            }
        } else {
            // 已办任务查询
            List<HistoricTaskInstance> histTasks = ((HistoricTaskInstanceQuery) query).listPage(start, req.getPageSize());
            for (HistoricTaskInstance histTask : histTasks) {
                records.add(convertToTodoTaskVo(histTask));
            }
        }
        addTransFields(records);
        return buildPageResult(records, total, req.getPageNo(), req.getPageSize());
    }

    private void addTransFields(List<TodoTaskVo> records) {
        Set<String> idsBizunit = records.stream().map(TodoTaskVo::getIdBizunit).collect(Collectors.toSet());
        if (CollectionUtil.isNotEmpty(idsBizunit)) {
            List<BizunitNameInfo> bizInfos = masterDataService.getBizunitList(idsBizunit);
            Map<String, String> map = bizInfos.stream().collect(Collectors.toMap(BizunitNameInfo::getIdBizunit, BizunitNameInfo::getName, (v1, v2) -> v1));
            records.forEach(record -> {
                String idBizunit = record.getIdBizunit();
                if (StrUtil.isNotBlank(idBizunit) && StrUtil.isNotBlank(map.get(idBizunit))) {
                    record.setBizunitName(map.get(idBizunit));
                }
            });
        }
    }

    /**
     * 构建查询条件（通用方法）
     */
    private void buildQueryConditions(TodoTaskReq req, TaskInfoQuery query) {
        // 根据资源编码查询
        if (StrUtil.isNotBlank(req.getIdResource())) {
            List<String> taskDefKeys = getTaskDefKeysByResCode(req.getIdResource());
            if (!ObjectUtils.isEmpty(taskDefKeys)) {
                query.processDefinitionKeyIn(taskDefKeys);
            }
        }
        // 根据用户id进行任务过滤
        String idUser = req.getIdUser();
        if (StrUtil.isNotBlank(idUser)) {
            query.taskAssignee(idUser);
        }
        // 根据单据编号进行变量模糊查询
        if (StrUtil.isNotBlank(req.getBillCode())) {
            query.processVariableValueLike("billCode", "%" + req.getBillCode() + "%");
        }
        // 根据经营主体ID进行变量精确查询
        if (StrUtil.isNotBlank(req.getIdBizunit())) {
            query.processVariableValueEquals("idBizunit", req.getIdBizunit());
        }
        LocalDate localDate = LocalDate.of(2025, 6, 1);
        Date after = Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
        query.taskCreatedAfter(after);
    }

    /**
     * 根据资源编码获取流程定义Key列表
     */
    private List<String> getTaskDefKeysByResCode(String idResource) {
        BillMetadataEntity metadata = billMetaService.lambdaQuery()
            .eq(StrUtil.isNotBlank(idResource), BillMetadataEntity::getIdResource, idResource)
            .eq(BillMetadataEntity::getDelFlag, CommonConstants.NO).one();
        if (metadata == null) {
            return Collections.emptyList();
        }
        String resCode = metadata.getBillCode();
        List<BillWorkflowEntity> billWorkflowList = billWorkflowService.lambdaQuery()
            .eq(StrUtil.isNotBlank(resCode), BillWorkflowEntity::getBillCode, resCode)
            .eq(BillWorkflowEntity::getDelFlag, CommonConstants.NO)
            .list();
        return billWorkflowList.stream()
            .map(BillWorkflowEntity::getFlowableModelKey)
            .collect(Collectors.toList());
    }

    /**
     * 构建分页结果
     */
    private PageResult<TodoTaskVo> buildPageResult(List<TodoTaskVo> records, long total, int pageNo, int pageSize) {
        Page<TodoTaskVo> page = new Page<>(pageNo, pageSize);
        page.setRecords(records);
        page.setTotal(total);
        return new PageResult<>(page, page.getRecords());
    }

    /**
     * 转换为TodoTaskVo（通用方法）
     */
    private TodoTaskVo convertToTodoTaskVo(TaskInfo taskInfo) {
        TodoTaskVo todoTaskVo = new TodoTaskVo();

        // 1. 任务名称
        todoTaskVo.setTaskName(taskInfo.getName());
        todoTaskVo.setTaskDefinitionKey(taskInfo.getTaskDefinitionKey());

        // 2. 获取流程定义和processDefKey
        String processDefinitionId = taskInfo.getProcessDefinitionId();
        Process process = SpringUtil.getBean(RepositoryService.class)
            .getBpmnModel(processDefinitionId).getMainProcess();
        String processDefKey = process.getId();

        // 3. 通过processDefKey联查t_bill_workflow表获取resCode和transCode
        BillWorkflowEntity billWorkflow = billWorkflowService.lambdaQuery()
            .eq(BillWorkflowEntity::getFlowableModelKey, processDefKey)
            .eq(BillWorkflowEntity::getDelFlag, CommonConstants.NO)
            .one();

        if (billWorkflow != null) {
            todoTaskVo.setResCode(billWorkflow.getBillCode());
            todoTaskVo.setResName(billWorkflow.getBillName());
            todoTaskVo.setTransCode(billWorkflow.getTransTypeCode());
            todoTaskVo.setTransName(billWorkflow.getTransTypeName());
        }

        // 4-6. 从流程变量中获取相关信息
        setVariableFields(todoTaskVo, taskInfo.getProcessVariables());

        // 7. 设置时间字段
        if (taskInfo.getCreateTime() != null) {
            todoTaskVo.setStartTime(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, taskInfo.getCreateTime()));
        }
        if (taskInfo instanceof HistoricTaskInstance) {
            HistoricTaskInstance histTaskInstance = (HistoricTaskInstance) taskInfo;
            if (histTaskInstance.getEndTime() != null) {
                todoTaskVo.setEndTime(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, histTaskInstance.getEndTime()));
            }
        }

        // 8. 流程名称
        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery()
            .processDefinitionId(processDefinitionId)
            .singleResult();

        if (processDefinition != null) {
            todoTaskVo.setProcessName(processDefinition.getName());
        }

        return todoTaskVo;
    }

    /**
     * 设置流程变量相关字段
     */
    private void setVariableFields(TodoTaskVo todoTaskVo, Map<String, Object> processVariables) {
        // 单据编号
        Object billCodeVar = processVariables.get("billCode");
        todoTaskVo.setBillCode(billCodeVar != null ? billCodeVar.toString() : null);

        //单据id
        Object billIdVar = processVariables.get("idBill");
        todoTaskVo.setBillId(billIdVar != null ? billIdVar.toString() : null);

        // 经营主体ID
        Object idBizunitVar = processVariables.get("idBizunit");
        todoTaskVo.setIdBizunit(idBizunitVar != null ? idBizunitVar.toString() : null);


        // 发起人
        Object applyUserIdVar = processVariables.get("applyUserId");
        String initiator = applyUserIdVar != null ? applyUserIdVar.toString() : null;
        todoTaskVo.setInitiator(initiator);

        // 发起人名称
        if (StrUtil.isNotBlank(initiator)) {
            String initiatorName = masterDataService.getUserNameByUserId(initiator);
            todoTaskVo.setInitiatorName(initiatorName);
        }
    }

    public Page<HisTaskVo> list(ProcessTodoReq req) {
        Page<HisTaskVo> page = new Page<HisTaskVo>();
        page.setSize(req.getSize());
        page.setCurrent(req.getCurrent());
        String userId = req.getUserId();
        HistoricTaskInstanceQuery histTaskQuery = historyService.createHistoricTaskInstanceQuery().taskAssignee(userId)
            .finished().includeProcessVariables().orderByHistoricTaskInstanceEndTime().desc();
        // 设置查询条件
        List<String> processDefinitionKeys = null;
        if (!ObjectUtils.isEmpty(req.getBillCode())) {
            List<BillWorkflowEntity> billWorkflowList = billWorkflowService.lambdaQuery()
                .eq(BillWorkflowEntity::getBillCode, req.getBillCode())
                .eq(!ObjectUtils.isEmpty(req.getTransTypeCode()), BillWorkflowEntity::getTransTypeCode,
                    req.getTransTypeCode())
                .eq(BillWorkflowEntity::getDelFlag, CommonConstants.NO).list();
            if (!ObjectUtils.isEmpty(billWorkflowList)) {
                processDefinitionKeys = billWorkflowList.stream().map(BillWorkflowEntity::getFlowableModelKey)
                    .collect(Collectors.toList());
            } else {
                return page;
            }
        }
        if (!ObjectUtils.isEmpty(processDefinitionKeys)) {
            histTaskQuery.processDefinitionKeyIn(processDefinitionKeys);
        }
        if (req.getBeginDate() != null) {
            histTaskQuery.taskCompletedAfter(req.getBeginDate());
        }
        if (req.getEndDate() != null) {
            histTaskQuery.taskCompletedBefore(req.getEndDate());
        }
        if (req.getTitle() != null) {
            histTaskQuery.processVariableValueLike(FlowableConstant.TITLE, "%" + req.getTitle() + "%");
        }
        // 查询总数
        page.setTotal(histTaskQuery.count());
        // 查询列表
        List<HistoricTaskInstance> histList;
        if (page.getSize() == -1) {
            histList = histTaskQuery.list();
        } else {
            int start = (int) ((page.getCurrent() - 1) * page.getSize());
            int size = (int) (page.getSize());
            histList = histTaskQuery.listPage(start, size);
        }
        List records = Lists.newArrayList();
        for (HistoricTaskInstance histTask : histList) {
            HisTaskVo hisTaskVo = new HisTaskVo(histTask);
            ProcessDefinition pd = repositoryService.createProcessDefinitionQuery()
                .processDefinitionId(hisTaskVo.getProcessDefinitionId()).singleResult();
            hisTaskVo.setProcessDefinitionName(pd.getName());
            hisTaskVo.setBack(flowTaskService.isBack(histTask));
            List<Task> currentTaskList = taskService.createTaskQuery()
                .processInstanceId(histTask.getProcessInstanceId()).list();
            if (((List) currentTaskList).size() > 0) {
                TaskVo currentTaskVo = new TaskVo(currentTaskList.get(0));
                hisTaskVo.setCurrentTask(currentTaskVo);
            }
            // 获取意见评论内容
            List<TaskComment> commentList = flowTaskService.getTaskComments(histTask.getId());
            if (commentList.size() > 0) {
                TaskComment comment = commentList.get(commentList.size() - 1);
                hisTaskVo.setComment(comment.getMessage());
                hisTaskVo.setLevel(comment.getLevel());
                hisTaskVo.setType(comment.getType());
                hisTaskVo.setStatus(comment.getStatus());
            }
            records.add(hisTaskVo);
        }
        page.setRecords(records);
        return page;
    }

    public Integer getProcessStatus(String processInstanceId) {
        HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery()
            .processInstanceId(processInstanceId).singleResult();
        if (historicProcessInstance == null) {
            throw new ServiceException("流程实例不存在");
        }
        // 也可以判断结束时间是否为空
        if (historicProcessInstance.getEndTime() != null) {
            return FlowableProcessCodeEnum.COMPLETED.getCode();
        }
        return FlowableProcessCodeEnum.WAITING.getCode();
    }

    public ExecuteProcessResp getBillFlowResp(String processInstanceId) {
        ExecuteProcessResp billFlowResp = new ExecuteProcessResp();
        billFlowResp.setProcessInstanceId(processInstanceId);
        billFlowResp.setStatus(this.getProcessStatus(processInstanceId));
        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
            .processInstanceId(processInstanceId).singleResult();
        if (processInstance == null) {
            return billFlowResp;
        }
        List<Task> tasks = taskService.createTaskQuery().processInstanceId(processInstance.getId()).list();
        if (ObjectUtils.isEmpty(tasks)) {
            throw new ServiceException("审批任务不存在");
        }
        List<String> assignees = new ArrayList<String>();
        for (Task task : tasks) {
            // 执行后续操作或展示审批人信息
            taskService.getIdentityLinksForTask(task.getId()).forEach(identityLink -> {
                assignees.add(identityLink.getUserId());
            });
        }
        billFlowResp.setUserIds(assignees);
        TaskVo taskVo = new TaskVo(tasks.get(0));
        String candidates = taskService.getIdentityLinksForTask(tasks.get(0).getId()).stream()
            .filter(identityLink -> IdentityLinkType.CANDIDATE.equals(identityLink.getType()))
            .map(IdentityLinkInfo::getUserId).collect(Collectors.joining(","));
        taskVo.setCandidates(candidates);
        billFlowResp.setTask(taskVo);
        return billFlowResp;
    }

    public List<TaskVo> getTasks(String processInstanceId) {
        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
            .processInstanceId(processInstanceId).singleResult();
        if (ObjectUtil.isNull(processInstance)) {
            return Collections.emptyList();
        }
        List<Task> tasks = taskService.createTaskQuery().processInstanceId(processInstance.getId()).list();
        List<TaskVo> entityList = new ArrayList<>();
        for (Task task : tasks) {
            TaskVo taskEntity = new TaskVo(task);
            taskEntity.setProcessName(processInstance.getProcessDefinitionName());
            String assignee = taskService.getIdentityLinksForTask(task.getId()).stream()
                .map(IdentityLinkInfo::getUserId).collect(Collectors.joining(","));
            if (ObjectUtil.isNotEmpty(assignee)) {
                String userName = masterDataService.getUserNameByUserId(assignee);
                taskEntity.setAssigneeName(userName);
                taskEntity.setAssignee(assignee);
            }
            entityList.add(taskEntity);
        }
        return entityList;
    }

    public List<String> getAssignees(String processInstanceId) {
        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
            .processInstanceId(processInstanceId).singleResult();
        List<Task> tasks = taskService.createTaskQuery().processInstanceId(processInstance.getId()).list();
        List<String> assignees = new ArrayList<String>();
        for (Task task : tasks) {
            String assignee = task.getAssignee();
            // 执行后续操作或展示审批人信息
            assignees.add(assignee);
        }
        return assignees;
    }

    /**
     * 根据key获取流程
     */
    public ProcessDefinition getProcessDefinitionByKey(String key) {
        ProcessDefinitionQuery processDefinitionQuery = repositoryService.createProcessDefinitionQuery().latestVersion()
            .orderByProcessDefinitionKey().asc();
        processDefinitionQuery.processDefinitionKey(key);
        List<ProcessDefinition> processDefinitionList = processDefinitionQuery.list();
        if (processDefinitionList.size() > 0) {
            return processDefinitionList.get(0);
        } else {
            return null;
        }
    }

    public String submitStartFormData(UserStartFormData userFormData) {
        // 调用业务系统接口，让业务系统更新其退款单号状态
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<UserStartFormData> formEntity = new HttpEntity<>(userFormData, headers); // 允许传递body中的null值
        try {
            ResponseEntity<String> rep = restTemplate.postForEntity(workflowCenterProperties.getStartPath(), formEntity,
                String.class);
            return rep.getBody();
        } catch (HttpServerErrorException e) {
            log.error("请求服务错误: {}", e.getResponseBodyAsString());
            throw new ServiceException("发起流程错误:" + e.getMessage());
        } catch (HttpClientErrorException e) {
            log.error("客户端错误: {}", e.getResponseBodyAsString());
            throw new ServiceException("请求客户端错误，请检查请求参数");
        } catch (Exception e) {
            log.error("未知错误: {}", e.getMessage());
            throw new ServiceException("请求失败，请稍后再试");
        }
    }

    public void submitTaskFormData(UserTaskFormData userTaskFormData) {
        // 调用业务系统接口，让业务系统更新其退款单号状态
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<UserTaskFormData> formEntity = new HttpEntity<>(userTaskFormData, headers); // 允许传递body中的null值
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(workflowCenterProperties.getSubmitPath());
        try {
            ResponseEntity<String> response = restTemplate.postForEntity(builder.toUriString(), formEntity,
                String.class);
        } catch (HttpServerErrorException e) {
            log.error("请求服务错误: {}", e.getResponseBodyAsString());
            throw new ServiceException("提交工单任务发生错误:：" + e.getMessage());
        } catch (HttpClientErrorException e) {
            log.error("客户端错误: {}", e.getResponseBodyAsString());
            throw new ServiceException("请求客户端错误，请检查请求参数");
        } catch (Exception e) {
            log.error("未知错误: {}", e.getMessage());
            throw new ServiceException("请求失败，请稍后再试");
        }
    }

    public Task queryTaskByProcessInsId(String insId, String userId) {
        // =============== 已经签收或者等待签收的任务 ===============
        TaskQuery todoTaskQuery = taskService.createTaskQuery().taskCandidateOrAssigned(userId).active()
            .includeProcessVariables().processInstanceId(insId).orderByTaskCreateTime().desc();
        List<Task> taskList = todoTaskQuery.list();
        if (ObjectUtils.isEmpty(taskList)) {
            return null;
        }
        if (taskList.size() > 1) {
            throw new ServiceException("存在多个待办任务");
        }
        return taskList.get(0);
    }

    /**
     * 终止流程实例
     *
     * @param procInsId     流程实例ID
     * @param processStatus 流程状态
     */
    public void stopProcessInstanceById(String procInsId, ProcessStatus processStatus, String comment, String userId) {
        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery().processInstanceId(procInsId)
            .singleResult();
        if (processInstance != null) {
            Task currentTask = taskService.createTaskQuery().processInstanceId(procInsId).list().get(0);
            // 1、添加审批记录
            TaskComment taskComment = new TaskComment();
            if (processStatus == ProcessStatus.REVOKE) {
                taskComment.setType(ActionType.REVOKE.getType());
                taskComment.setStatus(ActionType.REVOKE.getStatus());
                taskComment.setMessage(comment);
            } else if (processStatus == ProcessStatus.STOP) {
                taskComment.setType(ActionType.STOP.getType());
                taskComment.setStatus(ActionType.STOP.getStatus());
                taskComment.setMessage(comment);
            } else if (processStatus == ProcessStatus.REJECT) {
                taskComment.setType(ActionType.REJECT.getType());
                taskComment.setStatus(ActionType.REJECT.getStatus());
                taskComment.setMessage(comment);
            } else if (processStatus == ProcessStatus.DELETED) {
                taskComment.setType(ActionType.DELETED.getType());
                taskComment.setStatus(ActionType.DELETED.getStatus());
                taskComment.setMessage(comment);
            }
            taskService.addComment(currentTask.getId(), procInsId, taskComment.getCommentType(),
                taskComment.getFullMessage());
            if (StrUtil.isBlank(currentTask.getAssignee())) { // 未签收任务
                taskService.claim(currentTask.getId(), userId);
            }
            runtimeService.setVariable(procInsId, FlowableConstant.PROCESS_STATUS_CODE, processStatus.getCode());
            List<EndEvent> endNodes = flowableBpmnModelService
                .findEndFlowElement(processInstance.getProcessDefinitionId());
            String endId = endNodes.get(0).getId();
            // 2、执行终止
            List<Execution> executions = runtimeService.createExecutionQuery().parentId(procInsId).list();
            List<String> executionIds = new ArrayList<>();
            executions.forEach(execution -> executionIds.add(execution.getId()));
            this.moveExecutionsToSingleActivityId(executionIds, endId);
        }
    }

    /**
     * 执行跳转
     */
    protected void moveExecutionsToSingleActivityId(List<String> executionIds, String activityId) {
        runtimeService.createChangeActivityStateBuilder().moveExecutionsToSingleActivityId(executionIds, activityId)
            .changeState();
    }

    /**
     * 转办任务
     *
     * @param taskId 任务ID
     * @param userId 用户ID
     * @return 响应
     */
    public void transferTask(String taskId, String userId) {
        if (StringUtils.isBlank(userId) || StringUtils.isBlank(taskId)) {
            throw new ServiceException("转派失败, 参数异常");
        }
        // 设置当前流程任务办理人
        Authentication.setAuthenticatedUserId(userId);

        List<IdentityLink> currentLinks = taskService.getIdentityLinksForTask(taskId);
        for (IdentityLink currentLink : currentLinks) {
            if (currentLink.getUserId() != null) {
                taskService.deleteUserIdentityLink(taskId, currentLink.getUserId(), IdentityLinkType.CANDIDATE);
                taskService.deleteUserIdentityLink(taskId, currentLink.getUserId(), IdentityLinkType.ASSIGNEE);
                taskService.deleteUserIdentityLink(taskId, currentLink.getUserId(), IdentityLinkType.PARTICIPANT);
                taskService.deleteUserIdentityLink(taskId, currentLink.getUserId(), IdentityLinkType.OWNER);
            }
        }
        taskService.addUserIdentityLink(taskId, userId, IdentityLinkType.CANDIDATE);
    }

    /**
     * 挂起、激活流程实例
     */
    public String updateState(String state, String procDefId) {
        if (state.equals("active")) {
            repositoryService.activateProcessDefinitionById(procDefId, true, null);
            return "已激活ID为[" + procDefId + "]的流程定义。";
        } else if (state.equals("suspend")) {
            repositoryService.suspendProcessDefinitionById(procDefId, true, null);
            return "已挂起ID为[" + procDefId + "]的流程定义。";
        }
        return "无操作";
    }
}
