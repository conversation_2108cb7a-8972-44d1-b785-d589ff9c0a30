package com.fls.workflow.enums;

public enum FlowableProcessCodeEnum {
    DRAFT(0, "待提交"),
    WAITING(1, "待审核"),
    COMPLETED(2, "已完成"),
    REVOKE(3, "已撤回"),
    REJECT(4, "已驳回"),
    DELETED(5, "已作废");

    private final int code;

    private final String name;

    FlowableProcessCodeEnum(int code, String name) {
        this.name = name;
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public int getCode() {
        return code;
    }

    public static FlowableProcessCodeEnum getEnumByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (FlowableProcessCodeEnum processCodeEnum : FlowableProcessCodeEnum.values()) {
            if (processCodeEnum.getCode() == code.intValue()) {
                return processCodeEnum;
            }
        }
        return null;
    }
}
