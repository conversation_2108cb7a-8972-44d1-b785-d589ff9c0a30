package com.fls.workflow.service;

import com.fls.master.api.model.BizunitNameInfo;
import com.fls.workflow.mapper.BizunitMapper;
import com.fls.workflow.mapper.UserMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * created 2023/11/7 16:17:06
 */
@Service
public class MasterDataService {
    @Autowired
    private UserMapper userMapper;

    @Autowired
    private BizunitMapper bizunitMapper;

    public String getUserNameByUserId(String id) {
        return userMapper.getUserNameById(id);
    }

    public List<BizunitNameInfo> getBizunitList(Set<String> idsBizunit){
        return bizunitMapper.getBizunitList(idsBizunit);
    }
}
