package com.fls.workflow;

import java.net.InetAddress;
import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.metrics.buffering.BufferingApplicationStartup;

/**
 * 流程中心能力层
 *
 * <AUTHOR>
 * @since 2024-08-10
 */
@SpringBootApplication
@MapperScan("com.fls.**.mapper")
@EnableDubbo
public class FlsWorkflowApplication implements CommandLineRunner {
    @Value("${server.port}")
    private String port;

    public static void main(String[] args) {
        SpringApplication application = new SpringApplication(FlsWorkflowApplication.class);
        application.setApplicationStartup(new BufferingApplicationStartup(2048));
        application.run(args);
    }

    @Override
    public void run(String... args) throws Exception {
        System.out.println(">>>>>>>>>>>>>>>application start complete<<<<<<<<<<<<<");
        String ip = InetAddress.getLocalHost().getHostAddress();
        System.out.println("Jeeplus Application running at:\n\t" +
                "- Local: http://localhost:" + port + "/\n\t" +
                "- Network: http://" + ip + ":" + port + "/\n\t");
    }
}
