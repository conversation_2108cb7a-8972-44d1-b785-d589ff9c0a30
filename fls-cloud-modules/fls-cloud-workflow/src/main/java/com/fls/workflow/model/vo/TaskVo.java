package com.fls.workflow.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import org.flowable.task.api.Task;

import java.util.Date;

/**
 * task vo
 *
 * <AUTHOR>
 * @since 2024-08-10
 */
@Data
public class TaskVo {
    private String id;
    private String name;
    private String assignee;
    private String candidates;
    private String executionId;
    private String taskDefinitionKey;
    private Date createTime;
    private String processDefinitionId;
    private String processInstanceId;
    private String processDefKey;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String processName;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String assigneeName;

    public TaskVo() {
    }

    public TaskVo(Task task) {
        this.id = task.getId();
        this.name = task.getName();
        this.assignee = task.getAssignee();
        this.executionId = task.getExecutionId();
        this.taskDefinitionKey = task.getTaskDefinitionKey();
        this.createTime = task.getCreateTime();
        this.processDefinitionId = task.getProcessDefinitionId();
        this.processInstanceId = task.getProcessInstanceId();
    }
}
