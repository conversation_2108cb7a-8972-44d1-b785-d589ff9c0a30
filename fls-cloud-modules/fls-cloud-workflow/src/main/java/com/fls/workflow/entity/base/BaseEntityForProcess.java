package com.fls.workflow.entity.base;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 基础实体基类
 *
 * <AUTHOR>
 * @since 2024-08-10
 */
@Data
public abstract class BaseEntityForProcess implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 实体主键
     */
    @ExcelIgnore
    @TableId
    private String id;

    /**
     * 创建日期
     */
    @ExcelIgnore
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 创建人
     */
    @ExcelIgnore
    @TableField(fill = FieldFill.INSERT)
    private String createById;

    /**
     * 更新日期
     */
    @ExcelIgnore
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 更新人
     */
    @ExcelIgnore
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateById;

    /**
     * 逻辑删除标记
     */
    @ExcelIgnore
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private Integer delFlag;

    /**
     * 租户Id
     */
    @ExcelIgnore
    @TableField(fill = FieldFill.INSERT)
    private String tenantId;

    /**
     * 默认构造函数
     */

    public BaseEntityForProcess() {

    }

    /**
     * 构造函数
     */
    public BaseEntityForProcess(String id) {
        this.id = id;
    }


}
