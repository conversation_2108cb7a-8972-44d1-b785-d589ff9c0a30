package com.fls.workflow.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 流程表单启动数据
 *
 * <AUTHOR>
 * @since 2024-08-10
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserStartFormData {

    public UserStartFormData(String processDefinitionId, String title, String data, String userName, String userId) {
        this.assignee = null;
        this.processDefinitionId = processDefinitionId;
        this.title = title;
        this.data = data;
        this.userName = userName;
        this.userId = userId;
    }

    private String assignee;

    private String processDefinitionId;

    private String title;

    private String data;

    private String userName;

    private String userId;
}
