package com.fls.workflow.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fls.workflow.entity.base.BaseEntityForProcess;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 单据元数据
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("t_bill_metadata")
public class BillMetadataEntity extends BaseEntityForProcess {

    private String idResource;

    private String billCode;

    private String billName;

    private String metadata;

    private String defaultLimitType;

    private String defaultLimitVal;

    private String formdata;

    private String formId;

    private String primaryKey;

    private String bizunitKey;
}
