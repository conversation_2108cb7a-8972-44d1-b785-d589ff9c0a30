package com.fls.workflow.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.fls.workflow.entity.BillWorkflowEntity;
import com.fls.workflow.model.dto.UserDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


@Mapper
@DS("fls_db")
public interface UserMapper {
    /**
     * 根据用户id获取用户名称
     *
     * @param idUser 用户id
     * @return 用户名称
     */
    String getUserNameById(@Param("idUser") String idUser);
}
