package com.fls.workflow.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.fls.common.core.exception.ServiceException;
import com.fls.common.core.utils.JsonUtils;
import com.fls.workflow.api.RemoteWorkflowService;
import com.fls.workflow.api.model.ApproveStaticInfo;
import com.fls.workflow.api.model.ApproveStaticQuery;
import com.fls.workflow.api.model.BackNodeInfo;
import com.fls.workflow.api.model.BackNodeReq;
import com.fls.workflow.api.model.InitiateProcessReq;
import com.fls.workflow.api.model.InitiateProcessRsp;
import com.fls.workflow.api.model.StopProcessReq;
import com.fls.workflow.api.model.TaskInfo;
import com.fls.workflow.controller.ProcessController;
import com.fls.workflow.enums.ActionType;
import com.fls.workflow.enums.ProcessStatus;
import com.fls.workflow.model.Flow;
import com.fls.workflow.model.TaskComment;
import com.fls.workflow.model.UserTaskFormData;
import com.fls.workflow.model.request.StartProcessReq;
import com.fls.workflow.model.response.ExecuteProcessResp;
import com.fls.workflow.model.vo.TaskVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.flowable.engine.DynamicBpmnService;
import org.flowable.engine.HistoryService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.TaskService;
import org.flowable.engine.impl.dynamic.DynamicUserTaskBuilder;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.identitylink.api.IdentityLink;
import org.flowable.identitylink.api.IdentityLinkInfo;
import org.flowable.identitylink.api.IdentityLinkType;
import org.flowable.task.api.Task;
import org.flowable.task.api.TaskQuery;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.springframework.util.ObjectUtils;

import java.time.DayOfWeek;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@DubboService
@RequiredArgsConstructor
public class RemoteWorkflowServiceImpl implements RemoteWorkflowService {
    private final ProcessService processService;

    private final TaskService taskService;

    private final ProcessController processController;

    private final RuntimeService runtimeService;

    private final DynamicBpmnService dynamicBpmnService;

    private final FlowTaskService flowTaskService;

    private final HistoryService historyService;

    @Override
    public InitiateProcessRsp startProcess(InitiateProcessReq req) {
        StartProcessReq startProcessReq = new StartProcessReq();
        BeanUtil.copyProperties(req, startProcessReq, true);
        startProcessReq.setUserId(req.getInitiator());
        startProcessReq.setBillCode(req.getResourceCode());
        startProcessReq.setTransTypeCode(req.getTransCode());
        ExecuteProcessResp executeProcessResp = processService.startProcess(startProcessReq);
        InitiateProcessRsp initiateProcessRsp = new InitiateProcessRsp();
        if (ObjectUtil.isNotNull(executeProcessResp)) {
            initiateProcessRsp.setProcessInstanceId(executeProcessResp.getProcessInstanceId());
            initiateProcessRsp.setProcessDefinitionId(executeProcessResp.getTask().getProcessDefinitionId());
            initiateProcessRsp.setStatus(executeProcessResp.getStatus());
            TaskInfo taskInfo = new TaskInfo();
            BeanUtil.copyProperties(executeProcessResp.getTask(), taskInfo);
            initiateProcessRsp.setTaskInfo(taskInfo);
        }
        if (StrUtil.isEmpty(initiateProcessRsp.getProcessDefinitionId())) {
            // 通过流程实例ID获取流程实例对象
            ProcessInstance processInstance = runtimeService.createProcessInstanceQuery().processInstanceId(executeProcessResp.getProcessInstanceId()).singleResult();
            if (processInstance != null) {
                initiateProcessRsp.setProcessDefinitionId(processInstance.getProcessDefinitionId());
            }
        }
        return initiateProcessRsp;
    }

    @Override
    public void stopProcess(StopProcessReq stopProcessReq) {
        String operator = stopProcessReq.getOperator();
        Task task = processService.queryTaskByProcessInsId(stopProcessReq.getProcInsId(), operator);
        if (ObjectUtils.isEmpty(task)) {
            throw new RuntimeException("当前用户,任务不存在");
        }
        //判断当前用户是否有任务操作权限
        // 校验当前用户是否有操作权限
        if (!processService.hasPermission(task, operator)) {
            throw new RuntimeException("当前用户无权限终止此流程");
        }
        processService.stopProcessInstanceById(stopProcessReq.getProcInsId(), ProcessStatus.STOP, "工单终止", stopProcessReq.getOperator());
    }

    @Override
    public void claimTask(String taskId, String userId) {
        // 查询任务
        Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
        if (task == null) {
            throw new IllegalArgumentException("任务ID无效，任务不存在");
        }
        // 检查任务是否已被认领
        if (task.getAssignee() != null) {
            throw new IllegalStateException("任务已被用户 " + task.getAssignee() + " 认领");
        }
        // 认领任务
        try {
            taskService.claim(taskId, userId);
            log.info("任务 [" + taskId + "] 已成功被用户 [" + userId + "] 认领");
        }
        catch (Exception e) {
            throw new IllegalStateException("任务认领失败: " + e.getMessage(), e);
        }
    }

    @Override
    public void followUpTask(String taskId, Object data) {
        Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
        if (task == null) {
            throw new IllegalArgumentException("任务ID无效，任务不存在");
        }
        taskService.setVariables(taskId, BeanUtil.beanToMap(data));
    }

    @Override
    public void transferTask(String taskId, String userId) {
        processService.transferTask(taskId, userId);
    }

    @Override
    public void updateState(String state, String taskId) {
        //任务id获取流程实例id
        Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
        if (task == null) {
            throw new IllegalArgumentException("任务不存在，ID: " + taskId);
        }
        // 获取流程实例 ID
        String processInstanceId = task.getProcessInstanceId();
        String processDefinitionId = task.getProcessDefinitionId();
        processController.updateState(state, processDefinitionId, processInstanceId);
    }

    @Override
    public TaskInfo completeTask(String taskId, String userId, Object data) {
        Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
        if (task == null) {
            throw new IllegalArgumentException("任务不存在，ID: " + taskId);
        }
        // 获取流程实例 ID
        String processInstanceId = task.getProcessInstanceId();
        Flow flow = new Flow();
        flow.setProcInsId(processInstanceId);
        flow.setTaskId(task.getId());
        flow.setUserId(userId);
        TaskComment taskComment = new TaskComment();
        taskComment.setMessage("工单任务提交");
        taskComment.setStatus(ActionType.COMMIT.getStatus());
        taskComment.setCommentType(TaskComment.prefix + ActionType.COMMIT.getType());
        flow.setComment(taskComment);
        processService.submitTaskFormData(new UserTaskFormData(flow, JsonUtils.toJsonString(data)));
        List<Task> tasks = taskService.createTaskQuery().processInstanceId(processInstanceId).list();
        if (ObjectUtils.isEmpty(tasks)) {
            return null;
        }
        Task nextTask = tasks.get(0);
        TaskVo taskVo = new TaskVo(nextTask);
        List<IdentityLink> identityLinks = taskService.getIdentityLinksForTask(nextTask.getId());
        // 使用单个流处理来分别获取 candidates 和 assignee
        Map<String, String> resultMap = identityLinks.stream()
                .collect(Collectors.groupingBy(
                        identityLink -> IdentityLinkType.CANDIDATE.equals(identityLink.getType()) ? IdentityLinkType.CANDIDATE : IdentityLinkType.ASSIGNEE,
                        Collectors.mapping(IdentityLinkInfo::getUserId, Collectors.collectingAndThen(Collectors.toList(), list -> String.join(",", list)))
                ));
        taskVo.setCandidates(resultMap.getOrDefault(IdentityLinkType.CANDIDATE, ""));
        taskVo.setAssignee(resultMap.getOrDefault(IdentityLinkType.ASSIGNEE, ""));
        return BeanUtil.copyProperties(taskVo, TaskInfo.class);
    }

    @Override
    public void stopProcess(String procInstanceId, String userId) {
        processService.stopProcessInstanceById(procInstanceId, ProcessStatus.DELETED, "", userId);
    }

    @Override
    public void updateAssignee(String idTask, String idsAssignee, List<String> idsCoOperator) {
        Task task = taskService.createTaskQuery().taskId(idTask).singleResult();
        if (task == null) {
            throw new IllegalArgumentException("任务不存在，ID: " + idTask);
        }
        taskService.setAssignee(idTask, idsAssignee);
        if (ObjectUtil.isNotEmpty(idsCoOperator)) {
            updateTaskRelations(idTask, IdentityLinkType.PARTICIPANT, idsCoOperator);
        }
    }

    @Override
    public void updateCoOperator(String idTask, String idsCoOperator) {
        updateTaskRelations(idTask, IdentityLinkType.PARTICIPANT, Arrays.asList(idsCoOperator.split(",")));
    }

    @Override
    public void updateCandidates(String idTask, String idsCandidate) {
        updateTaskRelations(idTask, IdentityLinkType.CANDIDATE, Arrays.asList(idsCandidate.split(",")));
    }

    private void updateTaskRelations(String idTask, String identityType, List<String> relations) {
        Task task = taskService.createTaskQuery().taskId(idTask).singleResult();
        if (task == null) {
            throw new IllegalArgumentException("任务不存在，ID: " + idTask);
        }
        List<IdentityLink> identityLinks = taskService.getIdentityLinksForTask(task.getId());
        //先清除掉指定类型的干系人
        identityLinks.stream().filter(link -> identityType.equals(link.getType()))
                .forEach(link -> taskService.deleteUserIdentityLink(task.getId(), link.getUserId(), identityType));
        relations.forEach(relation -> taskService.addUserIdentityLink(idTask, relation, identityType));
    }

    @Override
    public TaskInfo addTask(String procInstanceId, String taskDefinitionKey) {
        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                .processInstanceId(procInstanceId)
                .singleResult();
        if (processInstance == null) {
            return null;
        }
        List<Task> tasks = taskService.createTaskQuery().processInstanceId(processInstance.getId()).list();
        if (ObjectUtils.isEmpty(tasks)) {
            throw new IllegalArgumentException("工单任务不存在");
        }
        DynamicUserTaskBuilder taskBuilder = new DynamicUserTaskBuilder();
        taskBuilder.setName("并行子任务");
        String taskDef = "a" + UUID.fastUUID();
        taskBuilder.setId(taskDef);
        dynamicBpmnService.injectParallelUserTask(tasks.get(0).getId(), taskBuilder);
        // 折中方案，重新遍历流程任务，并找到指定的流程任务并返回
        List<Task> reloadTasks = taskService.createTaskQuery().processInstanceId(processInstance.getId()).list();
        for (Task task : reloadTasks) {
            if (taskDef.equals(task.getTaskDefinitionKey())) {
                TaskInfo taskInfo = BeanUtil.copyProperties(task, TaskInfo.class);
                taskInfo.setTaskDefinitionKey("Resource_" + taskDefinitionKey);
                return taskInfo;
            }
        }
        return null;
    }

    @Override
    public List<BackNodeInfo> getBackNodes(String taskId) {
        return flowTaskService.getBackNodes(taskId).stream()
                .map(Flow::toBackNodeInfo)
                .collect(Collectors.toList());
    }

    @Override
    public String backNode(BackNodeReq backNodeReq) {
        TaskComment comment = new TaskComment();
        comment.setMessage(StringUtils.isEmpty(backNodeReq.getMessage()) ? "驳回至指定节点" : backNodeReq.getMessage());
        comment.setCommentType(TaskComment.prefix + ActionType.REJECT.getType());
        comment.setStatus(ActionType.REJECT.getStatus());
        String idProcIns = null;
        try {
            idProcIns = flowTaskService.backTask(backNodeReq.getBackTaskDefKey(), backNodeReq.getTaskId(), comment, null);
        }
        catch (Exception e) {
            throw new ServiceException("执行回退流程异常，请联系管理员");
        }
        //通过流程实例id查询当前任务id
        Task task = taskService.createTaskQuery().processInstanceId(idProcIns).singleResult();
        return task == null ? null : task.getId();
    }

    @Override
    public String getIdFinishedTaskByTaskDefKey(String idProcInst, String taskDefKey) {
        // 获取历史任务记录（已完成）
        List<HistoricTaskInstance> finishedTasks = historyService.createHistoricTaskInstanceQuery()
                .processInstanceId(idProcInst)
                .finished()
                .orderByHistoricTaskInstanceStartTime().asc()
                .list();
        // 取每个 taskDefKey 最新一次的 taskId（也可以反向取最后一个）
        Map<String, String> taskDefKeyToTaskIdMap = finishedTasks.stream()
                .collect(Collectors.toMap(
                        HistoricTaskInstance::getTaskDefinitionKey,
                        HistoricTaskInstance::getId,
                        (existing, replacement) -> replacement));
        return taskDefKeyToTaskIdMap.getOrDefault(taskDefKey, null);
    }

    @Override
    public ApproveStaticInfo getApproveTaskStatic(ApproveStaticQuery query) {
        LocalDateTime startTime = getStartTimeByType(query.getType());
        String userId = query.getUserId();
        LocalDateTime endTime = LocalDateTime.now();

        // 查询总数
        TaskQuery totalQuery = taskService.createTaskQuery()
                .taskAssignee(userId)
                .taskCreatedAfter(Date.from(startTime.atZone(ZoneId.systemDefault()).toInstant()))
                .taskCreatedBefore(Date.from(endTime.atZone(ZoneId.systemDefault()).toInstant()))
                .taskDefinitionKeyLike("UserTask%");

        long total = totalQuery.count();

        // 查询待处理数量（活跃任务）
        TaskQuery handleQuery = taskService.createTaskQuery()
                .active()
                .taskAssignee(userId)
                .taskCreatedAfter(Date.from(startTime.atZone(ZoneId.systemDefault()).toInstant()))
                .taskCreatedBefore(Date.from(endTime.atZone(ZoneId.systemDefault()).toInstant()))
                .taskDefinitionKeyLike("UserTask%");

        long handle = handleQuery.count();

        ApproveStaticInfo result = new ApproveStaticInfo();
        result.setTotal((int) total);
        result.setHandle((int) handle);

        return result;
    }

    private LocalDateTime getStartTimeByType(String type) {
        LocalDateTime now = LocalDateTime.now();
        switch (type) {
            case "day":
                return now.toLocalDate().atStartOfDay();
            case "week":
                return now.with(DayOfWeek.MONDAY).toLocalDate().atStartOfDay();
            case "month":
                return now.withDayOfMonth(1).toLocalDate().atStartOfDay();
            default:
                throw new IllegalArgumentException("不支持的统计类型: " + type);
        }
    }

        /**
     * 获取待审任务月度统计
     *
     * @param year 年份
     * @return 月度统计结果 Map<月份, 数量>
     */
    @Override
    public Map<String, Integer> getApproveTaskMonthStatic(Integer year, String userId) {
        LocalDateTime startTime = LocalDateTime.of(year, 1, 1, 0, 0, 0);
        LocalDateTime endTime = LocalDateTime.of(year, 12, 31, 23, 59, 59);

        Date startDate = Date.from(startTime.atZone(ZoneId.systemDefault()).toInstant());
        Date endDate = Date.from(endTime.atZone(ZoneId.systemDefault()).toInstant());

        // 查询年度内的所有任务
        List<Task> tasks = taskService.createTaskQuery()
                .taskAssignee(userId)
                .taskCreatedAfter(startDate)
                .taskCreatedBefore(endDate)
                .taskDefinitionKeyLike("UserTask%")
                .list();

        Map<String, Integer> monthData = new HashMap<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");

        for (Task task : tasks) {
            LocalDateTime createTime = task.getCreateTime().toInstant()
                    .atZone(ZoneId.systemDefault()).toLocalDateTime();
            String monthKey = createTime.format(formatter);
            monthData.merge(monthKey, 1, Integer::sum);
        }

        return monthData;
    }
}
