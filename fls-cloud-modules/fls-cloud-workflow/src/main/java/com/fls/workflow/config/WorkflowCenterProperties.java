package com.fls.workflow.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties(prefix = "workflow")
@Data
public class WorkflowCenterProperties {
    private String host = "http://localhost:8008";
    private String submitPath = "/flowable/form/submitData";

    private String startPath = "/flowable/form/submitStartData";

    private String backTaskPath = "/flowable/form/backTask";

    public String getSubmitPath() {
        return host + submitPath;
    }

    public String getBackTaskPath() {
        return host + backTaskPath;
    }

    public String getStartPath() {
        return host + startPath;
    }
}
