package com.fls.workflow.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.fls.todo.api.RemoteTodoService;
import com.fls.todo.api.model.TaskCancelReq;
import com.fls.todo.api.model.TaskCompleteReq;
import com.fls.todo.api.model.TaskTransferReq;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.flowable.engine.TaskService;
import org.flowable.task.api.Task;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

@Slf4j
@Service
public class TodoTaskService {

    @DubboReference
    private RemoteTodoService remoteTodoService;

    @Resource
    private TaskService taskService;

    @Async
    public void handleTodo(Task task, String idUser) {
        String idBill = task.getProcessVariables().get("idBill").toString();
        if (StrUtil.isNotBlank(idBill)) {
            log.info("handle manual complete todo task, idBill: {}, idUser: {}", idBill, idUser);
            TaskCompleteReq taskCompleteReq = new TaskCompleteReq(idBill, idUser);
            remoteTodoService.completeTask(taskCompleteReq);
        }
    }

    @Async
    public void cancelTodo(Task task, String idUser) {
        String idBill = getVariable(task, "idBill");
        if (StrUtil.isNotBlank(idBill)) {
            log.info("handle manual cancel todo task, idBill: {}, idUser: {}", idBill, idUser);
            TaskCancelReq taskCancelReq = new TaskCancelReq(idBill, idUser);
            remoteTodoService.cancelTask(taskCancelReq);
        }
    }

    @Async
    public void transferTodo(Task task, String operator, String idTransfer) {
        String idBill = getVariable(task, "idBill");
        if (StrUtil.isNotBlank(idBill)) {
            log.info("handle manual transfer todo task, idBill: {}, idUser: {},idTransfer: {}", idBill, operator, idTransfer);
            TaskTransferReq transferReq = new TaskTransferReq(idBill, operator, idTransfer);
            remoteTodoService.transfer(transferReq);
        }
    }

    private String getVariable(Task task, String key) {
        Map<String, Object> variables = taskService.getVariables(task.getId());
        if (CollUtil.isNotEmpty(variables)) {
            return (String) variables.get(key);
        }
        return StrUtil.EMPTY;
    }
}
