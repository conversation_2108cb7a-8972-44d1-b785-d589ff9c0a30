package com.fls.workflow.model.vo;

import lombok.Data;

/**
 * 待审任务信息
 *
 * <AUTHOR>
 * @since 2025-07-17
 */
@Data
public class TodoTaskVo {

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 资源id
     */
    private String idResource;

    /**
     * 资源编号
     */
    private String resCode;

    /**
     * 资源名称
     */
    private String resName;

    /**
     * 交易类型编号
     */
    private String transCode;

    /**
     * 交易类型名称
     */
    private String transName;

    /**
     * 单据id
     */
    private String billId;

    /**
     * 单据编号
     */
    private String billCode;

    /**
     * 经营主体id
     */
    private String idBizunit;

    /**
     * 经营主体名称
     */
    private String bizunitName;

    /**
     * 发起人用户id
     */
    private String initiator;

    /**
     * 发起人用户名称
     */
    private String initiatorName;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 流程名称
     */
    private String processName;

    /**
     * 任务定义key
     */
    private String taskDefinitionKey;
}
