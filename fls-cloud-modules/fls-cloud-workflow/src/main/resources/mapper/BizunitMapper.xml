<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fls.workflow.mapper.BizunitMapper">
    <select id="getBizunitList" resultType="com.fls.master.api.model.BizunitNameInfo">
        SELECT * from t_base_bizunit tbb where tbb.delete_flag = '0' and tbb.status = '2'
        <if test="idsBizunit != null and idsBizunit.size() > 0">
            and tbb.id_bizunit IN
            <foreach item="item" collection="idsBizunit" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>
</mapper>
