<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fls.workflow.mapper.UserMapper">
    <select id="getUserNameById" resultType="String">
        select
            tbu.name as userName
        from
            t_base_user tbu
                left join t_base_person tbp on
                tbu.id_identity = tbp.id_person
        where tbu.id_user = #{idUser}
    </select>
</mapper>
