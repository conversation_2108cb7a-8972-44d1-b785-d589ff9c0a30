# Tomcat
server:
  port: 9200
  # undertow 配置
  undertow:
    # HTTP post内容的最大大小。当值为-1时，默认值为大小是无限的
    max-http-post-size: -1
    # 以下的配置会影响buffer,这些buffer会用于服务器连接的IO操作,有点类似netty的池化内存管理
    # 每块buffer的空间大小,越小的空间被利用越充分
    buffer-size: 512
    # 是否分配的直接内存
    direct-buffers: true
    threads:
      # 设置IO线程数, 它主要执行非阻塞的任务,它们会负责多个连接, 默认设置每个CPU核心一个线程
      io: 8
      # 阻塞任务线程池, 当执行类似servlet请求阻塞操作, undertow会从这个线程池中取得线程,它的值设置取决于系统的负载
      worker: 256

# Spring
spring:
  application:
    # 应用名称
    name: fls-cloud-workflow
  main:
    allow-circular-references: true
  # datasource配置
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    dynamic:
      # 设置默认的数据源或者数据源组,默认值即为 master
      primary: master
      datasource:
        # 主库数据源
        master:
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: ***************************************************************************************************************************************************************************************
          username: flsoa
          password: flsoa
        fls_db:
          url: ****************************************************************************************************************************************************************************
          username: flsoa
          password: flsoa
          driver-class-name: com.mysql.cj.jdbc.Driver
      # 严格模式 匹配不到数据源则报错
      strict: true
      hikari:
        # 最大连接池数量
        maxPoolSize: 20
        # 最小空闲线程数量
        minIdle: 10
        # 配置获取连接等待超时的时间
        connectionTimeout: 30000
        # 校验超时时间
        validationTimeout: 5000
        # 空闲连接存活最大时间，默认10分钟
        idleTimeout: 600000
        # 此属性控制池中连接的最长生命周期，值0表示无限生命周期，默认30分钟
        maxLifetime: 1800000
        # 多久检查一次连接的活性
        keepaliveTime: 30000

  # nacos 配置
  cloud:
    nacos:
      # nacos 服务地址
      server-addr: ************:8848
      discovery:
        # 注册组
        group: DEFAULT_GROUP
        namespace: test
      password: nacos
      username: nacos
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  servlet:
    multipart:
      # 整个请求大小限制
      max-request-size: 20MB
      # 上传单个文件大小限制
      max-file-size: 10MB
  #jackson配置
  jackson:
    default-property-inclusion: NON_NULL
    # 日期格式化
    date-format: yyyy-MM-dd HH:mm:ss
    serialization:
      # 格式化输出
      INDENT_OUTPUT: false
      # 忽略无法转换的对象
      fail_on_empty_beans: false
    deserialization:
      # 允许对象忽略json中不存在的属性
      fail_on_unknown_properties: false
      ACCEPT_EMPTY_STRING_AS_NULL_OBJECT: true
  rabbitmq:
    host: ************
    port: 5672
    username: test
    password: test
    virtual-host: /devops_message
    #确认消息已发送到交换机（Exchange）
    publisher-confirm-type: correlated
    #确认消息已发送到队列（Queue）
    publisher-returns: true
    #消费方消息确认，手动确认
    listener:
      simple:
        acknowledge-mode: manual
        default-requeue-rejected: false
        retry:
          #监听重试是否可用
          enabled: true
          #最大重试次数，默认为3
          max-attempts: 5
          max-interval: 60000
  # redis通用配置 子服务可以自行配置进行覆盖
  redis:
    host: ************
    port: 6379
    # 密码
    # password:
    database: 0
    timeout: 10s
    ssl: false
# redisson 配置
redisson:
  # redis key前缀
  keyPrefix:
  # 线程池数量
  threads: 4
  # Netty线程池数量
  nettyThreads: 8
  # 单节点配置
  singleServerConfig:
    # 客户端名称
    clientName: ${spring.application.name}
    # 最小空闲连接数
    connectionMinimumIdleSize: 8
    # 连接池大小
    connectionPoolSize: 32
    # 连接空闲超时，单位：毫秒
    idleConnectionTimeout: 10000
    # 命令等待超时，单位：毫秒
    timeout: 3000
    # 发布和订阅连接池大小
    subscriptionConnectionPoolSize: 50
  # redisson 缓存配置
  cacheGroup:
    # 用例: @Cacheable(cacheNames="groupId", key="#XXX") 方可使用缓存组配置
    - groupId: redissonCacheMap
      # 组过期时间(脚本监控)
      ttl: 60000
      # 组最大空闲时间(脚本监控)
      maxIdleTime: 60000
      # 组最大长度
      maxSize: 0
dubbo:
  application:
    logger: slf4j
    # 元数据中心 local 本地 remote 远程 这里使用远程便于其他服务获取
    metadataType: remote
    # 可选值 interface、instance、all，默认是 all，即接口级地址、应用级地址都注册
    register-mode: instance
    service-discovery:
      # FORCE_INTERFACE，只消费接口级地址，如无地址则报错，单订阅 2.x 地址
      # APPLICATION_FIRST，智能决策接口级/应用级地址，双订阅
      # FORCE_APPLICATION，只消费应用级地址，如无地址则报错，单订阅 3.x 地址
      migration: FORCE_APPLICATION
  protocol:
    # 设置为 tri 即可使用 Triple 3.0 新协议
    # 性能对比 dubbo 协议并没有提升 但基于 http2 用于多语言异构等 http 交互场景
    # 使用 dubbo 协议通信
    name: dubbo
    # dubbo 协议端口(-1表示自增端口,从20880开始)
    port: -1
    # 指定dubbo协议注册ip
    host: ************
  # 注册中心配置
  registry:
    use-as-config-center: false
    # use-as-metadata-center: false
    address: nacos://${spring.cloud.nacos.server-addr}
    group: DUBBO_GROUP
    parameters:
      namespace: test-dubbo
      username: nacos
      password: nacos
  # 消费者相关配置
  consumer:
    # 结果缓存(LRU算法)
    # 会有数据不一致问题 建议在注解局部开启
    cache: false
    # 支持校验注解
    validation: true
    # 超时时间
    timeout: 60000
    # 初始化检查
    check: false
  scan:
    # 接口实现类扫描
    base-packages: com.fls.**.dubbo
  #禁止provicer处理自定义异常
  provider:
    filter: -exception

# Sa-Token配置
sa-token:
  # token名称 (同时也是cookie名称)
  token-name: Authorization
  # token有效期  单位: 秒
  timeout: 86400
  # token临时有效期 (指定时间无操作就过期) 单位: 秒
  activity-timeout: -1
  # 开启内网服务调用鉴权
  check-id-token: true
  # Id-Token的有效期 (单位: 秒)
  id-token-timeout: 600
  # 是否允许同一账号并发登录 (为true时允许一起登录, 为false时新登录挤掉旧登录)
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个token (为true时所有登录共用一个token, 为false时每次登录新建一个token)
  is-share: false
  # 是否尝试从header里读取token
  is-read-head: true
  # 是否尝试从cookie里读取token
  is-read-cookie: false
  # token前缀
  token-prefix: "Bearer"
  # jwt秘钥
  jwt-secret-key: fls-cloud@2022
  # 是否输出操作日志
  is-log: true

# 日志配置
logging:
  level:
    com.fls: info
    org.springframework: warn
    org.apache.dubbo: warn
    com.alibaba.nacos: warn
    com.chz.mapper: info
  config: classpath:logback.xml

# MyBatisPlus配置
mybatis-plus:
  # 不支持多包, 如有需要可在注解配置 或 提升扫包等级
  # 例如 com.**.**.mapper
  mapperPackage: com.fls.**.mapper
  # 对应的 XML 文件位置
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: com.fls.**.entity
  # 启动时是否检查 MyBatis XML 文件的存在，默认不检查
  checkConfigLocation: false
  configuration:
    # 自动驼峰命名规则（camel case）映射
    mapUnderscoreToCamelCase: true
    # MyBatis 自动映射策略
    # NONE：不启用 PARTIAL：只对非嵌套 resultMap 自动映射 FULL：对所有 resultMap 自动映射
    autoMappingBehavior: PARTIAL
    # MyBatis 自动映射时未知列或未知属性处理策
    # NONE：不做处理 WARNING：打印相关警告 FAILING：抛出异常和详细信息
    autoMappingUnknownColumnBehavior: NONE
    # 更详细的日志输出 会有性能损耗 org.apache.ibatis.logging.stdout.StdOutImpl
    # 关闭日志记录 (可单纯使用 p6spy 分析) org.apache.ibatis.logging.nologging.NoLoggingImpl
    # 默认日志输出 org.apache.ibatis.logging.slf4j.Slf4jImpl
    logImpl: org.apache.ibatis.logging.slf4j.Slf4jImpl
  global-config:
    # 是否打印 Logo banner
    banner: true
    dbConfig:
      # 主键类型
      # AUTO 自增 NONE 空 INPUT 用户输入 ASSIGN_ID 雪花 ASSIGN_UUID 唯一 UUID
      idType: ASSIGN_ID
      # 逻辑已删除值
      logicDeleteValue: 1
      # 逻辑未删除值
      logicNotDeleteValue: 0
      insertStrategy: NOT_NULL
      updateStrategy: NOT_NULL
      where-strategy: NOT_NULL
flowable:
  # 关闭定时任务Job
  async-executor-activate: true
  check-process-definitions: false
  process-definition-location-prefix: classpath:/processes/
  database-schema-update: false
workflow:
  host: http://************:8088
  submit-path: /flowable/form/submitData
  back-task-path: /flowable/form/backTask
