<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.fls</groupId>
        <artifactId>fls-cloud-modules</artifactId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>fls-cloud-workflow</artifactId>


    <dependencies>
        <!-- SpringCloud Alibaba Nacos -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <!-- Mysql Connector -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fls</groupId>
            <artifactId>fls-cloud-common-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fls</groupId>
            <artifactId>fls-cloud-common-log</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fls</groupId>
            <artifactId>fls-cloud-common-core</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>io.swagger.core.v3</groupId>
                    <artifactId>swagger-annotations</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.fls</groupId>
            <artifactId>fls-cloud-common-mybatis</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.fls</groupId>
                    <artifactId>fls-cloud-common-security</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>p6spy</groupId>
                    <artifactId>p6spy</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- Flowable -->
        <dependency>
            <groupId>org.flowable</groupId>
            <artifactId>flowable-spring-boot-starter-process-rest</artifactId>
            <version>6.7.2</version>
            <exclusions>
                <exclusion>
                    <groupId>io.swagger</groupId>
                    <artifactId>swagger-annotations</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.swagger</groupId>
                    <artifactId>swagger-models</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.fls</groupId>
            <artifactId>fls-cloud-common-dubbo</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fls</groupId>
            <artifactId>fls-cloud-dubbo-api-workflow</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fls</groupId>
            <artifactId>fls-cloud-dubbo-api-todo</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fls</groupId>
            <artifactId>fls-cloud-dubbo-api-master-data</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-amqp</artifactId>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>com.spotify</groupId>
                <artifactId>docker-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project>
