package com.fls.job.exception;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 业务错误枚举
 * <AUTHOR>
 * @date 2022/12/12/17:15
 */
@Getter
@AllArgsConstructor
public enum FlsJobExceptionEnum {
    /**
     * 异常码定义
     */
    JOB_NOT_EXISTS(10001, "任务记录不存在"),

    NEXT_VALID_TIME_ERROR(10002, "下次调度时间生成异常"),

    NONE_SCHEDULE_TYPE_NOT_VALID(10003, "NONE类型不适用"),

    CHILD_JOB_NOT_EXISTS(10004, "子任务不存在"),

    SCHEDULE_TYPE_NOT_MATCH(10005, "调度类型不匹配"),

    SCHEDULE_CONF_IS_ILLEGAL(10006, "调度配置非法");

    int code;
    String msg;
}
