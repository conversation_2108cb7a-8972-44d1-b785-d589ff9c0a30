package com.fls.job.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fls.common.core.domain.ResponseData;
import com.fls.job.entity.FlsJobGroup;
import com.fls.job.entity.dto.FlsJobGroupDTO;
import com.fls.job.entity.query.FlsJobGroupQuery;
import com.fls.job.service.IFlsJobGroupService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 任务分组
 * <AUTHOR>
 * @date 2022/12/03/14:28
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/jobgroup")
public class FlsJobGroupController {

    private final IFlsJobGroupService flsJobGroupService;

    @GetMapping("/page")
    public ResponseData<Page<FlsJobGroup>> pageList(Page page, FlsJobGroupQuery query){
        return ResponseData.ok(flsJobGroupService.pageList(page, query));
    }

    @PostMapping("/add")
    public ResponseData add(@Valid @RequestBody FlsJobGroupDTO group){
        return flsJobGroupService.add(group) ? ResponseData.ok() : ResponseData.fail();
    }

    @PutMapping("/edit")
    public ResponseData edit(@Valid @RequestBody FlsJobGroupDTO group){
        if (StrUtil.isBlank(group.getId())){
            return ResponseData.fail("任务分组主键不能为空");
        }
        return flsJobGroupService.edit(group) ? ResponseData.ok() : ResponseData.fail();
    }

    @DeleteMapping("/remove")
    public ResponseData remove(String id){
        return flsJobGroupService.delete(id) ? ResponseData.ok() : ResponseData.fail();
    }

    @GetMapping("/all")
    public ResponseData<List<FlsJobGroup>> getAllGroup(){
        return ResponseData.ok(flsJobGroupService.list());
    }
}
