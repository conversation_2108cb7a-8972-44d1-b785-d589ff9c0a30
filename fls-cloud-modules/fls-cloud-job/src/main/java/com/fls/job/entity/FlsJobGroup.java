package com.fls.job.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2022/12/03/13:50
 */
@Data
@TableName("t_job_group")
public class FlsJobGroup implements Serializable {
    /**
     * 任务分组id;
     */
    @TableId(type = IdType.INPUT)
    private String id;
    /**
     * 应用code
     */
    private String appCode;
    /**
     * 标题
     */
    private String title;
    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建人名称
     */
    private String createByName;

    /**
     * 最后一次修改人
     */
    private String updateBy;

    /**
     * 修改人名称
     */
    private String updateByName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 删除标识（0：否 1：是）
     */
    @TableLogic
    private Integer delFlag;
}
