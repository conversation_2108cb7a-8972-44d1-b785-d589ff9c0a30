package com.fls.job.thread;

import com.fls.job.constant.TriggerTypeEnum;
import com.fls.job.entity.FlsJobLog;
import com.fls.job.mapper.FlsJobLogMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 失败监控
 * <AUTHOR>
 * @date 2022/12/12/15:09
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class JobFailMonitorHelper {

    private final FlsJobLogMapper flsJobLogMapper;
    private final JobTriggerPoolHelper jobTriggerPoolHelper;

    private Thread monitorThread;
    private volatile boolean toStop = false;

    @PostConstruct
    public void start(){
        monitorThread = new Thread(() -> {
            while (!toStop){
                try {
                    List<String> failLogIds = flsJobLogMapper.findFailJobLogIds(1000);
                    if (failLogIds != null && !failLogIds.isEmpty()){
                        for (String failLogId : failLogIds) {
                            int lockRet = flsJobLogMapper.updateAlarmStatus(failLogId, 0, -1);
                            if (lockRet < 1){
                                continue;
                            }
                            FlsJobLog failLog = flsJobLogMapper.selectById(failLogId);
                            if (failLog.getExecutorFailRetryCount() > 0){
                                jobTriggerPoolHelper.trigger(failLog.getJobId(), TriggerTypeEnum.RETRY, (failLog.getExecutorFailRetryCount()-1));
                            }

                            // 警告相关,待定
                            int newAlarmStatus = 1;
                            flsJobLogMapper.updateAlarmStatus(failLogId, -1, newAlarmStatus);
                        }
                    }
                } catch (Exception e) {
                    if (!toStop) {
                        log.error(">>>>>>>>>>> fls-job, job fail monitor thread error:{}", e);
                    }
                }
                try {
                    TimeUnit.SECONDS.sleep(10);
                } catch (Exception e) {
                    if (!toStop) {
                        log.error(e.getMessage(), e);
                    }
                }
            }
            log.info(">>>>>>>>>>> fls-job, job fail monitor thread stop");
        });
        monitorThread.setDaemon(true);
        monitorThread.setName("fls-job, jobFailMonitorHelper");
        monitorThread.start();
    }

    @PreDestroy
    public void toStop(){
        toStop = true;
        // interrupt and wait
        monitorThread.interrupt();
        try {
            monitorThread.join();
        } catch (InterruptedException e) {
            log.error(e.getMessage(), e);
        }
    }
}
