package com.fls.job.constant;

/**
 * <AUTHOR>
 * @date 2022/11/29/9:57
 */
public enum ExecutorBlockStrategyEnum {
    /**
     * 串行
     */
    SERIAL_EXECUTION("Serial execution"),
    /*CONCURRENT_EXECUTION("并行"),*/
    /**
     * 丢弃后调度
     */
    DISCARD_LATER("Discard Later"),
    /**
     * 覆盖之前
     */
    COVER_EARLY("Cover Early");

    private String title;
    private ExecutorBlockStrategyEnum (String title) {
        this.title = title;
    }

    public void setTitle(String title) {
        this.title = title;
    }
    public String getTitle() {
        return title;
    }

    public static ExecutorBlockStrategyEnum match(String name, ExecutorBlockStrategyEnum defaultItem) {
        if (name != null) {
            for (ExecutorBlockStrategyEnum item:ExecutorBlockStrategyEnum.values()) {
                if (item.name().equals(name)) {
                    return item;
                }
            }
        }
        return defaultItem;
    }
}
