package com.fls.job.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2022/11/29/14:40
 */
@Data
@RefreshScope
@Configuration
@ConfigurationProperties(prefix = "fls.job")
public class FlsJobConfig{
    private int triggerPoolFastMax;

    private int triggerPoolSlowMax;

    private int logRetentionDays;
}
