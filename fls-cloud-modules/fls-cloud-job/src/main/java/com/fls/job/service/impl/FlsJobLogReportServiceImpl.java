package com.fls.job.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fls.job.entity.FlsJobLogReport;
import com.fls.job.entity.vo.FlsJobReportBaseVO;
import com.fls.job.entity.vo.FlsJobReportChartVO;
import com.fls.job.mapper.FlsJobGroupMapper;
import com.fls.job.mapper.FlsJobInfoMapper;
import com.fls.job.mapper.FlsJobLogReportMapper;
import com.fls.job.service.IFlsJobLogReportService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

@Service
@RequiredArgsConstructor
public class FlsJobLogReportServiceImpl extends ServiceImpl<FlsJobLogReportMapper, FlsJobLogReport> implements IFlsJobLogReportService {

    private final FlsJobInfoMapper flsJobInfoMapper;
    private final FlsJobGroupMapper flsJobGroupMapper;

    @Override
    public FlsJobReportBaseVO getBaseInfo() {
        Long jobInfoCount = flsJobInfoMapper.selectCount(null);
        int jobLogCount = 0;
        int jobLogSuccessCount = 0;
        FlsJobLogReport jobLogReport = this.baseMapper.queryLogReportTotal();
        if (jobLogReport != null){
            jobLogCount = jobLogReport.getRunningCount() + jobLogReport.getSuccessCount() + jobLogReport.getFailCount();
            jobLogSuccessCount = jobLogReport.getSuccessCount();
        }
        Long jobGroupCount = flsJobGroupMapper.selectCount(null);
        FlsJobReportBaseVO reportBaseVO = new FlsJobReportBaseVO();
        reportBaseVO.setJobCount(String.valueOf(jobInfoCount));
        reportBaseVO.setJobLogCount(String.valueOf(jobLogCount));
        reportBaseVO.setJobLogSuccessCount(String.valueOf(jobLogSuccessCount));
        reportBaseVO.setJobGroupCount(String.valueOf(jobGroupCount));
        return reportBaseVO;
    }

    @Override
    public FlsJobReportChartVO chart(String startDate, String endDate) {
        List<String> triggerDayList = new ArrayList<>();
        List<Integer> triggerDayCountRunningList = new ArrayList<>();
        List<Integer> triggerDayCountSuccessList = new ArrayList<>();
        List<Integer> triggerDayCountFailList = new ArrayList<>();
        int triggerCountRunningTotal = 0;
        int triggerCountSuccessTotal = 0;
        int triggerCountFailTotal = 0;

        // 获取给定范围的每日报告
        List<FlsJobLogReport> logReportList = this.baseMapper.queryLogReport(startDate, endDate);
        if (logReportList != null && logReportList.size() > 0){
            for (FlsJobLogReport item : logReportList) {
                String day = DateUtil.format(item.getTriggerDay(), DatePattern.NORM_DATE_PATTERN);
                int triggerDayCountRunning = item.getRunningCount();
                int triggerDayCountSuccess = item.getSuccessCount();
                int triggerDayCountFail = item.getFailCount();

                triggerDayList.add(day);
                triggerDayCountRunningList.add(triggerDayCountRunning);
                triggerDayCountSuccessList.add(triggerDayCountSuccess);
                triggerDayCountFailList.add(triggerDayCountFail);

                triggerCountRunningTotal += triggerDayCountRunning;
                triggerCountSuccessTotal += triggerDayCountSuccess;
                triggerCountFailTotal += triggerDayCountFail;
            }
        } else {
            final Calendar c = Calendar.getInstance();
            for (int i = -6; i <= 0; i++) {
                c.setTime(new Date());
                c.add(Calendar.DAY_OF_MONTH, i);
                triggerDayList.add(DateUtil.format(c.getTime(), DatePattern.NORM_DATE_PATTERN));
                triggerDayCountRunningList.add(0);
                triggerDayCountSuccessList.add(0);
                triggerDayCountFailList.add(0);
            }
        }
        FlsJobReportChartVO chartVO = new FlsJobReportChartVO();
        chartVO.setTriggerDayList(triggerDayList);
        chartVO.setTriggerDayCountRunningList(triggerDayCountRunningList);
        chartVO.setTriggerDayCountSuccessList(triggerDayCountSuccessList);
        chartVO.setTriggerDayCountFailList(triggerDayCountFailList);

        chartVO.setTriggerCountRunningTotal(triggerCountRunningTotal);
        chartVO.setTriggerCountSuccessTotal(triggerCountSuccessTotal);
        chartVO.setTriggerCountFailTotal(triggerCountFailTotal);
        return chartVO;
    }
}
