package com.fls.job.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fls.job.entity.FlsJobLog;
import com.fls.job.entity.query.FlsJobLogQuery;
import com.fls.job.mapper.FlsJobLogMapper;
import com.fls.job.service.IFlsJobLogService;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
public class FlsJobLogServiceImpl extends ServiceImpl<FlsJobLogMapper, FlsJobLog> implements IFlsJobLogService {
    @Override
    public Page<FlsJobLog> pageList(Page page, FlsJobLogQuery query) {
        return this.baseMapper.pageByQuery(page, query);
    }

    @Override
    public List<String> findClearLogIds(String jobGroup, String jobId, Date clearBeforeTime, int clearBeforeNum, int pageSize) {
        return this.baseMapper.findClearLogIds(jobGroup, jobId, clearBeforeTime, clearBeforeNum, pageSize);
    }

    @Override
    public boolean clearLog(List<String> logIds) {
        return this.baseMapper.clearLog(logIds) > 0;
    }
}
