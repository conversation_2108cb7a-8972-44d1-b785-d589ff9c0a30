package com.fls.job.controller;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fls.common.core.domain.ResponseData;
import com.fls.job.constant.TriggerTypeEnum;
import com.fls.job.entity.FlsJobInfo;
import com.fls.job.entity.dto.FlsJobInfoDTO;
import com.fls.job.entity.query.FlsJobInfoQuery;
import com.fls.job.entity.vo.FlsJobInfoVO;
import com.fls.job.service.IFlsJobInfoService;
import com.fls.job.thread.JobTriggerPoolHelper;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequiredArgsConstructor
@RequestMapping("/jobinfo")
public class FlsJobInfoController {

    private final IFlsJobInfoService flsJobInfoService;
    private final JobTriggerPoolHelper jobTriggerPoolHelper;

    @GetMapping("/page")
    public ResponseData<Page<FlsJobInfo>> pageList(Page page, FlsJobInfoQuery query){
        return ResponseData.ok(flsJobInfoService.pageList(page, query));
    }

    @PostMapping("/add")
    public ResponseData add(@Valid @RequestBody FlsJobInfoDTO info){
        if (StrUtil.isNotBlank(info.getRequestBody())){
            if (!JSONUtil.isTypeJSON(info.getRequestBody())){
                return ResponseData.fail("请求体参数非法");
            }
            try {
                Object requestBody = JSON.parse(info.getRequestBody());
                if (requestBody == null){
                    return ResponseData.fail("请求体参数非法");
                }
            } catch (Exception e) {
                return ResponseData.fail("请求体参数非法");
            }
        }
        return flsJobInfoService.add(info) ? ResponseData.ok() : ResponseData.fail();
    }

    @PutMapping("/edit")
    public ResponseData edit(@Valid @RequestBody FlsJobInfoDTO info){
        if (info.getId() == null){
            return ResponseData.fail("任务主键不能为空");
        }
        if (StrUtil.isNotBlank(info.getRequestBody())){
            if (!JSONUtil.isTypeJSON(info.getRequestBody())){
                return ResponseData.fail("请求体参数非法");
            }
            try {
                Object requestBody = JSON.parse(info.getRequestBody());
                if (requestBody == null){
                    return ResponseData.fail("请求体参数非法");
                }
            } catch (Exception e) {
                return ResponseData.fail("请求体参数非法");
            }
        }
        return flsJobInfoService.edit(info) ? ResponseData.ok() : ResponseData.fail();
    }

    @DeleteMapping("/remove")
    public ResponseData remove(String id){
        return flsJobInfoService.delete(id) ? ResponseData.ok() : ResponseData.fail();
    }

    @PutMapping("/stop")
    public ResponseData pause(String id){
        return flsJobInfoService.stop(id) ?  ResponseData.ok() : ResponseData.fail();
    }

    @PutMapping("/start")
    public ResponseData start(String id){
        return flsJobInfoService.start(id) ? ResponseData.ok() : ResponseData.fail();
    }

    @PostMapping("/trigger")
    public ResponseData triggerJob(String id){
        jobTriggerPoolHelper.trigger(id, TriggerTypeEnum.MANUAL, -1);
        return ResponseData.ok();
    }

    @GetMapping("/get")
    public ResponseData<List<FlsJobInfoVO>> getByQuery(FlsJobInfoQuery query){
        return ResponseData.ok(flsJobInfoService.getByQuery(query));
    }
}
