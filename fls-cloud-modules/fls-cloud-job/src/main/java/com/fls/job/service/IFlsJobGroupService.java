package com.fls.job.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.fls.job.entity.FlsJobGroup;
import com.fls.job.entity.FlsJobInfo;
import com.fls.job.entity.dto.FlsJobGroupDTO;
import com.fls.job.entity.query.FlsJobGroupQuery;

import javax.validation.Valid;

/**
 * 任务分组业务接口
 * <AUTHOR>
 * @date 2022/12/03/14:23
 */
public interface IFlsJobGroupService extends IService<FlsJobGroup> {
    /**
     * 分页列表
     * @param page 分页参数
     * @param query 查询参数
     * @return Page<FlsJobGroup>
     */
    Page<FlsJobGroup> pageList(Page page, FlsJobGroupQuery query);

    /**
     * 新增任务分组
     * @param group 任务分组信息
     * @return Boolean
     */
    Boolean add(@Valid FlsJobGroupDTO group);

    /**
     * 编辑任务分组
     * @param group 任务分组信息
     * @return Boolean
     */
    Boolean edit(@Valid FlsJobGroupDTO group);

    /**
     * 删除任务
     * @param id 任务分组id
     * @return Boolean
     */
    Boolean delete(String id);
}
