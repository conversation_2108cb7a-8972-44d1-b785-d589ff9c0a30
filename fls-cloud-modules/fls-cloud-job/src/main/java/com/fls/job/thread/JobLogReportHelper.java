package com.fls.job.thread;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fls.job.config.FlsJobConfig;
import com.fls.job.entity.FlsJobLogReport;
import com.fls.job.mapper.FlsJobLogMapper;
import com.fls.job.mapper.FlsJobLogReportMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 报表以及日志清理
 * <AUTHOR>
 * @date 2022/12/09/14:27
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class JobLogReportHelper {

    private final FlsJobConfig flsJobConfig;
    private final FlsJobLogMapper flsJobLogMapper;
    private final FlsJobLogReportMapper flsJobLogReportMapper;

    private Thread logReportThread;
    private volatile boolean toStop = false;

    @PostConstruct
    public void start(){
        logReportThread = new Thread(()->{
            // 最后一次清理日志时间
            long lastCleanLogTime = 0;

            while (!toStop){
                try {
                    for (int i = 0; i < 3; i++) {
                        // 以当前时间为准处理前面几天
                        Calendar today = Calendar.getInstance();
                        today.add(Calendar.DAY_OF_MONTH, -i);
                        today.set(Calendar.HOUR_OF_DAY, 0);
                        today.set(Calendar.MINUTE, 0);
                        today.set(Calendar.SECOND, 0);
                        today.set(Calendar.MILLISECOND, 0);
                        Date todayFrom = today.getTime();
                        today.set(Calendar.HOUR_OF_DAY, 23);
                        today.set(Calendar.MINUTE, 59);
                        today.set(Calendar.SECOND, 59);
                        today.set(Calendar.MILLISECOND, 999);
                        Date todayTo = today.getTime();

                        // 默认值
                        FlsJobLogReport toHandleReport = new FlsJobLogReport();
                        toHandleReport.setTriggerDay(todayFrom);
                        toHandleReport.setRunningCount(0);
                        toHandleReport.setSuccessCount(0);
                        toHandleReport.setFailCount(0);

                        // 每分钟处理日志报告
                        FlsJobLogReport reportResult = flsJobLogMapper.findLogReport(todayFrom, todayTo);
                        if (reportResult != null){
                            toHandleReport.setSuccessCount(reportResult.getSuccessCount() != null ? reportResult.getSuccessCount() : 0);
                            toHandleReport.setFailCount(reportResult.getFailCount() != null ? reportResult.getFailCount() : 0);
                            toHandleReport.setRunningCount(reportResult.getRunningCount() != null ? reportResult.getRunningCount() : 0);
                        }

                        int ret = flsJobLogReportMapper.updateByTime(toHandleReport);
                        if (ret < 1){
                            toHandleReport.setId(IdUtil.randomUUID());
                            flsJobLogReportMapper.insert(toHandleReport);
                        }
                    }
                } catch (Exception e) {
                    if (!toStop) {
                        log.error(">>>>>>>>>>> fls-job, job log report thread error:{}", e);
                    }
                }

                // 日志清理
                if (flsJobConfig.getLogRetentionDays() > 0
                        && System.currentTimeMillis() - lastCleanLogTime > 24*60*60*1000){
                    // 计算过期时间
                    Calendar expiredDay = Calendar.getInstance();
                    expiredDay.add(Calendar.DAY_OF_MONTH, -1 * flsJobConfig.getLogRetentionDays());
                    expiredDay.set(Calendar.HOUR_OF_DAY, 0);
                    expiredDay.set(Calendar.MINUTE, 0);
                    expiredDay.set(Calendar.SECOND, 0);
                    expiredDay.set(Calendar.MILLISECOND, 0);
                    Date clearBeforeTime = expiredDay.getTime();

                    // 清理过期日志
                    List<String> logIds;
                    do {
                        logIds = flsJobLogMapper.findClearLogIds(null, null, clearBeforeTime, 0, 1000);
                        if (logIds!=null && logIds.size()>0) {
                           flsJobLogMapper.clearLog(logIds);
                        }
                    } while (logIds!=null && logIds.size()>0);

                    // 更新清理日志时间
                    lastCleanLogTime = System.currentTimeMillis();
                }
                try {
                    TimeUnit.MINUTES.sleep(1);
                } catch (Exception e) {
                    if (!toStop) {
                        log.error(e.getMessage(), e);
                    }
                }
            }
            log.info(">>>>>>>>>>> fls-job, job log report thread stop");
        });
        logReportThread.setDaemon(true);
        logReportThread.setName("fls-job, admin JobLogReportHelper");
        logReportThread.start();
    }

    @PreDestroy
    public void toStop(){
        toStop = true;
        // interrupt and wait
        logReportThread.interrupt();
        try {
            logReportThread.join();
        } catch (InterruptedException e) {
            log.error(e.getMessage(), e);
        }
    }
}
