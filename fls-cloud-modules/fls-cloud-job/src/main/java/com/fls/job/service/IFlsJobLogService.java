package com.fls.job.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.fls.job.entity.FlsJobLog;
import com.fls.job.entity.query.FlsJobLogQuery;

import java.util.Date;
import java.util.List;

public interface IFlsJobLogService extends IService<FlsJobLog> {
    /**
     * 查询分页
     * @param page 分页参数
     * @param query 查询参数
     * @return Page<FlsJobLog>
     */
    Page<FlsJobLog> pageList(Page page, FlsJobLogQuery query);

    /**
     *
     * @param jobGroup
     * @param jobId
     * @param clearBeforeTime
     * @param clearBeforeNum
     * @param pageNum
     * @return
     */
    List<String> findClearLogIds(String jobGroup, String jobId, Date clearBeforeTime, int clearBeforeNum, int pageNum);

    /**
     * 清理日志
     * @param logIds 日志id集合
     * @return boolean
     */
    boolean clearLog(List<String> logIds);
}
