package com.fls.job.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fls.job.entity.FlsJobLogReport;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 调度报表Mapper
 * <AUTHOR>
 * @date 2022/12/07/17:22
 */
public interface FlsJobLogReportMapper extends BaseMapper<FlsJobLogReport> {
    /**
     * 根据调度时间更新
     * @param reportResult 参数
     * @return int
     */
    int updateByTime(@Param("report") FlsJobLogReport reportResult);

    /**
     * 获取日志数量
     * @return FlsJobLogReport
     */
    FlsJobLogReport queryLogReportTotal();

    /**
     * 获取给定范围的记录
     * @param startDate 起始
     * @param endDate 结束
     * @return List<FlsJobLogReport>
     */
    List<FlsJobLogReport> queryLogReport(@Param("from")String startDate, @Param("to")String endDate);
}
