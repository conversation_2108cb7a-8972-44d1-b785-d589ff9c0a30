package com.fls.job.core.trigger;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpException;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fls.job.constant.HandleStatusEnum;
import com.fls.job.constant.TriggerTypeEnum;
import com.fls.job.entity.FlsJobInfo;
import com.fls.job.entity.FlsJobLog;
import com.fls.job.mapper.FlsJobInfoMapper;
import com.fls.job.mapper.FlsJobLogMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/28/18:21
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class FlsJobTrigger {

    private final FlsJobInfoMapper flsJobInfoMapper;
    private final FlsJobLogMapper flsJobLogMapper;

    public void trigger(String jobId, TriggerTypeEnum triggerType, int failRetryCount){
        //获取任务信息
        FlsJobInfo info = flsJobInfoMapper.selectById(jobId);
        if (info == null){
            log.warn(">>>>>>>>>>>> trigger fail, jobId invalid，jobId={}", jobId);
            return;
        }
        int finalFailRetryCount = failRetryCount>=0?failRetryCount:info.getExecutorFailRetryCount();

        processTrigger(info, finalFailRetryCount, triggerType);
    }

    private void processTrigger(FlsJobInfo info, int finalFailRetryCount, TriggerTypeEnum triggerType) {
        // 记录调度日志
        FlsJobLog jobLog = new FlsJobLog();
        jobLog.setJobGroup(info.getJobGroup());
        jobLog.setJobId(info.getId());
        jobLog.setTriggerTime(LocalDateTime.now());
        jobLog.setId(IdUtil.randomUUID());
        jobLog.setExecutorFailRetryCount(finalFailRetryCount);
        jobLog.setTriggerType(triggerType.name());
        flsJobLogMapper.insert(jobLog);
        log.debug(">>>>>>>>>>> fls-mapper trigger start, jobId:{}", jobLog.getId());

        // 解析请求头参数
        HashMap<String, List<String>> headersMap = new HashMap<>(16);
        if (StrUtil.isNotBlank(info.getRequestHeaders())){
            String[] headers = info.getRequestHeaders().split("&");
            if (headers.length > 0){
                for (String header : headers) {
                    String[] kv = header.split(":");
                    headersMap.put(kv[0], Collections.singletonList(kv[1]));
                }
            }
        }

        // 解析表单参数
        HashMap<String, Object> paramsMap = new HashMap<>(16);
        if (StrUtil.isNotBlank(info.getRequestParams())){
            String[] params = info.getRequestParams().split("&");
            if (params.length > 0){
                for (String param : params) {
                    String[] kv = param.split(":");
                    paramsMap.put(kv[0], kv[1]);
                }
            }
        }

        // HTTP调用
        HttpRequest httpRequest = null;
        switch (info.getRequestMethod()){
            case "GET":
                httpRequest = HttpRequest.get(info.getRequestUrl())
                    .header(headersMap)
                    .form(paramsMap);
                break;
            case "POST":
                httpRequest = HttpRequest.post(info.getRequestUrl())
                    .header(headersMap)
                    .form(paramsMap)
                    .body(info.getRequestBody());
                break;
            case "PUT":
                httpRequest = HttpRequest.put(info.getRequestUrl())
                    .header(headersMap)
                    .form(paramsMap)
                    .body(info.getRequestBody());
                break;
            case "DELETE":
                httpRequest = HttpRequest.delete(info.getRequestUrl())
                    .header(headersMap)
                    .form(paramsMap);
                break;
            default:
                break;
        }
        if (httpRequest != null){
            if (info.getExecutorTimeout() != null){
                httpRequest.timeout(info.getExecutorTimeout() * 1000);
            }
            try {
                log.info("调用任务请求===>{} {} {}", httpRequest.getUrl(),httpRequest.headers()==null?"":httpRequest.headers().toString(),httpRequest.form()==null?"":httpRequest.form().toString());
                HttpResponse response = httpRequest.execute();
                log.info("调用任务返回===>{}", response.body());
                jobLog.setHttpStatus(String.valueOf(response.getStatus()));
                if (!response.isOk()){
                    jobLog.setTriggerMsg("调度失败,请检查网络环境以及配置信息");
                }
                String resultStr = response.body();
                if (StrUtil.isNotBlank(resultStr)){
                    JSONObject jsonObject = JSON.parseObject(resultStr);
                    jobLog.setTriggerCode(jsonObject.containsKey("code") ? jsonObject.getString("code") : "");
                    String triggerMsg = "";
                    if (jsonObject.containsKey("msg")){
                        triggerMsg = jsonObject.getString("msg");
                    }else if (jsonObject.containsKey("message")){
                        triggerMsg = jsonObject.getString("message");
                    }
                    if ("200".equals(jobLog.getTriggerCode()) || "0".equals(jobLog.getTriggerCode())){
                        jobLog.setHandleStatus(HandleStatusEnum.SUCCESS.getCode());
                    }else {
                        jobLog.setHandleStatus(HandleStatusEnum.FAIL.getCode());
                    }
                    jobLog.setTriggerMsg(triggerMsg);
                    jobLog.setResponseBody(resultStr);
                }else {
                    if ("200".equals(jobLog.getHttpStatus())){
                        jobLog.setHandleStatus(HandleStatusEnum.SUCCESS.getCode());
                    }
                }
            } catch (Exception e) {
                jobLog.setHandleStatus(HandleStatusEnum.FAIL.getCode());
                jobLog.setTriggerMsg("调度失败,请检查网络环境以及配置信息");
            }
            flsJobLogMapper.updateById(jobLog);
        }
        if (jobLog.getHandleStatus().equals(HandleStatusEnum.SUCCESS.getCode())){
            if (info.getChildJobId() != null && info.getChildJobId().trim().length()>0){
                String[] childJobIds = info.getChildJobId().split(",");
                for (String childJobId : childJobIds) {
                    trigger(childJobId, TriggerTypeEnum.PARENT, -1);
                }
            }
        }
        log.debug(">>>>>>>>>>> fls-job trigger end, jobId:{}", jobLog.getId());
    }
}
