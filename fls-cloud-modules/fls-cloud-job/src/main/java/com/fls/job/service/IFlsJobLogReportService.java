package com.fls.job.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fls.job.entity.FlsJobLogReport;
import com.fls.job.entity.vo.FlsJobReportBaseVO;
import com.fls.job.entity.vo.FlsJobReportChartVO;

import java.util.Date;

public interface IFlsJobLogReportService extends IService<FlsJobLogReport> {
    /**
     * 获取基本报告信息
     * @return FlsJobReportBaseVO
     */
    FlsJobReportBaseVO getBaseInfo();

    /**
     * 获取图表信息
     * @param startDate 起始时间
     * @param endDate 结束时间
     * @return ResponseData<FlsJobReportChartVO>
     */
    FlsJobReportChartVO chart(String startDate, String endDate);
}
