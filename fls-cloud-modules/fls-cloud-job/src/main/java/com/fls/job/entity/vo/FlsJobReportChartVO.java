package com.fls.job.entity.vo;

import lombok.Data;

import java.util.List;

/**
 * 图表VO
 * <AUTHOR>
 * @date 2022/12/10/9:13
 */
@Data
public class FlsJobReportChartVO {
    /**
     * 调度日期集合
     */
    private List<String> triggerDayList;
    /**
     * 调度日期对应运行中集合
     */
    private List<Integer> triggerDayCountRunningList;
    /**
     * 调度日期对应成功集合
     */
    private List<Integer> triggerDayCountSuccessList;
    /**
     * 调度日期对应失败集合
     */
    private List<Integer> triggerDayCountFailList;
    /**
     * 所选范围内运行中数量
     */
    private Integer triggerCountRunningTotal;
    /**
     * 所选范围内成功数量
     */
    private Integer triggerCountSuccessTotal;
    /**
     * 所选范围内失败数量
     */
    private Integer triggerCountFailTotal;
}
