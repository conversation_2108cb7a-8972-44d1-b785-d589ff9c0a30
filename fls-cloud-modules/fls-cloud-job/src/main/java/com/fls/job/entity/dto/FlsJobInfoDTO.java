package com.fls.job.entity.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
public class FlsJobInfoDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private String id;

    /**
     * 任务分组
     */
    @NotBlank(message = "任务分组不能为空")
    private String jobGroup;

    /**
     * 任务描述
     */
    @NotBlank(message = "任务描述不能为空")
    private String jobDesc;

    /**
     * 调度类型
     */
    @NotBlank(message = "调度类型不能为空")
    private String scheduleType;

    /**
     * 调度配置，值含义取决于调度类型
     */
    @NotBlank(message = "调度配置不能为空")
    private String scheduleConf;

    /**
     * 调度过期策略
     */
    @NotBlank(message = "调度过期策略不能为空")
    private String misfireStrategy;

    /**
     * 任务执行超时时间，单位秒
     */
    private Integer executorTimeout;

    /**
     * 失败重试次数
     */
    private Integer executorFailRetryCount;

    /**
     * 子任务ID，多个逗号分隔
     */
    private String childJobId;

    /**
     * 调度状态：0-停止，1-运行
     */
    private Integer triggerStatus;

    /**
     * 请求方法
     */
    @NotBlank(message = "请求方法不能为空")
    private String requestMethod;

    /**
     * 请求地址
     */
    @NotBlank(message = "请求地址不能为空")
    private String requestUrl;

    /**
     * 请求头参数
     */
    private String requestHeaders;

    /**
     * 请求form表单
     */
    private String requestParams;

    /**
     * 请求体
     */
    private String requestBody;
}
