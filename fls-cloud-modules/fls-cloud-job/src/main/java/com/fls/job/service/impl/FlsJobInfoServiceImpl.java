package com.fls.job.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fls.common.core.constant.CommonConstants;
import com.fls.common.satoken.utils.LoginHelper;
import com.fls.job.constant.ScheduleTriggerStatusEnum;
import com.fls.job.constant.ScheduleTypeEnum;
import com.fls.job.entity.FlsJobInfo;
import com.fls.job.entity.dto.FlsJobInfoDTO;
import com.fls.job.entity.query.FlsJobInfoQuery;
import com.fls.job.entity.vo.FlsJobInfoVO;
import com.fls.job.exception.FlsJobException;
import com.fls.job.exception.FlsJobExceptionEnum;
import com.fls.job.mapper.FlsJobInfoMapper;
import com.fls.job.mapper.FlsJobLogMapper;
import com.fls.job.service.IFlsJobInfoService;
import com.fls.job.thread.JobScheduleHelper;
import com.fls.upms.api.model.LoginUser;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.support.CronExpression;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Optional;

import static cn.hutool.core.text.CharSequenceUtil.isNumeric;

@Service
@RequiredArgsConstructor
public class FlsJobInfoServiceImpl extends ServiceImpl<FlsJobInfoMapper, FlsJobInfo> implements IFlsJobInfoService {

    private final FlsJobLogMapper flsJobLogMapper;

    @Override
    public Page<FlsJobInfo> pageList(Page page, FlsJobInfoQuery query) {
        return this.page(page, Wrappers.<FlsJobInfo>lambdaQuery()
            .eq(query.getTriggerStatus() != null, FlsJobInfo::getTriggerStatus, query.getTriggerStatus())
            .like(StrUtil.isNotBlank(query.getJobGroup()), FlsJobInfo::getJobGroup, query.getJobGroup())
            .like(StrUtil.isNotBlank(query.getJobDesc()), FlsJobInfo::getJobDesc, query.getJobDesc())
            .like(StrUtil.isNotBlank(query.getCreateBy()), FlsJobInfo::getCreateByName, query.getCreateBy()));
    }

    @Override
    public Boolean add(FlsJobInfoDTO info) {
        commonVerify(info);
        //整理参数
        FlsJobInfo flsJobInfo = new FlsJobInfo();
        BeanUtils.copyProperties(info, flsJobInfo);
        flsJobInfo.setId(IdUtil.randomUUID());
        // 接入人员
        LoginUser loginUser = (LoginUser) (StpUtil.getTokenSession().get(LoginHelper.LOGIN_USER_KEY));
        flsJobInfo.setCreateBy(loginUser != null ? loginUser.getIdUser() : "");
        flsJobInfo.setCreateByName(loginUser != null ? loginUser.getName() : "");
        return this.save(flsJobInfo);
    }

    @Override
    public Boolean edit(FlsJobInfoDTO info) {
        commonVerify(info);
        // 接入人员
        LoginUser loginUser = (LoginUser) (StpUtil.getTokenSession().get(LoginHelper.LOGIN_USER_KEY));
        FlsJobInfo existInfo = Optional.ofNullable(this.getById(info.getId())).orElseThrow(() -> new FlsJobException(FlsJobExceptionEnum.JOB_NOT_EXISTS.getCode(), FlsJobExceptionEnum.JOB_NOT_EXISTS.getMsg()));
        // next trigger time (5s后生效，避开预读周期)
        Long nextTriggerTime = existInfo.getTriggerNextTime();
        FlsJobInfo handleJob = new FlsJobInfo();
        handleJob.setScheduleType(info.getScheduleType());
        handleJob.setScheduleConf(info.getScheduleConf());
        boolean scheduleDataNotChanged = info.getScheduleType().equals(existInfo.getScheduleType()) && info.getScheduleConf().equals(existInfo.getScheduleConf());
        if (existInfo.getTriggerStatus() == ScheduleTriggerStatusEnum.ENABLE.getCode() && !scheduleDataNotChanged){
            try {
                Date nextValidTime = JobScheduleHelper.generateNextValidTime(handleJob, new Date(System.currentTimeMillis() + JobScheduleHelper.PRE_READ_MS));
                if (nextValidTime == null){
                    throw new FlsJobException(FlsJobExceptionEnum.NEXT_VALID_TIME_ERROR.getCode(), FlsJobExceptionEnum.NEXT_VALID_TIME_ERROR.getMsg());
                }
                nextTriggerTime = nextValidTime.getTime();
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                throw new FlsJobException(FlsJobExceptionEnum.NEXT_VALID_TIME_ERROR.getCode(), FlsJobExceptionEnum.NEXT_VALID_TIME_ERROR.getMsg());
            }
        }
        handleJob.setId(existInfo.getId());
        handleJob.setJobGroup(info.getJobGroup());
        handleJob.setJobDesc(info.getJobDesc());
        handleJob.setMisfireStrategy(info.getMisfireStrategy());
        handleJob.setExecutorTimeout(info.getExecutorTimeout());
        handleJob.setExecutorFailRetryCount(info.getExecutorFailRetryCount());
        handleJob.setChildJobId(info.getChildJobId());
        handleJob.setTriggerNextTime(nextTriggerTime);
        handleJob.setRequestMethod(info.getRequestMethod());
        handleJob.setRequestUrl(info.getRequestUrl());
        handleJob.setRequestHeaders(info.getRequestHeaders());
        handleJob.setRequestParams(info.getRequestParams());
        handleJob.setRequestBody(info.getRequestBody());
        handleJob.setUpdateBy(loginUser != null ? loginUser.getIdUser() : "");
        handleJob.setUpdateByName(loginUser != null ? loginUser.getName() : "");
        return this.updateById(handleJob);
    }

    @Override
    public Boolean delete(String id) {
        // 接入人员
        LoginUser loginUser = (LoginUser) (StpUtil.getTokenSession().get(LoginHelper.LOGIN_USER_KEY));
        FlsJobInfo info = this.getById(id);
        boolean deleteInfo = this.update(Wrappers.<FlsJobInfo>lambdaUpdate()
                .set(FlsJobInfo::getUpdateBy, loginUser != null ? loginUser.getIdUser() : "")
                .set(FlsJobInfo::getUpdateByName, loginUser != null ? loginUser.getName() : "")
                .set(FlsJobInfo::getDelFlag, CommonConstants.DELETE_FLAG_IS_DELETED)
                .eq(FlsJobInfo::getId, id));
        return info == null || (deleteInfo && flsJobLogMapper.deleteById(id) > 0);
    }

    @Override
    public Boolean stop(String id) {
        FlsJobInfo info = this.getById(id);
        info.setTriggerStatus(ScheduleTriggerStatusEnum.DISABLE.getCode());
        info.setTriggerLastTime(0L);
        info.setTriggerNextTime(0L);
        return this.updateById(info);
    }

    @Override
    public Boolean start(String id) {
        FlsJobInfo info = this.getById(id);

        //NONE类型不适用
        ScheduleTypeEnum scheduleTypeEnum = ScheduleTypeEnum.match(info.getScheduleType(), ScheduleTypeEnum.NONE);
        if (ScheduleTypeEnum.NONE == scheduleTypeEnum) {
            throw new FlsJobException(FlsJobExceptionEnum.NONE_SCHEDULE_TYPE_NOT_VALID.getCode(), FlsJobExceptionEnum.NONE_SCHEDULE_TYPE_NOT_VALID.getMsg());
        }

        long nextTriggerTime = 0;
        try {
            Date nextValidTime = JobScheduleHelper.generateNextValidTime(info, new Date(System.currentTimeMillis() + JobScheduleHelper.PRE_READ_MS));
            if (nextValidTime == null){
                throw new FlsJobException(FlsJobExceptionEnum.NEXT_VALID_TIME_ERROR.getCode(), FlsJobExceptionEnum.NEXT_VALID_TIME_ERROR.getMsg());
            }
            nextTriggerTime = nextValidTime.getTime();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new FlsJobException(FlsJobExceptionEnum.NEXT_VALID_TIME_ERROR.getCode(), FlsJobExceptionEnum.NEXT_VALID_TIME_ERROR.getMsg());
        }
        //启用
        info.setTriggerStatus(ScheduleTriggerStatusEnum.ENABLE.getCode());
        info.setTriggerLastTime(0L);
        info.setTriggerNextTime(nextTriggerTime);

        return this.updateById(info);
    }

    @Override
    public List<FlsJobInfoVO> getByQuery(FlsJobInfoQuery query) {
        return this.baseMapper.getByQuery(query);
    }

    public void commonVerify(FlsJobInfoDTO info){
        //获取调度类型
        ScheduleTypeEnum scheduleTypeEnum = Optional.ofNullable(ScheduleTypeEnum.match(info.getScheduleType(), null)).orElseThrow(() -> new FlsJobException(FlsJobExceptionEnum.SCHEDULE_TYPE_NOT_MATCH.getCode(), FlsJobExceptionEnum.SCHEDULE_TYPE_NOT_MATCH.getMsg()));

        //判断cron和速率模式的调度配置是否合法
        Optional.ofNullable(info.getScheduleConf()).filter(p -> (scheduleTypeEnum == ScheduleTypeEnum.CRON && CronExpression.isValidExpression(p))
            || (scheduleTypeEnum == ScheduleTypeEnum.FIX_RATE && Integer.parseInt(p) >= 1)).orElseThrow(() -> new FlsJobException(FlsJobExceptionEnum.SCHEDULE_CONF_IS_ILLEGAL.getCode(), FlsJobExceptionEnum.SCHEDULE_CONF_IS_ILLEGAL.getMsg()));

        //判断子任务是否存在
        Optional.ofNullable(info.getChildJobId()).filter(p -> p.trim().length() > 0).ifPresent(
            p -> {
                String[] childJobIds = p.split(",");
                for (String childJobIdItem : childJobIds) {
                    Optional.ofNullable(childJobIdItem)
                        .filter(item -> item.trim().length() > 0 && isNumeric(item) && this.getById(Integer.parseInt(item)) != null)
                        .orElseThrow(() -> new FlsJobException(FlsJobExceptionEnum.CHILD_JOB_NOT_EXISTS.getCode(), FlsJobExceptionEnum.CHILD_JOB_NOT_EXISTS.getMsg()));
                }
            }
        );
    }
}
