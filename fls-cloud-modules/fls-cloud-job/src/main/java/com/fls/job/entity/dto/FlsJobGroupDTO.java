package com.fls.job.entity.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/12/03/14:40
 */
@Data
public class FlsJobGroupDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 任务分组id;
     */
    private String id;
    /**
     * 应用code
     */
    @NotBlank(message = "应用code不能为空")
    private String appCode;
    /**
     * 标题
     */
    @NotBlank(message = "标题不能为空")
    private String title;
}
