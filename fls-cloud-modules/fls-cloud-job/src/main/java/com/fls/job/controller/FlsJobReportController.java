package com.fls.job.controller;

import com.fls.common.core.domain.ResponseData;
import com.fls.job.entity.vo.FlsJobReportBaseVO;
import com.fls.job.entity.vo.FlsJobReportChartVO;
import com.fls.job.service.IFlsJobLogReportService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

/**
 * 任务报表
 * <AUTHOR>
 * @date 2022/12/09/17:10
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/jobreport")
public class FlsJobReportController {

    private final IFlsJobLogReportService flsJobLogReportService;

    /**
     * 获取基本信息
     * @return ResponseData<FlsJobReportBaseVO>
     */
    @GetMapping("/base")
    public ResponseData<FlsJobReportBaseVO> getBaseInfo(){
        return ResponseData.ok(flsJobLogReportService.getBaseInfo());
    }

    /**
     * 获取图表信息
     * @param startDate 起始时间
     * @param endDate 结束时间
     * @return ResponseData<FlsJobReportChartVO>
     */
    @GetMapping("/chart")
    public ResponseData<FlsJobReportChartVO> chartInfo(String startDate, String endDate){
        return ResponseData.ok(flsJobLogReportService.chart(startDate, endDate));
    }
}
