package com.fls.job.thread;

import com.fls.job.constant.TriggerTypeEnum;
import com.fls.job.core.trigger.FlsJobTrigger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @date 2022/11/28/18:04
 */
@Slf4j
@Component
public class JobTriggerPoolHelper {
    @Autowired
    private FlsJobTrigger flsJobTrigger;

    @Resource
    private ThreadPoolExecutor fastTriggerPool;

    @Resource
    private ThreadPoolExecutor slowTriggerPool;

    private volatile long minTim = System.currentTimeMillis()/60000;
    private volatile ConcurrentMap<String, AtomicInteger> jobTimeoutCountMap = new ConcurrentHashMap<>();

    /**
     * 执行调度
     * @param jobId 任务id
     * @param triggerType 调度类型
     * @param failRetryCount 失败重试次数
     */
    public void trigger(String jobId, TriggerTypeEnum triggerType, int failRetryCount){
        //选择线程池
        ThreadPoolExecutor triggerPool = fastTriggerPool;
        AtomicInteger jobTimeoutCount = jobTimeoutCountMap.get(jobId);
        //在1分钟内失败10次
        if (jobTimeoutCount != null && jobTimeoutCount.get() > 10){
            triggerPool = slowTriggerPool;
        }
        triggerPool.execute(() -> {
            long start = System.currentTimeMillis();
            try {
                flsJobTrigger.trigger(jobId, triggerType, failRetryCount);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            } finally {
                long minTimNow = System.currentTimeMillis()/60000;
                if (minTim != minTimNow) {
                    minTim = minTimNow;
                    jobTimeoutCountMap.clear();
                }
                long cost = System.currentTimeMillis()-start;
                if (cost > 500) {       // ob-timeout threshold 500ms
                    AtomicInteger timeoutCount = jobTimeoutCountMap.putIfAbsent(jobId, new AtomicInteger(1));
                    if (timeoutCount != null) {
                        timeoutCount.incrementAndGet();
                    }
                }
            }
        });
    }

    @PreDestroy
    public void stop() {
        //triggerPool.shutdown();
        fastTriggerPool.shutdownNow();
        slowTriggerPool.shutdownNow();
        log.info(">>>>>>>>> fls-job trigger thread pool shutdown success.");
    }
}
