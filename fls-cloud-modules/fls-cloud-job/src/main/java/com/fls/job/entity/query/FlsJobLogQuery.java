package com.fls.job.entity.query;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/11/30/16:21
 */
@Data
public class FlsJobLogQuery implements Serializable{
    /**
     * 任务主键id
     */
    private String jobId;

    /**
     * 任务分组
     */
    private String jobGroup;

    /**
     * 任务过程状态
     */
    private Integer handleStatus;

    /**
     * 时间范围
     */
    private String filterTime;

    /**
     * 起始范围
     */
    private Date triggerTimeStart;

    /**
     * 终止范围
     */
    private Date triggerTimeEnd;
}
