package com.fls.job.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fls.common.core.constant.CommonConstants;
import com.fls.common.satoken.utils.LoginHelper;
import com.fls.job.entity.FlsJobGroup;
import com.fls.job.entity.dto.FlsJobGroupDTO;
import com.fls.job.entity.query.FlsJobGroupQuery;
import com.fls.job.exception.FlsJobException;
import com.fls.job.mapper.FlsJobGroupMapper;
import com.fls.job.service.IFlsJobGroupService;
import com.fls.upms.api.model.LoginUser;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.validation.Valid;
import java.util.Optional;

/**
 * 任务分组实现类
 * <AUTHOR>
 * @date 2022/12/03/14:24
 */
@Service
@RequiredArgsConstructor
public class FlsJobGroupServiceImpl extends ServiceImpl<FlsJobGroupMapper, FlsJobGroup> implements IFlsJobGroupService {
    @Override
    public Page<FlsJobGroup> pageList(Page page, FlsJobGroupQuery query) {
        return this.page(page, Wrappers.<FlsJobGroup>lambdaQuery()
            .like(StrUtil.isNotBlank(query.getAppCode()), FlsJobGroup::getAppCode, query.getAppCode())
            .like(StrUtil.isNotBlank(query.getTitle()), FlsJobGroup::getTitle, query.getTitle())
        );
    }

    @Override
    public Boolean add(FlsJobGroupDTO group) {
        FlsJobGroup flsJobGroup = new FlsJobGroup();
        // 整理参数
        BeanUtils.copyProperties(group, flsJobGroup);
        // 接入人员
        LoginUser loginUser = (LoginUser) (StpUtil.getTokenSession().get(LoginHelper.LOGIN_USER_KEY));
        flsJobGroup.setCreateBy(loginUser != null ? loginUser.getIdUser() : "");
        flsJobGroup.setCreateByName(loginUser != null ? loginUser.getName() : "");
        flsJobGroup.setId(IdUtil.randomUUID());
        return this.save(flsJobGroup);
    }

    @Override
    public Boolean edit(@Valid FlsJobGroupDTO group) {
        FlsJobGroup jobGroup = Optional.ofNullable(this.getById(group.getId())).orElseThrow(() -> new FlsJobException(1,"失败"));
        // 整理参数
        jobGroup.setAppCode(group.getAppCode());
        jobGroup.setTitle(group.getTitle());
        // 接入人员
        LoginUser loginUser = (LoginUser) (StpUtil.getTokenSession().get(LoginHelper.LOGIN_USER_KEY));
        jobGroup.setUpdateBy(loginUser != null ? loginUser.getIdUser() : "");
        jobGroup.setUpdateByName(loginUser != null ? loginUser.getName() : "");
        return this.updateById(jobGroup);
    }

    @Override
    public Boolean delete(String id) {
        // 接入人员
        LoginUser loginUser = (LoginUser) (StpUtil.getTokenSession().get(LoginHelper.LOGIN_USER_KEY));
        return this.update(Wrappers.<FlsJobGroup>lambdaUpdate()
                .set(FlsJobGroup::getUpdateBy, loginUser != null ? loginUser.getIdUser() : "")
                .set(FlsJobGroup::getUpdateByName, loginUser != null ? loginUser.getName() : "")
                .set(FlsJobGroup::getDelFlag, CommonConstants.DELETE_FLAG_IS_DELETED)
                .eq(FlsJobGroup::getId, id));
    }
}
