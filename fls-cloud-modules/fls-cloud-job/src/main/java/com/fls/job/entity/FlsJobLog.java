package com.fls.job.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@TableName("t_job_log")
public class FlsJobLog implements Serializable {
    @TableId(type = IdType.INPUT)
    private String id;

    /**
     * 任务主键id
     */
    private String jobId;

    /**
     * 任务分组
     */
    private String jobGroup;

    /**
     * 失败重试次数
     */
    private Integer executorFailRetryCount;

    /**
     * 调度-时间
     */
    private LocalDateTime triggerTime;

    /**
     * 调度类型
     */
    private String triggerType;

    /**
     * 处理状态
     */
    private Integer handleStatus;

    /**
     * HTTP状态码
     */
    private String httpStatus;

    /**
     * 调度-结果
     */
    private String triggerCode;

    /**
     * 调度-日志
     */
    private String triggerMsg;

    /**
     * 响应体
     */
    private String responseBody;

    /**
     * 告警状态：0-默认、1-无需告警、2-告警成功、3-告警失败
     */
    private Integer alarmStatus;
}
