package com.fls.job.constant;

/**
 * 处理状态
 * <AUTHOR>
 * @date 2022/12/08/15:11
 */
public enum  HandleStatusEnum {
    /**
     * 任务过程状态
     */
    PROCESSING(0, "处理中"),

    SUCCESS(1, "成功"),


    FAIL(2, "失败");

    private int code;
    private String title;

    HandleStatusEnum(int code, String title) {
        this.code = code;
        this.title = title;
    }

    public String getTitle() {
        return title;
    }

    public int getCode() {
        return code;
    }
}
