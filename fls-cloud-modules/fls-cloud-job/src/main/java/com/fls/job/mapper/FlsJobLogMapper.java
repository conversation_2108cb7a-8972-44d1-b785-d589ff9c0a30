package com.fls.job.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fls.job.entity.FlsJobLog;
import com.fls.job.entity.FlsJobLogReport;
import com.fls.job.entity.query.FlsJobLogQuery;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface FlsJobLogMapper extends BaseMapper<FlsJobLog> {
    /**
     * 查询条件分页
     * @param page 分页参数
     * @param query 查询条件
     * @return Page<FlsJobLog>
     */
    Page<FlsJobLog> pageByQuery(Page page, @Param("query") FlsJobLogQuery query);

    /**
     *
     * @param jobGroup
     * @param jobId
     * @param clearBeforeTime
     * @param clearBeforeNum
     * @param pageSize
     * @return
     */
    List<String> findClearLogIds(@Param("jobGroup") String jobGroup,
                               @Param("jobId") String jobId,
                               @Param("clearBeforeTime") Date clearBeforeTime,
                               @Param("clearBeforeNum") int clearBeforeNum,
                               @Param("pageSize") int pageSize);

    /**
     * 清理日志
     * @param logIds 日志id集合
     * @return int
     */
    int clearLog(@Param("logIds") List<String> logIds);

    /**
     * 根据指定时间范围获取报告信息
     * @param todayFrom 起始范围
     * @param todayTo 终止范围
     * @return FlsJobLogReport
     */
    FlsJobLogReport findLogReport(@Param("from")Date todayFrom, @Param("to")Date todayTo);

    /**
     * 获取失败的日志ID
     * @param size 获取数量
     * @return List<String>
     */
    List<String> findFailJobLogIds(@Param("size") int size);

    /**
     * 更改告警状态
     * @param failLogId 日志id
     * @param oldAlarmStatus 旧告警状态
     * @param newAlarmStatus 新告警状态
     * @return int
     */
    int updateAlarmStatus(@Param("failLogId")String failLogId, @Param("oldAlarmStatus")int oldAlarmStatus, @Param("newAlarmStatus")int newAlarmStatus);
}
