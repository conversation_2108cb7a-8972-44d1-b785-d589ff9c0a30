package com.fls.job.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fls.job.entity.FlsJobInfo;
import com.fls.job.entity.query.FlsJobInfoQuery;
import com.fls.job.entity.vo.FlsJobInfoVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface FlsJobInfoMapper extends BaseMapper<FlsJobInfo> {
    /**
     * 查询指定时间前的任务
     * @param maxNextTime 指定时间
     * @param pagesize 获取数量
     * @return List<FlsJobInfo>
     */
    List<FlsJobInfo> scheduleJobQuery(@Param("maxNextTime") long maxNextTime, @Param("pagesize") int pagesize);

    /**
     * 修改任务信息
     * @param jobInfo 任务信息
     * @return int
     */
    int scheduleUpdate(@Param("info")FlsJobInfo jobInfo);

    /**
     * 根据条件获取
     * @param query 条件参数
     * @return List<FlsJobInfoVO>
     */
    List<FlsJobInfoVO> getByQuery(@Param("query")FlsJobInfoQuery query);
}
