package com.fls.job.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fls.common.core.domain.ResponseData;
import com.fls.job.entity.FlsJobLog;
import com.fls.job.entity.query.FlsJobLogQuery;
import com.fls.job.service.IFlsJobLogService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.TimeZone;

/**
 * <AUTHOR>
 * @date 2022/11/30/16:12
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/joblog")
public class FlsJobLogController {
    private final IFlsJobLogService flsJobLogService;

    @GetMapping("/page")
    public ResponseData<Page<FlsJobLog>> pageList(Page page, FlsJobLogQuery query) throws ParseException {
        // 解析时间参数
        if (StrUtil.isNotBlank(query.getFilterTime())){
            String[] temp = query.getFilterTime().split(",");
            SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
            SimpleDateFormat normalDf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            df.setTimeZone(TimeZone.getTimeZone("UTC"));
            if (temp.length == 2) {
                Date triggerTimeStart = normalDf.parse(normalDf.format(df.parse(temp[0].replaceAll("\"", ""))));
                Date triggerTimeEnd = normalDf.parse(normalDf.format(df.parse(temp[1].replaceAll("\"", ""))));
                query.setTriggerTimeStart(triggerTimeStart);
                query.setTriggerTimeEnd(triggerTimeEnd);
            }
        }

        // 查询日志
        return ResponseData.ok(flsJobLogService.pageList(page, query));
    }

    @DeleteMapping("/clearLog")
    public ResponseData clearLog(String jobGroup, String jobId, int type){
        Date clearBeforeTime = null;
        int clearBeforeNum = 0;
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        if (type == 1) {
            // 清理一个月之前日志数据
            calendar.add(Calendar.MONTH, -1);
        } else if (type == 2) {
            // 清理三个月之前日志数据
            calendar.add(Calendar.MONTH, -3);
        } else if (type == 3) {
            // 清理六个月之前日志数据
            calendar.add(Calendar.MONTH, -6);
        } else if (type == 4) {
            // 清理一年之前日志数据
            calendar.add(Calendar.YEAR, -1);
        } else if (type == 5) {
            // 清理一千条以前日志数据
            clearBeforeNum = 1000;
        } else if (type == 6) {
            // 清理一万条以前日志数据
            clearBeforeNum = 10000;
        } else if (type == 7) {
            // 清理三万条以前日志数据
            clearBeforeNum = 30000;
        } else if (type == 8) {
            // 清理十万条以前日志数据
            clearBeforeNum = 100000;
        } else if (type == 9) {
            // 清理所有日志数据
            clearBeforeNum = 0;
        } else {
            return ResponseData.fail();
        }
        clearBeforeTime = calendar.getTime();
        List<String> logIds;
        do {
            logIds = flsJobLogService.findClearLogIds(jobGroup, jobId, clearBeforeTime, clearBeforeNum, 1000);
            if (logIds!=null && logIds.size()>0) {
                flsJobLogService.clearLog(logIds);
            }
        } while (logIds!=null && logIds.size()>0);

        return ResponseData.ok();
    }
}
