package com.fls.job.thread;

import com.fls.job.config.FlsJobConfig;
import com.fls.job.constant.MisfireStrategyEnum;
import com.fls.job.constant.ScheduleTriggerStatusEnum;
import com.fls.job.constant.ScheduleTypeEnum;
import com.fls.job.constant.TriggerTypeEnum;
import com.fls.job.core.CronExpression;
import com.fls.job.entity.FlsJobInfo;
import com.fls.job.mapper.FlsJobInfoMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2022/11/28/17:00
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class JobScheduleHelper {

    private final FlsJobInfoMapper flsJobInfoMapper;
    private final FlsJobConfig flsJobConfig;
    private final DataSource dataSource;
    private final JobTriggerPoolHelper jobTriggerPoolHelper;

    private Thread scheduleThread;
    private Thread ringThread;
    private volatile boolean scheduleThreadToStop = false;
    private volatile boolean ringThreadToStop = false;
    private volatile static Map<Integer, List<String>> ringData = new ConcurrentHashMap<>();

    /**
     * 预读时间
     */
    public static final long PRE_READ_MS = 5000;

    @PostConstruct
    public void start() {
        // 计划任务线程
        scheduleThread = new Thread(() -> {
            try {
                TimeUnit.MILLISECONDS.sleep(5000 - System.currentTimeMillis() % 1000);
            } catch (InterruptedException e) {
                if (!scheduleThreadToStop) {
                    log.error(e.getMessage(), e);
                }
            }
            log.info(">>>>>>>>> init fls-job scheduler success.");
            // 如果不解耦的话,http调用会阻塞，qps不能衡量
            int preReadCount = (flsJobConfig.getTriggerPoolFastMax() + flsJobConfig.getTriggerPoolSlowMax()) * 20;

            while (!scheduleThreadToStop) {
                // 扫描任务信息
                long start = System.currentTimeMillis();

                Connection conn = null;
                Boolean connAutoCommit = null;
                PreparedStatement preparedStatement = null;

                boolean preReadSuc = true;
                try {
                    conn = dataSource.getConnection();
                    connAutoCommit = conn.getAutoCommit();
                    conn.setAutoCommit(false);

                    // 数据库锁
                    preparedStatement = conn.prepareStatement("select * from t_job_lock where lock_name = 'schedule_lock' for update");
                    preparedStatement.execute();

                    // 加上预读时间后读取数据
                    long nowTime = System.currentTimeMillis();
                    List<FlsJobInfo> scheduleList = flsJobInfoMapper.scheduleJobQuery(nowTime + PRE_READ_MS, preReadCount);
                    if (scheduleList != null && scheduleList.size() > 0) {
                        for (FlsJobInfo jobInfo : scheduleList) {
                            // 跳过了时间轮
                            if (nowTime > jobInfo.getTriggerNextTime() + PRE_READ_MS) {
                                // 调度时间-已知将要调度时间 > 5
                                log.warn(">>>>>>>>>>> fls-job, schedule misfire, jobId = " + jobInfo.getId());

                                // 调度过期策略
                                MisfireStrategyEnum misfireStrategyEnum = MisfireStrategyEnum.match(jobInfo.getMisfireStrategy(), MisfireStrategyEnum.DO_NOTHING);
                                if (MisfireStrategyEnum.FIRE_ONCE_NOW == misfireStrategyEnum) {
                                    // FIRE_ONCE_NOW 》 trigger
                                    jobTriggerPoolHelper.trigger(jobInfo.getId(), TriggerTypeEnum.MISFIRE, -1);
                                    log.debug(">>>>>>>>>>> fls-job, schedule push trigger : jobId = " + jobInfo.getId());
                                }
                                // 刷新下次调度时间
                                refreshNextValidTime(jobInfo, new Date());
                            } else if (nowTime > jobInfo.getTriggerNextTime()) {
                                // 调度时间-已知将要调度时间 < 5
                                jobTriggerPoolHelper.trigger(jobInfo.getId(), TriggerTypeEnum.CRON, -1);
                                log.debug(">>>>>>>>>>> fls-job, schedule push trigger : jobId = " + jobInfo.getId());
                                refreshNextValidTime(jobInfo, new Date());
                                // 已知将要调度时间在5秒区间内
                                if (jobInfo.getTriggerStatus() == ScheduleTriggerStatusEnum.ENABLE.getCode() && nowTime + PRE_READ_MS > jobInfo.getTriggerNextTime()) {
                                    // 1、时间轮秒数
                                    int ringSecond = (int) ((jobInfo.getTriggerNextTime() / 1000) % 60);

                                    // 2、放入时间轮
                                    pushTimeRing(ringSecond, jobInfo.getId());

                                    // 3、fresh next
                                    refreshNextValidTime(jobInfo, new Date(jobInfo.getTriggerNextTime()));
                                }
                            } else {
                                // 已知将要调度时间大于调度时间
                                // 1、make ring second
                                int ringSecond = (int) ((jobInfo.getTriggerNextTime() / 1000) % 60);

                                // 2、push time ring
                                pushTimeRing(ringSecond, jobInfo.getId());

                                // 3、fresh next
                                refreshNextValidTime(jobInfo, new Date(jobInfo.getTriggerNextTime()));
                            }
                        }
                        // 3、update trigger info
                        for (FlsJobInfo jobInfo : scheduleList) {
                            flsJobInfoMapper.scheduleUpdate(jobInfo);
                        }
                    } else {
                        preReadSuc = false;
                    }
                } catch (Exception e) {
                    if (!scheduleThreadToStop) {
                        log.error(">>>>>>>>>>> fls-job, JobScheduleHelper#scheduleThread error:{}", e);
                    }
                } finally {
                    if (conn != null) {
                        try {
                            conn.commit();
                        } catch (SQLException e) {
                            if (!scheduleThreadToStop) {
                                log.error(e.getMessage(), e);
                            }
                        }
                        try {
                            conn.setAutoCommit(connAutoCommit);
                        } catch (SQLException e) {
                            if (!scheduleThreadToStop) {
                                log.error(e.getMessage(), e);
                            }
                        }
                        try {
                            conn.close();
                        } catch (SQLException e) {
                            if (!scheduleThreadToStop) {
                                log.error(e.getMessage(), e);
                            }
                        }
                    }
                    if (null != preparedStatement) {
                        try {
                            preparedStatement.close();
                        } catch (SQLException e) {
                            if (!scheduleThreadToStop) {
                                log.error(e.getMessage(), e);
                            }
                        }
                    }
                }
                long cost = System.currentTimeMillis() - start;

                if (cost < 1000) {
                    try {
                        TimeUnit.MILLISECONDS.sleep((preReadSuc ? 1000 : PRE_READ_MS) - System.currentTimeMillis() % 1000);
                    } catch (InterruptedException e) {
                        if (!scheduleThreadToStop) {
                            log.error(e.getMessage(), e);
                        }
                    }
                }
            }
            log.info(">>>>>>>>>>> fls-job, JobScheduleHelper#scheduleThread stop");
        });
        scheduleThread.setDaemon(true);
        scheduleThread.setName("fls-job,JobScheduleHelper#scheduleThread");
        scheduleThread.start();

        // 时间轮线程
        ringThread = new Thread(()->{
            while (!ringThreadToStop){
                try {
                    TimeUnit.MILLISECONDS.sleep(1000 - System.currentTimeMillis() % 1000);
                } catch (InterruptedException e) {
                    if (!ringThreadToStop) {
                        log.error(e.getMessage(), e);
                    }
                }
                // 按秒分配
                try {
                    List<String> ringItemData = new ArrayList<>();
                    int nowSecond = Calendar.getInstance().get(Calendar.SECOND);   // 避免处理耗时太长，跨过刻度，向前校验一个刻度；
                    for (int i = 0; i < 2; i++) {
                        List<String> tmpData = ringData.remove( (nowSecond+60-i)%60 );
                        if (tmpData != null) {
                            ringItemData.addAll(tmpData);
                        }
                    }
                    // 时间轮调度
                    log.debug(">>>>>>>>>>> fls-job, time-ring beat : " + nowSecond + " = " + Collections.singletonList(ringItemData));
                    if (ringItemData.size() > 0){
                        for (String jobId : ringItemData) {
                            jobTriggerPoolHelper.trigger(jobId, TriggerTypeEnum.CRON, -1);
                        }
                        // 清理任务
                        ringItemData.clear();
                    }
                } catch (Exception e) {
                    if (!ringThreadToStop){
                        log.error(">>>>>>>>>>> fls-job, JobScheduleHelper#ringThread error:{}", e);
                    }
                }
            }
            log.info(">>>>>>>>>>> fls-job, JobScheduleHelper#ringThread stop");
        });
        ringThread.setDaemon(true);
        ringThread.setName("fls-job, JobScheduleHelper#ringThread");
        ringThread.start();
    }

    /**
     * 放入时间轮
     * @param ringSecond 时间位
     * @param jobId 时间位要放入的任务id
     */
    private void pushTimeRing(int ringSecond, String jobId){
        // push async ring
        List<String> ringItemData = ringData.computeIfAbsent(ringSecond, k -> new ArrayList<>());
        ringItemData.add(jobId);

        log.debug(">>>>>>>>>>> fls-job, schedule push time-ring : " + ringSecond + " = " + Collections.singletonList(ringItemData));
    }

    /**
     * 刷新给定时间的下一次满足条件时间
     * @param jobInfo 任务信息
     * @param fromTime 给定时间
     * @throws Exception
     */
    private void refreshNextValidTime(FlsJobInfo jobInfo, Date fromTime) throws Exception {
        Date nextValidTime = generateNextValidTime(jobInfo, fromTime);
        if (nextValidTime != null) {
            jobInfo.setTriggerLastTime(jobInfo.getTriggerNextTime());
            jobInfo.setTriggerNextTime(nextValidTime.getTime());
        } else {
            jobInfo.setTriggerStatus(0);
            jobInfo.setTriggerLastTime(0L);
            jobInfo.setTriggerNextTime(0L);
            log.warn(">>>>>>>>>>> fls-job, refreshNextValidTime fail for job: jobId={}, scheduleType={}, scheduleConf={}",
                jobInfo.getId(), jobInfo.getScheduleType(), jobInfo.getScheduleConf());
        }
    }

    public static Date generateNextValidTime(FlsJobInfo jobInfo, Date fromTime) throws Exception {
        ScheduleTypeEnum scheduleTypeEnum = ScheduleTypeEnum.match(jobInfo.getScheduleType(), null);
        if (ScheduleTypeEnum.CRON == scheduleTypeEnum) {
            Date nextValidTime = new CronExpression(jobInfo.getScheduleConf()).getNextValidTimeAfter(fromTime);
            return nextValidTime;
        } else if (ScheduleTypeEnum.FIX_RATE == scheduleTypeEnum /*|| ScheduleTypeEnum.FIX_DELAY == scheduleTypeEnum*/) {
            return new Date(fromTime.getTime() + Integer.valueOf(jobInfo.getScheduleConf())*1000 );
        }
        return null;
    }

    @PreDestroy
    public void toStop(){

        // 1、stop schedule
        scheduleThreadToStop = true;
        try {
            TimeUnit.SECONDS.sleep(1);  // wait
        } catch (InterruptedException e) {
            log.error(e.getMessage(), e);
        }
        if (scheduleThread.getState() != Thread.State.TERMINATED){
            // interrupt and wait
            scheduleThread.interrupt();
            try {
                scheduleThread.join();
            } catch (InterruptedException e) {
                log.error(e.getMessage(), e);
            }
        }

        // if has ring data
        boolean hasRingData = false;
        if (!ringData.isEmpty()) {
            for (int second : ringData.keySet()) {
                List<String> tmpData = ringData.get(second);
                if (tmpData!=null && tmpData.size()>0) {
                    hasRingData = true;
                    break;
                }
            }
        }
        if (hasRingData) {
            try {
                TimeUnit.SECONDS.sleep(8);
            } catch (InterruptedException e) {
                log.error(e.getMessage(), e);
            }
        }

        // stop ring (wait job-in-memory stop)
        ringThreadToStop = true;
        try {
            TimeUnit.SECONDS.sleep(1);
        } catch (InterruptedException e) {
            log.error(e.getMessage(), e);
        }
        if (ringThread.getState() != Thread.State.TERMINATED){
            // interrupt and wait
            ringThread.interrupt();
            try {
                ringThread.join();
            } catch (InterruptedException e) {
                log.error(e.getMessage(), e);
            }
        }

        log.info(">>>>>>>>>>> fls-job, JobScheduleHelper stop");
    }
}
