package com.fls.job.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;

import java.util.concurrent.*;

/**
 * <AUTHOR>
 * @date 2022/12/06/17:15
 */
@Slf4j
@EnableAsync
@Configuration
public class ThreadPoolConfig {
    private final FlsJobConfig flsJobConfig;

    @Autowired
    public ThreadPoolConfig(FlsJobConfig flsJobConfig) {
        this.flsJobConfig = flsJobConfig;
    }

    @Bean("fastTriggerPool")
    public Executor fastTriggerPool(){
        return new ThreadPoolExecutor(
            10,
            flsJobConfig.getTriggerPoolFastMax(),
            60L,
            TimeUnit.SECONDS,
            new LinkedBlockingQueue<Runnable>(1000),
            new ThreadFactory() {
                @Override
                public Thread newThread(Runnable r) {
                    return new Thread(r, "fls-job,JobTriggerPoolHelper-fastTriggerPool-" + r.hashCode());
                }
            });
    }

    @Bean("slowTriggerPool")
    public Executor slowTriggerPool(){
        return new ThreadPoolExecutor(
            10,
            flsJobConfig.getTriggerPoolSlowMax(),
            60L,
            TimeUnit.SECONDS,
            new LinkedBlockingQueue<Runnable>(2000),
            new ThreadFactory() {
                @Override
                public Thread newThread(Runnable r) {
                    return new Thread(r, "fls-job, admin JobTriggerPoolHelper-slowTriggerPool-" + r.hashCode());
                }
            });
    }
}
