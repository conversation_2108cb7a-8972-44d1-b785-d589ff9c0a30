package com.fls.job.constant;

/**
 * <AUTHOR> 2020-10-29 21:11:23
 */
public enum ScheduleTypeEnum {

    NONE("NONE", "无"),

    /**
     * schedule by cron
     */
    CRON("CRON", "CRON表达式"),

    /**
     * schedule by fixed rate (in seconds)
     */
    FIX_RATE("FIX_RATE", "固定速率"),

    /**
     * schedule by fix delay (in seconds)， after the last time
     */
    /*FIX_DELAY(I18nUtil.getString("schedule_type_fix_delay"))*/;

    private String code;
    private String title;

    ScheduleTypeEnum(String code, String title) {
        this.code = code;
        this.title = title;
    }

    public String getTitle() {
        return title;
    }

    public static ScheduleTypeEnum match(String name, ScheduleTypeEnum defaultItem){
        for (ScheduleTypeEnum item: ScheduleTypeEnum.values()) {
            if (item.name().equals(name)) {
                return item;
            }
        }
        return defaultItem;
    }

}
