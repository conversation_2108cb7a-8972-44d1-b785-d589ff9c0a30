package com.fls.job.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.fls.job.entity.FlsJobInfo;
import com.fls.job.entity.dto.FlsJobInfoDTO;
import com.fls.job.entity.query.FlsJobInfoQuery;
import com.fls.job.entity.vo.FlsJobInfoVO;

import java.util.List;

public interface IFlsJobInfoService extends IService<FlsJobInfo> {
    /**
     * 分页列表
     * @param page 分页参数
     * @param query 查询参数
     * @return Page<FlsJobInfo>
     */
    Page<FlsJobInfo> pageList(Page page, FlsJobInfoQuery query);

    /**
     * 新增任务
     * @param info 任务配置
     * @return Boolean
     */
    Boolean add(FlsJobInfoDTO info);

    /**
     * 编辑任务
     * @param info 任务配置
     * @return Boolean
     */
    Boolean edit(FlsJobInfoDTO info);

    /**
     * 删除任务
     * @param id 任务id
     * @return Boolean
     */
    Boolean delete(String id);

    /**
     * 暂停
     * @param id 任务id
     * @return Boolean
     */
    Boolean stop(String id);

    /**
     * 启用
     * @param id 任务id
     * @return Boolean
     */
    Boolean start(String id);

    /**
     * 根据条件获取
     * @param query 条件参数
     * @return List<FlsJobInfoVO>
     */
    List<FlsJobInfoVO> getByQuery(FlsJobInfoQuery query);
}
