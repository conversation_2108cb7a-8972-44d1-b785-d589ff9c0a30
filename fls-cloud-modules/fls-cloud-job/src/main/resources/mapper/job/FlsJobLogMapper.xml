<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fls.job.mapper.FlsJobLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.fls.job.entity.FlsJobLog">
        <id column="id" property="id" />
        <result column="job_id" property="jobId" />
        <result column="executor_fail_retry_count" property="executorFailRetryCount" />
        <result column="trigger_time" property="triggerTime" />
        <result column="trigger_code" property="triggerCode" />
        <result column="trigger_msg" property="triggerMsg" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, job_group, job_id, executor_fail_retry_count, trigger_time, trigger_type, trigger_code, trigger_msg, handle_status
    </sql>

    <update id="updateAlarmStatus">
        UPDATE t_job_log
		SET
			`alarm_status` = #{newAlarmStatus}
		WHERE `id`= #{failLogId} AND `alarm_status` = #{oldAlarmStatus}
    </update>

    <delete id="clearLog">
        delete from t_job_log
        WHERE id in
        <foreach collection="logIds" item="item" open="(" close=")" separator="," >
            #{item}
        </foreach>
    </delete>

    <select id="pageByQuery" resultType="com.fls.job.entity.FlsJobLog">
        SELECT <include refid="Base_Column_List" />
        FROM t_job_log AS t
        <where>
            <if test="query.jobId != null and query.jobId != ''">
                AND t.job_id = #{query.jobId}
            </if>
            <if test="query.jobGroup != null and query.jobGroup != ''">
                AND t.job_group = #{query.jobGroup}
            </if>
            <if test="query.triggerTimeStart != null">
                AND t.trigger_time <![CDATA[ >= ]]> #{query.triggerTimeStart}
            </if>
            <if test="query.triggerTimeEnd != null">
                AND t.trigger_time <![CDATA[ <= ]]> #{query.triggerTimeEnd}
            </if>
            <if test="query.handleStatus != null">
                AND t.handle_status = #{query.handleStatus}
            </if>
        </where>
        ORDER BY t.trigger_time DESC
    </select>

    <select id="findClearLogIds" resultType="java.lang.String">
        SELECT id FROM t_job_log
        <where>
            <if test="jobId != null and jobId != ''">
                AND t.job_id = #{jobId}
            </if>
            <if test="jobGroup != null and jobGroup != ''">
                AND t.job_group = #{jobGroup}
            </if>
            <if test="clearBeforeTime != null">
                AND trigger_time <![CDATA[ <= ]]> #{clearBeforeTime}
            </if>
            <if test="clearBeforeNum gt 0">
                AND id NOT in(
                SELECT id FROM(
                SELECT id FROM t_job_log AS tt
                <where>
                    <if test="jobGroup != null and jobGroup != ''">
                        AND t.job_group = #{jobGroup}
                    </if>
                    <if test="jobId != null and jobId != ''">
                        AND t.job_id = #{jobId}
                    </if>
                </where>
                ORDER BY t.trigger_time desc
                LIMIT 0, #{clearBeforeNum}
                ) t1
                )
            </if>
        </where>
        order by id asc
        LIMIT #{pageSize}
    </select>

    <select id="findLogReport" resultType="com.fls.job.entity.FlsJobLogReport">
        SELECT
			SUM(CASE WHEN handle_status = 0 then 1 else 0 end) as runningCount,
			SUM(CASE WHEN handle_status = 1 then 1 else 0 end) as successCount,
			SUM(CASE WHEN handle_status = 2 then 1 else 0 end) as failCount
		FROM t_job_log
		WHERE trigger_time BETWEEN #{from} and #{to}
    </select>

    <select id="findFailJobLogIds" resultType="java.lang.String">
        SELECT id FROM `t_job_log`
		WHERE `handle_status` = 2
		AND `alarm_status` = 0
		ORDER BY `trigger_time` ASC
		LIMIT #{size}
    </select>

</mapper>
