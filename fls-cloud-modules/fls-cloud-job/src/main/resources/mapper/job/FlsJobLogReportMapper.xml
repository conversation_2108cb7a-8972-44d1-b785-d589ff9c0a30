<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fls.job.mapper.FlsJobLogReportMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.fls.job.entity.FlsJobLogReport">
        <id column="id" property="id" />
        <result column="trigger_day" property="triggerDay" />
        <result column="running_count" property="runningCount" />
        <result column="success_count" property="successCount" />
        <result column="fail_count" property="failCount" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, trigger_day, running_count, success_count, fail_count
    </sql>

    <update id="updateByTime">
        UPDATE t_job_log_report
        SET `running_count` = #{report.runningCount},
        	`success_count` = #{report.successCount},
        	`fail_count` = #{report.failCount}
        WHERE `trigger_day` = #{report.triggerDay}
    </update>

    <select id="queryLogReportTotal" resultType="com.fls.job.entity.FlsJobLogReport">
        SELECT
			SUM(running_count) runningCount,
			SUM(success_count) successCount,
			SUM(fail_count) failCount
		FROM t_job_log_report AS t
    </select>

    <select id="queryLogReport" resultType="com.fls.job.entity.FlsJobLogReport">
        SELECT <include refid="Base_Column_List" />
        FROM t_job_log_report AS t
        WHERE t.trigger_day between #{from} and #{to}
        ORDER BY t.trigger_day ASC
    </select>

</mapper>
