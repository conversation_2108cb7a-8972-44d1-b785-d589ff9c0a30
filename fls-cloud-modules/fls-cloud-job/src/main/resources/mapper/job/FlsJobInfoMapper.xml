<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fls.job.mapper.FlsJobInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.fls.job.entity.FlsJobInfo">
        <id column="id" property="id" />
        <result column="job_desc" property="jobDesc" />
        <result column="schedule_type" property="scheduleType" />
        <result column="schedule_conf" property="scheduleConf" />
        <result column="misfire_strategy" property="misfireStrategy" />
        <result column="executor_timeout" property="executorTimeout" />
        <result column="executor_fail_retry_count" property="executorFailRetryCount" />
        <result column="child_job_id" property="childJobId" />
        <result column="trigger_status" property="triggerStatus" />
        <result column="trigger_last_time" property="triggerLastTime" />
        <result column="trigger_next_time" property="triggerNextTime" />
        <result column="request_method" property="requestMethod" />
        <result column="request_url" property="requestUrl" />
        <result column="request_headers" property="requestHeaders" />
        <result column="request_params" property="requestParams" />
        <result column="request_body" property="requestBody" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, job_desc, schedule_type, schedule_conf, misfire_strategy, executor_timeout, executor_fail_retry_count, child_job_id, trigger_status, trigger_last_time, trigger_next_time, request_method, request_url, request_headers, request_params, request_body, create_by, update_by, create_time, update_time, del_flag
    </sql>
    <update id="scheduleUpdate">
        UPDATE t_job_info
		SET
			trigger_last_time = #{info.triggerLastTime},
			trigger_next_time = #{info.triggerNextTime},
			trigger_status = #{info.triggerStatus}
		WHERE id = #{info.id}
    </update>

    <select id="scheduleJobQuery" parameterType="java.util.HashMap" resultType="com.fls.job.entity.FlsJobInfo">
        SELECT <include refid="Base_Column_List" />
        FROM t_job_info AS t
        WHERE t.trigger_status = 1
        and t.del_flag = 0
        and t.trigger_next_time <![CDATA[ <= ]]> #{maxNextTime}
        ORDER BY id ASC
        LIMIT #{pagesize}
    </select>

    <select id="getByQuery" resultType="com.fls.job.entity.vo.FlsJobInfoVO">
        SELECT id, job_desc jobDesc FROM t_job_info AS t
        WHERE t.del_flag = 0
        <if test="query.jobGroup != null and query.jobGroup != ''">
            AND t.job_group = #{query.jobGroup}
        </if>
    </select>

</mapper>
