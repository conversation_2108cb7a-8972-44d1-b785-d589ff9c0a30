package com.fls.pay.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("t_pay_info")
public class PayInfo {
    @TableId(value = "id_pay_info", type = IdType.ASSIGN_UUID)
    private String idPayInfo; //主键
    private String orderNo; //商户订单号
    private String tradeNo; //平台订单号
    private String transactionId; //交易ID
    private String payType; //支付类型 1=微信，2=支付宝，3=银联
    private String subPayType; //具体支付类型 0=未知，1=建行龙支付，7=云闪付，8=微信支付，9=支付宝
    private String tradeType; //交易类型
    private String tradeState; //交易状态
    private Integer payerTotal; //买家支付金额（分）
    private Integer orderTotal; //订单金额（分）
    private Date payTime; //支付时间
    private String content; //通知参数
    private Date createTime; //创建时间
    private Date updateTime; //修改时间
    private String ts; //时间戳
    private String deleteFlag; //是否删除 0=否，1=是 参见yesorno
}
