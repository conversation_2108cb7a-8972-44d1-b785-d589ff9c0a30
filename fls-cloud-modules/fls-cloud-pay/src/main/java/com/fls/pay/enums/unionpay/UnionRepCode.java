package com.fls.pay.enums.unionpay;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum UnionRepCode {
    /**
     * 系统级处理成功
     */
    SUCCESS("20000"),

    /**
     * 缺少必选参数
     */
    MISSING_PARAMS("40000"),

    /**
     * 授权权限不足
     */
    INVALID_AUTH("40001"),

    /**
     * 非法参数
     */
    INVALID_PARAMS("40002"),

    /**
     * 无效请求
     */
    INVALID_REQUEST("40004"),

    /**
     * 业务处理失败
     */
    BIZ_FAIL("50000"),

    /**
     * 服务不可用
     */
    UNAVAILABLE_SERVICE("50003"),

    /**
     * 业务级处理成功
     */
    ACQ_SUCCESS("ACQ.SUCCESS");

    /**
     * 类型
     */
    private final String type;
}
