package com.fls.pay.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("t_pay_refund_apply")
public class PayRefundApply {
    @TableId(value = "id_pay_refund_apply", type = IdType.ASSIGN_UUID)
    private String idPayRefundApply; //主键
    private String orderNo; //商户订单号
    private String refundNo; //商户退款单号
    private String tradeNo; //平台订单号
    private String refundId; //平台退款单号
    private Integer orderTotal; //订单金额（分）
    private Integer refundAmount; //退单金额（分）
    private String reason; //退单原因
    private String notifyUrl; //退款结果通知业务系统url
    private String content; //平台应答
    private String status; //状态 0-无效 1-有效
    private Date createTime; //创建时间
    private String ts; //时间戳
    private String deleteFlag; //是否删除 0=否，1=是 参见yesorno
}
