package com.fls.pay.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@Configuration
@RefreshScope
@ConfigurationProperties(prefix = "unionpay")
@Data
public class UnionPayConfig {
    //商户号
    private String mchId;
    //MD5密钥
    private String md5Key;
    //银联支付服务器地址
    private String domain;
    //支付结果通知地址
    private String notifyDomain;
    //微信小程序appid
    private String wxMiniAppid;
}
