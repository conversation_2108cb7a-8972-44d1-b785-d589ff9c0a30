package com.fls.pay.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.HashMap;
import java.util.Map;

@Data
@Accessors(chain = true)
public class R {
    private Integer code; //返回码
    private String msg; //返回信息
    private Map<String,Object> data = new HashMap<>(); //返回数据

    public static R ok(){
        R r = new R();
        r.setCode(200);
        r.setMsg("成功");
        return r;
    }

    public static R fail(){
        R r = new R();
        r.setCode(500);
        r.setMsg("失败");
        return r;
    }

    public static R fail(String msg){
        R r = new R();
        r.setCode(500);
        r.setMsg(msg);
        return r;
    }

    public R data(String key, Object value){
        this.data.put(key,value);
        return this;
    }
}
