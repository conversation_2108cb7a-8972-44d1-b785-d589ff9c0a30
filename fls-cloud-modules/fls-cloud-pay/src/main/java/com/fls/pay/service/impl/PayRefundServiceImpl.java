package com.fls.pay.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fls.pay.constant.SysDictionary;
import com.fls.pay.entity.PayRefund;
import com.fls.pay.mapper.PayRefundMapper;
import com.fls.pay.service.PayRefundService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;
import java.util.UUID;

@Slf4j
@Service
public class PayRefundServiceImpl extends ServiceImpl<PayRefundMapper, PayRefund> implements PayRefundService {

    @Override
    public void createRefundUnionPay(Map<String, Object> body) {
        log.info("记录银联平台退款日志 ===>");
        JSONObject response = (JSONObject)body.get("response");
        //获取商户订单号
        String outTradeNo = (String)response.get("out_trade_no");
        //获取平台订单号
        String tradeNo = (String)response.get("trade_no");
        //获取商户退款单号
        String outRefundNo = (String)response.get("out_refund_no");
        //获取平台退款单号
        String refundNo = (String)response.get("refund_no");
        //获取退款状态
            /*SUCCESS—退款成功
            REFUNDCLOSE—退款关闭
            PROCESSING—退款处理中
            CHANGE—退款异常*/
        String refundState = (String)response.get("refund_state");
        //获取订单总金额
        Integer totalAmount = Integer.parseInt((String)response.get("total_amount"));
        //获取退款总金额
        Integer refundAmount = Integer.parseInt((String)response.get("refund_amount"));
        //获取实际退款金额
        Integer realRefundAmount = Integer.parseInt((String)response.get("real_refund_amount"));

        Date now = new Date();
        String ts = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(now);

        PayRefund payRefund = new PayRefund();
        payRefund.setIdPayRefund(UUID.randomUUID().toString());
        payRefund.setOrderNo(outTradeNo);
        payRefund.setRefundNo(outRefundNo);
        payRefund.setTradeNo(tradeNo);
        payRefund.setRefundId(refundNo);
        payRefund.setOrderTotal(totalAmount);
        payRefund.setRefundAmount(refundAmount);
        payRefund.setReason(null);
        payRefund.setRefundStatus(refundState);
        payRefund.setContent(body.toString());
        payRefund.setCreateTime(now);
        payRefund.setUpdateTime(now);
        payRefund.setTs(ts);
        payRefund.setDeleteFlag(SysDictionary.YESORNO_NO);

        baseMapper.insert(payRefund);
    }

    @Override
    public PayRefund getRefund(String refundNo) {
        QueryWrapper<PayRefund> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("refund_no",refundNo);
        queryWrapper.eq("refund_status", "SUCCESS");
        queryWrapper.eq("delete_flag", SysDictionary.YESORNO_NO);
        PayRefund payRefund = baseMapper.selectOne(queryWrapper);
        return payRefund;
    }
}
