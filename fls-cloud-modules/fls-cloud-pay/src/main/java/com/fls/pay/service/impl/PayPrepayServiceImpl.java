package com.fls.pay.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fls.pay.constant.SysDictionary;
import com.fls.pay.entity.PayPrepay;
import com.fls.pay.mapper.PayPrepayMapper;
import com.fls.pay.service.PayPrepayService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class PayPrepayServiceImpl extends ServiceImpl<PayPrepayMapper, PayPrepay> implements PayPrepayService {

    @Override
    public PayPrepay getPrepay(String orderNo) {
        QueryWrapper<PayPrepay> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("order_no",orderNo);
        queryWrapper.eq("status", SysDictionary.YESORNO_YES);
        queryWrapper.eq("delete_flag", SysDictionary.YESORNO_NO);
        PayPrepay payPrepay = baseMapper.selectOne(queryWrapper);
        return payPrepay;
    }
}
