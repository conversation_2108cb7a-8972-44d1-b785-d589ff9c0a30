package com.fls.pay.service;

import java.util.Map;

public interface UnionPayService {
    /**
     * 统一下单
     * @param orderNo 商户订单号
     * @param totalAmount 订单总金额
     * @param transType 交易方式
     * @param notifyUrl 支付结果通知url
     * @param appid 微信appid
     * @param openid 微信用户openid
     * @return
     * @throws Exception
     */
    Map<String, Object> unifiedOrder(String orderNo, String totalAmount, String transType, String notifyUrl, String appid, String openid) throws Exception;

    /**
     * 查询订单
     * @param orderNo 商户订单号
     * @return
     * @throws Exception
     */
    Map<String, Object> orderQuery(String orderNo) throws Exception;

    /**
     * 撤销订单
     * @param orderNo 商户订单号
     * @return
     * @throws Exception
     */
    Map<String, Object> reverse(String orderNo) throws Exception;

    /**
     * 关闭订单
     * @param orderNo 商户订单号
     * @return
     * @throws Exception
     */
    Map<String, Object> closeOrder(String orderNo) throws Exception;

    /**
     * 申请退款
     * @param orderNo 商户订单号
     * @param refundNo 商户退款单号
     * @param orderTotal 订单金额
     * @param refundAmount 退款金额
     * @param reason 退款原因
     * @param notifyUrl 退款结果通知业务系统url
     * @return
     * @throws Exception
     */
    Map<String, Object> refund(String orderNo, String refundNo, String orderTotal, String refundAmount, String reason, String notifyUrl) throws Exception;

    /**
     * 退款查询
     * @param refundNo 商户退款单号
     * @return
     * @throws Exception
     */
    Map<String, Object> refundQuery(String refundNo) throws Exception;

    /**
     * 退款查询（分页查询）
     * @param orderNo 商户订单号
     * @param offset 偏移量，当部分退款次数超过10次时可使用，表示返回的查询结果从这个偏移量开始取记录，默认为0
     * @return
     * @throws Exception
     */
    Map<String, Object> refundQueryExt(String orderNo, String offset) throws Exception;

    /**
     * 支付结果通知
     * @param bodyMap 支付平台回调请求体
     */
    Map<String, Object> payNotify(Map<String, Object> bodyMap);

    /**
     * 退款结果通知
     * @param bodyMap 支付平台回调请求体
     */
    Map<String, Object> refundNotify(Map<String, Object> bodyMap);
}
