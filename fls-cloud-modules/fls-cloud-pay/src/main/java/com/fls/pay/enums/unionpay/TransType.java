package com.fls.pay.enums.unionpay;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum TransType {
    /**
     * 主扫支付（用户扫商家的码）
     */
    CSB("csb"),

    /**
     * 被扫支付（商家扫用户的码）
     */
    BSC("bsc"),

    /**
     * 微信APP支付
     */
    WX_APP("wx_app"),

    /**
     * 微信JSAPI支付
     */
    WX_MP("wx_mp"),

    /**
     * 微信小程序支付
     */
    WX_APPLET("wx_applet"),

    /**
     * 支付宝服务窗支付
     */
    ALIPAY_FW("alipay_fw"),

    /**
     * 银联JS支付
     */
    UNIONPAY_JS("unionpay_js");

    /**
     * 交易类型
     */
    private final String type;
}
