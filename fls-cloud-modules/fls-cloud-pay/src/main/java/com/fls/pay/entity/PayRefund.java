package com.fls.pay.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("t_pay_refund")
public class PayRefund {
    @TableId(value = "id_pay_refund", type = IdType.ASSIGN_UUID)
    private String idPayRefund; //主键
    private String orderNo; //商户订单号
    private String refundNo; //商户退单号
    private String tradeNo; //平台订单号
    private String refundId; //平台退款单号
    private Integer orderTotal; //订单金额（分）
    private Integer refundAmount; //退单金额（分）
    private String reason; //退单原因
    private String refundStatus; //退款状态
    private String content; //通知参数
    private Date createTime; //创建时间
    private Date updateTime; //更新时间
    private String ts; //时间戳
    private String deleteFlag; //是否删除 0=否，1=是 参见yesorno
}
