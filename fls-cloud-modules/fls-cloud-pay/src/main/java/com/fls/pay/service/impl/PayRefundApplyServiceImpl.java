package com.fls.pay.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fls.pay.constant.SysDictionary;
import com.fls.pay.entity.PayRefundApply;
import com.fls.pay.mapper.PayRefundApplyMapper;
import com.fls.pay.service.PayRefundApplyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class PayRefundApplyServiceImpl extends ServiceImpl<PayRefundApplyMapper, PayRefundApply> implements PayRefundApplyService {

    @Override
    public PayRefundApply getRefundApply(String refundNo) {
        QueryWrapper<PayRefundApply> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("refund_no",refundNo);
        queryWrapper.eq("status", SysDictionary.YESORNO_YES);
        queryWrapper.eq("delete_flag", SysDictionary.YESORNO_NO);
        PayRefundApply refundApply = baseMapper.selectOne(queryWrapper);
        return refundApply;
    }
}
