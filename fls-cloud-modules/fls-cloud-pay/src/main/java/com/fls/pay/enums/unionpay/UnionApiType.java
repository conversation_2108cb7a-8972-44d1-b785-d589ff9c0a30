package com.fls.pay.enums.unionpay;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum UnionApiType {
    /**
     * 统一下单
     */
    UNIFIED_ORDER("/pay/unifiedorder"),

    /**
     * 查询订单
     */
    ORDER_QUERY("/pay/orderquery"),

    /**
     * 撤销订单
     */
    REVERSE("/pay/reverse"),

    /**
     * 关闭订单
     */
    CLOSE_ORDER("/pay/closeorder"),

    /**
     * 申请退款
     */
    REFUND("/pay/refund"),

    /**
     * 退款查询
     */
    REFUND_QUERY("/pay/refundquery"),

    /**
     * 退款查询扩展（根据支付单号查询）
     */
    REFUND_QUERY_EXT("/pay/refundqueryext");

    /**
     * 类型
     */
    private final String type;
}
