package com.fls.pay.util;

import org.springframework.util.DigestUtils;

import java.util.*;

public class SignUtil {
    /**
     * 请求串排序
     * @param request
     * @return
     */
    public static String genSignMessage(Map<String, Object> request){
        List<String> list = new ArrayList<String>();
        list.addAll(request.keySet());
        Collections.sort(list);
        StringBuilder sb = new StringBuilder();
        for(String paramKey:list){
            String value = request.get(paramKey).toString();
            //空值剔除不参与签名
            if (value != null){
                if (sb.length() > 0){
                    sb.append('&');
                }
                sb.append(paramKey).append('=').append(value);
            }
        }
        return sb.toString();
    }

    /**
     * MD5加密
     * @param plainText
     * @return
     */
    public static String sign(String plainText){
        String sign = DigestUtils.md5DigestAsHex(plainText.getBytes());
        return sign.toUpperCase();
    }

    /**
     * 生成随机字符串
     * @param seed 种子
     * @param length 字符串长度
     * @return
     */
    public static String genNonce(long seed, int length) {
        String base = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYAC0123456789";
        Random random = new Random();
        random.setSeed(seed);
        StringBuilder sb = new StringBuilder(length);
        for (int i = 0; i < length; i++) {
            sb.append(base.charAt(random.nextInt(base.length())));
        }
        return sb.toString();
    }
}
