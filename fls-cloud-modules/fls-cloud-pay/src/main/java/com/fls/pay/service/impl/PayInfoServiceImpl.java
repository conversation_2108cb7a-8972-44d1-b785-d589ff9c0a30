package com.fls.pay.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fls.pay.constant.SysDictionary;
import com.fls.pay.entity.PayInfo;
import com.fls.pay.enums.PayType;
import com.fls.pay.mapper.PayInfoMapper;
import com.fls.pay.service.PayInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;
import java.util.UUID;

@Slf4j
@Service
public class PayInfoServiceImpl extends ServiceImpl<PayInfoMapper, PayInfo> implements PayInfoService {
    @Override
    public void createPayInfoUnionPay(Map<String, Object> body) {
        log.info("记录银联平台支付日志 ===>");
        JSONObject response = (JSONObject)body.get("response");
        //获取商户订单号
        String outTradeNo = (String)response.get("out_trade_no");
        //获取平台订单号
        String tradeNo = (String)response.get("trade_no");
        //第三方订单号（云闪付/微信/支付宝等）
        String outTransactionId = (String)response.get("out_transaction_id");
        //获取交易类型
            /*csb 主扫支付
            bsc 被扫支付
            wx_app 微信App支付
            wx_mp 微信JSAPI支付
            wx_applet 微信小程序支付*/
        String tradeType = (String)response.get("trade_type");
        //获取交易状态
            /*SUCCESS：支付成功
            REFUND：转入退款
            NOTPAY：未支付
            CLOSED：已关闭
            REVOKED：已撤销（付款码支付）
            USERPAYING：用户支付中（付款码支付）
            PAYERROR：支付失败(其他原因，如银行返回失败)*/
        String tradeState = (String)response.get("trade_state");
        //获取具体支付类型
        String paymentType = (String)response.get("payment_type");

        //获取用户支付金额
        Integer payerTotal = Integer.parseInt((String)response.get("real_amount"));
        //获取订单总金额
        Integer total = Integer.parseInt((String)response.get("total_amount"));

        Date now = new Date();
        String ts = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(now);

        PayInfo payInfo = new PayInfo();
        payInfo.setIdPayInfo(UUID.randomUUID().toString());
        payInfo.setOrderNo(outTradeNo);
        payInfo.setTradeNo(tradeNo);
        payInfo.setTransactionId(outTransactionId);
        payInfo.setPayType(PayType.UNIONPAY.getType());
        payInfo.setSubPayType(paymentType);
        payInfo.setTradeType(tradeType);
        payInfo.setTradeState(tradeState);
        payInfo.setPayerTotal(payerTotal);
        payInfo.setOrderTotal(total);
        payInfo.setPayTime(null); //接口未返回支付完成时间
        payInfo.setContent(body.toString());
        payInfo.setCreateTime(now);
        payInfo.setUpdateTime(now);
        payInfo.setTs(ts);
        payInfo.setDeleteFlag(SysDictionary.YESORNO_NO);

        baseMapper.insert(payInfo);
    }

    @Override
    public PayInfo getPayInfo(String orderNo) {
        QueryWrapper<PayInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("order_no",orderNo);
        queryWrapper.eq("trade_state", "SUCCESS");
        queryWrapper.eq("delete_flag", SysDictionary.YESORNO_NO);
        PayInfo payInfo = baseMapper.selectOne(queryWrapper);
        return payInfo;
    }


}
