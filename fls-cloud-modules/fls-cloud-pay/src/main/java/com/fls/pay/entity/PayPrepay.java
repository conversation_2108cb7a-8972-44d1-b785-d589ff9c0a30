package com.fls.pay.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("t_pay_prepay")
public class PayPrepay {
    @TableId(value = "id_pay_prepay", type = IdType.ASSIGN_UUID)
    private String idPayPrepay; //主键
    private String orderNo; //商户订单号
    private String tradeNo; //平台订单号
    private String transactionId; //交易ID
    private String codeUrl; //支付二维码
    private String payType; //支付类型 1=微信，2=支付宝，3=银联
    private Integer orderTotal; //订单金额（分）
    private String notifyUrl; //支付结果通知业务系统url
    private String content; //平台应答
    private String appid; //微信appid
    private String openid; //微信用户openid
    private String status; //状态 0-无效 1-有效
    private Date createTime; //创建时间
    private String ts; //时间戳
    private String deleteFlag; //是否删除 0=否，1=是 参见yesorno
}
