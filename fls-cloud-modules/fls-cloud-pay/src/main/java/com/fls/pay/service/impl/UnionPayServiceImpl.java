package com.fls.pay.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.fls.pay.config.UnionPayConfig;
import com.fls.pay.constant.SysDictionary;
import com.fls.pay.entity.PayInfo;
import com.fls.pay.entity.PayPrepay;
import com.fls.pay.entity.PayRefund;
import com.fls.pay.entity.PayRefundApply;
import com.fls.pay.enums.PayType;
import com.fls.pay.enums.unionpay.TransType;
import com.fls.pay.enums.unionpay.UnionApiType;
import com.fls.pay.enums.unionpay.UnionNotifyType;
import com.fls.pay.enums.unionpay.UnionRepCode;
import com.fls.pay.service.*;
import com.fls.pay.util.SignUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.locks.ReentrantLock;

@Service
@Slf4j
public class UnionPayServiceImpl implements UnionPayService {
    @Resource
    private UnionPayConfig unionPayConfig;

    @Resource
    private PayInfoService payInfoService;

    @Resource
    private PayPrepayService payPrepayService;

    @Resource
    private PayRefundApplyService payRefundApplyService;

    @Resource
    private PayRefundService payRefundService;

    @Resource
    private RestTemplate restTemplate;

    private final ReentrantLock lock = new ReentrantLock();

    @Override
    public Map<String, Object> unifiedOrder(String orderNo,String totalAmount,String transType,String notifyUrl,String appid,String openid) throws Exception {
        Map<String,Object> map = new HashMap<>();
        Map<String,Object> dataMap = new HashMap<>();
        PayPrepay prepay = payPrepayService.getPrepay(orderNo);
        if(prepay!=null){
            log.info("预支付交易单已存在===>{}",orderNo);
            //查看是否过期（银联平台接口文档中无有效期介绍），暂时不做处理
            //返回预支付ID（平台订单号）
            dataMap.put("pTradeNo",prepay.getTradeNo()); //平台订单号
            dataMap.put("codeUrl",prepay.getCodeUrl()); //支付二维码
            dataMap.put("orderNo",orderNo);
            //如果是小程序支付
            if(TransType.WX_APPLET.getType().equals(transType)){
                JSONObject content = JSON.parseObject(prepay.getContent());
                JSONObject bizResponse = content.getJSONObject("response");
                JSONObject extend = (JSONObject)bizResponse.get("extend");
                dataMap.put("wxAppid",unionPayConfig.getWxMiniAppid());
                dataMap.put("timeStamp",extend.get("timeStamp"));
                dataMap.put("nonceStr",extend.get("nonceStr"));
                dataMap.put("package",extend.get("package"));
                dataMap.put("signType",extend.get("signType"));
                dataMap.put("paySign",extend.get("paySign"));
            }
            map.put("data",dataMap);
            map.put("code", UnionRepCode.SUCCESS.getType());
            map.put("msg", UnionRepCode.SUCCESS);
            return map;
        }
        //公共请求参数
        JSONObject jObject = getCommonRequest();
        //请求业务参数
        JSONObject bizJson = new JSONObject();
        bizJson.put("trans_type",transType); //交易方式
        bizJson.put("out_trade_no",orderNo); //商户系统内部订单号
        bizJson.put("total_amount",totalAmount); //订单总金额，单位：分
        //bizJson.put("body",""); //对一笔交易的具体描述信息
        //bizJson.put("attach",""); //附加数据
        //bizJson.put("currency","156"); //货币类型，156 人民币
        //bizJson.put("time_start",""); //商户订单生成时间，格式为yyyyMMddHHmmss
        //bizJson.put("time_expire",""); //订单失效时间，格式为yyyyMMddHHmmss
        bizJson.put("notify_url",unionPayConfig.getNotifyDomain().concat(UnionNotifyType.PAY_NOTIFY.getType())); //通知地址
        //bizJson.put("mch_create_ip",unionPayConfig.getMchCreateIp()); //订单生成的机器IP
        if(TransType.WX_APPLET.getType().equals(transType)){
            JSONObject extend = new JSONObject();
            extend.put("app_id",appid);
            extend.put("open_id",openid);
            bizJson.put("extend",extend);
        }
        jObject.put("biz_content",bizJson);

        //签名
        String signStr = SignUtil.sign(SignUtil.genSignMessage(jObject)+"&key="+unionPayConfig.getMd5Key());
        jObject.put("sign",signStr);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> formEntity = new HttpEntity<String>(JSONObject.toJSONString(jObject, SerializerFeature.WriteMapNullValue), headers); //允许传递body中的null值
        log.info("调用统一下单接口，请求参数===>{}",jObject.toJSONString());
        //调用统一下单接口
        ResponseEntity<JSONObject> rep = restTemplate.postForEntity(unionPayConfig.getDomain().concat(UnionApiType.UNIFIED_ORDER.getType()), formEntity, JSONObject.class);
        JSONObject result = rep.getBody();
        log.info("调用统一下单接口，返回响应===>{}",result.toJSONString());
        JSONObject bizResponse = result.getJSONObject("response");
        if (UnionRepCode.SUCCESS.getType().equals(result.get("code"))) { //系统级处理成功
            if(UnionRepCode.ACQ_SUCCESS.getType().equals(bizResponse.get("sub_code"))){ //业务级处理成功
                //返回预支付ID（平台订单号）
                dataMap.put("pTradeNo",bizResponse.get("trade_no")); //平台订单号
                dataMap.put("orderNo",orderNo);
                String codeUrl = null;
                if(bizResponse.get("extend") != null){
                    Map extend = (HashMap)bizResponse.get("extend");
                    //以下是获取交易类型为主扫支付的返回参数
                    dataMap.put("codeUrl",extend.get("code_url")); //支付二维码，交易类型为csb时返回
                    dataMap.put("qrcodeUrl",extend.get("qrcode_url"));
                    codeUrl = extend.get("code_url")==null?null:(String)extend.get("code_url");
                    //以下获取交易类型为APP/微信公众号/微信小程序的返回参数
                    dataMap.put("wxAppid",unionPayConfig.getWxMiniAppid());
                    dataMap.put("timeStamp",extend.get("timeStamp"));
                    dataMap.put("nonceStr",extend.get("nonceStr"));
                    dataMap.put("package",extend.get("package"));
                    dataMap.put("signType",extend.get("signType"));
                    dataMap.put("paySign",extend.get("paySign"));
                }
                map.put("data",dataMap);
                map.put("code", UnionRepCode.SUCCESS.getType());
                map.put("msg", UnionRepCode.SUCCESS);

                //保存预支付单
                PayPrepay payPrepay = new PayPrepay();
                payPrepay.setIdPayPrepay(UUID.randomUUID().toString());
                payPrepay.setOrderNo(orderNo);
                payPrepay.setTradeNo((String)bizResponse.get("trade_no"));
                payPrepay.setTransactionId(null);
                payPrepay.setCodeUrl(codeUrl);
                payPrepay.setPayType(PayType.UNIONPAY.getType());
                payPrepay.setOrderTotal(Integer.parseInt(totalAmount));
                payPrepay.setNotifyUrl(notifyUrl);
                payPrepay.setContent(result.toJSONString());
                payPrepay.setAppid(appid);
                payPrepay.setOpenid(openid);
                payPrepay.setStatus(SysDictionary.YESORNO_YES);
                Date now = new Date();
                payPrepay.setCreateTime(now);
                payPrepay.setTs(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(now));
                payPrepay.setDeleteFlag(SysDictionary.YESORNO_NO);
                payPrepayService.save(payPrepay);
                log.info("银联支付统一下单成功===>业务级响应码{}，订单号{}",bizResponse.get("sub_code"),orderNo);
            }else{
                map.put("code",bizResponse.get("sub_code"));
                map.put("msg",bizResponse.get("sub_msg"));
                log.info("银联支付统一下单失败===>业务级响应码{}，订单号{}",bizResponse.get("sub_code"),orderNo);
            }
        } else {
            log.info("银联支付统一下单失败===>系统级响应码{}，订单号{}",result.get("code"),orderNo);
            //如果是50000，返回具体失败原因
            if(UnionRepCode.BIZ_FAIL.getType().equals(result.get("code"))){
                map.put("code",bizResponse.get("sub_code"));
                map.put("msg",bizResponse.get("sub_msg"));
            }else{
                map.put("code",result.get("code"));
                map.put("msg",result.get("msg"));
            }
        }

        return map;
    }

    @Override
    public Map<String, Object> orderQuery(String orderNo) throws Exception {
        Map<String,Object> map = new HashMap<>();
        Map<String,Object> dataMap = new HashMap<>();
        //公共请求参数
        JSONObject jObject = getCommonRequest();
        //请求业务参数
        JSONObject bizJson = new JSONObject();
        bizJson.put("out_trade_no",orderNo); //商户系统内部订单号
        jObject.put("biz_content",bizJson);

        //签名
        String signStr = SignUtil.sign(SignUtil.genSignMessage(jObject)+"&key="+unionPayConfig.getMd5Key());
        jObject.put("sign",signStr);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> formEntity = new HttpEntity<String>(JSONObject.toJSONString(jObject, SerializerFeature.WriteMapNullValue), headers); //允许传递body中的null值
        log.info("调用查询订单接口，请求参数===>{}",jObject.toJSONString());
        //调用查询订单接口
        ResponseEntity<JSONObject> rep = restTemplate.postForEntity(unionPayConfig.getDomain().concat(UnionApiType.ORDER_QUERY.getType()), formEntity, JSONObject.class);
        JSONObject result = rep.getBody();
        log.info("调用查询订单接口，返回响应===>{}",result.toJSONString());
        JSONObject bizResponse = result.getJSONObject("response");
        if (UnionRepCode.SUCCESS.getType().equals(result.get("code"))) { //系统级处理成功
            if(UnionRepCode.ACQ_SUCCESS.getType().equals(bizResponse.get("sub_code"))){ //业务级处理成功
                //返回交易状态
                dataMap.put("subCode",bizResponse.get("sub_code"));
                dataMap.put("subMsg",bizResponse.get("sub_msg"));
                dataMap.put("orderNo",orderNo);
                dataMap.put("pTradeNo",bizResponse.get("trade_no")); //平台订单号
                dataMap.put("tradeState",bizResponse.get("trade_state")); //交易状态
                map.put("data",dataMap);
                map.put("code", UnionRepCode.SUCCESS.getType());
                map.put("msg", UnionRepCode.SUCCESS);
                /**
                * SUCCESS--支付成功
                * REFUNDED--退款成功
                * REFUNDING--退款中
                * NOTPAY--未支付
                * CLOSED--已关闭
                * USERPAYING--用户支付中（等待输密码，需要查询）
                * PAYERROR--支付失败(其他原因)
                */
                log.info("银联支付查询订单成功===>业务级响应码{}，订单号{}",bizResponse.get("sub_code"),orderNo);
            }else{
                map.put("code",bizResponse.get("sub_code"));
                map.put("msg",bizResponse.get("sub_msg"));
                log.info("银联支付查询订单失败===>业务级响应码{}，订单号{}",bizResponse.get("sub_code"),orderNo);
            }
        } else {
            log.info("银联支付查询订单失败===>系统级响应码{}，订单号{}",result.get("code"),orderNo);
            //如果是50000，返回具体失败原因
            if(UnionRepCode.BIZ_FAIL.getType().equals(result.get("code"))){
                map.put("code",bizResponse.get("sub_code"));
                map.put("msg",bizResponse.get("sub_msg"));
            }else{
                map.put("code",result.get("code"));
                map.put("msg",result.get("msg"));
            }
        }

        return map;
    }

    @Override
    public Map<String, Object> reverse(String orderNo) throws Exception {
        Map<String,Object> map = new HashMap<>();
        Map<String,Object> dataMap = new HashMap<>();
        //公共请求参数
        JSONObject jObject = getCommonRequest();
        //请求业务参数
        JSONObject bizJson = new JSONObject();
        bizJson.put("out_trade_no",orderNo); //商户系统内部订单号
        jObject.put("biz_content",bizJson);

        //签名
        String signStr = SignUtil.sign(SignUtil.genSignMessage(jObject)+"&key="+unionPayConfig.getMd5Key());
        jObject.put("sign",signStr);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> formEntity = new HttpEntity<String>(JSONObject.toJSONString(jObject, SerializerFeature.WriteMapNullValue), headers); //允许传递body中的null值
        log.info("调用撤销订单接口，请求参数===>{}",jObject.toJSONString());
        //调用撤销订单接口
        ResponseEntity<JSONObject> rep = restTemplate.postForEntity(unionPayConfig.getDomain().concat(UnionApiType.REVERSE.getType()), formEntity, JSONObject.class);
        JSONObject result = rep.getBody();
        log.info("调用撤销订单接口，返回响应===>{}",result.toJSONString());
        JSONObject bizResponse = result.getJSONObject("response");
        if (UnionRepCode.SUCCESS.getType().equals(result.get("code"))) { //系统级处理成功
            if("ACQ.SUCCESS".equals(bizResponse.get("sub_code"))){ //业务级处理成功
                //返回撤销状态
                dataMap.put("subCode",bizResponse.get("sub_code"));
                dataMap.put("subMsg",bizResponse.get("sub_msg"));
                dataMap.put("orderNo",orderNo);
                dataMap.put("pTradeNo",bizResponse.get("trade_no")); //平台订单号
                map.put("data",dataMap);
                map.put("code", UnionRepCode.SUCCESS.getType());
                map.put("msg", UnionRepCode.SUCCESS);
                log.info("银联支付撤销订单成功===>业务级响应码{}，订单号{}",bizResponse.get("sub_code"),orderNo);
            }else{
                map.put("code",bizResponse.get("sub_code"));
                map.put("msg",bizResponse.get("sub_msg"));
                log.info("银联支付撤销订单失败===>业务级响应码{}，订单号{}",bizResponse.get("sub_code"),orderNo);
            }
        } else {
            log.info("银联支付撤销订单失败===>系统级响应码{}，订单号{}",result.get("code"),orderNo);
            //如果是50000，返回具体失败原因
            if(UnionRepCode.BIZ_FAIL.getType().equals(result.get("code"))){
                map.put("code",bizResponse.get("sub_code"));
                map.put("msg",bizResponse.get("sub_msg"));
            }else{
                map.put("code",result.get("code"));
                map.put("msg",result.get("msg"));
            }
        }

        return map;
    }

    @Override
    public Map<String, Object> closeOrder(String orderNo) throws Exception {
        Map<String,Object> map = new HashMap<>();
        Map<String,Object> dataMap = new HashMap<>();
        //公共请求参数
        JSONObject jObject = getCommonRequest();
        //请求业务参数
        JSONObject bizJson = new JSONObject();
        bizJson.put("out_trade_no",orderNo); //商户系统内部订单号
        jObject.put("biz_content",bizJson);

        //签名
        String signStr = SignUtil.sign(SignUtil.genSignMessage(jObject)+"&key="+unionPayConfig.getMd5Key());
        jObject.put("sign",signStr);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> formEntity = new HttpEntity<String>(JSONObject.toJSONString(jObject, SerializerFeature.WriteMapNullValue), headers); //允许传递body中的null值
        log.info("调用关闭订单接口，请求参数===>{}",jObject.toJSONString());
        //调用关闭订单接口
        ResponseEntity<JSONObject> rep = restTemplate.postForEntity(unionPayConfig.getDomain().concat(UnionApiType.CLOSE_ORDER.getType()), formEntity, JSONObject.class);
        JSONObject result = rep.getBody();
        log.info("调用关闭订单接口，返回响应===>{}",result.toJSONString());
        JSONObject bizResponse = result.getJSONObject("response");
        if (UnionRepCode.SUCCESS.getType().equals(result.get("code"))) { //系统级处理成功
            if("ACQ.SUCCESS".equals(bizResponse.get("sub_code"))){ //业务级处理成功
                //返回关闭状态
                dataMap.put("subCode",bizResponse.get("sub_code"));
                dataMap.put("subMsg",bizResponse.get("sub_msg"));
                dataMap.put("orderNo",orderNo);
                dataMap.put("pTradeNo",bizResponse.get("trade_no")); //平台订单号
                map.put("data",dataMap);
                map.put("code", UnionRepCode.SUCCESS.getType());
                map.put("msg", UnionRepCode.SUCCESS);
                log.info("银联支付关闭订单成功===>业务级响应码{}，订单号{}",bizResponse.get("sub_code"),orderNo);
            }else{
                map.put("code",bizResponse.get("sub_code"));
                map.put("msg",bizResponse.get("sub_msg"));
                log.info("银联支付关闭订单失败===>业务级响应码{}，订单号{}",bizResponse.get("sub_code"),orderNo);
            }
        } else {
            log.info("银联支付关闭订单失败===>系统级响应码{}，订单号{}",result.get("code"),orderNo);
            //如果是50000，返回具体失败原因
            if(UnionRepCode.BIZ_FAIL.getType().equals(result.get("code"))){
                map.put("code",bizResponse.get("sub_code"));
                map.put("msg",bizResponse.get("sub_msg"));
            }else{
                map.put("code",result.get("code"));
                map.put("msg",result.get("msg"));
            }
        }

        return map;
    }

    @Override
    public Map<String, Object> refund(String orderNo, String refundNo, String orderTotal, String refundAmount, String reason, String notifyUrl) throws Exception {
        Map<String,Object> map = new HashMap<>();
        Map<String,Object> dataMap = new HashMap<>();
        //公共请求参数
        JSONObject jObject = getCommonRequest();
        //请求业务参数
        JSONObject bizJson = new JSONObject();
        bizJson.put("out_trade_no",orderNo); //商户系统内部订单号
        bizJson.put("out_refund_no",refundNo); //商户系统内部退款单号
        bizJson.put("refund_amount",refundAmount); //退款金额，单位分
        bizJson.put("refund_reason",reason); //退款的原因说明
        bizJson.put("notify_url",unionPayConfig.getNotifyDomain().concat(UnionNotifyType.REFUND_NOTIFY.getType())); //通知地址
        jObject.put("biz_content",bizJson);

        //签名
        String signStr = SignUtil.sign(SignUtil.genSignMessage(jObject)+"&key="+unionPayConfig.getMd5Key());
        jObject.put("sign",signStr);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> formEntity = new HttpEntity<String>(JSONObject.toJSONString(jObject, SerializerFeature.WriteMapNullValue), headers); //允许传递body中的null值
        log.info("调用申请退款接口，请求参数===>{}",jObject.toJSONString());
        //调用申请退款接口
        ResponseEntity<JSONObject> rep = restTemplate.postForEntity(unionPayConfig.getDomain().concat(UnionApiType.REFUND.getType()), formEntity, JSONObject.class);
        JSONObject result = rep.getBody();
        log.info("调用申请退款接口，返回响应===>{}",result.toJSONString());
        JSONObject bizResponse = result.getJSONObject("response");
        if (UnionRepCode.SUCCESS.getType().equals(result.get("code"))) { //系统级处理成功
            if("ACQ.SUCCESS".equals(bizResponse.get("sub_code"))){ //业务级处理成功
                //返回申请退款状态
                dataMap.put("subCode",bizResponse.get("sub_code"));
                dataMap.put("subMsg",bizResponse.get("sub_msg"));
                dataMap.put("orderNo",orderNo);
                dataMap.put("refundNo",refundNo);
                dataMap.put("pRefundNo",bizResponse.get("refund_no")); //平台退款单号
                dataMap.put("pTradeNo",bizResponse.get("trade_no")); //平台订单号
                map.put("data",dataMap);
                map.put("code", UnionRepCode.SUCCESS.getType());
                map.put("msg", UnionRepCode.SUCCESS);

                //保存退款申请
                PayRefundApply refundApply = new PayRefundApply();
                refundApply.setIdPayRefundApply(UUID.randomUUID().toString());
                refundApply.setOrderNo(orderNo);
                refundApply.setRefundNo(refundNo);
                refundApply.setTradeNo((String)bizResponse.get("trade_no"));
                refundApply.setRefundId((String)bizResponse.get("refund_no"));
                refundApply.setOrderTotal(Integer.parseInt(orderTotal));
                refundApply.setRefundAmount(Integer.parseInt(refundAmount));
                refundApply.setReason(reason);
                refundApply.setNotifyUrl(notifyUrl);
                refundApply.setContent(result.toJSONString());
                refundApply.setStatus(SysDictionary.YESORNO_YES);
                Date now = new Date();
                refundApply.setCreateTime(now);
                refundApply.setTs(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(now));
                refundApply.setDeleteFlag(SysDictionary.YESORNO_NO);
                payRefundApplyService.save(refundApply);
                log.info("银联支付申请退款受理成功===>业务级响应码{}，订单号{}",bizResponse.get("sub_code"),orderNo);
            }else{
                map.put("code",bizResponse.get("sub_code"));
                map.put("msg",bizResponse.get("sub_msg"));
                log.info("银联支付申请退款受理失败===>业务级响应码{}，订单号{}",bizResponse.get("sub_code"),orderNo);
            }
        } else {
            log.info("银联支付申请退款受理失败===>系统级响应码{}，订单号{}",result.get("code"),orderNo);
            //如果是50000，返回具体失败原因
            if(UnionRepCode.BIZ_FAIL.getType().equals(result.get("code"))){
                map.put("code",bizResponse.get("sub_code"));
                map.put("msg",bizResponse.get("sub_msg"));
            }else{
                map.put("code",result.get("code"));
                map.put("msg",result.get("msg"));
            }
        }

        return map;
    }

    @Override
    public Map<String, Object> refundQuery(String refundNo) throws Exception {
        Map<String,Object> map = new HashMap<>();
        Map<String,Object> dataMap = new HashMap<>();
        //公共请求参数
        JSONObject jObject = getCommonRequest();
        //请求业务参数
        JSONObject bizJson = new JSONObject();
        bizJson.put("out_refund_no",refundNo); //商户系统内部退款单号
        jObject.put("biz_content",bizJson);

        //签名
        String signStr = SignUtil.sign(SignUtil.genSignMessage(jObject)+"&key="+unionPayConfig.getMd5Key());
        jObject.put("sign",signStr);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> formEntity = new HttpEntity<String>(JSONObject.toJSONString(jObject, SerializerFeature.WriteMapNullValue), headers); //允许传递body中的null值
        log.info("调用退款查询接口，请求参数===>{}",jObject.toJSONString());
        //调用退款查询接口
        ResponseEntity<JSONObject> rep = restTemplate.postForEntity(unionPayConfig.getDomain().concat(UnionApiType.REFUND_QUERY.getType()), formEntity, JSONObject.class);
        JSONObject result = rep.getBody();
        log.info("调用退款查询接口，返回响应===>{}",result.toJSONString());
        JSONObject bizResponse = result.getJSONObject("response");
        if (UnionRepCode.SUCCESS.getType().equals(result.get("code"))) { //系统级处理成功
            if(UnionRepCode.ACQ_SUCCESS.getType().equals(bizResponse.get("sub_code"))){ //业务级处理成功
                //返回退款状态
                dataMap.put("subCode",bizResponse.get("sub_code"));
                dataMap.put("subMsg",bizResponse.get("sub_msg"));
                dataMap.put("orderNo",bizResponse.get("out_trade_no"));
                dataMap.put("refundNo",refundNo);
                dataMap.put("pRefundNo",bizResponse.get("refund_no")); //平台退款单号
                dataMap.put("pTradeNo",bizResponse.get("trade_no")); //平台订单号
                dataMap.put("orderTotal",bizResponse.get("total_amount")); //订单金额，单位：分
                dataMap.put("refundAmount",bizResponse.get("refund_amount")); //退款总金额，单位：分
                dataMap.put("realRefundAmount",bizResponse.get("real_refund_amount")); //实际退款金额，单位：分
                dataMap.put("refundState",bizResponse.get("refund_state")); //退款状态
                map.put("data",dataMap);
                map.put("code", UnionRepCode.SUCCESS.getType());
                map.put("msg", UnionRepCode.SUCCESS);
                /**
                * SUCCESS—退款成功
                * REFUNDCLOSE—退款关闭
                * PROCESSING—退款处理中
                * CHANGE—退款异常
                */
                log.info("银联支付退款查询成功===>业务级响应码{}，退款单号{}",bizResponse.get("sub_code"),refundNo);
            }else{
                map.put("code",bizResponse.get("sub_code"));
                map.put("msg",bizResponse.get("sub_msg"));
                log.info("银联支付退款查询失败===>业务级响应码{}，退款单号{}",bizResponse.get("sub_code"),refundNo);
            }
        } else {
            log.info("银联支付退款查询失败===>系统级响应码{}，退款单号{}",result.get("code"),refundNo);
            //如果是50000，返回具体失败原因
            if(UnionRepCode.BIZ_FAIL.getType().equals(result.get("code"))){
                map.put("code",bizResponse.get("sub_code"));
                map.put("msg",bizResponse.get("sub_msg"));
            }else{
                map.put("code",result.get("code"));
                map.put("msg",result.get("msg"));
            }
        }

        return map;
    }

    @Override
    public Map<String, Object> refundQueryExt(String orderNo, String offset) throws Exception {
        Map<String,Object> map = new HashMap<>();
        Map<String,Object> dataMap = new HashMap<>();
        //公共请求参数
        JSONObject jObject = getCommonRequest();
        //请求业务参数
        JSONObject bizJson = new JSONObject();
        bizJson.put("out_trade_no",orderNo); //商户系统内部订单号
        bizJson.put("offset",offset); //偏移量，当部分退款次数超过10次可使用
        jObject.put("biz_content",bizJson);

        //签名
        String signStr = SignUtil.sign(SignUtil.genSignMessage(jObject)+"&key="+unionPayConfig.getMd5Key());
        jObject.put("sign",signStr);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> formEntity = new HttpEntity<String>(JSONObject.toJSONString(jObject, SerializerFeature.WriteMapNullValue), headers); //允许传递body中的null值
        log.info("调用退款分页查询接口，请求参数===>{}",jObject.toJSONString());
        //调用退款查询接口
        ResponseEntity<JSONObject> rep = restTemplate.postForEntity(unionPayConfig.getDomain().concat(UnionApiType.REFUND_QUERY_EXT.getType()), formEntity, JSONObject.class);
        JSONObject result = rep.getBody();
        log.info("调用退款分页查询接口，返回响应===>{}",result.toJSONString());
        JSONObject bizResponse = result.getJSONObject("response");
        if (UnionRepCode.SUCCESS.getType().equals(result.get("code"))) { //系统级处理成功
            if(UnionRepCode.ACQ_SUCCESS.getType().equals(bizResponse.get("sub_code"))){ //业务级处理成功
                //返回退款状态
                dataMap.put("subCode",bizResponse.get("sub_code"));
                dataMap.put("subMsg",bizResponse.get("sub_msg"));
                dataMap.put("orderNo",orderNo);
                dataMap.put("pTradeNo",bizResponse.get("trade_no")); //平台交易单号
                dataMap.put("orderTotal",bizResponse.get("total_amount")); //订单金额，单位：分
                List lst = (List)bizResponse.get("refund_list");
                List<Map<String,String>> refundList = new ArrayList<>();
                for(Object o:lst){
                    Map<String,String> refund = new HashMap<>();
                    refund.put("refundNo",(String)((Map)o).get("out_refund_no")); //系统内部退款单号
                    refund.put("pRefundNo",(String)((Map)o).get("refund_no")); //平台退款单号
                    refund.put("refundAmount",(String)((Map)o).get("refund_amount")); //申请退款金额，单位：分
                    refund.put("realRefundAmount",(String)((Map)o).get("refund_amount")); //实际退款金额，单位：分
                    refund.put("refundState",(String)((Map)o).get("refund_state")); //退款状态
                    /**
                     * SUCCESS—退款成功
                     * REFUNDCLOSE—退款关闭
                     * PROCESSING—退款处理中
                     * CHANGE—退款异常
                     */
                    refundList.add(refund);
                }
                dataMap.put("refundList",refundList);
                map.put("data",dataMap);
                map.put("code", UnionRepCode.SUCCESS.getType());
                map.put("msg", UnionRepCode.SUCCESS);
                log.info("银联支付查询多笔退款成功===>业务级响应码{}，订单号{}",bizResponse.get("sub_code"),orderNo);
            }else{
                map.put("code",bizResponse.get("sub_code"));
                map.put("msg",bizResponse.get("sub_msg"));
                log.info("银联支付查询多笔退款失败===>业务级响应码{}，订单号{}",bizResponse.get("sub_code"),orderNo);
            }
        } else {
            log.info("银联支付查询多笔退款失败===>系统级响应码{}，订单号{}",result.get("code"),orderNo);
            //如果是50000，返回具体失败原因
            if(UnionRepCode.BIZ_FAIL.getType().equals(result.get("code"))){
                map.put("code",bizResponse.get("sub_code"));
                map.put("msg",bizResponse.get("sub_msg"));
            }else{
                map.put("code",result.get("code"));
                map.put("msg",result.get("msg"));
            }
        }

        return map;
    }

    @Override
    public Map<String, Object> payNotify(Map<String,Object> bodyMap) {
        Map<String,Object> map = new HashMap<>();
        //签名值
        String sign = (String)bodyMap.get("sign");
        //计算签名值
        bodyMap.remove("sign");
        String signStr = SignUtil.sign(SignUtil.genSignMessage(bodyMap)+"&key="+unionPayConfig.getMd5Key());
        //验证签名值
        if(sign.equalsIgnoreCase(signStr)){
            //获取商户订单号及状态
            JSONObject bizResponse = (JSONObject)bodyMap.get("response");
            String orderNo = (String)bizResponse.get("out_trade_no");
            String tradeState = (String)bizResponse.get("trade_state");
            //查询回调通知url
            PayPrepay prepay = payPrepayService.getPrepay(orderNo);
            String innerNotifyUrl = prepay.getNotifyUrl();
            //避免函数重入造成的数据混乱，如果分布式需使用分布式锁
            if(lock.tryLock()) {
                try {
                    //调用业务系统接口，让业务系统更新其订单状态
                    HttpHeaders headers = new HttpHeaders();
                    headers.setContentType(MediaType.APPLICATION_JSON);
                    JSONObject jObject = new JSONObject();
                    jObject.put("orderNo",orderNo);
                    jObject.put("tradeState",tradeState);
                    HttpEntity<String> formEntity = new HttpEntity<String>(JSONObject.toJSONString(jObject, SerializerFeature.WriteMapNullValue), headers); //允许传递body中的null值
                    ResponseEntity<JSONObject> rep = restTemplate.postForEntity(innerNotifyUrl, formEntity, JSONObject.class);
                    PayInfo payInfo = payInfoService.getPayInfo(orderNo);
                    if(payInfo==null){
                        //记录支付日志
                        payInfoService.createPayInfoUnionPay(bodyMap);
                    }
                }finally {
                    //需主动释放锁
                    lock.unlock();
                }
            }
            map.put("code", UnionRepCode.SUCCESS.getType());
            map.put("msg","通知成功");
            return map;
        }else{
            map.put("code", UnionRepCode.INVALID_PARAMS.getType());
            map.put("msg","无效签名");
            return map;
        }

    }

    @Override
    public Map<String, Object> refundNotify(Map<String, Object> bodyMap) {
        Map<String,Object> map = new HashMap<>();
        //签名值
        String sign = (String)bodyMap.get("sign");
        //计算签名值
        bodyMap.remove("sign");
        String signStr = SignUtil.sign(SignUtil.genSignMessage(bodyMap)+"&key="+unionPayConfig.getMd5Key());
        //验证签名值
        if(sign.equalsIgnoreCase(signStr)){
            //获取商户退款单号及状态
            JSONObject bizResponse = (JSONObject)bodyMap.get("response");
            String refundNo = (String)bizResponse.get("out_refund_no");
            String refundState = (String)bizResponse.get("refund_state");
            //查询回调通知url
            PayRefundApply refundApply = payRefundApplyService.getRefundApply(refundNo);
            String innerNotifyUrl = refundApply.getNotifyUrl();
            //避免函数重入造成的数据混乱，如果分布式需使用分布式锁
            if(lock.tryLock()) {
                try {
                    //调用业务系统接口，让业务系统更新其退款单号状态
                    HttpHeaders headers = new HttpHeaders();
                    headers.setContentType(MediaType.APPLICATION_JSON);
                    JSONObject jObject = new JSONObject();
                    jObject.put("refundNo",refundNo);
                    jObject.put("refundState",refundState);
                    HttpEntity<String> formEntity = new HttpEntity<String>(JSONObject.toJSONString(jObject, SerializerFeature.WriteMapNullValue), headers); //允许传递body中的null值
                    ResponseEntity<JSONObject> rep = restTemplate.postForEntity(innerNotifyUrl, formEntity, JSONObject.class);
                    PayRefund refund = payRefundService.getRefund(refundNo);
                    if(refund==null){
                        //记录退款单日志
                        payRefundService.createRefundUnionPay(bodyMap);
                    }
                }finally {
                    //需主动释放锁
                    lock.unlock();
                }
            }
            map.put("code", UnionRepCode.SUCCESS.getType());
            map.put("msg","通知成功");
            return map;
        }else{
            map.put("code", UnionRepCode.INVALID_PARAMS.getType());
            map.put("msg","无效签名");
            return map;
        }
    }

    private JSONObject getCommonRequest(){
        JSONObject jObject = new JSONObject();
        Date now = new Date();
        jObject.put("version","1.2"); //调用的接口版本，固定为：1.2
        jObject.put("chnl_code","000001"); //渠道标识 银联条码：000001
        jObject.put("mer_no",unionPayConfig.getMchId()); //渠道商户号
        jObject.put("format","json"); //请求参数格式，仅支持json
        jObject.put("charset","UTF-8"); //字符集，缺省为UTF-8
        jObject.put("encrypt_type","AES"); //加密方式，仅支持AES
        jObject.put("timestamp",new SimpleDateFormat("yyyyMMddHHmmss").format(now)); //交易发生时间戳，yyyyMMddHHmmss 格式
        jObject.put("nonce_str", SignUtil.genNonce(System.currentTimeMillis()/1000,32)); //随机字符串
        jObject.put("sign_type","MD5"); //签名类型，仅支持MD5
        return jObject;
    }
}
