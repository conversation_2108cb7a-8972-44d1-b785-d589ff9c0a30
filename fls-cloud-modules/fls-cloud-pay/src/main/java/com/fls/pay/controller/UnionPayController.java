package com.fls.pay.controller;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.fls.pay.enums.unionpay.TransType;
import com.fls.pay.enums.unionpay.UnionRepCode;
import com.fls.pay.service.UnionPayService;
import com.fls.pay.vo.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

@CrossOrigin
@RestController
@RequestMapping("/api/unionpay")
@Slf4j
public class UnionPayController {

    @Resource
    private UnionPayService unionPayService;

    @PostMapping("native/pay")
    public R nativePay(HttpServletRequest request, HttpServletResponse response, @RequestBody String jsonString){
        JSONObject jsonObject = JSONObject.parseObject(jsonString);
        //校验必传参数
        String[] notNullParams={"order_no","total_amount","notify_url"};
        Map<String, Object> checkResult = checkRequired(jsonObject, notNullParams);
        if(!checkResult.isEmpty()){
            return R.fail((String)checkResult.get("msg"));
        }
        log.info("发起native支付请求===>{}",jsonString);
        //获取订单号
        String orderNo = (String)jsonObject.get("order_no");
        //获取订单金额
        String totalAmount = (String)jsonObject.get("total_amount");
        //获取回调业务系统url
        String notifyUrl = (String)jsonObject.get("notify_url");

        //返回支付二维码链接和订单号
        Map<String,Object> map = null;
        try {
            map = unionPayService.unifiedOrder(orderNo,totalAmount,TransType.CSB.getType(),notifyUrl,null,null);
            if(UnionRepCode.SUCCESS.getType().equals(map.get("code"))){
                return R.ok().setData((Map)map.get("data"));
            }else{
                return R.fail((String)map.get("msg"));
            }
        } catch (Exception e) {
            e.printStackTrace();
            return R.fail();
        }

    }

    @PostMapping("jsapi/pay")
    public R jsapiPay(HttpServletRequest request, HttpServletResponse response, @RequestBody String jsonString){
        JSONObject jsonObject = JSONObject.parseObject(jsonString);
        //校验必传参数
        String[] notNullParams={"order_no","total_amount","notify_url"};
        Map<String, Object> checkResult = checkRequired(jsonObject, notNullParams);
        if(!checkResult.isEmpty()){
            return R.fail((String)checkResult.get("msg"));
        }
        log.info("发起jsapi支付请求===>{}",jsonString);
        //获取订单号
        String orderNo = (String)jsonObject.get("order_no");
        //获取订单金额
        String totalAmount = (String)jsonObject.get("total_amount");
        //获取回调业务系统url
        String notifyUrl = (String)jsonObject.get("notify_url");
        //微信appid
        String appid = (String)jsonObject.get("appid");
        //微信appid
        String openid = (String)jsonObject.get("openid");

        //返回平台订单号
        Map<String,Object> map = null;
        try {
            map = unionPayService.unifiedOrder(orderNo,totalAmount,TransType.WX_APPLET.getType(),notifyUrl,appid,openid);
            if(UnionRepCode.SUCCESS.getType().equals(map.get("code"))){
                return R.ok().setData((Map)map.get("data"));
            }else{
                return R.fail((String)map.get("msg"));
            }
        } catch (Exception e) {
            e.printStackTrace();
            return R.fail();
        }

    }

    @PostMapping("pay/query")
    public R query(HttpServletRequest request, HttpServletResponse response, @RequestBody String jsonString){
        JSONObject jsonObject = JSONObject.parseObject(jsonString);
        //校验必传参数
        String[] notNullParams={"order_no"};
        Map<String, Object> checkResult = checkRequired(jsonObject, notNullParams);
        if(!checkResult.isEmpty()){
            return R.fail((String)checkResult.get("msg"));
        }
        log.info("查询订单支付状态请求===>{}",jsonString);
        try {
            //查询订单支付状态
            Map<String,Object> map = unionPayService.orderQuery((String)jsonObject.get("order_no"));
            if(UnionRepCode.SUCCESS.getType().equals(map.get("code"))){
                return R.ok().setData((Map)map.get("data"));
            }else{
                return R.fail((String)map.get("msg"));
            }
        } catch (Exception e) {
            e.printStackTrace();
            return R.fail();
        }
    }

    @PostMapping("reverse")
    public R reverse(HttpServletRequest request, HttpServletResponse response, @RequestBody String jsonString){
        JSONObject jsonObject = JSONObject.parseObject(jsonString);
        //校验必传参数
        String[] notNullParams={"order_no"};
        Map<String, Object> checkResult = checkRequired(jsonObject, notNullParams);
        if(!checkResult.isEmpty()){
            return R.fail((String)checkResult.get("msg"));
        }
        log.info("撤销订单请求===>{}",jsonString);
        try {
            //撤销订单
            Map<String,Object> map = unionPayService.reverse((String)jsonObject.get("order_no"));
            if(UnionRepCode.SUCCESS.getType().equals(map.get("code"))){
                return R.ok().setData((Map)map.get("data"));
            }else{
                return R.fail((String)map.get("msg"));
            }
        } catch (Exception e) {
            e.printStackTrace();
            return R.fail();
        }
    }

    @PostMapping("close")
    public R close(HttpServletRequest request, HttpServletResponse response, @RequestBody String jsonString){
        JSONObject jsonObject = JSONObject.parseObject(jsonString);
        //校验必传参数
        String[] notNullParams={"order_no"};
        Map<String, Object> checkResult = checkRequired(jsonObject, notNullParams);
        if(!checkResult.isEmpty()){
            return R.fail((String)checkResult.get("msg"));
        }
        log.info("关闭订单请求===>{}",jsonString);
        try {
            //关闭订单
            Map<String,Object> map = unionPayService.closeOrder((String)jsonObject.get("order_no"));
            if(UnionRepCode.SUCCESS.getType().equals(map.get("code"))){
                return R.ok().setData((Map)map.get("data"));
            }else{
                return R.fail((String)map.get("msg"));
            }
        } catch (Exception e) {
            e.printStackTrace();
            return R.fail();
        }
    }

    @PostMapping("pay/notify")
    public String nativeNotify(HttpServletRequest request, HttpServletResponse response, @RequestBody String jsonString){
        try {
            JSONObject jsonObject = JSONObject.parseObject(jsonString, Feature.OrderedField);
            log.info("支付完成通知===>{}",jsonString);
            //支付结果通知
            unionPayService.payNotify(jsonObject);

            //返回应答
            response.setStatus(200);
            return "SUCCESS";
        } catch (Exception e) {
            e.printStackTrace();
            response.setStatus(500);
            return "FAIL";
        }
    }



    @PostMapping("refund")
    public R refund(HttpServletRequest request, HttpServletResponse response, @RequestBody String jsonString){
        JSONObject jsonObject = JSONObject.parseObject(jsonString);
        //校验必传参数
        String[] notNullParams={"order_no","refund_no","order_total","refund_amount","notify_url"};
        Map<String, Object> checkResult = checkRequired(jsonObject, notNullParams);
        if(!checkResult.isEmpty()){
            return R.fail((String)checkResult.get("msg"));
        }
        log.info("申请退款请求===>{}",jsonString);
        //商户订单号
        String orderNo = (String)jsonObject.get("order_no");
        //退款单号
        String refundNo = (String)jsonObject.get("refund_no");
        //订单金额
        String orderTotal = (String)jsonObject.get("order_total");
        //退款金额
        String refundAmount = (String)jsonObject.get("refund_amount");
        //退款原因
        String reason = (String)jsonObject.get("reason");
        //退款结果通知url
        String notifyUrl = (String)jsonObject.get("notify_url");

        try {
            //申请退款
            Map<String,Object> map = unionPayService.refund(orderNo,refundNo,orderTotal,refundAmount,reason,notifyUrl);
            if(UnionRepCode.SUCCESS.getType().equals(map.get("code"))){
                return R.ok().setData((Map)map.get("data"));
            }else{
                return R.fail((String)map.get("msg"));
            }
        } catch (Exception e) {
            e.printStackTrace();
            return R.fail();
        }
    }

    @PostMapping("refund/query")
    public R refundQuery(HttpServletRequest request, HttpServletResponse response, @RequestBody String jsonString){
        JSONObject jsonObject = JSONObject.parseObject(jsonString);
        //校验必传参数
        String[] notNullParams={"refund_no"};
        Map<String, Object> checkResult = checkRequired(jsonObject, notNullParams);
        if(!checkResult.isEmpty()){
            return R.fail((String)checkResult.get("msg"));
        }
        log.info("查询订单支付状态请求===>{}",jsonString);
        try {
            //查询申请退款状态
            Map<String,Object> map = unionPayService.refundQuery((String)jsonObject.get("refund_no"));
            if(UnionRepCode.SUCCESS.getType().equals(map.get("code"))){
                return R.ok().setData((Map)map.get("data"));
            }else{
                return R.fail((String)map.get("msg"));
            }
        } catch (Exception e) {
            e.printStackTrace();
            return R.fail();
        }
    }

    @PostMapping("refund/query-ext")
    public R refundQueryExt(HttpServletRequest request, HttpServletResponse response, @RequestBody String jsonString){
        JSONObject jsonObject = JSONObject.parseObject(jsonString);
        //校验必传参数
        String[] notNullParams={"order_no"};
        Map<String, Object> checkResult = checkRequired(jsonObject, notNullParams);
        if(!checkResult.isEmpty()){
            return R.fail((String)checkResult.get("msg"));
        }
        log.info("查询订单支付状态请求===>{}",jsonString);
        //商户订单号
        String orderNo = (String)jsonObject.get("order_no");
        //退款单号
        String offset = (String)jsonObject.get("offset");
        try {
            //查询申请退款状态
            Map<String,Object> map = unionPayService.refundQueryExt(orderNo,offset);
            if(UnionRepCode.SUCCESS.getType().equals(map.get("code"))){
                return R.ok().setData((Map)map.get("data"));
            }else{
                return R.fail((String)map.get("msg"));
            }
        } catch (Exception e) {
            e.printStackTrace();
            return R.fail();
        }
    }

    @PostMapping("refund/notify")
    public String payNotify(HttpServletRequest request, HttpServletResponse response, @RequestBody String jsonString){
        try {
            JSONObject jsonObject = JSONObject.parseObject(jsonString, Feature.OrderedField);
            log.info("退款完成通知===>{}",jsonString);
            //支付结果通知
            unionPayService.refundNotify(jsonObject);

            //返回应答
            response.setStatus(200);
            return "SUCCESS";
        } catch (Exception e) {
            e.printStackTrace();
            response.setStatus(500);
            return "FAIL";
        }
    }

    private Map<String,Object> checkRequired(JSONObject jsonObject, String[] params){
        Map<String,Object> rtn = new HashMap<>();
        for(String param:params){
            if(jsonObject.get(param)==null){
                rtn.put("code", "500");
                rtn.put("msg", "缺少参数"+"[" + param + "]");
                break;
            }
        }
        return rtn;
    }
}
