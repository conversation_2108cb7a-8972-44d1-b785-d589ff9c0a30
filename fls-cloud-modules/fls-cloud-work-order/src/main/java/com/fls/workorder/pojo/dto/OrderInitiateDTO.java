package com.fls.workorder.pojo.dto;

import com.fasterxml.jackson.annotation.JsonUnwrapped;
import java.io.Serializable;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * 工单发起DTO
 *
 * <AUTHOR>
 * @since 2024-12-27
 */
@Data
public class OrderInitiateDTO implements Serializable {

    private static final long serialVersionUID = 5429187099135432691L;

    /**
     * 资源id
     */
    @NotBlank(message = "工单资源id不能为空")
    private String idResource;
    /**
     * 工单发起人
     */
    @NotBlank(message = "发起人不能为空")
    private String initiator;

    /**
     * 工单名称
     */
    @NotBlank(message = "工单名称不能为空")
    private String orderName;

    /**
     * 来源系统标识
     */
    @NotBlank(message = "来源系统标识不能为空")
    private String projectCode;

    /**
     * 备注说明
     */
    private String remarks;

    /**
     * 工单实例数据
     */
    @NotNull(message = "工单实例数据不能为空")
    private Object data;

    /**
     * 交易类型编码
     */
    private String transCode;

    /**
     * 工单实例id
     */
    @NotBlank(message = "工单实例id不能为空")
    private String idInstance;

    /**
     * 下一个工单实例id
     */
    private String idNextInstance;

    /**
     * 工单截止完成时间
     */
    private String deadline;

    /**
     * 单据所属信息
     */
    @JsonUnwrapped
    private BillMetaInfo billMetaInfo;
}