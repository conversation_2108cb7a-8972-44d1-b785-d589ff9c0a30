package com.fls.workorder.pojo.vo;

import lombok.Data;

import java.util.List;

/**
 * 工单记录视图对象
 *
 * <AUTHOR>
 * @since 2024-12-31
 */
@Data
public class WorkorderRecordVo {
    /**
     * 工单实例id
     */
    private String idInstance;
    /**
     * 工单资源id
     */
    private String idResource;
    /**
     * 工单记录主键
     */
    private String idWorkorderRecord;
    /**
     * 工单发起时间
     */
    private String initiateTime;

    /**
     * 工单结束时间
     */
    private String endTime;

    /**
     * 工单编号
     */
    private String orderCode;

    /**
     * 工单发起人
     */
    private String initiator;
    /**
     * 来源系统标识
     */
    private String projectName;
    /**
     * 工单备注
     */
    private String remarks;
    /**
     * 工单状态
     */
    private String status;
    /**
     * 工单实例名称
     */
    private String workorderName;
    /**
     * 工单需求完成时间
     */
    private String deadline;

    /**
     * 流程实例id
     */
    private String idProcIns;

    /**
     * 工单任务列表
     */
    private List<TaskRecordDetailVo> tasks;
}
