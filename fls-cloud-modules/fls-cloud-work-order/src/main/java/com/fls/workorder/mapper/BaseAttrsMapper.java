package com.fls.workorder.mapper;

import com.fls.workorder.pojo.dto.BaseAttrsDTO;
import com.fls.workorder.pojo.dto.UserNameDTO;
import java.util.List;
import java.util.Set;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 工单任务信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-27
 */
@Mapper
public interface BaseAttrsMapper {

    /**
     * 根据用户ID查询基础属性
     *
     * @param userId 用户ID
     * @return 基础属性DTO对象
     */
    BaseAttrsDTO queryBaseAttrsByUserId(@Param("userId") String userId);

    /**
     * 根据用户ID查询工作组ID
     *
     * @param userId 用户ID
     * @return 工作组ID
     */
    List<String> queryWorkTeamIdByUserId(@Param("userId") String userId);

    /**
     * 通过班组id查询基础信息
     *
     * @param idWorkteam 班组id
     * @return 基础信息
     */
    BaseAttrsDTO queryBaseAttrsByTeamId(@Param("idWorkteam") String idWorkteam);

    /**
     * 通过部门id查询基础信息
     *
     * @param idDept 部门id
     * @return 基础信息
     */
    BaseAttrsDTO queryBaseAttrsByDeptId(@Param("idDept") String idDept);

    /**
     * 通过经营主体id查询基础信息
     *
     * @param idBiz 经营主体id
     * @return 基础信息
     */
    BaseAttrsDTO queryBaseAttrsByBizId(@Param("idBiz") String idBiz);

    /**
     * 批量查询用户名称
     *
     * @param idsUser 用户id列表
     * @return 用户名称映射
     */
    List<UserNameDTO> queryNamesByIds(@Param("idsUser") Set<String> idsUser);
}
