package com.fls.workorder.convert;

import com.fls.todo.api.enums.TodoTaskStatusEnum;
import com.fls.todo.api.model.TodoMessage;
import com.fls.workorder.entity.WorkorderTaskDefinitionEntity;
import com.fls.workorder.entity.WorkorderTaskRecordEntity;
import com.fls.workorder.enums.TaskTypeEnum;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.ReportingPolicy;

/**
 * WorkflowConvert
 *
 * <AUTHOR>
 * @since 2024-12-28
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface TodoMessageConvert {

    @Mappings({
        @Mapping(source = "taskRecord.sourceProjectName", target = "sourceProjectName"),
        @Mapping(source = "definition.visitPath", target = "visitPath"),
        @Mapping(source = "definition.visitParam", target = "visitParam"),
        @Mapping(source = "definition.appletVisitPath", target = "appletVisitPath"),
        @Mapping(source = "definition.appletVisitParam", target = "appletVisitParam"),
        @Mapping(source = "taskRecord.idWorkorderTaskRecord", target = "idSourceRecord"),
        @Mapping(source = "taskRecord.taskCode", target = "taskCode"),
        @Mapping(source = "definition.idTaskResource", target = "idResource"),
        @Mapping(source = "definition.workorderTaskName", target = "todoTaskName"),
        @Mapping(source = "taskRecord.assigneeType", target = "assigneeType"),
        @Mapping(source = "taskRecord.idsAssignee", target = "assignees"),
        @Mapping(source = "taskRecord.idsCandidate", target = "candidates"),
        @Mapping(source = "taskRecord.idsCoOperator", target = "coOperators"),
        @Mapping(source = "taskRecord.idBizUnit", target = "idBizunit"),
        @Mapping(source = "taskRecord.startTime", target = "startTime"),
        @Mapping(source = "taskRecord.endTime", target = "endTime"),
        @Mapping(source = "taskRecord.createBy", target = "initiator"),
        @Mapping(source = "taskRecord.createBy", target = "createBy"),
        @Mapping(source = "taskRecord.updateBy", target = "updateBy")
    })
    TodoMessage orderTaskToMessage(WorkorderTaskRecordEntity taskRecord, WorkorderTaskDefinitionEntity definition);

    @AfterMapping
    default void afterMapping(WorkorderTaskRecordEntity taskRecord, @MappingTarget TodoMessage message) {
        message.setTaskStatus(TodoTaskStatusEnum.PENDING_HANDLE.getCode());
        message.setTaskType(TaskTypeEnum.ORDER.getCode());
    }
}
