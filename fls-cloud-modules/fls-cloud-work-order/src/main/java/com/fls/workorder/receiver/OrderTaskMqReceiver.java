package com.fls.workorder.receiver;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.fls.todo.api.constant.MessageMqConstant;
import com.fls.todo.api.model.OrderTaskMessage;
import com.fls.workorder.pojo.dto.OrderCreateTaskDTO;
import com.fls.workorder.service.IWorkorderManagerService;
import com.fls.workorder.service.IWorkorderTaskRecordService;
import com.rabbitmq.client.Channel;
import java.io.IOException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;

/**
 * 消息接收处理逻辑，暂时先移除，统一改为同步逻辑
 *
 * <AUTHOR>
 * @since 2025-02-06
 */
@Slf4j
@RequiredArgsConstructor
public class OrderTaskMqReceiver {

    private final IWorkorderTaskRecordService workorderTaskRecordService;

    private final IWorkorderManagerService workorderManagerService;

    @RabbitListener(bindings = @QueueBinding(value = @Queue(value = MessageMqConstant.WORKORDER_TASK_QUEUE, durable = "true"),
            exchange = @Exchange(value = MessageMqConstant.WORKORDER_TASK_EXCHANGE),
            key = MessageMqConstant.WORKORDER_TASK_TOPIC))
    public void onMessage(Message message, Channel channel) {
        long deliveryTag = message.getMessageProperties().getDeliveryTag();
        try {
            Thread.sleep(5000);
            OrderTaskMessage taskMessage = JSONUtil.toBean(new String(message.getBody()), OrderTaskMessage.class);
            if (StrUtil.equals(taskMessage.getEventName(), "end")) {
                workorderManagerService.completeByProcIns(taskMessage.getIdProcInst());
                log.info("received complete task message consumer success, record procInsId: {}", taskMessage.getIdProcInst());
            }
            else {
                OrderCreateTaskDTO orderCreateTaskDTO = new OrderCreateTaskDTO();
                BeanUtil.copyProperties(taskMessage, orderCreateTaskDTO);
                String orderTask = workorderTaskRecordService.createOrderTask(orderCreateTaskDTO);
                log.info("received task message consumer success,create order taskId: {}", orderTask);
            }
            channel.basicAck(deliveryTag, false);
        }
        catch (Exception e) {
            try {
                // 拒绝消息并直接丢弃消息，暂时未引入死信队列，后续可以考虑使用死信队列进行处理
                channel.basicNack(deliveryTag, false, false);
            }
            catch (IOException ioException) {
                log.error("Failed to nack message: {}", ioException.getMessage());
            }
            log.error("received task message consumer failed, deliveryTag: {},error msg: {}", deliveryTag, e.getMessage());
        }
    }
}