package com.fls.workorder.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fls.common.mybatis.core.page.PageResult;
import com.fls.workorder.convert.WorkorderRecordConvert;
import com.fls.workorder.entity.WorkorderRecordEntity;
import com.fls.workorder.mapper.WorkorderRecordMapper;
import com.fls.workorder.pojo.query.WorkorderPageQuery;
import com.fls.workorder.pojo.vo.WorkorderRecordVo;
import com.fls.workorder.service.IWorkorderRecordService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 工单记录信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-27
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class WorkorderRecordServiceImpl extends ServiceImpl<WorkorderRecordMapper, WorkorderRecordEntity> implements IWorkorderRecordService {

    private final WorkorderRecordConvert recordConvert;

    @Override
    public PageResult<WorkorderRecordVo> queryWorkOrderPage(WorkorderPageQuery pageQuery) {
        Page<WorkorderRecordEntity> page = new Page<>(pageQuery.getPageNo(), pageQuery.getPageSize());
        Page<WorkorderRecordEntity> recordEntities = lambdaQuery()
                .eq(pageQuery.getStatus() != null, WorkorderRecordEntity::getWorkorderStatus, pageQuery.getStatus())
                .eq(pageQuery.getIdResource() != null, WorkorderRecordEntity::getIdWorkorderResource, pageQuery.getIdResource())
                .like(pageQuery.getWorkorderName() != null, WorkorderRecordEntity::getWorkorderInstanceName, pageQuery.getWorkorderName())
                .eq(pageQuery.getInitiator() != null, WorkorderRecordEntity::getInitiator, pageQuery.getInitiator())
                .between(WorkorderRecordEntity::getCreateTime, pageQuery.getBegin() + " 00:00:00", pageQuery.getEnd() + " 23:59:59").page(page);
        List<WorkorderRecordVo> workorderRecordVos = recordConvert.recordEntityToVoList(recordEntities.getRecords());
        return new PageResult<>(page, workorderRecordVos);
    }

    @Override
    public WorkorderRecordEntity queryWorkOrderByProcIns(String procInsId) {
        if (procInsId == null) {
            throw new IllegalArgumentException("procInsId cannot be null");
        }
        return lambdaQuery().eq(WorkorderRecordEntity::getIdProcinst, procInsId).one();
    }
}
