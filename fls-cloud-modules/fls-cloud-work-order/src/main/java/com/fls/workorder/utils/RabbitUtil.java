package com.fls.workorder.utils;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.fls.todo.api.constant.MessageMqConstant;
import com.fls.todo.api.model.TodoMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;

/**
 * RabbitUtil
 *
 * <AUTHOR>
 * @since 2025-01-17
 */
@Slf4j
public class RabbitUtil {

    public static void sendTodoMessage(TodoMessage todoMessage) {
        try {
            RabbitTemplate rabbitTemplate = SpringUtil.getBean(RabbitTemplate.class);
            if (ObjectUtil.isNull(rabbitTemplate)) {
                log.warn("not found rabbitTemplate bean, return logic.");
            }
            rabbitTemplate.convertAndSend(MessageMqConstant.TODO_MESSAGE_EXCHANGE, MessageMqConstant.TODO_MESSAGE_TOPIC, JSONUtil.toJsonStr(todoMessage));
        }
        catch (Exception e) {
            //做错误日志登记，此处不做事务回滚
            log.error("failed to send todo message, error:{}, taskId:{}", e.getMessage(), todoMessage.getIdSourceRecord());
        }
    }
}
