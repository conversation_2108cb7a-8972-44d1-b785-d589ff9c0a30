package com.fls.workorder.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fls.workorder.entity.WorkorderOperationEntity;
import com.fls.workorder.pojo.query.WorkorderOperationQuery;
import com.fls.workorder.pojo.vo.WorkorderOperationVo;
import java.util.List;

/**
 * <p>
 * 工单流转历史记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-27
 */
public interface IWorkorderOperationService extends IService<WorkorderOperationEntity> {
    /**
     * 根据工单ID查询历史记录
     *
     * @param idQuery 记录ID
     * @return 历史记录列表
     */
    List<WorkorderOperationVo> queryOrderOperationsById(WorkorderOperationQuery idQuery);

    /**
     * 根据工单ID查询历史记录
     *
     * @param workorderId 记录ID
     * @return 历史记录列表
     */
    List<WorkorderOperationVo> queryWorkOrderOperationsById(String workorderId);

    /**
     * 根据工单ID查询历史记录
     *
     * @param workorderTaskId 记录ID
     * @return 历史记录列表
     */
    List<WorkorderOperationVo> queryWorkorderTaskOperationsById(String workorderTaskId);

    /**
     * 删除操作日志记录
     *
     * @param id 日志记录ID
     */
    void deleteOperationById(String id);
}
