package com.fls.workorder.pojo.query;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 基础权限查询
 *
 * <AUTHOR>
 * @since 2025-08-07
 */
@Data
public class BaseAuthQuery {
    @Schema(description = "用户id")
    private String userId;

    @Schema(description = "菜单路由")
    private String href;

    @JsonIgnore
    private List<String> authBizunitIds;
}
