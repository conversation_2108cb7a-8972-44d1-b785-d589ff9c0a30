package com.fls.workorder.pojo.dto;

import cn.hutool.core.util.StrUtil;
import lombok.Data;

/**
 * BillMetaInfo 单据所属元数据信息
 *
 * <AUTHOR>
 * @since 2025-06-11
 */
@Data
public class BillMetaInfo {
    /**
     * 单据所属组织
     */
    private String idOrg;

    /**
     * 单据所属经营主体
     */
    private String idBizunit;

    /**
     * 单据所属部门
     */
    private String idDepartment;

    /**
     * 单据所属班组
     */
    private String idWorkteam;

    /**
     * 资产编号
     */
    private String assetCode;

    /**
     * 判断是否没有所属信息（组织、主体、部门、班组都为空）
     */
    public boolean isUnassigned() {
        return StrUtil.isAllBlank(idOrg, idBizunit, idDepartment, idWorkteam);
    }
}
