package com.fls.workorder.enums;

import cn.hutool.core.util.StrUtil;
import lombok.Getter;

/**
 * 任务提交类型
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Getter
public enum SubmitTypeEnum {
    /**
     * 主办提交
     */
    ASSIGNEE("0", "主办提交"),
    /**
     * 协办提交
     */
    COOPERATE("1", "协办提交");

    private final String type;

    private final String name;

    SubmitTypeEnum(String type, String name) {
        this.type = type;
        this.name = name;
    }

    public static String getName(String code){
        for(SubmitTypeEnum taskTypeEnum : SubmitTypeEnum.values()){
            if(taskTypeEnum.getType().equals(code)){
                return taskTypeEnum.getName();
            }
        }
        return StrUtil.EMPTY;
    }
}
