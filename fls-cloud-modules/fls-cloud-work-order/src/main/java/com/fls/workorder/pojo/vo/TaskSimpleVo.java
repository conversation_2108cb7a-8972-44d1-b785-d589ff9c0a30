package com.fls.workorder.pojo.vo;

import lombok.Data;

/**
 * 工单任务视图对象
 *
 * <AUTHOR>
 * @since 2024-12-31
 */
@Data
public class TaskSimpleVo {
    /**
     * 流程实例id
     */
    private String idProcInst;
    /**
     * 流程任务id
     */
    private String idProcTask;
    /**
     * 办理人id列表
     */
    private String idsAssignee;
    /**
     * 候选人id列表
     */
    private String idsCandidate;
    /**
     * 协办人id列表
     */
    private String idsCoOperator;
    /**
     * 工单记录id
     */
    private String idWorkorderRecord;
    /**
     * 工单任务记录id
     */
    private String idWorkorderTaskRecord;
    /**
     * 工单任务状态，{@link com.fls.workorder.enums.TaskStatusEnum}
     */
    private Integer taskStatus;
    /**
     * 工单状态，{@link com.fls.workorder.enums.OrderStatusEnum}
     */
    private Integer orderStatus;
    /**
     * 工单任务资源id
     */
    private String idTaskResource;
}
