package com.fls.workorder.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fls.common.mybatis.core.page.PageResult;
import com.fls.workorder.entity.WorkorderRecordEntity;
import com.fls.workorder.pojo.query.WorkorderPageQuery;
import com.fls.workorder.pojo.vo.WorkorderRecordVo;

/**
 * <p>
 * 工单记录信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-27
 */
public interface IWorkorderRecordService extends IService<WorkorderRecordEntity> {
    /**
     * 查询工单详情分页
     * @param pageQuery 工单查询参数
     * @return 工单详情分页结果
     */
    PageResult<WorkorderRecordVo> queryWorkOrderPage(WorkorderPageQuery pageQuery);

    /**
     * 根据流程实例ID查询工单详情
     * @param procInsId 流程实例ID
     * @return 工单详情实体
     */
    WorkorderRecordEntity queryWorkOrderByProcIns(String procInsId);
}
