package com.fls.workorder.service;

import com.fls.workorder.entity.BaseAttrsEntity;
import com.fls.workorder.pojo.dto.BillMetaInfo;
import com.fls.workorder.pojo.dto.UserNameDTO;
import java.util.List;
import java.util.Set;

/**
 * 工单管理服务类
 *
 * <AUTHOR>
 * @since 2024-12-27
 */
public interface IBaseAttrsService {
    /**
     * 填充基础属性信息,通过发起人所在组织主体
     *
     * @param attrsEntity 基础属性实体对象
     * @param userId      用户ID
     */
    void fillBaseAttrs(BaseAttrsEntity attrsEntity, String userId);

    /**
     * 填充基础属性信息，通过单据所属信息
     *
     * @param attrsEntity  基础属性对象
     * @param billMetaInfo 单据所属信息
     * @param initiator 单据发起人
     */
    void fillBaseAttrs(BaseAttrsEntity attrsEntity, BillMetaInfo billMetaInfo, String initiator);

    /**
     * 通过id列表查询用户名称
     *
     * @param idsUser 用户id列表
     * @return 用户名称映射
     */
    List<UserNameDTO> queryNamesByIds(Set<String> idsUser);
}
