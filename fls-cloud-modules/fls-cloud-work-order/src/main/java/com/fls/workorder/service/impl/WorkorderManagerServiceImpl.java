package com.fls.workorder.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.fls.common.core.exception.ServiceException;
import com.fls.master.api.RemoteResourceService;
import com.fls.master.api.model.ResourceInfo;
import com.fls.upms.api.RemoteUserService;
import com.fls.workflow.api.RemoteWorkflowService;
import com.fls.workflow.api.enums.ProcessStatusEnum;
import com.fls.workflow.api.model.InitiateProcessReq;
import com.fls.workflow.api.model.InitiateProcessRsp;
import com.fls.workflow.api.model.StopProcessReq;
import com.fls.workflow.api.model.TaskInfo;
import com.fls.workorder.convert.OrderTaskRecordConvert;
import com.fls.workorder.convert.ProcessConvert;
import com.fls.workorder.convert.WorkorderRecordConvert;
import com.fls.workorder.entity.WorkorderRecordEntity;
import com.fls.workorder.enums.OperationTypeEnum;
import com.fls.workorder.enums.OrderStatusEnum;
import com.fls.workorder.pojo.dto.OperationAddDTO;
import com.fls.workorder.pojo.dto.OrderInitiateDTO;
import com.fls.workorder.pojo.dto.OrderTerminateDTO;
import com.fls.workorder.pojo.dto.UserNameDTO;
import com.fls.workorder.pojo.vo.OrderInitiateVo;
import com.fls.workorder.pojo.vo.TaskRecordDetailVo;
import com.fls.workorder.pojo.vo.WorkorderRecordVo;
import com.fls.workorder.service.IBaseAttrsService;
import com.fls.workorder.service.IOperationProduceService;
import com.fls.workorder.service.IWorkorderManagerService;
import com.fls.workorder.service.IWorkorderRecordService;
import com.fls.workorder.service.IWorkorderTaskRecordService;
import com.fls.workorder.system.TaskThreadPoolExecutor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 工单管理服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WorkorderManagerServiceImpl implements IWorkorderManagerService {

    @DubboReference
    private RemoteResourceService remoteResourceService;

    @DubboReference
    private RemoteWorkflowService remoteWorkflowService;

    private final IWorkorderRecordService workorderRecordService;

    private final IWorkorderTaskRecordService workorderTaskRecordService;

    private final ProcessConvert processConvert;

    private final WorkorderRecordConvert workorderRecordConvert;

    private final OrderTaskRecordConvert taskRecordConvert;

    private final TaskThreadPoolExecutor taskThreadPoolExecutor;

    private final IBaseAttrsService baseAttrsService;

    private final IOperationProduceService produceService;

    @DubboReference
    private RemoteUserService remoteUserService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OrderInitiateVo initiate(OrderInitiateDTO initiateDTO) {
        String idResource = initiateDTO.getIdResource();
        //资源id获取工单资源信息
        ResourceInfo resourceInfo = remoteResourceService.getResourceById(idResource);
        if (ObjectUtil.isNull(resourceInfo)) {
            throw new RuntimeException("工单资源不存在");
        }
        //发起工单流程
        InitiateProcessReq initiateProcessReq = processConvert.orderToProcessReq(initiateDTO, resourceInfo.getCode());
        InitiateProcessRsp startProcessRsp = remoteWorkflowService.startProcess(initiateProcessReq);
        log.info("执行发起工单流程操作，返回结果：{}", startProcessRsp);
        if (ObjectUtil.isNull(startProcessRsp) || startProcessRsp.getStatus() != ProcessStatusEnum.RUNNING.getCode()) {
            throw new RuntimeException("发起工单流程失败");
        }
        WorkorderRecordEntity workorderRecordEntity = workorderRecordConvert.mergedToEntity(initiateDTO, resourceInfo, startProcessRsp);
        baseAttrsService.fillBaseAttrs(workorderRecordEntity, initiateDTO.getBillMetaInfo(), initiateDTO.getInitiator());
        //工单编号
        String orderCode = remoteResourceService.genBIllCode(idResource);
        workorderRecordEntity.setOrderCode(orderCode);
        //工单记录保存
        workorderRecordService.save(workorderRecordEntity);
        taskThreadPoolExecutor.putTask(() -> {
            produceService.produceOrderOperation(OperationTypeEnum.INITIATE.getTitle(), workorderRecordEntity, initiateDTO.getInitiator());
        });
        OrderInitiateVo.OrderInitiateVoBuilder builder = OrderInitiateVo.builder();
        builder.idWorkorderRecord(workorderRecordEntity.getIdWorkorderRecord());
        // 创建工单任务
        TaskInfo taskInfo = startProcessRsp.getTaskInfo();
        if (ObjectUtil.isNotNull(taskInfo)) {
            String idTask = workorderTaskRecordService.createOrderTask(taskInfo, initiateDTO.getIdNextInstance());
            builder.idWorkorderTask(idTask);
        }
        return builder.build();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void terminate(OrderTerminateDTO terminateDTO) {
        // 校验工单记录
        WorkorderRecordEntity workorderRecord = validateWorkorderRecord(terminateDTO.getIdWorkorderRecord());
        // 校验工单流程
        String idProcInst = workorderRecord.getIdProcinst();
        if (ObjectUtil.isNull(idProcInst)) {
            throw new ServiceException("工单流程不存在");
        }
        // 终止工单流程
        String operator = terminateDTO.getOperator();
        stopWorkOrderProcess(idProcInst, operator);
        // 终止工单任务
        terminateWorkOrderTask(workorderRecord, operator);
        // 更新工单记录状态
        updateWorkOrderStatus(workorderRecord, OrderStatusEnum.TERMINATE);
        taskThreadPoolExecutor.putTask(() -> {
            produceService.produceOrderOperation(OperationTypeEnum.TERMINATE.getTitle(), workorderRecord, operator, null, terminateDTO.getRemarks());
        });
    }

    @Override
    public void complete(String workorderId) {
        // 校验工单记录
        WorkorderRecordEntity workorderRecord = validateWorkorderRecord(workorderId);
        // 更新工单记录状态
        updateWorkOrderStatus(workorderRecord, OrderStatusEnum.END);
        taskThreadPoolExecutor.putTask(() -> {
            produceService.produceOrderOperation(OperationTypeEnum.END.getTitle(), workorderRecord, workorderRecord.getCreateBy());
        });
    }

    @Override
    public void completeByProcIns(String procInsId) {
        if (StrUtil.isEmpty(procInsId)) {
            throw new ServiceException("流程实例ID不能为空");
        }
        WorkorderRecordEntity workorderRecord = workorderRecordService.queryWorkOrderByProcIns(procInsId);
        if (ObjectUtil.isNull(workorderRecord)) {
            throw new ServiceException("工单记录不存在");
        }
        updateWorkOrderStatus(workorderRecord, OrderStatusEnum.END);
        taskThreadPoolExecutor.putTask(() -> {
            produceService.produceOrderOperation(OperationTypeEnum.END.getTitle(), workorderRecord, workorderRecord.getCreateBy());
        });
    }

    private WorkorderRecordEntity validateWorkorderRecord(String workorderRecordId) {
        WorkorderRecordEntity workorderRecord = workorderRecordService.getById(workorderRecordId);
        if (ObjectUtil.isNull(workorderRecord)) {
            throw new ServiceException("工单记录不存在");
        }
        return workorderRecord;
    }

    private void stopWorkOrderProcess(String idProcInst, String operator) {
        StopProcessReq stopProcessReq = new StopProcessReq();
        stopProcessReq.setOperator(operator);
        stopProcessReq.setProcInsId(idProcInst);
        try {
            remoteWorkflowService.stopProcess(stopProcessReq);
        } catch (RuntimeException e) {
            log.error("终止工单流程失败，流程ID: {}, 操作员: {}", idProcInst, operator, e);
            throw new ServiceException("终止工单流程失败: " + e.getMessage());
        }
    }

    private void terminateWorkOrderTask(WorkorderRecordEntity workorderRecord, String operator) {
        if (workorderRecord.getWorkorderStatus() == OrderStatusEnum.PROGRESS.getCode()) {
            workorderTaskRecordService.terminateByWorkId(workorderRecord.getIdWorkorderRecord(), operator);
        }
    }

    @Override
    public void updateWorkOrderStatus(WorkorderRecordEntity workorderRecord, OrderStatusEnum orderStatusEnum) {
        workorderRecord.setWorkorderStatus(orderStatusEnum.getCode());
        workorderRecordService.updateById(workorderRecord);
    }

    @Override
    public void addWorkorderOperation(OperationAddDTO operationAddDTO) {
        String idRecord = operationAddDTO.getIdRecord();
        String operator = operationAddDTO.getOperator();
        String title = operationAddDTO.getTitle();
        String idLink = operationAddDTO.getIdLink();
        String remark = operationAddDTO.getRemark();
        // 执行相应的操作
        if (operationAddDTO.getType() == 1) {
            produceService.produceOrderOperation(title, workorderRecordService.getById(idRecord), operator, idLink, remark);
        } else {
            produceService.produceTaskOperation(title, workorderTaskRecordService.getById(idRecord), operator, idLink, remark);
        }
    }

    @Override
    public WorkorderRecordVo queryWorkOrderDetail(String id) {
        WorkorderRecordEntity workorderRecordEntity = workorderRecordService.getById(id);
        if (ObjectUtil.isNull(workorderRecordEntity)) {
            throw new ServiceException("查询失败，工单记录不存在.");
        }
        WorkorderRecordVo workorderRecordVo = workorderRecordConvert.entityToVo(workorderRecordEntity);
        List<TaskRecordDetailVo> taskRecordVos = workorderTaskRecordService.voListByTaskId(id);
        if (CollUtil.isNotEmpty(taskRecordVos)) {
            enrichTaskRecordUserNames(taskRecordVos);
        }
        workorderRecordVo.setTasks(taskRecordVos);
        return workorderRecordVo;
    }

    /**
     * 补充任务记录中的用户名称（主办人、候选人、协办人）
     *
     * @param taskVos 任务记录列表
     */
    public void enrichTaskRecordUserNames(List<TaskRecordDetailVo> taskVos) {
        // 收集所有用户 ID
        Set<String> allUserIds = taskVos.stream().flatMap(taskVo -> {
            Set<String> ids = new HashSet<>();
            if (StrUtil.isNotBlank(taskVo.getIdsAssignee())) {
                ids.add(taskVo.getIdsAssignee());
            }
            ids.addAll(StrUtil.split(taskVo.getIdsCandidate(), ","));
            ids.addAll(StrUtil.split(taskVo.getIdsCoOperator(), ","));
            return ids.stream();
        }).collect(Collectors.toSet());
        // 查询用户 ID → 姓名映射
        List<UserNameDTO> userIdNames = baseAttrsService.queryNamesByIds(allUserIds);
        if (ObjectUtil.isNotEmpty(userIdNames)) {
            Map<String, String> nameMaps = userIdNames.stream().collect(Collectors.toMap(UserNameDTO::getIdUser, UserNameDTO::getName));
            // 设置任务人员名称
            for (TaskRecordDetailVo taskVo : taskVos) {
                taskVo.setAssigneeName(nameMaps.get(taskVo.getIdsAssignee()));
                taskVo.setCandidateNames(joinUserNames(taskVo.getIdsCandidate(), nameMaps));
                taskVo.setCoOperatorNames(joinUserNames(taskVo.getIdsCoOperator(), nameMaps));
            }
        }
    }

    private String joinUserNames(String idStr, Map<String, String> userIdNames) {
        return StrUtil.split(idStr, ",").stream().map(userIdNames::get).filter(StrUtil::isNotBlank).collect(Collectors.joining(","));
    }
}
