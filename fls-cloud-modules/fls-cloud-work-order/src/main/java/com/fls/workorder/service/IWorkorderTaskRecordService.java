package com.fls.workorder.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.fls.common.mybatis.core.page.PageResult;
import com.fls.todo.api.model.TodoTaskQuery;
import com.fls.todo.api.model.TodoTaskVo;
import com.fls.workflow.api.model.BackNodeInfo;
import com.fls.workflow.api.model.TaskInfo;
import com.fls.workorder.entity.WorkorderTaskRecordEntity;
import com.fls.workorder.pojo.dto.OrderCreateTaskDTO;
import com.fls.workorder.pojo.dto.TaskAddDTO;
import com.fls.workorder.pojo.dto.TaskAssigneeUpdateDTO;
import com.fls.workorder.pojo.dto.TaskBackNodeOptDTO;
import com.fls.workorder.pojo.dto.TaskBackNodeQueryDTO;
import com.fls.workorder.pojo.dto.TaskCandidateUpdateDTO;
import com.fls.workorder.pojo.dto.TaskClaimDTO;
import com.fls.workorder.pojo.dto.TaskCoOperatorUpdateDTO;
import com.fls.workorder.pojo.dto.TaskCompleteDTO;
import com.fls.workorder.pojo.dto.TaskFollowDTO;
import com.fls.workorder.pojo.dto.TaskSuspendDTO;
import com.fls.workorder.pojo.dto.TaskTerminateDTO;
import com.fls.workorder.pojo.dto.TaskTransferDTO;
import com.fls.workorder.pojo.query.OrderTaskPageQuery;
import com.fls.workorder.pojo.query.TaskCenterQuery;
import com.fls.workorder.pojo.vo.TaskCenterDetailVo;
import com.fls.workorder.pojo.vo.TaskRecordDetailVo;
import com.fls.workorder.pojo.vo.TaskRecordVo;
import com.fls.workorder.pojo.vo.TaskSimpleVo;

import java.util.List;

/**
 * <p>
 * 工单任务信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-27
 */
public interface IWorkorderTaskRecordService extends IService<WorkorderTaskRecordEntity> {
    /**
     * 根据工单ID查询任务记录
     *
     * @param workorderId 工单ID
     * @return 任务记录列表
     */
    List<WorkorderTaskRecordEntity> listByWorkorderId(String workorderId);

    /**
     * 通过工单id查询任务的vo列表（带路由信息）
     *
     * @param workorderId 工单id
     * @return 任务vo列表
     */
    List<TaskRecordDetailVo> voListByTaskId(String workorderId);

    /**
     * 根据工单ID终止任务记录
     *
     * @param workorderId 工单ID
     * @param operator    操作人
     */
    void terminateByWorkId(String workorderId, String operator);

    /**
     * 查询工单任务记录分页
     *
     * @param pageQuery 分页查询条件
     * @return 分页结果
     */
    PageResult<TaskRecordVo> queryOrderTaskPage(OrderTaskPageQuery pageQuery);

    /**
     * 创建工单任务
     *
     * @param taskInfo       任务信息
     * @param taskInstanceId 任务实例ID
     * @return 工单任务id
     */
    String createOrderTask(TaskInfo taskInfo, String taskInstanceId);

    /**
     * 创建工单任务
     *
     * @param createTaskDTO 创建任务DTO
     * @return 工单任务id
     */
    String createOrderTask(OrderCreateTaskDTO createTaskDTO);

    /**
     * 添加工单任务
     *
     * @param taskAddDTO 添加任务DTO
     */
    String addOrderTask(TaskAddDTO taskAddDTO);

    /**
     * 认领工单任务
     *
     * @param claimDTO 认领任务DTO
     */
    void claimTask(TaskClaimDTO claimDTO);

    /**
     * 跟进工单任务
     *
     * @param followDTO 跟进任务DTO
     */
    void followTask(TaskFollowDTO followDTO);

    /**
     * 跟进工单任务
     *
     * @param transferDTO 跟进任务DTO
     * @return 任务概要信息
     */
    TaskSimpleVo transferTask(TaskTransferDTO transferDTO);

    /**
     * 挂起工单任务
     *
     * @param suspendDTO 挂起任务DTO
     */
    void suspendTask(TaskSuspendDTO suspendDTO);

    /**
     * 完成工单任务
     *
     * @param completeDTO 完成任务DTO
     */
    TaskRecordVo completeTask(TaskCompleteDTO completeDTO);

    /**
     * 终止工单任务
     *
     * @param terminateDTO 终止任务DTO
     * @return 任务概要信息
     */
    TaskSimpleVo terminateTask(TaskTerminateDTO terminateDTO);

    /**
     * 更新任务负责人
     *
     * @param assigneeUpdateDTO 更新任务负责人DTO
     * @return 任务概要信息
     */
    TaskSimpleVo updateAssignee(TaskAssigneeUpdateDTO assigneeUpdateDTO);

    /**
     * 更新任务协办人
     *
     * @param coOperatorUpdateDTO 更新任务协办人DTO
     */
    void updateCoOperator(TaskCoOperatorUpdateDTO coOperatorUpdateDTO);

    /**
     * 更新任务候选人
     *
     * @param candidateUpdateDTO 更新任务候选人DTO
     */
    void updateCandidate(TaskCandidateUpdateDTO candidateUpdateDTO);

    /**
     * 获取任务可驳回节点列表
     *
     * @return 节点列表
     */
    List<BackNodeInfo> getBackNodes(TaskBackNodeQueryDTO backNodeQueryDTO);

    /**
     * 驳回任务至指定节点
     *
     * @param backNodeOptDTO 驳回任务请求
     *
     * @return 任务信息
     */
    TaskRecordVo backNode(TaskBackNodeOptDTO backNodeOptDTO);

    /**
     * 待领任务查询
     *
     * @param query 查询参数
     * @return 待领任务列表
     */
    Page<TodoTaskVo> queryUnclaimedTasks(TodoTaskQuery query);

    /**
     * 任务中心分页查询
     *
     * @param query 查询参数
     * @return 分页结果
     */
    PageResult<TaskCenterDetailVo> queryTaskCenterPage(TaskCenterQuery query);

    /**
     * 通过工单任务id查询任务详情
     *
     * @param idTask 工单任务id
     * @return 任务详情
     */
    TaskRecordDetailVo votByTaskId(String idTask);
}
