package com.fls.workorder.entity;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 工单任务信息
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("t_workorder_task_record")
public class WorkorderTaskRecordEntity extends BaseAttrsEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 工单任务记录主键
     */
    @TableId(value = "id_workorder_task_record", type = IdType.ASSIGN_UUID)
    private String idWorkorderTaskRecord;

    /**
     * 工单记录id
     */
    private String idWorkorderRecord;

    /**
     * 工单任务定义id
     */
    private String idTaskDefinition;

    /**
     * 工单任务时效
     */
    private String taskLimit;

    /**
     * 工单任务资源id
     */
    private String idTaskResource;

    /**
     * 来源系统标识
     */
    private String sourceProjectName;

    /**
     * 工单任务描述信息（描述工单任务工作内容）
     */
    private String taskDescription;

    /**
     * 工单任务实例id
     */
    private String idTaskInstance;

    /**
     * 工单任务实例名称
     */
    private String taskInstanceName;

    /**
     * 流程任务id
     */
    private String idProctask;

    /**
     * 流程实例id
     */
    private String idProcinst;

    /**
     * 工单状态，0：新提交，1：进行中，2：已完成，3：已关闭
     */
    private int taskStatus;

    /**
     * 工单任务备注
     */
    private String taskRemarks;

    /**
     * 分配类型，0：系统，1：指派，2：认领
     */
    private String assigneeType;

    /**
     * 任务办理人
     */
    private String idsAssignee;

    /**
     * 任务候选人
     */
    private String idsCandidate;

    /**
     * 任务协办人
     */
    private String idsCoOperator;

    /**
     * 任务所有人
     */
    private String owner;

    /**
     * 任务提交人用户id
     */
    private String idSubmitter;

    /**
     * 任务提交类型
     */
    private String submitType;

    /**
     * 任务要求完成时间
     */
    private LocalDateTime deadline;

    /**
     * 任务开始时间
     */
    private LocalDateTime startTime;

    /**
     * 任务结束时间
     */
    private LocalDateTime endTime;

    /**
     * 工单任务提交数据
     */
    private String formJson;

    /**
     * 任务编号
     */
    private String taskCode;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 删除标记: 0-正常, 1-删除
     */
    private String deleteFlag;

    /**
     * 创建时间戳
     */
    private LocalDateTime ts;

    public boolean hasOnlyCandidate(){
        return StrUtil.isNotBlank(this.getIdsCandidate()) && StrUtil.isBlank(this.getIdsAssignee());
    }
}
