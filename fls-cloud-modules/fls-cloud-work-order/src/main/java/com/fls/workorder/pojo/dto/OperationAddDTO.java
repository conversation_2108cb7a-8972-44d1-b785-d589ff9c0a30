package com.fls.workorder.pojo.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 工单操作添加DTO
 *
 * <AUTHOR>
 * @since 2024-12-27
 */
@Data
public class OperationAddDTO {
    /**
     * 记录id
     */
    @NotBlank(message = "记录id不能为空")
    private String idRecord;
    /**
     * 操作时间，可选值，指定具体时间，或者不填取，后台取当前时间，时间格式yyyy-MM-dd HH:mm:ss，精确到秒
     */
    private String operateTime;
    /**
     * 操作人id
     */
    @NotBlank(message = "操作人id不能为空")
    private String operator;
    /**
     * 操作记录标题
     */
    @NotBlank(message = "记录标题不能为空")
    private String title;
    /**
     * 记录类型
     */
    @NotNull(message = "记录类型不能为空")
    private Integer type;
    /**
     * 附件idLink
     */
    private String idLink;

    /**
     * 操作说明
     */
    private String remark;
}
