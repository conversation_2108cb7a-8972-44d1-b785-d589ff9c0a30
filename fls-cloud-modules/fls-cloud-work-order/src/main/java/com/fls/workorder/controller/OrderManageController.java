package com.fls.workorder.controller;

import cn.hutool.core.lang.Dict;
import com.fls.common.core.domain.ResponseData;
import com.fls.common.mybatis.core.page.PageResult;
import com.fls.workorder.pojo.dto.OrderCompleteDTO;
import com.fls.workorder.pojo.dto.OrderInitiateDTO;
import com.fls.workorder.pojo.dto.OrderTerminateDTO;
import com.fls.workorder.pojo.query.WorkorderPageQuery;
import com.fls.workorder.pojo.vo.OrderInitiateVo;
import com.fls.workorder.pojo.vo.WorkorderRecordVo;
import com.fls.workorder.service.IWorkorderManagerService;
import com.fls.workorder.service.IWorkorderRecordService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 工单管理
 *
 * <AUTHOR>
 * @since 2024-12-27
 */
@Tag(name = "工单管理")
@Slf4j
@RestController
@RequestMapping("/workorder")
@RequiredArgsConstructor
public class OrderManageController {

    private final IWorkorderManagerService workorderManagerService;

    private final IWorkorderRecordService workorderRecordService;

    /**
     * 发起工单
     *
     * @param initiateDTO 发起工单DTO
     * @return 工单记录id响应
     */
    @PostMapping("/initiate")
    public ResponseData<OrderInitiateVo> initiate(@Valid @RequestBody OrderInitiateDTO initiateDTO) {
        OrderInitiateVo initiateVo = workorderManagerService.initiate(initiateDTO);
        String idWorkorderRecord = initiateVo.getIdWorkorderRecord();
        WorkorderRecordVo orderDetail = workorderManagerService.queryWorkOrderDetail(idWorkorderRecord);
        initiateVo.setOrderDetail(orderDetail);
        return ResponseData.ok(initiateVo);
    }

    /**
     * 工单终止
     * @param terminateDTO 终止工单DTO
     * @return 终止成功响应
     */
    @PostMapping("/terminate")
    public ResponseData<Void> terminate(@Valid @RequestBody OrderTerminateDTO terminateDTO) {
        workorderManagerService.terminate(terminateDTO);
        return ResponseData.ok();
    }

    /**
     * 工单完结
     * @param orderCompleteDTO 完结工单DTO
     * @return 完结成功响应
     */
    @PostMapping("/complete")
    public ResponseData<Void> complete(@Valid @RequestBody OrderCompleteDTO orderCompleteDTO) {
        String idWorkorderRecord = orderCompleteDTO.getIdWorkorderRecord();
        workorderManagerService.complete(idWorkorderRecord);
        return ResponseData.ok();
    }

    /**
     * 查询工单列表分页
     * @param pageQuery 分页查询条件
     * @return 工单列表分页响应
     */
    @PostMapping("/list")
    public ResponseData<PageResult<WorkorderRecordVo>> queryWorkOrderPage(@Valid @RequestBody WorkorderPageQuery pageQuery) {
        return ResponseData.ok(workorderRecordService.queryWorkOrderPage(pageQuery));
    }

    /**
     * 查询工单详情
     * @param detailQuery 详情查询参数
     * @return 工单详情信息
     */
    @PostMapping("/detail")
    public ResponseData<WorkorderRecordVo> queryWorkOrderPage(@RequestBody Dict detailQuery) {
        return ResponseData.ok(workorderManagerService.queryWorkOrderDetail(detailQuery.getStr("id")));
    }


}
