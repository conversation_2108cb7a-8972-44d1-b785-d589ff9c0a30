package com.fls.workorder.enums;

import cn.hutool.core.util.StrUtil;
import lombok.Getter;

/**
 * 工单任务状态
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Getter
public enum TaskStatusEnum {
    /**
     * 初提交
     */
    INIT(0, "初提交"),
    CLAIM(1, "待认领"),
    PROGRESS(2, "进行中"),
    SUSPEND(3, "已挂起"),
    TERMINATE(4, "已终止"),
    END(5, "已完成");

    private final int code;
    private final String status;

    TaskStatusEnum(int code, String status) {
        // 成员变量
        this.code = code;
        this.status = status;
    }

    public static boolean isSuspend(int taskStatus) {
        return taskStatus == SUSPEND.code;
    }

    public static String getDesc(int status) {
        for (TaskStatusEnum statusEnum : values()) {
            if (statusEnum.getCode() == status) {
                return statusEnum.getStatus();
            }
        }
        return StrUtil.EMPTY;
    }
}
