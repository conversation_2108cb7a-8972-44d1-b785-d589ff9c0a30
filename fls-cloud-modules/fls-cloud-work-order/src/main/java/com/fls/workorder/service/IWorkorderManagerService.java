package com.fls.workorder.service;

import com.fls.workorder.entity.WorkorderRecordEntity;
import com.fls.workorder.enums.OrderStatusEnum;
import com.fls.workorder.pojo.dto.OperationAddDTO;
import com.fls.workorder.pojo.dto.OrderInitiateDTO;
import com.fls.workorder.pojo.dto.OrderTerminateDTO;
import com.fls.workorder.pojo.vo.OrderInitiateVo;
import com.fls.workorder.pojo.vo.WorkorderRecordVo;

/**
 * 工单管理服务类
 *
 * <AUTHOR>
 * @since 2024-12-27
 */
public interface IWorkorderManagerService {
    /**
     * 发起工单
     * @param initiateDTO 工单发起参数
     * @return 工单记录id
     */
    OrderInitiateVo initiate(OrderInitiateDTO initiateDTO);

    /**
     * 终止工单
     * @param terminateDTO 工单终止参数
     */
    void terminate(OrderTerminateDTO terminateDTO);

    /**
     * 完成工单
     * @param workorderId 工单记录id
     */
    void complete(String workorderId);

    /**
     * 完成工单
     * @param procInsId 工单流程实例id
     */
    void completeByProcIns(String procInsId);

    /**
     * 更新工单状态
     * @param workorderRecord 工单记录
     * @param orderStatusEnum 工单状态枚举
     */
    void updateWorkOrderStatus(WorkorderRecordEntity workorderRecord, OrderStatusEnum orderStatusEnum);

    /**
     * 创建工单操作记录
     *
     * @param operationAddDTO 操作记录信息
     */
    void addWorkorderOperation(OperationAddDTO operationAddDTO);

    /**
     * 查询工单详情，因为日志操作对两个基础service查询产生了循环依赖，所以这里单独抽取出来
     * @param id 工单记录id
     * @return 工单记录详情
     */
    WorkorderRecordVo queryWorkOrderDetail(String id);
}
