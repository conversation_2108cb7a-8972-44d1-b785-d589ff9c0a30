package com.fls.workorder.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.fls.workorder.entity.BaseAttrsEntity;
import com.fls.workorder.mapper.BaseAttrsMapper;
import com.fls.workorder.pojo.dto.BaseAttrsDTO;
import com.fls.workorder.pojo.dto.BillMetaInfo;
import com.fls.workorder.pojo.dto.UserNameDTO;
import com.fls.workorder.service.IBaseAttrsService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * BaseAttrsServiceImpl
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Service
@RequiredArgsConstructor
public class BaseAttrsServiceImpl implements IBaseAttrsService {

    private final BaseAttrsMapper baseAttrsMapper;

    /**
     * 根据用户id更新实体，放在工单管理服务内，作为统一处理基础属性的方法，按职责单一原则，可能放在BaseAttrsEntity更合适
     *
     * @param attrsEntity 实体对象
     * @param userId      用户id
     */
    @Override
    public void fillBaseAttrs(BaseAttrsEntity attrsEntity, String userId) {
        //获取用户属性
        BaseAttrsDTO baseAttrsDTO = baseAttrsMapper.queryBaseAttrsByUserId(userId);
        //填充基础字段
        attrsEntity.setIdOrg(baseAttrsDTO.getIdOrg());
        attrsEntity.setIdBizUnit(baseAttrsDTO.getIdBizUnit());
        attrsEntity.setIdDepartment(baseAttrsDTO.getIdDepartment());
    }

    @Override
    public void fillBaseAttrs(BaseAttrsEntity attrsEntity, BillMetaInfo billMetaInfo, String initiator) {
        if (billMetaInfo.isUnassigned()) {
            fillBaseAttrs(attrsEntity, initiator);
        }
        String idOrg = billMetaInfo.getIdOrg();
        String idBizunit = billMetaInfo.getIdBizunit();
        String idDepartment = billMetaInfo.getIdDepartment();
        String idWorkteam = billMetaInfo.getIdWorkteam();
        BaseAttrsDTO baseAttrsDTO = null;
        if (StrUtil.isNotBlank(idWorkteam)) {
            // 优先使用班组填充
            baseAttrsDTO = baseAttrsMapper.queryBaseAttrsByTeamId(idWorkteam);
        }
        else if (StrUtil.isNotBlank(idDepartment)) {
            // 其次使用部门
            baseAttrsDTO = baseAttrsMapper.queryBaseAttrsByDeptId(idDepartment);
        }
        else if (StrUtil.isNotBlank(idBizunit)) {
            // 再其次使用主体
            baseAttrsDTO = baseAttrsMapper.queryBaseAttrsByBizId(idBizunit);
        }
        else if (StrUtil.isNotBlank(idOrg)) {
            // 最后使用组织，仅构造部分字段
            baseAttrsDTO = new BaseAttrsDTO();
            baseAttrsDTO.setIdOrg(idOrg);
        }

        if (ObjectUtil.isNotNull(baseAttrsDTO)) {
            attrsEntity.setIdWorkteam(baseAttrsDTO.getIdWorkteam());
            attrsEntity.setIdDepartment(baseAttrsDTO.getIdDepartment());
            attrsEntity.setIdBizUnit(baseAttrsDTO.getIdBizUnit());
            attrsEntity.setIdOrg(baseAttrsDTO.getIdOrg());
        }
        attrsEntity.setAssetCode(billMetaInfo.getAssetCode());
    }

    @Override
    public List<UserNameDTO> queryNamesByIds(Set<String> idsUser) {
        List<UserNameDTO> stringStringMap = baseAttrsMapper.queryNamesByIds(idsUser);
        return ObjectUtil.isNotNull(stringStringMap) ? stringStringMap : Collections.emptyList();
    }
}
