package com.fls.workorder.pojo.dto;

import javax.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 工单任务跟进DTO
 *
 * <AUTHOR>
 * @since 2024-12-31
 */
@Data
public class TaskFollowDTO {
    /**
     * 表单提交数据
     */
    private Object data;
    /**
     * 工单任务记录id
     */
    @NotBlank(message = "工单任务记录id不能为空")
    private String idTask;
    /**
     * 操作人id
     */
    @NotBlank(message = "操作人id不能为空")
    private String operator;
    /**
     * 来源项目名称
     */
    private String sourceProjectName;
    /**
     * 工单任务实例id
     */
    private String idTaskInstance;

    /**
     * 任务实例名称
     */
    private String taskInstanceName;
    /**
     * 任务备注
     */
    private String taskRemarks;

}
