package com.fls.workorder.pojo.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class TaskCompleteDTO {
    /**
     * 工单任务记录id
     */
    @NotBlank(message = "任务记录id不能为空")
    private String idTask;
    /**
     * 操作人id
     */
    @NotBlank(message = "操作人id不能为空")
    private String operator;
    /**
     * 操作数据
     */
    private Object data;
    /**
     * 来源项目名称
     */
    private String sourceProjectName;
    /**
     * 工单任务实例id
     */
    private String idTaskInstance;

    /**
     * 任务实例名称
     */
    private String taskInstanceName;
    /**
     * 任务备注
     */
    private String taskRemarks;

    /**
     * 下一个工单实例id
     */
    private String idNextInstance;

    /**
     * 完成说明
     */
    private String remark;
}
