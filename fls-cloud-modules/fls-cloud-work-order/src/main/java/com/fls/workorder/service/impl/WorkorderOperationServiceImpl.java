package com.fls.workorder.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fls.common.core.exception.ServiceException;
import com.fls.workorder.convert.WorkorderOperationConvert;
import com.fls.workorder.entity.WorkorderOperationEntity;
import com.fls.workorder.entity.WorkorderTaskRecordEntity;
import com.fls.workorder.enums.OperationTypeEnum;
import com.fls.workorder.mapper.WorkorderOperationMapper;
import com.fls.workorder.pojo.query.WorkorderOperationQuery;
import com.fls.workorder.pojo.vo.WorkorderOperationVo;
import com.fls.workorder.service.IWorkorderOperationService;
import com.fls.workorder.service.IWorkorderTaskRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 工单流转历史记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WorkorderOperationServiceImpl extends ServiceImpl<WorkorderOperationMapper, WorkorderOperationEntity> implements IWorkorderOperationService {

    private final WorkorderOperationConvert workorderOperationConvert;

    private final IWorkorderTaskRecordService workorderTaskRecordService;

    @Override
    public List<WorkorderOperationVo> queryOrderOperationsById(WorkorderOperationQuery idQuery) {
        String workOrderId = idQuery.getIdRecord();
        Integer queryType = idQuery.getType();
        return queryType == 1 ? queryWorkOrderOperationsById(workOrderId) : queryWorkorderTaskOperationsById(workOrderId);
    }

    @Override
    public List<WorkorderOperationVo> queryWorkOrderOperationsById(String workorderId) {
        List<WorkorderOperationEntity> workorderHistoryEntities = lambdaQuery().eq(WorkorderOperationEntity::getIdSourceRecord, workorderId).list();
        if (ObjectUtil.isEmpty(workorderHistoryEntities)) {
            return Collections.emptyList();
        }
        //查询工单任务流转记录
        List<WorkorderTaskRecordEntity> workorderTaskRecordEntities = workorderTaskRecordService.listByWorkorderId(workorderId);
        log.debug("query workorder task list result is：{}", workorderHistoryEntities);
        Set<String> collect = workorderTaskRecordEntities.stream().map(WorkorderTaskRecordEntity::getIdWorkorderTaskRecord).collect(Collectors.toSet());
        LambdaQueryWrapper<WorkorderOperationEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(CollectionUtil.isNotEmpty(collect), WorkorderOperationEntity::getIdSourceRecord, collect)
            .and(w -> w
                .eq(WorkorderOperationEntity::getOperationTitle, OperationTypeEnum.TASK_COMPLETE.getTitle())
                .or()
                .eq(WorkorderOperationEntity::getOperationTitle, OperationTypeEnum.TASK_END.getTitle())
            );
        List<WorkorderOperationEntity> operationEntities = baseMapper.selectList(wrapper);
        //合并集合并按照时间排序
        workorderHistoryEntities.addAll(operationEntities);
        List<WorkorderOperationVo> workorderOperationVos = workorderOperationConvert.EntityToVoList(workorderHistoryEntities);
        workorderOperationVos.sort(Comparator.comparing(WorkorderOperationVo::getStartTime));
        return workorderOperationVos;
    }

    @Override
    public List<WorkorderOperationVo> queryWorkorderTaskOperationsById(String workorderTaskId) {
        List<WorkorderOperationEntity> workorderHistoryEntities = lambdaQuery().eq(WorkorderOperationEntity::getIdSourceRecord, workorderTaskId).list();
        List<WorkorderOperationVo> workorderOperationVos = workorderOperationConvert.EntityToVoList(workorderHistoryEntities);
        workorderOperationVos.sort(Comparator.comparing(WorkorderOperationVo::getStartTime));
        return workorderOperationVos;
    }

    @Override
    public void deleteOperationById(String id) {
        //按照记录id删除操作日志
        WorkorderOperationEntity operation = getById(id);
        ObjectUtil.defaultIfNull(operation, () -> {
            throw new ServiceException("操作日志不存在");
        });
        removeById(id);
    }
}
