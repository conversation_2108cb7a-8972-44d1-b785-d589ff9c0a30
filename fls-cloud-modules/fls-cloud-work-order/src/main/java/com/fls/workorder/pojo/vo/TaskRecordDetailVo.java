package com.fls.workorder.pojo.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 工单任务视图对象
 *
 * <AUTHOR>
 * @since 2024-12-31
 */
@Data
public class TaskRecordDetailVo {
    /**
     * 任务提交表单数据
     */
    private String formJson;
    /**
     * 流程实例id
     */
    private String idProcinst;
    /**
     * 流程任务id
     */
    private String idProctask;
    /**
     * 办理人id列表
     */
    private String idsAssignee;

    /**
     * 办理人名称
     */
    private String assigneeName;

    /**
     * 候选人id列表
     */
    private String idsCandidate;

    /**
     * 候选人名称列表
     */
    private String candidateNames;

    /**
     * 协办人id列表
     */
    private String idsCoOperator;

    /**
     * 协办人名称列表
     */
    private String coOperatorNames;

    /**
     * 工单任务定义id
     */
    private String idTaskDefinition;

    /**
     * 工单任务时效
     */
    private String taskLimit;
    /**
     * 工单任务实例id
     */
    private String idTaskInstance;
    /**
     * 工单任务资源id
     */
    private String idTaskResource;
    /**
     * 工单记录id
     */
    private String idWorkorderRecord;
    /**
     * 工单任务记录id
     */
    private String idWorkorderTaskRecord;
    /**
     * 任务所有人
     */
    private String owner;
    /**
     * 来源系统标识
     */
    private String sourceProjectName;
    /**
     * 工单任务描述信息（描述工单任务工作内容）
     */
    private String taskDescription;
    /**
     * 工单任务实例名称
     */
    private String taskInstanceName;
    /**
     * 工单任务备注
     */
    private String taskRemarks;
    /**
     * 工单任务状态，0：新提交，1：进行中，2：已完成，3：已关闭
     */
    private String taskStatus;

    /**
     * 任务开始时间
     */
    private LocalDateTime startTime;

    /**
     * 任务结束时间
     */
    private LocalDateTime endTime;

    /**
     * 任务要求完成时间
     */
    private LocalDateTime deadline;

    /**
     * 任务编号
     */
    private String taskCode;

    /**
     * 小程序资源访问参数
     */
    private String appletVisitParam;

    /**
     * 小程序资源访问菜单路径
     */
    private String appletVisitPath;

    /**
     * 来源系统地址
     */
    private String sourceProjectUrl;

    /**
     * 资源访问参数
     */
    private String visitParam;

    /**
     * 资源访问菜单路径
     */
    private String visitPath;

    /**
     * 是否协办人可以提交，0-否，1-是
     */
    private String coSubmitFlag;
}
