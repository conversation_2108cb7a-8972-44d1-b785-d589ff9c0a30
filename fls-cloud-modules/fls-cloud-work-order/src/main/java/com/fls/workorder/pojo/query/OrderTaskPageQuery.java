package com.fls.workorder.pojo.query;

import javax.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 工单任务列表请求
 *
 * <AUTHOR>
 * @since 2024-12-27
 */
@Data
public class OrderTaskPageQuery {
    /**
     * 经营主体id，非必填，精确匹配
     */
    private String idBizUnit;
    /**
     * 部门id，非必填，精确匹配
     */
    private String idDept;
    /**
     * 组织id，非必填，精确匹配
     */
    private String idOrg;
    /**
     * 工单记录id
     */
    private String idWorkorder;
    /**
     * 工作班组id，非必填，精确匹配
     */
    private String idWorkTeam;
    /**
     * 页码，校验规则：页码最小不能小于1
     */
    private Long pageNo;
    /**
     * 页记录大小，校验规则：非必填，默认20，取值范围1-5000
     */
    private Long pageSize;
    /**
     * 参与人id
     */
    private String participator;
    /**
     * 工单任务状态，工单任务状态，0：新提交，1：进行中，2：已完成，3：已关闭
     */
    private Long status;
    /**
     * 任务定义id
     */
    private String taskDefine;
    /**
     * 工单任务名称，检索字段，模糊匹配
     */
    private String taskName;
    /**
     * 开始时间，格式：yyyy-MM-dd
     */
    @NotBlank(message = "开始时间不能为空")
    private String begin;
    /**
     * 结束时间，格式：yyyy-MM-dd
     */
    @NotBlank(message = "结束时间不能为空")
    private String end;
}