package com.fls.workorder.controller;

import com.fls.common.core.domain.ResponseData;
import com.fls.workorder.pojo.dto.OperationAddDTO;
import com.fls.workorder.pojo.dto.OperationDelDTO;
import com.fls.workorder.pojo.query.WorkorderOperationQuery;
import com.fls.workorder.pojo.vo.WorkorderOperationVo;
import com.fls.workorder.service.IWorkorderManagerService;
import com.fls.workorder.service.IWorkorderOperationService;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import javax.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 工单操作日志
 *
 * <AUTHOR>
 * @since 2024-12-27
 */
@Tag(name = "工单操作日志")
@Slf4j
@RestController
@RequestMapping("/workorder/operation")
@RequiredArgsConstructor
public class OperationController {

    private final IWorkorderOperationService workorderOperationService;

    private final IWorkorderManagerService managerService;

    /**
     *  查询工单历史记录
     * @param idQuery   工单记录id
     * @return 工单历史记录响应
     */
    @PostMapping("/list")
    public ResponseData<List<WorkorderOperationVo>> queryWorkOrderHistory(@Valid @RequestBody WorkorderOperationQuery idQuery) {
        return ResponseData.ok(workorderOperationService.queryOrderOperationsById(idQuery));
    }


    @PostMapping("/add")
    public ResponseData<Void> addOperation(@Valid @RequestBody OperationAddDTO operationAddDTO) {
        managerService.addWorkorderOperation(operationAddDTO);
        return ResponseData.ok();
    }

    @PostMapping("/delete")
    public ResponseData<Void> delOperation(@Valid @RequestBody OperationDelDTO operationDelDTO) {
        workorderOperationService.deleteOperationById(operationDelDTO.getIdOperation());
        return ResponseData.ok();
    }
}
