package com.fls.workorder.convert;

import cn.hutool.core.util.ObjectUtil;
import com.fls.common.core.utils.DateUtils;
import com.fls.workorder.constant.WorkorderConstant;
import com.fls.workorder.entity.WorkorderOperationEntity;
import com.fls.workorder.entity.WorkorderRecordEntity;
import com.fls.workorder.entity.WorkorderTaskRecordEntity;
import com.fls.workorder.pojo.vo.WorkorderOperationVo;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.ReportingPolicy;

/**
 * WorkorderHistoryConvert
 *
 * <AUTHOR>
 * @since 2024-12-28
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface WorkorderOperationConvert {

    @Mappings({
            @Mapping(source = "idWorkorderOperation", target = "idOperation"),
            @Mapping(source = "idSourceRecord", target = "idRecord"),
            @Mapping(source = "idOperator", target = "operator"),
            @Mapping(source = "recordStatus", target = "status"),
            @Mapping(source = "operationTitle", target = "title"),
            @Mapping(source = "startTime", target = "startTime",dateFormat = DateUtils.YYYY_MM_DD_HH_MM_SS),
            @Mapping(source = "recordType", target = "type")
    })
    WorkorderOperationVo EntityToVo(WorkorderOperationEntity entity);

    @AfterMapping
    default void afterEntityToVo(WorkorderOperationEntity entity, @MappingTarget WorkorderOperationVo operationVo) {
        if (ObjectUtil.isNotNull(entity.getStartTime()) && ObjectUtil.isNotNull(entity.getStartTime())) {
            operationVo.setDuration(String.valueOf(Duration.between(entity.getStartTime(), entity.getEndTime()).getSeconds()));
        }
    }

    List<WorkorderOperationVo> EntityToVoList(List<WorkorderOperationEntity> entityList);

    @Mappings({
            @Mapping(source = "idWorkorderRecord", target = "idSourceRecord"),
            @Mapping(source = "workorderInstanceName", target = "recordName"),
            @Mapping(source = "sourceProjectName", target = "sourceProjectName"),
            @Mapping(source = "workorderStatus", target = "recordStatus"),
            @Mapping(constant = "1", target = "recordType"),
            @Mapping(constant = WorkorderConstant.SYSTEM_OPERATOR, target = "idOperator"),
            @Mapping(constant = WorkorderConstant.SYSTEM_OPERATOR_NAME, target = "operatorName"),
    })
    WorkorderOperationEntity orderEntityToOperation(WorkorderRecordEntity orderEntity);

    @AfterMapping
    default void afterOrderEntityToOperation(WorkorderRecordEntity orderEntity, @MappingTarget WorkorderOperationEntity operationEntity) {
        operationEntity.setStartTime(LocalDateTime.now());
        operationEntity.setEndTime(LocalDateTime.now());
    }

    @Mappings({
            @Mapping(source = "idWorkorderTaskRecord", target = "idSourceRecord"),
            @Mapping(source = "taskInstanceName", target = "recordName"),
            @Mapping(source = "sourceProjectName", target = "sourceProjectName"),
            @Mapping(source = "taskStatus", target = "recordStatus"),
            @Mapping(constant = "2", target = "recordType"),
            @Mapping(constant = WorkorderConstant.SYSTEM_OPERATOR, target = "idOperator"),
            @Mapping(constant = WorkorderConstant.SYSTEM_OPERATOR_NAME, target = "operatorName"),
    })
    WorkorderOperationEntity taskEntityToOperation(WorkorderTaskRecordEntity taskRecordEntity);

    @AfterMapping
    default void afterTaskEntityToOperation(WorkorderTaskRecordEntity taskRecordEntity, @MappingTarget WorkorderOperationEntity operationEntity) {
        operationEntity.setStartTime(LocalDateTime.now());
        operationEntity.setEndTime(LocalDateTime.now());
    }
}
