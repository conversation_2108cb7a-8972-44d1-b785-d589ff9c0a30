package com.fls.workorder.pojo.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 任务办理人和协办人更新DTO
 *
 * <AUTHOR>
 * @since 2025-01-13
 */
@Data
public class TaskAssigneeUpdateDTO {
    /**
     * 任务办理id
     */
    @NotBlank(message = "任务办理人id不能为空")
    private String assignee;
    /**
     * 任务候选人id列表
     */
    private List<String> idsCandidate;
    /**
     * 任务协办人id列表
     */
    private List<String> idsCoOperator;
    /**
     * 工单任务id
     */
    private String idTask;
    /**
     * 操作类型，操作类型枚举，1：指派，2：认领
     */
    private Integer operateType;
    /**
     * 操作人id
     */
    private String operator;

    /**
     * 要求完成时间
     */
    private LocalDateTime deadline;
}
