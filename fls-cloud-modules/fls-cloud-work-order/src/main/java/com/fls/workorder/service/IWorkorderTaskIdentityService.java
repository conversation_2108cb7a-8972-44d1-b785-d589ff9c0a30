package com.fls.workorder.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fls.workorder.entity.WorkorderTaskIdentityEntity;

import java.util.List;

/**
 * <p>
 * 工单任务身份记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-01
 */
public interface IWorkorderTaskIdentityService extends IService<WorkorderTaskIdentityEntity> {

    /**
     * 批量保存任务身份记录
     *
     * @param taskIdentities 任务身份记录列表
     * @return 是否保存成功
     */
    boolean saveBatch(List<WorkorderTaskIdentityEntity> taskIdentities);

    /**
     * 根据任务记录ID查询身份记录
     *
     * @param idTaskRecord 任务记录ID
     * @return 身份记录列表
     */
    List<WorkorderTaskIdentityEntity> listByTaskRecordId(String idTaskRecord);

    /**
     * 根据任务记录ID和身份类型查询身份记录
     *
     * @param idTaskRecord 任务记录ID
     * @param type         身份类型
     * @return 身份记录列表
     */
    List<WorkorderTaskIdentityEntity> listByTaskRecordIdAndType(String idTaskRecord, String type);

    /**
     * 创建候选人身份记录
     *
     * @param idTaskRecord  任务记录ID
     * @param candidateIds  候选人ID列表，逗号分隔
     */
    void createCandidateIdentities(String idTaskRecord, String candidateIds);
}
