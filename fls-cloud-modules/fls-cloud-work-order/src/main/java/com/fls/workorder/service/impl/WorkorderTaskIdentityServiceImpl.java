package com.fls.workorder.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fls.workorder.entity.WorkorderTaskIdentityEntity;
import com.fls.workorder.mapper.WorkorderTaskIdentityMapper;
import com.fls.workorder.service.IWorkorderTaskIdentityService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 工单任务身份记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-01
 */
@Slf4j
@Service
public class WorkorderTaskIdentityServiceImpl extends ServiceImpl<WorkorderTaskIdentityMapper, WorkorderTaskIdentityEntity>
        implements IWorkorderTaskIdentityService {

    private static final String IDENTITY_TYPE_CANDIDATE = "candidate";

    @Override
    public boolean saveBatch(List<WorkorderTaskIdentityEntity> taskIdentities) {
        if (taskIdentities == null || taskIdentities.isEmpty()) {
            log.warn("Task identities list is empty, skip saving");
            return true;
        }
        return super.saveBatch(taskIdentities);
    }

    @Override
    public List<WorkorderTaskIdentityEntity> listByTaskRecordId(String idTaskRecord) {
        if (StrUtil.isBlank(idTaskRecord)) {
            return new ArrayList<>();
        }
        return lambdaQuery()
                .eq(WorkorderTaskIdentityEntity::getIdTaskRecord, idTaskRecord)
                .list();
    }

    @Override
    public List<WorkorderTaskIdentityEntity> listByTaskRecordIdAndType(String idTaskRecord, String type) {
        if (StrUtil.isBlank(idTaskRecord) || StrUtil.isBlank(type)) {
            return new ArrayList<>();
        }
        return lambdaQuery()
                .eq(WorkorderTaskIdentityEntity::getIdTaskRecord, idTaskRecord)
                .eq(WorkorderTaskIdentityEntity::getType, type)
                .list();
    }

    @Override
    public void createCandidateIdentities(String idTaskRecord, String candidateIds) {
        if (StrUtil.isBlank(idTaskRecord) || StrUtil.isBlank(candidateIds)) {
            log.info("Task record id or candidate ids is blank, skip creating candidate identities. taskRecordId: {}, candidateIds: {}", 
                    idTaskRecord, candidateIds);
            return;
        }

        // 解析候选人ID列表
        List<String> candidateIdList = Arrays.stream(candidateIds.split(","))
                .map(String::trim)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toList());

        if (candidateIdList.isEmpty()) {
            log.info("No valid candidate ids found, skip creating candidate identities. candidateIds: {}", candidateIds);
            return;
        }

        // 创建候选人身份记录
        List<WorkorderTaskIdentityEntity> identities = candidateIdList.stream()
                .map(candidateId -> new WorkorderTaskIdentityEntity(IDENTITY_TYPE_CANDIDATE, idTaskRecord, candidateId))
                .collect(Collectors.toList());

        // 批量保存
        boolean success = saveBatch(identities);
        if (success) {
            log.info("Successfully created {} candidate identities for task record: {}", identities.size(), idTaskRecord);
        } else {
            log.error("Failed to create candidate identities for task record: {}", idTaskRecord);
        }
    }
}
