package com.fls.workorder.pojo.vo;

import com.fls.workorder.enums.AssigneeTypeEnum;
import com.fls.workorder.enums.TaskStatusEnum;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 任务中心信息
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
public class TaskCenterDetailVo {

    /**
     * 工单任务记录id
     */
    private String idTask;
    /**
     * 工单任务实例名称
     */
    private String taskName;

    /**
     * 任务编号
     */
    private String taskCode;

    /**
     * 资产编号
     */
    private String assetCode;

    /**
     * 资源id
     */
    private String idResource;

    /**
     * 工单任务状态，0：新提交，1：进行中，2：已完成，3：已关闭
     */
    private String taskStatus;

    /**
     * 工单任务状态描述
     */
    private String taskStatusDesc;

    /**
     * 工单记录id
     */
    private String sourceName;

    /**
     * 经营主体id
     */
    private String idBizunit;

    /**
     * 经营主体名称
     */
    private String bizunitName;

    /**
     * 发起人用户id
     */
    private String initiator;

    /**
     * 发起人名称
     */
    private String initiatorName;

    /**
     * 分配类型
     */
    private String assigneeType;

    /**
     * 分配类型描述
     */
    private String assigneeTypeDesc;

    /**
     * 办理人id列表
     */
    private String idsAssignee;

    /**
     * 办理人名称
     */
    private String assigneeName;
    /**
     * 候选人id列表
     */
    private String idsCandidate;

    /**
     * 候选人名称列表
     */
    private String candidateNames;

    /**
     * 协办人id列表
     */
    private String idsCoOperator;

    /**
     * 协办人名称列表
     */
    private String coOperatorNames;

    /**
     * 任务开始时间
     */
    private LocalDateTime startTime;

    /**
     * 任务结束时间
     */
    private LocalDateTime endTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 资源访问参数
     */
    private String visitParam;

    /**
     * 资源访问菜单路径
     */
    private String visitPath;

    /**
     * 小程序资源访问参数
     */
    private String appletVisitParam;

    /**
     * 小程序资源访问菜单路径
     */
    private String appletVisitPath;

    /**
     * 来源项目编码
     */
    private String sourceProjectCode;

    public void fillTransField() {
        this.setAssigneeTypeDesc(AssigneeTypeEnum.getName(this.getAssigneeType()));
        this.setTaskStatusDesc(TaskStatusEnum.getDesc(Integer.parseInt(this.getTaskStatus())));
    }
}
