package com.fls.workorder.enums;

import cn.hutool.core.util.StrUtil;
import lombok.Getter;

/**
 * 待办任务类型枚举
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Getter
public enum TaskTypeEnum {
    /**
     * 工单待办
     */
    ORDER("0", "工单待办"),
    /**
     * 业务待办
     */
    BIZ("1", "业务待办"),
    /**
     * 审批待办
     */
    APPROVE("2", "审批待办");

    private final String code;

    private final String name;

    TaskTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getName(String code){
        for(TaskTypeEnum taskTypeEnum : TaskTypeEnum.values()){
            if(taskTypeEnum.getCode().equals(code)){
                return taskTypeEnum.getName();
            }
        }
        return StrUtil.EMPTY;
    }
}
