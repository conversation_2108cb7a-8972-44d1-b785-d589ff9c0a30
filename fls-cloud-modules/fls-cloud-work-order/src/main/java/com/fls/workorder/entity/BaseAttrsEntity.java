package com.fls.workorder.entity;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 基础属性实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-27
 */
@Data
@Accessors(chain = true)
public class BaseAttrsEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 组织ID
     */
    private String idOrg;

    /**
     * 经营主体id
     */
    private String idBizUnit;

    /**
     * 部门ID
     */
    private String idDepartment;

    /**
     * 班组id
     */
    private String idWorkteam;

    /**
     * 资产编号
     */
    private String assetCode;
}
