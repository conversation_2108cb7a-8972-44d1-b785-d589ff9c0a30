package com.fls.workorder.pojo.dto;

import javax.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 工单终止DTO
 *
 * <AUTHOR>
 * @since 2024-12-27
 */
@Data
public class OrderTerminateDTO {
    /**
     * 操作人
     */
    @NotBlank(message = "操作人不能为空")
    private String operator;
    /**
     * 工单记录id
     */
    @NotBlank(message = "工单记录id不能为空")
    private String idWorkorderRecord;
    /**
     * 备注说明，必填，工单终止必须要填终止原因说明
     */
    private String remarks;
}