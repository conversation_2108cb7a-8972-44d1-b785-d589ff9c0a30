package com.fls.workorder.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fls.workorder.entity.WorkorderOperationEntity;
import com.fls.workorder.entity.WorkorderRecordEntity;
import com.fls.workorder.entity.WorkorderTaskRecordEntity;

import java.util.List;

/**
 * <p>
 * 操作日志生成服务类，解耦合循环依赖
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-27
 */
public interface IOperationProduceService extends IService<WorkorderOperationEntity> {

    /**
     * 创建工单操作记录
     *
     * @param title    标题
     * @param record   工单记录
     * @param operator 操作人id
     */
    void produceOrderOperation(String title, WorkorderRecordEntity record, String operator);

    /**
     * 创建工单操作记录
     *
     * @param title    标题
     * @param record   工单记录
     * @param operator 操作人id
     * @param idLink   附件idLink
     */
    void produceOrderOperation(String title, WorkorderRecordEntity record, String operator, String idLink, String remark);

    /**
     * 创建工单任务操作记录
     *
     * @param title    标题
     * @param task     任务记录
     * @param operator 操作人id
     */
    void produceTaskOperation(String title, WorkorderTaskRecordEntity task, String operator);

    /**
     * 创建工单任务操作记录
     *
     * @param title    标题
     * @param task     任务记录
     * @param operator 操作人id
     * @param idLink   附件idLink
     */
    void produceTaskOperation(String title, WorkorderTaskRecordEntity task, String operator, String idLink, String remark);

    /**
     * 创建工单任务操作记录
     *
     * @param title    标题
     * @param tasks    任务记录列表
     * @param operator 操作人id
     */
    void produceTasksOperation(String title, List<WorkorderTaskRecordEntity> tasks, String operator);

}
