package com.fls.workorder.pojo.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import javax.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 创建工单任务DTO
 *
 * <AUTHOR>
 * @since 2024-12-31
 */
@Data
public class OrderCreateTaskDTO {
    /**
     * 办理人id列表
     */
    private String assignees;

    /**
     * 候选人id列表
     */
    private String candidates;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 流程实例id
     */
    @NotBlank(message = "流程实例id不能为空")
    private String idProcInst;

    /**
     * 流程任务id
     */
    @NotBlank(message = "流程任务id不能为空")
    private String idProcTask;

    /**
     * 工单任务定义id
     */
    @NotBlank(message = "工单任务定义id不能为空")
    private String idTaskDefinition;

    /**
     * 工单记录id
     */
    @NotBlank(message = "工单记录id不能为空")
    private String idWorkorderRecord;

    /**
     * 工单任务实例id
     */
    @JsonIgnore
    private String taskInstanceId;

    /**
     * 工单任务备注
     */
    private String remarks;
}