package com.fls.workorder;

import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.metrics.buffering.BufferingApplicationStartup;

/**
 * 工单模块启动入口
 *
 * <AUTHOR>
 * @since 2024-12-10
 */
@SpringBootApplication
@EnableDubbo
public class FlsWorkOrderApplication {
    public static void main(String[] args) {
        SpringApplication application = new SpringApplication(FlsWorkOrderApplication.class);
        application.setApplicationStartup(new BufferingApplicationStartup(2048));
        application.run(args);
    }
}
