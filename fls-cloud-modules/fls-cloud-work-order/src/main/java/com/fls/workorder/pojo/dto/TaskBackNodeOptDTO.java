package com.fls.workorder.pojo.dto;

import javax.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 工单任务驳回操作DTO
 *
 * <AUTHOR>
 * @since 2025-06-11
 */
@Data
public class TaskBackNodeOptDTO {

    @NotBlank(message = "发起人不能为空")
    private String idTask;

    @NotBlank(message = "驳回任务节点标识")
    private String backTaskDefKey;

    @NotBlank(message = "操作人不能为空")
    private String operator;

    /**
     * 驳回备注
     */
    private String message;
}