package com.fls.workorder.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.csp.sentinel.util.AssertUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fls.upms.api.RemoteUserService;
import com.fls.workorder.constant.WorkorderConstant;
import com.fls.workorder.convert.WorkorderOperationConvert;
import com.fls.workorder.entity.WorkorderOperationEntity;
import com.fls.workorder.entity.WorkorderRecordEntity;
import com.fls.workorder.entity.WorkorderTaskRecordEntity;
import com.fls.workorder.mapper.WorkorderOperationMapper;
import com.fls.workorder.service.IOperationProduceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 工单流转历史记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OperationProduceServiceImpl extends ServiceImpl<WorkorderOperationMapper, WorkorderOperationEntity> implements IOperationProduceService {

    private final WorkorderOperationConvert workorderOperationConvert;

    @DubboReference
    private RemoteUserService remoteUserService;

    @Override
    public void produceOrderOperation(String title, WorkorderRecordEntity record, String operator) {
        createOperation(title, record, operator, WorkorderConstant.ORDER_TYPE, null, null);
    }

    @Override
    public void produceOrderOperation(String title, WorkorderRecordEntity record, String operator, String idLink, String remark) {
        createOperation(title, record, operator, WorkorderConstant.ORDER_TYPE, idLink, remark);
    }

    @Override
    public void produceTaskOperation(String title, WorkorderTaskRecordEntity task, String operator) {
        createOperation(title, task, operator, WorkorderConstant.TASK_TYPE, null, null);
    }

    @Override
    public void produceTaskOperation(String title, WorkorderTaskRecordEntity task, String operator, String idLink, String remark) {
        createOperation(title, task, operator, WorkorderConstant.TASK_TYPE, idLink, remark);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void produceTasksOperation(String title, List<WorkorderTaskRecordEntity> tasks, String operator) {
        List<WorkorderOperationEntity> operations = tasks.stream().map(taskRecord -> {
            updateLatestOperation(taskRecord.getIdWorkorderTaskRecord());
            WorkorderOperationEntity newOperation = workorderOperationConvert.taskEntityToOperation(taskRecord);
            // 设置操作人信息
            if (!StrUtil.equals(operator, WorkorderConstant.SYSTEM_OPERATOR)) {
                String userName = remoteUserService.getUserNameByUserId(operator);
                newOperation.setIdOperator(operator);
                newOperation.setOperatorName(userName);
            }
            // 设置操作标题并保存
            newOperation.setOperationTitle(title);
            return newOperation;
        }).collect(Collectors.toList());
        saveBatch(operations);
        log.info("批量操作记录已保存，records size: {}, operationTitle: {}, recordType: {}", tasks.size(), title, WorkorderConstant.TASK_TYPE);
    }

    private void createOperation(String title, Object record, String operator, String recordType, String idLink, String remark) {
        AssertUtil.notNull(record, "操作日志记录不存在");
        String recordId;
        WorkorderOperationEntity newOperation;
        // 根据记录类型转换为操作实体
        if (WorkorderConstant.ORDER_TYPE.equals(recordType)) {
            WorkorderRecordEntity orderRecord = (WorkorderRecordEntity) record;
            recordId = orderRecord.getIdWorkorderRecord();
            updateLatestOperation(recordId);
            newOperation = workorderOperationConvert.orderEntityToOperation(orderRecord);
        } else if (WorkorderConstant.TASK_TYPE.equals(recordType)) {
            WorkorderTaskRecordEntity taskRecord = (WorkorderTaskRecordEntity) record;
            recordId = taskRecord.getIdWorkorderTaskRecord();
            updateLatestOperation(recordId);
            newOperation = workorderOperationConvert.taskEntityToOperation(taskRecord);
        } else {
            throw new IllegalArgumentException("未知的记录类型");
        }

        // 设置操作人信息
        if (!StrUtil.equals(operator, WorkorderConstant.SYSTEM_OPERATOR)) {
            String userName = remoteUserService.getUserNameByUserId(operator);
            newOperation.setIdOperator(operator);
            newOperation.setOperatorName(userName);
        }

        // 设置操作标题并保存
        newOperation.setOperationTitle(title);
        newOperation.setIdLink(idLink);
        newOperation.setRemark(remark);
        save(newOperation);
        log.info("操作记录已保存，recordId: {}, operationTitle: {}, recordType: {}", recordId, title, recordType);
    }

    /**
     * 更新最近的操作记录
     *
     * @param recordId 源记录ID
     */
    private void updateLatestOperation(String recordId) {
        WorkorderOperationEntity latestOperation =
            lambdaQuery().eq(WorkorderOperationEntity::getIdSourceRecord, recordId).last("ORDER BY update_time DESC LIMIT 1").one();
        if (ObjectUtil.isNotNull(latestOperation)) {
            log.info("找到匹配的操作记录，recordId: {}, latest record title: {}", recordId, latestOperation.getOperationTitle());
            latestOperation.setEndTime(LocalDateTime.now());
            updateById(latestOperation);
        }
    }
}
