package com.fls.workorder.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 工单任务身份记录
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("t_workorder_task_identity")
public class WorkorderTaskIdentityEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 身份类型
     */
    private String type;

    /**
     * 工单任务记录id
     */
    private String idTaskRecord;

    /**
     * 候选用户id
     */
    private String idUser;

    /**
     * 构造函数
     */
    public WorkorderTaskIdentityEntity() {
    }

    /**
     * 构造函数
     *
     * @param type         身份类型
     * @param idTaskRecord 工单任务记录id
     * @param idUser       用户id
     */
    public WorkorderTaskIdentityEntity(String type, String idTaskRecord, String idUser) {
        this.type = type;
        this.idTaskRecord = idTaskRecord;
        this.idUser = idUser;
    }
}
