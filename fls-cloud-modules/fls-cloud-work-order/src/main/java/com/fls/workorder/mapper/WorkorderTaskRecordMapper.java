package com.fls.workorder.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fls.todo.api.model.TodoTaskQuery;
import com.fls.todo.api.model.TodoTaskVo;
import com.fls.workorder.entity.WorkorderTaskRecordEntity;
import com.fls.workorder.pojo.query.TaskCenterQuery;
import com.fls.workorder.pojo.vo.TaskCenterDetailVo;
import com.fls.workorder.pojo.vo.TaskRecordDetailVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 工单任务信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-27
 */
public interface WorkorderTaskRecordMapper extends BaseMapper<WorkorderTaskRecordEntity> {

    /**
     * 通过工单id查询任务详情列表
     *
     * @param workorderId 工单id
     * @return 任务详情vo
     */
    List<TaskRecordDetailVo> getTaskVoListByWorkorderId(@Param("workorderId") String workorderId);

    /**
     * 通过任务id查询任务详情
     * @param idTask 任务id
     * @return 任务详情
     */
    TaskRecordDetailVo getTaskVoByIdTask(@Param("idTask") String idTask);

    /**
     * 查询待认领任务信息
     *
     * @param page  分页参数
     * @param query 查询参数
     * @return 待认领任务列表
     */
    Page<TodoTaskVo> getTaskDetailByParam(Page<TodoTaskVo> page, @Param("query") TodoTaskQuery query);

    /**
     * 任务中心分页查询
     *
     * @param page  分页对象
     * @param query 查询参数
     * @return 结果列表
     */
    List<TaskCenterDetailVo> selectTaskCenterPage(Page<?> page, @Param("query") TaskCenterQuery query);
}
