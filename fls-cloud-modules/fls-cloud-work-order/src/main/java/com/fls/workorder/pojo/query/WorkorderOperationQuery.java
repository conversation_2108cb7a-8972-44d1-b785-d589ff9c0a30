package com.fls.workorder.pojo.query;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * 工单流转历史查询参数
 *
 * <AUTHOR>
 * @since 2024-12-27
 */
@Data
public class WorkorderOperationQuery {
    /**
     * 工单记录id
     */
    @NotBlank(message = "记录id不能为空")
    private String idRecord;

    /**
     * 类型枚举，1：工单，2：工单任务
     */
    @NotNull(message = "类型枚举不能为空")
    private Integer type;
}