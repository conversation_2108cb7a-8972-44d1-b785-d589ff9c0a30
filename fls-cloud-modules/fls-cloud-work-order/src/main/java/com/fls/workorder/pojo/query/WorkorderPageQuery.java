package com.fls.workorder.pojo.query;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 工单记录查询参数
 *
 * <AUTHOR>
 * @since 2024-12-27
 */
@Data
public class WorkorderPageQuery {
    /**
     * 开始时间，格式：yyyy-MM-dd
     */
    @NotBlank(message = "开始时间不能为空")
    private String begin;
    /**
     * 结束时间，格式：yyyy-MM-dd
     */
    @NotBlank(message = "结束时间不能为空")
    private String end;
    /**
     * 工单资源id，检索字段，精确匹配
     */
    private String idResource;
    /**
     * 发起人名称，检索字段，模糊匹配
     */
    private String initiator;
    /**
     * 页码，校验规则：页码最小不能小于1
     */
    @Min(value = 1, message = "页码最小不能小于1")
    private Integer pageNo;
    /**
     * 页记录大小，校验规则：非必填，默认20，取值范围1-5000
     */
    @Min(value = 1, message = "页记录大小最小不能小于1")
    @Max(value = 5000, message = "页记录大小最大不能超过5000")
    private Integer pageSize;
    /**
     * 工单资源编码，检索字段，精确匹配
     */
    private String resourceCode;
    /**
     * 工单状态，工单状态，0：新制单，1：进行中，2：已完成，3：已关闭
     */
    private Integer status;
    /**
     * 工单名称，检索字段，模糊匹配
     */
    private String workorderName;
}