package com.fls.workorder.pojo.vo;

import lombok.Data;

/**
 * 工单流转历史VO
 *
 * <AUTHOR>
 * @since 2024-12-31
 */
@Data
public class WorkorderOperationVo {
    /**
     * 用时时间，单位：秒
     */
    private String duration;
    /**
     * 操作结束时间
     */
    private String endTime;
    /**
     * 流转记录id
     */
    private String idOperation;
    /**
     * 原始记录id
     */
    private String idRecord;
    /**
     * 操作人id
     */
    private String operator;
    /**
     * 操作人名称
     */
    private String operatorName;
    /**
     * 流转记录名称
     */
    private String recordName;
    /**
     * 操作开始时间
     */
    private String startTime;
    /**
     * 记录状态，状态
     */
    private String status;
    /**
     * 操作记录标题
     */
    private String title;
    /**
     * 记录类型
     */
    private String type;
    /**
     * 附件idLink
     */
    private String idLink;

    /**
     * 操作说明
     */
    private String remark;
}
