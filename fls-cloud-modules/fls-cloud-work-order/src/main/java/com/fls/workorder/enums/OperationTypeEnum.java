package com.fls.workorder.enums;

import lombok.Getter;

@Getter
public enum OperationTypeEnum {
    INITIATE("发起工单"),
    PROGRESS("工单进行"),
    TERMINATE("工单终止"),
    END("工单完成"),
    TASK_INIT("工单任务初始化"),
    TASK_ASSIGN("工单任务指派"),
    TASK_CLAIM("工单任务认领"),
    TASK_COOPERATE("工单任务更新协办人"),
    TASK_CANDIDATE("工单任务更新候选人"),
    TASK_BACK_NODE("工单任务驳回"),
    TASK_COMPLETE("工单任务完成"),
    TASK_FOLLOW("工单任务跟进"),
    TASK_SUSPEND("工单任务挂起"),
    TASK_ACTIVE("工单任务激活"),
    TASK_TRANSFER("工单任务转办"),
    TASK_TERMINATE("工单任务终止"),
    TASK_END("工单任务结束");

    private final String title;

    // 构造方法
    OperationTypeEnum(String title) {
        // 成员变量
        this.title = title;
    }
}
