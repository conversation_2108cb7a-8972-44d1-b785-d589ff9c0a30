package com.fls.workorder.convert;

import com.fls.workflow.api.model.InitiateProcessReq;
import com.fls.workorder.pojo.dto.OrderInitiateDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

/**
 * WorkflowConvert
 *
 * <AUTHOR>
 * @since 2024-12-28
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ProcessConvert {
    @Mapping(target = "title", source = "dto.orderName")
    InitiateProcessReq orderToProcessReq(OrderInitiateDTO dto, String resourceCode);
}
