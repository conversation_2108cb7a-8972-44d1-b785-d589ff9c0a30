package com.fls.workorder.convert;

import com.fls.master.api.model.ResourceInfo;
import com.fls.workflow.api.model.InitiateProcessRsp;
import com.fls.workorder.entity.WorkorderRecordEntity;
import com.fls.workorder.enums.OrderStatusEnum;
import com.fls.workorder.pojo.dto.OrderInitiateDTO;
import com.fls.workorder.pojo.vo.WorkorderRecordVo;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * WorkflowConvert
 *
 * <AUTHOR>
 * @since 2024-12-28
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface WorkorderRecordConvert {

    @Mappings({
        @Mapping(target = "updateBy", source = "dto.initiator"),
        @Mapping(target = "createBy", source = "dto.initiator"),
        @Mapping(target = "initiator", source = "dto.initiator"),
        @Mapping(target = "idWorkorderInstance", source = "dto.idInstance"),
        @Mapping(target = "idProcinst", source = "initiateProcessRsp.processInstanceId"),
        @Mapping(target = "idProcdef", source = "initiateProcessRsp.processDefinitionId"),
        @Mapping(target = "sourceProjectName", source = "dto.projectCode"),
        @Mapping(target = "idWorkorderResource", source = "resource.idResource"),
        @Mapping(target = "workorderInstanceName", source = "dto.orderName")
    })
    WorkorderRecordEntity mergedToEntity(OrderInitiateDTO dto, ResourceInfo resource, InitiateProcessRsp initiateProcessRsp);

    @AfterMapping
    default void afterMergedToEntity(OrderInitiateDTO dto, ResourceInfo resource, InitiateProcessRsp initiateProcessRsp, @MappingTarget WorkorderRecordEntity entity) {
        entity.setWorkorderStatus(OrderStatusEnum.INIT.getCode());
    }

    @Mappings({
        @Mapping(target = "idInstance", source = "idWorkorderInstance"),
        @Mapping(target = "idResource", source = "idWorkorderResource"),
        @Mapping(target = "idWorkorderRecord", source = "idWorkorderRecord"),
        @Mapping(target = "projectName", source = "sourceProjectName"),
        @Mapping(target = "status", source = "workorderStatus"),
        @Mapping(target = "deadline", source = "deadline"),
        @Mapping(target = "idProcIns", source = "idProcinst"),
        @Mapping(target = "workorderName", source = "workorderInstanceName")
    })
    WorkorderRecordVo entityToVo(WorkorderRecordEntity entity);

    List<WorkorderRecordVo> recordEntityToVoList(List<WorkorderRecordEntity> entityList);
}
