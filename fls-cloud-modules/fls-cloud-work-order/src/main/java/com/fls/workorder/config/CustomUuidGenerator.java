package com.fls.workorder.config;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.incrementer.IdentifierGenerator;
import java.util.UUID;
import org.springframework.stereotype.Component;

@Component
public class CustomUuidGenerator implements IdentifierGenerator {

    // 雪花算法生成器
    private final Snowflake snowflake = IdUtil.getSnowflake();

    @Override
    public boolean assignId(Object idValue) {
        // 默认逻辑，如果主键值为空，则需要生成新主键
        return idValue == null;
    }

    @Override
    public Number nextId(Object entity) {
        // 数字主键生成逻辑（适用于 ASSIGN_ID）
        return snowflake.nextId();
    }

    @Override
    public String nextUUID(Object entity) {
        // 生成带 "-" 的 UUID（适用于 ASSIGN_UUID）
        return UUID.randomUUID().toString();
    }
}
