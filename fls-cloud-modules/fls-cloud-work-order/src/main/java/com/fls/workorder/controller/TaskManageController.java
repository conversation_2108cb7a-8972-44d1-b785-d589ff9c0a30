package com.fls.workorder.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.fls.common.core.domain.ResponseData;
import com.fls.common.core.exception.ServiceException;
import com.fls.common.mybatis.core.page.PageResult;
import com.fls.workflow.api.model.BackNodeInfo;
import com.fls.workorder.pojo.dto.OrderCreateTaskDTO;
import com.fls.workorder.pojo.dto.TaskAddDTO;
import com.fls.workorder.pojo.dto.TaskAssigneeUpdateDTO;
import com.fls.workorder.pojo.dto.TaskBackNodeOptDTO;
import com.fls.workorder.pojo.dto.TaskBackNodeQueryDTO;
import com.fls.workorder.pojo.dto.TaskBackPrevNodeDTO;
import com.fls.workorder.pojo.dto.TaskCandidateUpdateDTO;
import com.fls.workorder.pojo.dto.TaskClaimDTO;
import com.fls.workorder.pojo.dto.TaskCoOperatorUpdateDTO;
import com.fls.workorder.pojo.dto.TaskCompleteDTO;
import com.fls.workorder.pojo.dto.TaskFollowDTO;
import com.fls.workorder.pojo.dto.TaskSuspendDTO;
import com.fls.workorder.pojo.dto.TaskTerminateDTO;
import com.fls.workorder.pojo.dto.TaskTransferDTO;
import com.fls.workorder.pojo.query.OrderTaskPageQuery;
import com.fls.workorder.pojo.query.TaskCenterQuery;
import com.fls.workorder.pojo.vo.TaskCenterDetailVo;
import com.fls.workorder.pojo.vo.TaskRecordDetailVo;
import com.fls.workorder.pojo.vo.TaskRecordVo;
import com.fls.workorder.pojo.vo.TaskSimpleVo;
import com.fls.workorder.service.IWorkorderTaskRecordService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * 工单任务管理
 *
 * <AUTHOR>
 * @since 2024-12-27
 */
@Tag(name = "工单任务管理")
@Slf4j
@RestController
@RequestMapping("/workorder/task")
@RequiredArgsConstructor
public class TaskManageController {

    private final IWorkorderTaskRecordService workorderTaskRecordService;

    /**
     * 查询工单任务列表
     *
     * @param pageQuery 工单任务查询参数
     * @return 工单任务列表
     */
    @PostMapping("/list")
    public ResponseData<PageResult<TaskRecordVo>> queryOrderTaskPage(@Valid @RequestBody OrderTaskPageQuery pageQuery) {
        return ResponseData.ok(workorderTaskRecordService.queryOrderTaskPage(pageQuery));
    }

    @PostMapping("/center")
    public ResponseData<PageResult<TaskCenterDetailVo>> queryOrderTaskPage(@Valid @RequestBody TaskCenterQuery pageQuery) {
        return ResponseData.ok(workorderTaskRecordService.queryTaskCenterPage(pageQuery));
    }

    @PostMapping("/detail")
    public ResponseData<TaskRecordDetailVo> detail(@RequestBody TaskSuspendDTO pageQuery) {
        if (StrUtil.isBlank(pageQuery.getIdTask())) {
            throw new ServiceException("工单任务id不能为空");
        }
        return ResponseData.ok(workorderTaskRecordService.votByTaskId(pageQuery.getIdTask()));
    }

    /**
     * 生成工单任务
     *
     * @param createTaskDTO 生成工单任务参数
     * @return 工单任务id
     */
    @PostMapping("/create")
    public ResponseData<Dict> create(@Valid @RequestBody OrderCreateTaskDTO createTaskDTO) {
        String idOrderTask = workorderTaskRecordService.createOrderTask(createTaskDTO);
        return ResponseData.ok(Dict.create().set("idTask", idOrderTask));
    }

    /**
     * 新增工单任务
     *
     * @param taskAddDTO 新增工单任务DTO
     * @return 工单任务id
     */
    @PostMapping("/add")
    public ResponseData<Dict> add(@Valid @RequestBody TaskAddDTO taskAddDTO) {
        String idOrderTask = workorderTaskRecordService.addOrderTask(taskAddDTO);
        return ResponseData.ok(Dict.create().set("idTask", idOrderTask));
    }

    /**
     * 工单任务认领
     *
     * @param claimDTO 工单任务认领DTO
     * @return 工单任务id
     */
    @PostMapping("/claim")
    public ResponseData<Void> claim(@Valid @RequestBody TaskClaimDTO claimDTO) {
        workorderTaskRecordService.claimTask(claimDTO);
        return ResponseData.ok();
    }

    /**
     * 工单任务跟进
     *
     * @param followDTO 任务跟进DTO
     * @return 通用响应
     */
    @PostMapping("/follow")
    public ResponseData<Void> follow(@Valid @RequestBody TaskFollowDTO followDTO) {
        workorderTaskRecordService.followTask(followDTO);
        return ResponseData.ok();
    }

    /**
     * 工单任务转办
     *
     * @param transferDTO 转办任务DTO
     * @return 通用响应
     */
    @PostMapping("/transfer")
    public ResponseData<TaskSimpleVo> transfer(@Valid @RequestBody TaskTransferDTO transferDTO) {
        return ResponseData.ok(workorderTaskRecordService.transferTask(transferDTO));
    }

    /**
     * 工单任务完成
     *
     * @param completeDTO 任务完成DTO
     * @return 通用响应
     */
    @PostMapping("/complete")
    public ResponseData<TaskRecordVo> complete(@Valid @RequestBody TaskCompleteDTO completeDTO) {
        TaskRecordVo taskRecordVo = workorderTaskRecordService.completeTask(completeDTO);
        return ObjectUtil.isNotNull(taskRecordVo) ? ResponseData.ok(taskRecordVo) : ResponseData.ok();
    }

    /**
     * 工单任务挂起
     *
     * @param suspendDTO 挂起任务DTO
     * @return 通用响应
     */
    @PostMapping("/suspend")
    public ResponseData<Void> suspend(@Valid @RequestBody TaskSuspendDTO suspendDTO) {
        workorderTaskRecordService.suspendTask(suspendDTO);
        return ResponseData.ok();
    }

    /**
     * 工单任务终止
     *
     * @param terminateDTO 任务终止DTO
     * @return 通用响应
     */
    @PostMapping("/terminate")
    public ResponseData<TaskSimpleVo> terminate(@Valid @RequestBody TaskTerminateDTO terminateDTO) {
        return ResponseData.ok(workorderTaskRecordService.terminateTask(terminateDTO));
    }

    /**
     * 更新工单任务办理人和协办人
     *
     * @param assigneeUpdateDTO 工单任务办理人和协办人更新DTO
     * @return 通用响应
     */
    @PostMapping("/assignee/update")
    public ResponseData<TaskSimpleVo> updateAssignee(@Valid @RequestBody TaskAssigneeUpdateDTO assigneeUpdateDTO) {
        return ResponseData.ok(workorderTaskRecordService.updateAssignee(assigneeUpdateDTO));
    }

    /**
     * 更新工单任务协办人
     *
     * @param coOperatorUpdateDTO 工单任务协办人更新DTO
     * @return 通用响应
     */
    @PostMapping("/cooperator/update")
    public ResponseData<Void> updateCoOperator(@Valid @RequestBody TaskCoOperatorUpdateDTO coOperatorUpdateDTO) {
        workorderTaskRecordService.updateCoOperator(coOperatorUpdateDTO);
        return ResponseData.ok();
    }

    /**
     * 更新工单任务候选人
     *
     * @param candidateUpdateDTO 工单任务候选人更新DTO
     * @return 通用响应
     */
    @PostMapping("/candidate/update")
    public ResponseData<Void> updateCandidate(@Valid @RequestBody TaskCandidateUpdateDTO candidateUpdateDTO) {
        workorderTaskRecordService.updateCandidate(candidateUpdateDTO);
        return ResponseData.ok();
    }

    /**
     * 获取可驳回任务节点
     *
     * @param backNodeQueryDTO 查询请求
     * @return 任务节点列表
     */
    @PostMapping("/back/nodes")
    public ResponseData<List<BackNodeInfo>> getBackNodes(@Valid @RequestBody TaskBackNodeQueryDTO backNodeQueryDTO) {
        return ResponseData.ok(workorderTaskRecordService.getBackNodes(backNodeQueryDTO));
    }

    /**
     * 驳回任务至节点
     *
     * @param backNodeOptDTO 驳回请求
     * @return 通用响应
     */
    @PostMapping("/reject")
    public ResponseData<TaskRecordVo> backNode(@Valid @RequestBody TaskBackNodeOptDTO backNodeOptDTO) {
        return ResponseData.ok(workorderTaskRecordService.backNode(backNodeOptDTO));
    }

    /**
     * 驳回至前一任务节点
     *
     * @param backNodeOptDTO 驳回请求
     * @return 通用响应
     */
    @PostMapping("/reject/previous")
    public ResponseData<TaskRecordVo> backPrevNode(@Valid @RequestBody TaskBackPrevNodeDTO backNodeOptDTO) {
        String idTask = backNodeOptDTO.getIdTask();
        TaskBackNodeQueryDTO taskBackNodeQueryDTO = new TaskBackNodeQueryDTO();
        taskBackNodeQueryDTO.setIdTask(idTask);
        List<BackNodeInfo> backNodes = workorderTaskRecordService.getBackNodes(taskBackNodeQueryDTO);
        if (ObjectUtil.isEmpty(backNodes)) {
            throw new ServiceException("未找到上一节点任务");
        }
        BackNodeInfo backNodeInfo = backNodes.get(backNodes.size() - 1);
        TaskBackNodeOptDTO taskBackNodeOptDTO = new TaskBackNodeOptDTO();
        BeanUtil.copyProperties(backNodeOptDTO, taskBackNodeOptDTO);
        taskBackNodeOptDTO.setBackTaskDefKey(backNodeInfo.getTaskDefKey());
        return ResponseData.ok(workorderTaskRecordService.backNode(taskBackNodeOptDTO));
    }
}
