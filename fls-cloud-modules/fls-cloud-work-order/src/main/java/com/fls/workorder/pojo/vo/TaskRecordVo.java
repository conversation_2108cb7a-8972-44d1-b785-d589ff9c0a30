package com.fls.workorder.pojo.vo;

import lombok.Data;

/**
 * 工单任务视图对象
 *
 * <AUTHOR>
 * @since 2024-12-31
 */
@Data
public class TaskRecordVo {
    /**
     * 任务提交表单数据
     */
    private String formJson;
    /**
     * 流程实例id
     */
    private String idProcinst;
    /**
     * 流程任务id
     */
    private String idProctask;
    /**
     * 办理人id列表
     */
    private String idsAssignee;

    /**
     * 办理人名称
     */
    private String assigneeName;

    /**
     * 候选人id列表
     */
    private String idsCandidate;

    /**
     * 候选人名称列表
     */
    private String candidateNames;

    /**
     * 协办人id列表
     */
    private String idsCoOperator;

    /**
     * 协办人名称列表
     */
    private String coOperatorNames;

    /**
     * 工单任务定义id
     */
    private String idTaskDefinition;

    /**
     * 工单任务时效
     */
    private String taskLimit;
    /**
     * 工单任务实例id
     */
    private String idTaskInstance;
    /**
     * 工单任务资源id
     */
    private String idTaskResource;
    /**
     * 工单记录id
     */
    private String idWorkorderRecord;
    /**
     * 工单任务记录id
     */
    private String idWorkorderTaskRecord;
    /**
     * 任务所有人
     */
    private String owner;
    /**
     * 来源系统标识
     */
    private String sourceProjectName;
    /**
     * 工单任务描述信息（描述工单任务工作内容）
     */
    private String taskDescription;
    /**
     * 工单任务实例名称
     */
    private String taskInstanceName;
    /**
     * 工单任务备注
     */
    private String taskRemarks;
    /**
     * 工单任务状态，0：新提交，1：进行中，2：已完成，3：已关闭
     */
    private String taskStatus;

    /**
     * 接口任务编号
     */
    private String taskCode;
}
