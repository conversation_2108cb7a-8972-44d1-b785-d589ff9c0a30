<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fls.workorder.mapper.WorkorderTaskRecordMapper">
    <select id="getTaskVoListByWorkorderId" resultType="com.fls.workorder.pojo.vo.TaskRecordDetailVo">
        select twt.*, twd.visit_param, twd.visit_path, twd.applet_visit_param, twd.applet_visit_path, twd.co_submit_flag
        from t_workorder_task_record twt
                 join t_workorder_task_definition twd on twt.id_task_definition = twd.id_task_definition
        WHERE twt.id_workorder_record = #{workorderId}
          AND (twt.ids_assignee IS NULL OR twt.ids_assignee != 'system')
          and twt.delete_flag = '0' ORDER BY twt.create_time
    </select>

    <select id="getTaskDetailByParam" resultType="com.fls.todo.api.model.TodoTaskVo">
        select twd.visit_param, twd.visit_path, twd.applet_visit_param, twd.applet_visit_path,
        '0' as taskType,twt.assignee_type, twt.id_task_resource as idResource,
        twt.id_workorder_task_record as idSourceRecord,twt.task_code as taskCode,
        twt.task_instance_name as recordName,twt.source_project_name as sourceProjectName,
        '5' as taskStatus,twt.id_biz_unit as idBizunit,tbb.name as bizunitName,
        twt.start_time as startTime,twt.end_time as endTime,twt.create_by as initiator,twt.create_time as createTime,
        tbu.name as initiatorName,tbr.name as resourceName,twt.id_workorder_task_record as idTodoTask,twr.order_code as
        sourceRecordCode
        from t_workorder_task_record twt
        left join t_workorder_record twr on twt.id_workorder_record = twr.id_workorder_record
        left join t_workorder_task_definition twd on twt.id_task_definition = twd.id_task_definition
        left join t_base_bizunit tbb on twt.id_biz_unit = tbb.id_bizunit
        left join t_base_user tbu on twt.create_by = tbu.id_user
        left join t_base_resource tbr on twt.id_task_resource = tbr.id_resource
        WHERE twt.delete_flag = '0' and twt.task_status = '1'
        <if test="query.idResource != null and query.idResource != ''">
            AND twt.id_task_resource = #{query.idResource}
        </if>
        <if test="query.idUser != null and query.idUser != ''">
            AND twt.ids_candidate LIKE CONCAT('%', #{query.idUser}, '%')
        </if>
        <if test="query.sourceProjectName != null and query.sourceProjectName != ''">
            AND twt.source_project_name = #{query.sourceProjectName}
        </if>
        <if test="query.keyword != null and query.keyword != ''">
            AND (twt.task_instance_name LIKE CONCAT('%', #{query.keyword}, '%') or twt.task_code LIKE CONCAT('%',
            #{query.keyword}, '%'))
        </if>
        <if test="query.recordCode != null and query.recordCode != ''">
            AND twr.order_code LIKE CONCAT('%', #{query.recordCode}, '%')
        </if>
        <if test="query.idBizunit != null and query.idBizunit != ''">
            AND twt.id_biz_unit = #{query.idBizunit}
        </if>
        <if test="query.beginDate != null and query.beginDate != ''">
            AND twt.create_time &gt;= CONCAT(#{query.beginDate}, ' 00:00:00')
        </if>

        <if test="query.endDate != null and query.endDate != ''">
            AND twt.create_time &lt;= CONCAT(#{query.endDate}, ' 23:59:59')
        </if>
        ORDER BY twt.create_time DESC
    </select>

    <select id="selectTaskCenterPage" resultType="com.fls.workorder.pojo.vo.TaskCenterDetailVo">
        SELECT
        twt.id_workorder_task_record AS idTask,
        twt.task_instance_name AS taskName,
        twt.task_code AS taskCode,
        twt.task_status AS taskStatus,
        twt.id_task_resource as idResource,
        CONCAT(twr.workorder_instance_name, ' ', twr.order_code) AS sourceName,
        twt.id_biz_unit AS idBizunit,
        tbb.name AS bizunitName,
        twr.initiator AS initiator,
        twt.assignee_type AS assigneeType,
        twt.ids_assignee AS idsAssignee,
        twt.ids_candidate AS idsCandidate,
        twt.ids_co_operator AS idsCoOperator,
        twt.start_time AS startTime,
        twt.end_time AS endTime,
        twt.create_time as createTime,
        twtd.visit_path AS visitPath,
        twtd.visit_param AS visitParam,
        twtd.applet_visit_path AS appletVisitPath,
        twtd.applet_visit_param AS appletVisitParam,
        twt.asset_code AS assetCode,
        twt.source_project_name as sourceProjectCode
        FROM t_workorder_task_record twt
        LEFT JOIN t_workorder_task_definition twtd ON twt.id_task_definition = twtd.id_task_definition
        LEFT JOIN t_workorder_record twr ON twt.id_workorder_record = twr.id_workorder_record
        LEFT JOIN t_base_bizunit tbb ON twt.id_biz_unit = tbb.id_bizunit
        WHERE twt.delete_flag = '0' AND (twt.ids_assignee IS NULL OR twt.ids_assignee != 'system')
        <if test="query.status != null and query.status != ''">
            AND twt.task_status = #{query.status}
        </if>
        <if test="query.keyword != null and query.keyword != ''">
            AND (twt.task_code = #{query.keyword} or twt.task_instance_name LIKE CONCAT('%', #{query.keyword}, '%') or twr.order_code = #{query.keyword} or twt.asset_code LIKE CONCAT('%', #{query.keyword}, '%'))
        </if>
        <if test="query.name != null and query.name != ''">
            AND twt.task_instance_name LIKE CONCAT('%', #{query.name}, '%')
        </if>
        <if test="query.code != null and query.code != ''">
            AND twt.task_code = #{query.code}
        </if>
        <if test="query.sourceCode != null and query.sourceCode != ''">
            AND twr.order_code = #{query.sourceCode}
        </if>
        <if test="query.idBizunit != null and query.idBizunit != ''">
            AND twt.id_biz_unit = #{query.idBizunit}
        </if>
        <if test="query.idResource != null and query.idResource != ''">
            AND twt.id_task_resource = #{query.idResource}
        </if>
        <if test="query.beginDate != null and query.beginDate != ''">
            AND twt.create_time &gt;= CONCAT(#{query.beginDate}, ' 00:00:00')
        </if>
        <if test="query.endDate != null and query.endDate != ''">
            AND twt.create_time &lt;= CONCAT(#{query.endDate}, ' 23:59:59')
        </if>
        <if test="query.assignee != null and query.assignee != ''">
            AND twt.ids_assignee = #{query.assignee}
        </if>
        <if test="query.candidate != null and query.candidate != ''">
            AND twt.ids_candidate LIKE CONCAT('%', #{query.candidate}, '%')
        </if>
        <if test="query.assetCode != null and query.assetCode != ''">
            AND twt.asset_code LIKE CONCAT('%', #{query.assetCode}, '%')
        </if>
        <if test="query.authBizunitIds != null and query.authBizunitIds.size() > 0">
            AND twt.id_biz_unit IN
            <foreach item="item" collection="query.authBizunitIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY twt.create_time DESC
    </select>

    <select id="getTaskVoByIdTask" resultType="com.fls.workorder.pojo.vo.TaskRecordDetailVo">
        select twt.*, twd.visit_param, twd.visit_path, twd.applet_visit_param, twd.applet_visit_path, twd.co_submit_flag
        from t_workorder_task_record twt
                 join t_workorder_task_definition twd on twt.id_task_definition = twd.id_task_definition
        WHERE twt.id_workorder_task_record = #{idTask}
          AND (twt.ids_assignee IS NULL OR twt.ids_assignee != 'system')
          and twt.delete_flag = '0'
    </select>
</mapper>
