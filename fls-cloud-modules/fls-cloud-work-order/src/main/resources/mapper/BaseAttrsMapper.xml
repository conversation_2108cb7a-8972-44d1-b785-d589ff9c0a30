<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fls.workorder.mapper.BaseAttrsMapper">

    <select id="queryBaseAttrsByUserId" resultType="com.fls.workorder.pojo.dto.BaseAttrsDTO">
        SELECT tbp.id_org,
               tbp.id_department,
               tbp.id_bizunit,
               tbp.id_person
        FROM t_base_user tbu LEFT JOIN t_base_person tbp ON tbu.id_identity = tbp.id_person
        WHERE tbu.identity_type = '0'
          AND tbu.status = '2'
          AND tbu.delete_flag = '0'
          AND tbu.id_user = #{userId}
    </select>
    <select id="queryWorkTeamIdByUserId" resultType="java.lang.String">
        SELECT DISTINCT twt.id_workteam
        FROM t_base_user tbu
                 JOIN t_base_person tbp ON tbu.id_identity = tbp.id_person
                 LEFT JOIN t_base_workteam_member twtm ON tbp.id_person = twtm.id_member
                 LEFT JOIN t_base_workteam twt ON twtm.id_workteam = twt.id_workteam
        WHERE tbu.identity_type = '0'
          AND (twt.id_headman = tbu.id_user OR twtm.id_member IS NOT NULL)
          AND tbu.id_user = #{userId}
    </select>
    <select id="queryBaseAttrsByTeamId" resultType="com.fls.workorder.pojo.dto.BaseAttrsDTO">
        SELECT id_org, id_bizunit, id_department, id_workteam
        FROM t_base_workteam
        WHERE status = '2'
          AND delete_flag = '0'
          AND id_workteam = #{idWorkteam}
    </select>
    <select id="queryBaseAttrsByDeptId" resultType="com.fls.workorder.pojo.dto.BaseAttrsDTO">
        SELECT id_org, id_department
        FROM t_base_department
        WHERE status = '2'
          AND delete_flag = '0'
          AND id_department = #{idDept}
    </select>
    <select id="queryBaseAttrsByBizId" resultType="com.fls.workorder.pojo.dto.BaseAttrsDTO">
        SELECT id_org, id_bizunit
        FROM t_base_bizunit
        WHERE status = '2'
          AND delete_flag = '0'
          AND id_bizunit = #{idBiz}
    </select>
    <select id="queryNamesByIds" resultType="com.fls.workorder.pojo.dto.UserNameDTO">
        SELECT
        id_user,
        name
        FROM
        t_base_user
        WHERE
        id_user IN
        <foreach collection="idsUser" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND status = '2'
        AND delete_flag = '0'
    </select>
</mapper>
