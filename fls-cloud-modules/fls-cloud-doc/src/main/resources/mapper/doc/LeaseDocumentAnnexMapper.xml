<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fls.doc.mapper.LeaseDocumentAnnexMapper">
    <insert id="saveAnnex" parameterType="com.fls.doc.param.DocumentAnnexParam">
        INSERT INTO t_lease_document_annex (id_annex, id_doc, id_doc_cat,${valTable},creator)
        VALUES (#{idAnnex},#{idDoc},#{idDocCat},#{val},#{creator});
    </insert>
    <select id="selectAnnex" resultType="com.fls.doc.param.DocumentAnnexParam">
        select id_annex,id_doc,id_doc_cat,${valTable} as val
        from t_lease_document_annex where 1=1 and delete_flag='0' and ${valTable} is not null and ${valTable} != ''
        and id_doc = #{idDoc} and id_doc_cat=#{idDocCat}
    </select>
</mapper>
