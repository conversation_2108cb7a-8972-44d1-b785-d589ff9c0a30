package com.fls.doc.controller;

import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.ObjectUtil;
import com.fls.common.core.domain.ResponseData;
import com.fls.common.core.enums.FlagEnum;
import com.fls.common.core.exception.ServiceException;
import com.fls.doc.entity.*;
import com.fls.doc.enums.DocStatusEnum;
import com.fls.doc.param.DocnmertListParam;
import com.fls.doc.param.DocumentAddParam;
import com.fls.doc.param.DocumentAnnexParam;
import com.fls.doc.param.ValidList;
import com.fls.doc.result.DocAddBatchResult;
import com.fls.doc.result.LeaseDocumentListResult;
import com.fls.doc.service.*;
import com.fls.doc.utils.ApprovalConfing;
import com.fls.doc.utils.ApprovalUtils;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <p>
 * LA资料列表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-10
 */
@Slf4j
@RestController
@RequestMapping("/document")
@RequiredArgsConstructor
public class LeaseDocumentController {

    private final LeaseDocumentService leaseDocumentService;
    private final BaseResourceService resourceService;
    private final ApprovalUtils approvalUtils;
    private final ApprovalConfing approvalConfing;
    private final LeaseDocumentAnnexService annexService;

    @PostMapping(value = "listPage")
    public ResponseData<Page<LeaseDocumentListResult>> list(@RequestBody DocnmertListParam param) {
        MPJLambdaWrapper<LeaseDocument> wrapper=new MPJLambdaWrapper<>();
        wrapper.distinct().selectAll(LeaseDocument.class)
            .selectAs(BaseOrg::getShortname,LeaseDocumentListResult::getOrgName)
            .selectAs(BaseBizunit::getName,LeaseDocumentListResult::getBizunitName)
            .selectAs(BasePerson::getName,LeaseDocumentListResult::getPersonName)
            .selectAs(LeaseDocumentCategory::getDcName,LeaseDocumentListResult::getDocCatName)
            .selectAs("org",BaseOrg::getShortname,LeaseDocumentListResult::getIdInCompany)
            .selectAs("bizunit",BasePerson::getName,LeaseDocumentListResult::getKeepingBizunitName)
            .leftJoin(BaseOrg.class,BaseOrg::getIdOrg,LeaseDocument::getIdOrg)
            .leftJoin(BaseBizunit.class,BaseBizunit::getIdBizunit,LeaseDocument::getIdBizunit)
            .leftJoin(BasePerson.class,BasePerson::getIdPerson,LeaseDocument::getIdPerson)
            .leftJoin(BaseOrg.class,"org",BaseOrg::getIdOrg,LeaseDocument::getIdInCompany)
            .leftJoin(BaseBizunit.class,"bizunit",BaseBizunit::getIdBizunit,LeaseDocument::getKeepingBizunit)
            .leftJoin(LeaseDocumentCategory.class,LeaseDocumentCategory::getIdDocCat,LeaseDocument::getIdDocCat);
        if(ObjectUtil.isNotEmpty(param.getIdLinkDoc())){
            wrapper.leftJoin(LeaseDocumentRelation.class,LeaseDocumentRelation::getIdDoc,LeaseDocument::getIdDoc)
                .eq(LeaseDocumentRelation::getIdLinkDoc,param.getIdLinkDoc());
        }
        Page<LeaseDocumentListResult> aPage=new Page<>();
        if(ObjectUtil.isNotEmpty(param.getCurrent())||ObjectUtil.isNotEmpty(param.getPageSize())){
            aPage= leaseDocumentService.selectJoinListPage(new Page<>(param.getCurrent(), param.getPageSize()),LeaseDocumentListResult.class,wrapper);
        }else {
            List<LeaseDocumentListResult> results = leaseDocumentService.selectJoinList(LeaseDocumentListResult.class, wrapper);
            aPage.setRecords(results);
            aPage.setTotal(ObjectUtil.isNotEmpty(results.size())?results.size():0L);
        }
        for (LeaseDocumentListResult record : aPage.getRecords()) {
            List<DocumentAnnexParam> annexParams = annexService.getAnnexParams(record.getIdDoc(), record.getIdDocCat());
            record.setAnnexParams(annexParams);
        }
        return ResponseData.ok(aPage);
    }
    @PostMapping(value = "quoteListPage")
    public ResponseData<Page<LeaseDocumentListResult>> quoteList(@RequestBody DocnmertListParam param) {
        MPJLambdaWrapper<LeaseDocument> wrapper=new MPJLambdaWrapper<>();
        wrapper.distinct().selectAll(LeaseDocument.class)
            .selectAs(BaseOrg::getShortname,LeaseDocumentListResult::getOrgName)
            .selectAs(BaseBizunit::getName,LeaseDocumentListResult::getBizunitName)
            .selectAs(BasePerson::getName,LeaseDocumentListResult::getPersonName)
            .selectAs(LeaseDocumentCategory::getDcName,LeaseDocumentListResult::getDocCatName)
            .selectAs("org",BaseOrg::getShortname,LeaseDocumentListResult::getIdInCompany)
            .selectAs("bizunit",BasePerson::getName,LeaseDocumentListResult::getKeepingBizunitName)
            .leftJoin(BaseOrg.class,BaseOrg::getIdOrg,LeaseDocument::getIdOrg)
            .leftJoin(BaseBizunit.class,BaseBizunit::getIdBizunit,LeaseDocument::getIdBizunit)
            .leftJoin(BasePerson.class,BasePerson::getIdPerson,LeaseDocument::getIdPerson)
            .leftJoin(BaseOrg.class,"org",BaseOrg::getIdOrg,LeaseDocument::getIdInCompany)
            .leftJoin(BaseBizunit.class,"bizunit",BaseBizunit::getIdBizunit,LeaseDocument::getKeepingBizunit)
            .leftJoin(LeaseDocumentCategory.class,LeaseDocumentCategory::getIdDocCat,LeaseDocument::getIdDocCat)
            .eq(LeaseDocument::getIdOrg,param.getIdOrg())
            .eq(LeaseDocument::getIdDocCat,param.getIdDocCat())
            .eq(LeaseDocument::getStatus,DocStatusEnum.NORMAL.getCode())
            .eq(LeaseDocumentCategory::getQuoteFlag, FlagEnum.YES.getCode());
        if(ObjectUtil.isNotEmpty(param.getIdLinkDocs())){
            wrapper.leftJoin(LeaseDocumentRelation.class,LeaseDocumentRelation::getIdDoc,LeaseDocument::getIdDoc)
                .in(LeaseDocumentRelation::getIdLinkDoc,param.getIdLinkDocs());
        }
        Page<LeaseDocumentListResult> aPage=new Page<>();
        if(ObjectUtil.isNotEmpty(param.getCurrent())||ObjectUtil.isNotEmpty(param.getPageSize())){
            aPage= leaseDocumentService.selectJoinListPage(new Page<>(param.getCurrent(), param.getPageSize()),LeaseDocumentListResult.class,wrapper);
        }else {
            List<LeaseDocumentListResult> results = leaseDocumentService.selectJoinList(LeaseDocumentListResult.class, wrapper);
            aPage.setRecords(results);
            aPage.setTotal(ObjectUtil.isNotEmpty(results.size())?results.size():0L);
        }
        for (LeaseDocumentListResult record : aPage.getRecords()) {
            List<DocumentAnnexParam> annexParams = annexService.getAnnexParams(record.getIdDoc(), record.getIdDocCat());
            record.setAnnexParams(annexParams);
        }
        return ResponseData.ok(aPage);
    }

    @PostMapping(value = "entityList")
    public ResponseData<Object> entityList(@RequestBody Dict dict){
        String idLinkDoc = dict.getStr("idLinkDoc");
        MPJLambdaWrapper<LeaseDocument> wrapper=new MPJLambdaWrapper<>();
        wrapper.selectAll(LeaseDocument.class)
            .selectAs(BaseOrg::getShortname,LeaseDocumentListResult::getOrgName)
            .selectAs(BaseBizunit::getName,LeaseDocumentListResult::getBizunitName)
            .selectAs(BasePerson::getName,LeaseDocumentListResult::getPersonName)
            .selectAs(LeaseDocumentCategory::getDcName,LeaseDocumentListResult::getDocCatName)
            .selectAs("org",BaseOrg::getShortname,LeaseDocumentListResult::getInCompanyName)
            .selectAs("bizunit",BasePerson::getName,LeaseDocumentListResult::getKeepingBizunitName)
            .leftJoin(BaseOrg.class,BaseOrg::getIdOrg,LeaseDocument::getIdOrg)
            .leftJoin(BaseBizunit.class,BaseBizunit::getIdBizunit,LeaseDocument::getIdBizunit)
            .leftJoin(BasePerson.class,BasePerson::getIdPerson,LeaseDocument::getIdPerson)
            .leftJoin(BaseOrg.class,"org",BaseOrg::getIdOrg,LeaseDocument::getIdInCompany)
            .leftJoin(BaseBizunit.class,"bizunit",BaseBizunit::getIdBizunit,LeaseDocument::getKeepingBizunit)
            .leftJoin(LeaseDocumentRelation.class,LeaseDocumentRelation::getIdDoc,LeaseDocument::getIdDoc)
            .leftJoin(LeaseDocumentCategory.class,LeaseDocumentCategory::getIdDocCat,LeaseDocument::getIdDocCat)
            .eq(LeaseDocumentRelation::getIdLinkDoc,idLinkDoc)
            .in(LeaseDocument::getStatus,Arrays.asList(DocStatusEnum.NORMAL.getCode(),DocStatusEnum.INVALID.getCode()));
        List<LeaseDocumentListResult> list = leaseDocumentService.selectJoinList(LeaseDocumentListResult.class,wrapper);
        for (LeaseDocumentListResult record : list) {
            List<DocumentAnnexParam> annexParams = annexService.getAnnexParams(record.getIdDoc(), record.getIdDocCat());
            record.setAnnexParams(annexParams);
        }
        return ResponseData.ok(list);
    }
    @PostMapping(value = "saveBatch")
    public ResponseData<Object> saveBatch(@RequestBody @Validated ValidList<DocumentAddParam> params) {
        DocAddBatchResult result = leaseDocumentService.saveBatch(params);
        List<String> ids = result.getDatamaintainanceIds();
        Map<String,Object> map=new HashMap<>();
        map.put("idLinkDoc",result.getIdLinkDoc());
        if(ObjectUtil.isNotEmpty(ids)){
            for (String id : ids) {
                BaseResource resource =resourceService.getResourceByCode(approvalConfing.getResourceCodeDocApply());
                String submitMethod="resobj.submit";
                String resourceCode=resource.getCode();
                String idUser = params.get(0).getIdUser();
                try {
                    approvalUtils.submitAudit(submitMethod, resourceCode, id, idUser);
                } catch (Exception e) {
                    log.error("资料申请流程提交失败！资源id={}，单据id={}",resource.getIdResource(),id);
                    return ResponseData.fail("资料保存成功！资料维护申请流程提交失败:"+ e.getMessage(),map);
                }
            }
        }
        return ResponseData.ok("资料保存成功!",map);
    }
    @PostMapping(value = "invalid")
    public ResponseData<Object> invalid(@RequestBody Dict dict) {
        String idDoc = dict.getStr("idDoc");
        LeaseDocument document = leaseDocumentService.getById(idDoc);
        if (ObjectUtil.isEmpty(document)) {
            throw new ServiceException("未查询到资料档案，请检查！");
        }
        document.setStatus(DocStatusEnum.DAMAGE.getCode());
        return ResponseData.ok(leaseDocumentService.updateById(document));
    }

}
