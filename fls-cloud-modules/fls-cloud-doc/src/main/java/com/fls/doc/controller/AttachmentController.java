package com.fls.doc.controller;

import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.fls.common.core.constant.CommonConstants;
import com.fls.common.core.domain.ResponseData;
import com.fls.common.core.exception.ServiceException;
import com.fls.doc.entity.Attachment;
import com.fls.doc.entity.BaseUser;
import com.fls.doc.result.AttachmentResult;
import com.fls.doc.service.AttachmentService;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;


/**
 * <p>
 * 附件表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-10
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("attachment")
public class AttachmentController {
    private final AttachmentService attachmentService;
    @PostMapping(value = "list")
    public ResponseData<List<AttachmentResult>> list(@RequestBody Dict dict) {
        String idLink = dict.getStr("idLink");
        if (ObjectUtil.isEmpty(idLink)){
            throw new ServiceException("缺少必填参数[idLink]");
        }
        MPJLambdaWrapper<Attachment> wrapper = new MPJLambdaWrapper<>();
        wrapper.selectAll(Attachment.class)
            .selectAs(BaseUser::getName, AttachmentResult::getCreatorName)
            .leftJoin(BaseUser.class,BaseUser::getIdUser,Attachment::getCreator)
            .eq(Attachment::getIdLink,idLink)
            .eq(Attachment::getDeleteFlag, CommonConstants.DELETE_FLAG_NOT_DELETED);
        return ResponseData.ok(attachmentService.selectJoinList(AttachmentResult.class,wrapper));
    }

    @PostMapping(value = "getOne")
    public ResponseData<Attachment> getOne(@RequestBody Dict dict) {
        String idAttachment = dict.getStr("idAttachment");
        if (ObjectUtil.isEmpty(idAttachment)){
            throw new ServiceException("缺少必填参数[idAttachment]");
        }
        return ResponseData.ok(attachmentService.getById(idAttachment));
    }

    @PostMapping(value = "upload")
    public ResponseData<List<Attachment>> upload(@RequestParam("files") MultipartFile[] files, @RequestParam(value = "identity", required = false) String identity, @RequestParam(value = "idResource", required = false) String idResource, @RequestParam(value = "idLink", required = false) String idLink, @RequestParam(value = "idUser", required = false) String idUser) {
        return ResponseData.ok(attachmentService.uploads(files, identity, idResource, idLink, idUser));
    }

    @GetMapping("download/{idAttachment}")
    public void download(HttpServletResponse response, @PathVariable("idAttachment") String idAttachment) {
        attachmentService.download(response, null, idAttachment);
    }

    @GetMapping("download/{identify}/{idAttachment}")
    public void download(HttpServletResponse response, @PathVariable("identify") String identify, @PathVariable("idAttachment") String idAttachment) {
        attachmentService.download(response, identify, idAttachment);
    }

    @PostMapping(value = "delete")
    public ResponseData<Boolean> delete(@RequestBody Dict dict) {
        String idAttachment = dict.getStr("idAttachment");
        if (ObjectUtil.isEmpty(idAttachment)) {
            throw new ServiceException("缺少必填参数[idAttachment]");
        }
        return ResponseData.ok(attachmentService.del(idAttachment));
    }
    @PostMapping("downloadZip")
    public void downloadZip(HttpServletResponse response, @RequestBody Dict dict){
        String idAttachments=dict.getStr("idAttachments");
        if (ObjectUtil.isEmpty(idAttachments)) {
            throw new ServiceException("附件id不能为空");
        }
        try {
            List<String> ids = JSON.parseArray(idAttachments, String.class);
            attachmentService.downloadZip(response,ids);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @GetMapping("downloadZip")
    public void downloadZip(HttpServletResponse response,String idAttachments){
        //设置返回响应头
        response.reset();
        if (ObjectUtil.isEmpty(idAttachments)) {
            throw new ServiceException("附件id不能为空");
        }
        String[] split = idAttachments.split(",");
        List<String> ids = Arrays.stream(split).collect(Collectors.toList());
        try {
            attachmentService.downloadZip(response,ids);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }
}
