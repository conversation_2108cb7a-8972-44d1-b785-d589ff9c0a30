package com.fls.doc.service;

import com.fls.doc.entity.LeaseDocumentAnnex;
import com.baomidou.mybatisplus.extension.service.IService;
import com.fls.doc.param.DocumentAnnexParam;

import java.util.List;

/**
 * <p>
 * LA资料附加参数表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-10
 */
public interface LeaseDocumentAnnexService extends IService<LeaseDocumentAnnex> {
    Boolean add(DocumentAnnexParam param);

    List<DocumentAnnexParam> getAnnexParams(String idDoc,String idDocCat);
}
