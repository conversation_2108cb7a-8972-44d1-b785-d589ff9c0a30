package com.fls.doc.controller;

import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fls.common.core.constant.CommonConstants;
import com.fls.common.core.domain.ResponseData;
import com.fls.common.core.exception.ServiceException;
import com.fls.doc.entity.*;
import com.fls.doc.result.DocumentCategoryRecListResult;
import com.fls.doc.service.LeaseDocumentCategoryService;
import com.fls.doc.service.LeaseDocumentTypeService;
import com.fls.doc.service.LeaseDocumentValidatorService;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * LA资料分类 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-10
 */
@RestController
@RequestMapping("/document/type")
@RequiredArgsConstructor
public class LeaseDocumentTypeController {
    private final LeaseDocumentTypeService documentTypeService;

    @PostMapping(value = "list")
    public ResponseData<Object> list(@RequestBody Dict dict) {
        String code = dict.getStr("code");
        String name = dict.getStr("name");
        MPJLambdaWrapper<LeaseDocumentType> wrapper=new MPJLambdaWrapper<>();
        wrapper.selectAll(LeaseDocumentType.class)
            .eq(ObjectUtil.isNotEmpty(code),LeaseDocumentType::getDtCode,code)
            .eq(ObjectUtil.isNotEmpty(name),LeaseDocumentType::getDtName,name);
        return ResponseData.ok(documentTypeService.selectJoinList(LeaseDocumentType.class,wrapper));
    }
}
