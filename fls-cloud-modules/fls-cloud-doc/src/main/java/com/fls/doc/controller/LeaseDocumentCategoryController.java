package com.fls.doc.controller;

import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.ObjectUtil;
import com.fls.common.core.constant.CommonConstants;
import com.fls.common.core.domain.ResponseData;
import com.fls.common.core.exception.ServiceException;
import com.fls.doc.entity.*;
import com.fls.doc.result.DocumentCategoryRecListResult;
import com.fls.doc.service.LeaseDocumentValidatorService;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fls.doc.service.LeaseDocumentCategoryService;

import java.util.List;

/**
 * <p>
 * LA资料分类 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-10
 */
@RestController
@RequestMapping("/document/category")
@RequiredArgsConstructor
public class LeaseDocumentCategoryController {
    private final LeaseDocumentCategoryService documentCategoryService;
    private final LeaseDocumentValidatorService validatorService;
    @Value("${document.category.default-validator-id}")
    private String defaultValidatorId;

    @PostMapping(value = "list")
    public ResponseData<Page<LeaseDocumentCategory>> list(@RequestBody Dict dict) {
        String idDocType = dict.getStr("idDocType");
        String code = dict.getStr("code");
        String name = dict.getStr("name");
        long current =dict.getLong("current");
        long pageSize = dict.getLong("pageSize");
        MPJLambdaWrapper<LeaseDocumentCategory> wrapper=new MPJLambdaWrapper<>();
        wrapper.selectAll(LeaseDocumentCategory.class)
            .selectAs(LeaseDocumentType::getDtName,LeaseDocumentCategory::getDocTypeName)
            .leftJoin(LeaseDocumentType.class,LeaseDocumentType::getIdDocType,LeaseDocumentCategory::getIdDocCat)
            .eq(ObjectUtil.isNotEmpty(code),LeaseDocumentCategory::getCode,code)
            .eq(ObjectUtil.isNotEmpty(name),LeaseDocumentCategory::getDcName,name)
            .eq(ObjectUtil.isNotEmpty(idDocType),LeaseDocumentCategory::getIdDocType,idDocType);
        Page<LeaseDocumentCategory> aPage = new Page<>();
        if (current==0||pageSize==0) {
            documentCategoryService.selectJoinList(LeaseDocumentCategory.class,wrapper);
        }else {
            aPage=documentCategoryService.selectJoinListPage(new Page<>(current, pageSize),LeaseDocumentCategory.class,wrapper);
        }
        return ResponseData.ok(aPage);
    }

    @PostMapping(value = "recList")
    public ResponseData<List<DocumentCategoryRecListResult>> recList(@RequestBody Dict dict) {
        String idResource = dict.getStr("idResource");
        if (ObjectUtil.isEmpty(idResource)) {
            throw new ServiceException("缺少必填项[idResource]");
        }
        MPJLambdaWrapper<LeaseDocumentCategory> wrapper=new MPJLambdaWrapper<>();
        wrapper.selectAll(LeaseDocumentCategory.class)
            .selectAs(LeaseDocumentType::getDtName,LeaseDocumentCategory::getDocTypeName)
            .leftJoin(LeaseDocumentType.class,LeaseDocumentType::getIdDocType,LeaseDocumentCategory::getIdDocType)
            .selectCollection(LeaseDocumentParameters.class,DocumentCategoryRecListResult::getExts)
            .leftJoin(LeaseDocumentParameters.class,on -> on.eq(LeaseDocumentParameters::getIdDocCat,LeaseDocumentCategory::getIdDocCat).eq(LeaseDocumentParameters::getDeleteFlag,CommonConstants.DELETE_FLAG_NOT_DELETED))
            .selectCollection(LeaseDocumentValidator.class,DocumentCategoryRecListResult::getValidators)
            .leftJoin(LeaseDocumentValidator.class,LeaseDocumentValidator::getIdDocCat,LeaseDocumentCategory::getIdDocCat)
            .leftJoin(BaseResdoccate.class,BaseResdoccate::getIdDoccate,LeaseDocumentCategory::getIdDocCat)
            .eq(BaseResdoccate::getIdResource,idResource)
            .eq(LeaseDocumentCategory::getStatus, CommonConstants.COMMON_STATUS_NORMAL)
            .eq(LeaseDocumentCategory::getDeleteFlag,CommonConstants.DELETE_FLAG_NOT_DELETED);
        List<DocumentCategoryRecListResult> results = documentCategoryService.selectJoinList(DocumentCategoryRecListResult.class, wrapper);
        for (DocumentCategoryRecListResult result : results) {
            if (ObjectUtil.isEmpty(result.getValidators())) {
                result.getValidators().add(validatorService.getOne(new MPJLambdaWrapper<LeaseDocumentValidator>().eq(LeaseDocumentValidator::getIdDocCat, defaultValidatorId).last("limit 1")));
            }
        }
        return ResponseData.ok(results);
    }
}
