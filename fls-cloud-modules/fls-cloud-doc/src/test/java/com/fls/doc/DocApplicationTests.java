package com.fls.doc;


import cn.hutool.core.util.HexUtil;
import cn.hutool.core.util.ObjectUtil;
import com.fls.common.core.enums.FlagEnum;
import com.fls.common.core.exception.ServiceException;
import com.fls.doc.entity.LeaseDocumentParameters;
import com.fls.doc.entity.LeaseDocumentValidator;
import com.fls.doc.param.DocumentAddParam;
import com.fls.doc.param.DocumentAnnexParam;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@SpringBootTest
@Slf4j
class DocApplicationTests {

    @Test
    void contextLoads() {
    }
    @Test
    public void test(){
        Set<String> fileNames=new HashSet<>();
        fileNames.add("sdd.jpg");
        fileNames.add("sdd(1).jpg");
        String fileName="sdd.jpg";
        String fileName1 = getFileName(fileNames, fileName);
        System.out.println(fileName1+"**********"+fileName);
    }
    private String getFileName(Set<String> fileNames, String fileName){
        for (String name : fileNames) {
            if (name.equals(fileName)) {
                String suffix = fileName.substring(fileName.lastIndexOf("."));
                fileName=fileName.replace(suffix,"(1)"+suffix);
                getFileName(fileNames,fileName);
            }
        }
        return fileName;
    }
    @Test
    public void test1(){
        DocumentAddParam params=new DocumentAddParam();
        LeaseDocumentValidator validator= new LeaseDocumentValidator();
        validator.setIdOrgRequireFlag("1");
        validator.setIdBizunitRequireFlag("1");
        validator.setIdOrgKeepRequireFlag("1");
        validator.setIdBizunitKeepRequireFlag("1");
        validator.setIdPersonRequireFlag("1");
        validator.setPlaceRequireFlag("1");
        verificationDocumentValidator(params,validator);
    }

    private void verificationDocumentValidator(DocumentAddParam params, LeaseDocumentValidator validator) {
        assert !validator.getIdOrgRequireFlag().equals(FlagEnum.YES.getCode()) || !ObjectUtil.isEmpty(params.getIdOrg()) : "缺少必填参数[idOrg]";
        assert !validator.getIdBizunitRequireFlag().equals(FlagEnum.YES.getCode())  || !ObjectUtil.isEmpty(params.getIdBizunit()) : "缺少必填参数[idBizunit]";
        assert !validator.getIdOrgKeepRequireFlag().equals(FlagEnum.YES.getCode())  || !ObjectUtil.isEmpty(params.getIdInCompany()) : "缺少必填参数[idInCompany]";
        assert !validator.getIdBizunitKeepRequireFlag().equals(FlagEnum.YES.getCode())  || !ObjectUtil.isEmpty(params.getKeepingBizunit()) : "缺少必填参数[keepingBizunit]";
        assert !validator.getIdPersonRequireFlag().equals(FlagEnum.YES.getCode())  || !ObjectUtil.isEmpty(params.getIdPerson()) : "缺少必填参数[idPerson]";
        assert !validator.getPlaceRequireFlag().equals(FlagEnum.YES.getCode())  || !ObjectUtil.isEmpty(params.getLdPlace()) : "缺少必填参数[ldPlace]";
    }
    @Test
    public void tset2(){
        List<LeaseDocumentParameters> documentParameters =new ArrayList<>();
        LeaseDocumentParameters parameters=new LeaseDocumentParameters();
        parameters.setIdAdditionParem("11111");
        parameters.setName("测试1");
        parameters.setSeltiveFlag("1");
        documentParameters.add(parameters);
        LeaseDocumentParameters parameters1=new LeaseDocumentParameters();
        parameters1.setIdAdditionParem("22222");
        parameters1.setName("测试0");
        parameters1.setSeltiveFlag("1");
        documentParameters.add(parameters1);
        List<DocumentAnnexParam> annexParams=new ArrayList<>();
        DocumentAnnexParam param=new DocumentAnnexParam();
        param.setIdAdditionParem("11111");
        param.setVal("123123");
        annexParams.add(param);
        DocumentAddParam params=new DocumentAddParam();
        params.setAnnexParams(annexParams);
        if (ObjectUtil.isNotEmpty(documentParameters)) {
            for (LeaseDocumentParameters parameter : documentParameters) {
                String idAdditionParem=null;
                for (DocumentAnnexParam annexParam : params.getAnnexParams()) {
                    //如果有相同的参数并且必填但是参数为空的报错
                    if (parameter.getIdAdditionParem().equals(annexParam.getIdAdditionParem())) {
                        idAdditionParem=parameter.getIdAdditionParem();
                        if (parameter.getSeltiveFlag().equals(FlagEnum.YES.getCode()) && ObjectUtil.isEmpty(annexParam.getVal())) {
                            throw new ServiceException("资料分类额外参数[" + parameter.getName() + "]值必填，请检查");
                        }
                    }
                }
                //如果上面没有找到并且这个参数必填报错
                if (ObjectUtil.isEmpty(idAdditionParem)&& parameter.getSeltiveFlag().equals(FlagEnum.YES.getCode())){
                    throw new ServiceException("缺少资料分类额外必填参数[" + parameter.getName() + "]");
                }
            }
        }
    }
    @Test
    public void test3(){

    }

}

