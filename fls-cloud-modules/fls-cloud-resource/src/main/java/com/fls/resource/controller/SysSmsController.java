package com.fls.resource.controller;


import cn.hutool.core.util.RandomUtil;
import com.fls.common.core.constant.CacheConstants;
import com.fls.common.core.constant.CommonConstants;
import com.fls.common.core.domain.ResponseData;
import com.fls.common.core.utils.SpringUtils;
import com.fls.common.core.web.controller.BaseController;
import com.fls.common.message.config.SmsAutoConfiguration;
import com.fls.common.redis.utils.RedisUtils;
import com.fls.common.message.config.properties.SmsProperties;
import com.fls.common.message.core.SmsTemplate;
import com.fls.common.message.entity.SmsResult;
import com.fls.resource.entity.bo.SendSmsBO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

/**
 * 短信功能
 *
 * <AUTHOR>
 * @date 2022/06/28
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/sms")
public class SysSmsController extends BaseController {

    private final SmsProperties smsProperties;

    @Autowired
    private SmsAutoConfiguration smsAutoConfiguration;

    @Value("${sms.templateId}")
    private String templateId;
    /**
     * 短信验证码
     *
     * @param phonenumber 用户手机号
     */
    @GetMapping("/code")
    public ResponseData<String> smsCaptcha(@NotBlank(message = "{user.phonenumber.not.blank}") String phonenumber) {
        if (!smsProperties.getEnabled()) {
            ResponseData.fail("当前系统没有开启短信功能！");
        }
        String key = CacheConstants.CAPTCHA_CODE_KEY + phonenumber;
        String code = RandomUtil.randomNumbers(4);
        RedisUtils.setCacheObject(key, code, Duration.ofSeconds(CommonConstants.CAPTCHA_EXPIRATION));
        // 验证码模板id 自行处理 (查数据库或写死均可)
        Map<String, String> map = new HashMap<>(1);
        map.put("code", code);
        SmsTemplate smsTemplate = SpringUtils.getBean(SmsTemplate.class);
        SmsResult result = smsTemplate.send(phonenumber, templateId, map);
        if (!result.getIsSuccess()) {
            log.error("验证码短信发送异常 => {}", result);
            return ResponseData.fail(result.getMessage());
        }
        return ResponseData.ok("发送成功",code);
    }

    @PostMapping("/sendBySms")
    public ResponseData<Void> sendBySms(@RequestBody SendSmsBO sendSmsBO) {
        if (!smsProperties.getEnabled()) {
            ResponseData.fail("当前系统没有开启短信功能！");
        }
        SmsTemplate smsTemplate = SpringUtils.getBean(SmsTemplate.class);
        SmsResult result = smsTemplate.send(sendSmsBO.getPhones(), sendSmsBO.getTemplateId(), sendSmsBO.getParam());
        if (!result.getIsSuccess()) {
            log.error("短信发送异常 => {}", result);
            return ResponseData.fail(result.getMessage());
        }
        return ResponseData.ok();
    }
}
