package com.fls.resource.service;

import com.fls.resource.entity.bo.SysOssBo;
import com.fls.common.mybatis.core.page.PageQuery;
import com.fls.common.mybatis.core.page.TableDataInfo;
import com.fls.resource.entity.SysOss;
import com.fls.resource.entity.vo.SysOssVo;
import org.springframework.web.multipart.MultipartFile;

import java.util.Collection;
import java.util.List;

/**
 * 文件上传 服务层
 *
 * <AUTHOR>
 * @date 2022/06/28
 */
public interface ISysOssService {

    TableDataInfo<SysOssVo> queryPageList(SysOssBo sysOss, PageQuery pageQuery);

    List<SysOssVo> listByIds(Collection<Long> ossIds);

    SysOss getById(Long ossId);

    SysOss upload(MultipartFile file);

    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

}
