package com.fls.resource.controller;

import com.fls.common.core.domain.ResponseData;
import com.fls.resource.entity.bo.SysOssConfigBo;
import com.fls.resource.entity.vo.SysOssConfigVo;
import com.fls.resource.service.ISysOssConfigService;
import com.fls.common.core.validate.AddGroup;
import com.fls.common.core.validate.EditGroup;
import com.fls.common.core.validate.QueryGroup;
import com.fls.common.core.web.controller.BaseController;
import com.fls.common.mybatis.core.page.PageQuery;
import com.fls.common.mybatis.core.page.TableDataInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;

/**
 * 对象存储配置Controller
 *
 * <AUTHOR>
 * @date 2022/06/28
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/oss/config")
public class SysOssConfigController extends BaseController {

    private final ISysOssConfigService iSysOssConfigService;

    /**
     * 查询对象存储配置列表
     */
    @GetMapping("/list")
    public TableDataInfo<SysOssConfigVo> list(@Validated(QueryGroup.class) SysOssConfigBo bo, PageQuery pageQuery) {
        return iSysOssConfigService.queryPageList(bo, pageQuery);
    }

    /**
     * 获取对象存储配置详细信息
     *
     * @param ossConfigId OSS配置ID
     */
    @GetMapping("/{ossConfigId}")
    public ResponseData<SysOssConfigVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable("ossConfigId") Long ossConfigId) {
        return ResponseData.ok(iSysOssConfigService.queryById(ossConfigId));
    }

    /**
     * 新增对象存储配置
     */
    @PostMapping("/add")
    public ResponseData<Void> add(@Validated(AddGroup.class) @RequestBody SysOssConfigBo bo) {
        return toAjax(iSysOssConfigService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改对象存储配置
     */
    @PostMapping("/edit")
    public ResponseData<Void> edit(@Validated(EditGroup.class) @RequestBody SysOssConfigBo bo) {
        return toAjax(iSysOssConfigService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除对象存储配置
     *
     * @param ossConfigIds OSS配置ID串
     */
    @GetMapping("/{ossConfigIds}")
    public ResponseData<Void> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ossConfigIds) {
        return toAjax(iSysOssConfigService.deleteWithValidByIds(Arrays.asList(ossConfigIds), true) ? 1 : 0);
    }

    /**
     * 状态修改
     */
    @PostMapping("/changeStatus")
    public ResponseData<Void> changeStatus(@RequestBody SysOssConfigBo bo) {
        return toAjax(iSysOssConfigService.updateOssConfigStatus(bo));
    }
}
