package com.fls.master.pojo.vo;

import com.fls.master.entity.BaseResTranType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.time.LocalDateTime;

/**
 * ResTranTypeVO
 *
 * <AUTHOR>
 */
@Data
public class ResTranTypeVO {

    @Schema(description = "主键")
    private String idResTrantype;

    @Schema(description = "资源主键")
    private String idResource;

    @Schema(description = "资源编码")
    private String resourceCode;

    @Schema(description = "交易类型")
    private String tranType;

    @Schema(description = "是否启用审批流，0=假，1=真")
    private String needprocFlag;

    @Schema(description = "启用状态：1=未启用，2=已启用，3=已停用")
    private String status;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "创建人")
    private String creator;

    @Schema(description = "作废时间")
    private LocalDateTime invalidTime;

    @Schema(description = "作废人")
    private String invalider;

    @Schema(description = "交易类型编码")
    private String tranCode;

    @Schema(description = "是否系统预置0=否，1=是，")
    private String presetFlag;

    @Schema(description = "显示顺序")
    private Integer displayOrder;

    @Schema(description = "资源审核页url地址")
    private String incalLink;

    @Schema(description = "NC交易类型主键")
    private String pkBilltypeid;

    public static ResTranTypeVO of(BaseResTranType source) {
        ResTranTypeVO target = new ResTranTypeVO();
        BeanUtils.copyProperties(source,target);
        return target;
    }
}
