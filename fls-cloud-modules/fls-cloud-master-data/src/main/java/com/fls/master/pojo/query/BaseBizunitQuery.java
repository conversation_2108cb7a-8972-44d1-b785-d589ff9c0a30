package com.fls.master.pojo.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 经营主体表查询
 *
 * <AUTHOR>
 * @since 2024-11-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "经营主体表查询")
public class BaseBizunitQuery extends ArchiveQuery {
    @Schema(description = "名称")
    private String name;

    @Schema(description = "内部编号")
    private String innercode;

    @Schema(description = "经营主体类型 1=终端，2=批发，3=外贸 参见bizunit_type")
    private Integer bizunitType;

    @Schema(description = "所属组织")
    private String idOrg;

}
