package com.fls.master.pojo.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 档案详情查询详情查询
 *
 * <AUTHOR>
 * @since 2024-11-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "档案详情查询")
public class ArchiveDetailQuery {
    @Schema(description = "主键id")
    private String id;
    @Schema(description = "档案编码")
    private String code;
}
