package com.fls.master.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 资源编码表(BaseRescode)表实体类
 *
 * <AUTHOR>
 * @since 2024-11-09 11:36:47
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_base_rescode")
public class BaseRescode implements Serializable {

    private static final long serialVersionUID = 1L;

     //主键
    @TableId(value = "id_rescode", type = IdType.ASSIGN_UUID)
    private String idRescode;

     //资源主键
    private String idResource;

     //资源编码
    private String resourceCode;

     //前缀名
    private String prefix;

     //编码长度
    private Integer lenCode;

     //流水号
    private Integer flowNum;

     //启用状态：1=未启用，2=已启用，3=已停用，默认1，参见basestatus
    private String status;

     //创建时间
    private LocalDateTime createTime;

     //创建人
    private String creator;

     //时间戳
    private LocalDateTime ts;

     //是否删除：0=否，1=是，默认0，参见yesorno
    private String deleteFlag;

}

