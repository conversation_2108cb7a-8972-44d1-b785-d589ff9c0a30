package com.fls.master.convert;

import com.fls.master.entity.BaseWorkTeamMemberEntity;
import com.fls.master.pojo.vo.BaseWorkTeamMemberVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 工作班组成员表-Mapstruct
*
* <AUTHOR>
* @since  2024-11-12
*/
@Mapper
public interface BaseWorkTeamMemberConvert {
    BaseWorkTeamMemberConvert INSTANCE = Mappers.getMapper(BaseWorkTeamMemberConvert.class);

    BaseWorkTeamMemberEntity voToEntity(BaseWorkTeamMemberVO vo);

    BaseWorkTeamMemberVO entityToVo(BaseWorkTeamMemberEntity entity);

    List<BaseWorkTeamMemberVO> toVoList(List<BaseWorkTeamMemberEntity> list);

}
