package com.fls.master.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fls.common.core.constant.CommonConstants;
import com.fls.common.core.domain.ResponseData;
import com.fls.master.config.ExternalSystemApiConfig;
import com.fls.master.constant.LanYunUrlConst;
import com.fls.master.constant.ResourceCodeConst;
import com.fls.master.entity.BaseAddressEntity;
import com.fls.master.entity.BaseAssetModelnumber;
import com.fls.master.entity.BaseBrand;
import com.fls.master.entity.BaseForkliftmodelsSupplier;
import com.fls.master.entity.BaseOrg;
import com.fls.master.entity.LeaseAssetType;
import com.fls.master.entity.LeaseAssets;
import com.fls.master.pojo.dto.F4AddressDTO;
import com.fls.master.pojo.dto.F4StatusDTO;
import com.fls.master.pojo.query.AssetsQuery;
import com.fls.master.pojo.query.StatusQuery;
import com.fls.master.pojo.vo.AssetsDetailsVO;
import com.fls.master.pojo.vo.AssetsTypeVO;
import com.fls.master.pojo.vo.AssetsVO;
import com.fls.master.pojo.vo.F4AddressVO;
import com.fls.master.pojo.vo.F4StatusVO;
import com.fls.master.pojo.vo.PageResult;
import com.fls.master.service.AssetsManageService;
import com.fls.master.service.LeaseAssetTypeService;
import com.fls.master.service.LeaseAssetsService;
import com.fls.master.service.PermissionService;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * AssetsManageServiceImpl
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
@SuppressWarnings("unchecked")
public class AssetsManageServiceImpl implements AssetsManageService {

    public static final String FIXED_USER_ID = "jcsroc6rdhafkrl6698iu9brn";

    private final LeaseAssetTypeService assetsType;

    private final LeaseAssetsService assets;

    private final PermissionService permission;

    private final ExternalSystemApiConfig externalSystemApiConfig;

    @Override
    public List<AssetsTypeVO> assetsTypePublicList(StatusQuery query) {
        List<LeaseAssetType> list = assetsType.lambdaQuery()
            .eq(LeaseAssetType::getDeleteFlag, CommonConstants.DELETE_FLAG_NOT_DELETED)
            .eq(Objects.nonNull(query.getStatus()), LeaseAssetType::getStatus, query.getStatus())
            .list();

        return list.stream()
            .map(AssetsTypeVO::of)
            .collect(Collectors.toList());
    }


    @Override
    public PageResult<AssetsVO> assetsPublicPage(AssetsQuery query) {
        MPJLambdaWrapper<LeaseAssets> wrapper = getCommonAssetsWrapper(query);
        Page page = assets.selectJoinListPage(query.getPageInfo(), AssetsVO.class, wrapper);
        return new PageResult<>(page, page.getRecords());

    }

    private MPJLambdaWrapper<LeaseAssets> getCommonAssetsWrapper(AssetsQuery query) {
        MPJLambdaWrapper<LeaseAssets> wrapper = new MPJLambdaWrapper<>();
        wrapper.selectAll(LeaseAssets.class)
            .selectAs(LeaseAssetType::getName, AssetsVO::getAssetTypeName)
            .selectAs(BaseBrand::getName, AssetsVO::getBrandName)
            .selectAs(BaseAddressEntity::getFullName, AssetsVO::getAddressName)
            .selectAs(BaseAssetModelnumber::getName, AssetsVO::getModelnumberName)
            .leftJoin(LeaseAssetType.class, LeaseAssetType::getIdAssetType, LeaseAssets::getIdAssetType)
            .leftJoin(BaseBrand.class, BaseBrand::getIdBrand, LeaseAssets::getIdBrand)
            .leftJoin(BaseAddressEntity.class, BaseAddressEntity::getIdAddress, LeaseAssets::getIdAddress)
            .leftJoin(BaseAssetModelnumber.class, BaseAssetModelnumber::getIdModelnumber, LeaseAssets::getIdModelnumber)
            .eq(LeaseAssets::getDeleteFlag, CommonConstants.DELETE_FLAG_NOT_DELETED)
            .like(StrUtil.isNotBlank(query.getCode()), LeaseAssets::getLaCode, query.getCode())
            .like(StrUtil.isNotBlank(query.getName()), LeaseAssets::getLaName, query.getName())
            .eq(StrUtil.isNotBlank(query.getIdAssetType()), LeaseAssets::getIdAssetType, query.getIdAssetType())
            .eq(StrUtil.isNotBlank(query.getAssetStatus()), LeaseAssets::getAssetStatus, query.getAssetStatus());
        return wrapper;
    }


    @Override
    public PageResult<AssetsVO> assetsPrivatePage(AssetsQuery query) {
        MPJLambdaWrapper<LeaseAssets> wrapper = getCommonAssetsWrapper(query);
        List<String> resourceId = permission.getPrivateResourceId(query.getUserId(), query.getHref(), ResourceCodeConst.ASSETS_ARCHIVES_CODE);
        wrapper.in(ObjectUtil.isNotEmpty(resourceId), LeaseAssets::getIdAssets, resourceId);
        Page page = assets.selectJoinListPage(query.getPageInfo(), AssetsVO.class, wrapper);
        return new PageResult<>(page, page.getRecords());
    }


    @Override
    public AssetsDetailsVO assetsPrivateDetails(String laCode) {
        MPJLambdaWrapper<LeaseAssets> wrapper = new MPJLambdaWrapper<>();
        wrapper.selectAll(LeaseAssets.class)
            .selectAs(LeaseAssetType::getName, AssetsDetailsVO::getAssetTypeName)
            .selectAs(BaseBrand::getName, AssetsDetailsVO::getBrandName)
            .selectAs(BaseAddressEntity::getName, AssetsDetailsVO::getAddressName)
            .selectAs(BaseAssetModelnumber::getName, AssetsDetailsVO::getModelnumberName)
            .selectAs(BaseForkliftmodelsSupplier::getName, AssetsDetailsVO::getForkliftmodelsSupplierName)
            .selectAs(BaseOrg::getName, AssetsDetailsVO::getOrgManageName)
            .selectAs(BaseOrg::getName, AssetsDetailsVO::getOrgUseName)
            .selectAs(BaseOrg::getName, AssetsDetailsVO::getOrgServiceName)
            .selectAs(BaseOrg::getName, AssetsDetailsVO::getOrgOutName)
            .leftJoin(LeaseAssetType.class, LeaseAssetType::getIdAssetType, LeaseAssets::getIdAssetType)
            .leftJoin(BaseBrand.class, BaseBrand::getIdBrand, LeaseAssets::getIdBrand)
            .leftJoin(BaseAddressEntity.class, BaseAddressEntity::getIdAddress, LeaseAssets::getIdAddress)
            .leftJoin(BaseAssetModelnumber.class, BaseAssetModelnumber::getIdModelnumber, LeaseAssets::getIdModelnumber)
            .leftJoin(BaseForkliftmodelsSupplier.class, BaseForkliftmodelsSupplier::getIdForkliftmodelsSupplier, LeaseAssets::getIdForkliftmodelsSupplier)
            .leftJoin(BaseOrg.class, BaseOrg::getIdOrg, LeaseAssets::getIdOrgManage)
            .leftJoin(BaseOrg.class, BaseOrg::getIdOrg, LeaseAssets::getIdOrgUse)
            .leftJoin(BaseOrg.class, BaseOrg::getIdOrg, LeaseAssets::getIdOrgService)
            .leftJoin(BaseOrg.class, BaseOrg::getIdOrg, LeaseAssets::getIdOrgOut)
            .eq(LeaseAssets::getDeleteFlag, CommonConstants.DELETE_FLAG_NOT_DELETED)
            .eq(LeaseAssets::getLaCode, laCode)
            .last("limit 1");
        return assets.selectJoinOne(AssetsDetailsVO.class, wrapper);
    }


    @Override
    public Pair<String, F4StatusVO> assetsF4PublicStatus(String laCode) {
        Map<String, Object> map = Maps.newHashMap();
        map.put("userId", FIXED_USER_ID);
        map.put("laCode", laCode);
        String url = externalSystemApiConfig.getLangYunIot() + LanYunUrlConst.F4_STATUS_URL;
        log.info("请求朗云接口地址:{},参数:{}", url, map);
        String info = HttpUtil.post(url, map);
        log.info("请求响应结果：{}", info);
        ResponseData<F4StatusDTO> bean = JSONUtil.toBean(info, new TypeReference<ResponseData<F4StatusDTO>>() {
        }, true);

        F4StatusVO vo = new F4StatusVO();
        Optional.ofNullable(bean.getData())
            .map(F4StatusDTO::getLaCode)
            .ifPresent(vo::setLaCode);

        Optional.ofNullable(bean.getData())
            .map(F4StatusDTO::getProblemState)
            .ifPresent(vo::setState);

        Optional.ofNullable(bean.getData())
            .map(F4StatusDTO::getProblemList)
            .map(CollUtil::getFirst)
            .ifPresent(first -> {
                vo.setMessage(first.getMessage());
                vo.setData(first.getData());
            });
        return Pair.of(bean.getMsg(), vo);
    }


    @Override
    public Pair<String, F4AddressVO> assetsF4PublicAddress(String assetId) {
        Map<String, Object> map = Maps.newHashMap();
        map.put("userId", FIXED_USER_ID);
        map.put("assetId", assetId);
        String url = externalSystemApiConfig.getLangYunIot() + LanYunUrlConst.F4_ADDRESS_URL;
        log.info("请求朗云接口地址:{},参数:{}", url, map);
        String info = HttpUtil.post(url, map);
        log.info("请求响应结果：{}", info);
        ResponseData<List<F4AddressDTO>> bean = JSONUtil.toBean(info, new TypeReference<ResponseData<List<F4AddressDTO>>>() {
        }, true);
        F4AddressVO vo = new F4AddressVO();
        Optional.ofNullable(bean.getData())
            .map(CollUtil::getFirst)
            .ifPresent(i -> BeanUtils.copyProperties(i, vo));
        return Pair.of(bean.getMsg(), vo);
    }
}
