package com.fls.master.pojo.vo;

import com.fls.master.entity.LeaseAssetType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.beans.BeanUtils;

/**
 * AssetsTypeVO
 *
 * <AUTHOR>
 */
@Data
public class AssetsTypeVO {

    @Schema(description = "主键")
    private String idAssetType;

    @Schema(description = "编码")
    private String code;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "产品分类名称")
    private String proename;

    @Schema(description = "资产分类")
    private String idAssetClass;

    @Schema(description = "启用状态：1=未启用，2=已启用，3=已停用")
    private String status;

    public static AssetsTypeVO of(LeaseAssetType source) {
        AssetsTypeVO vo = new AssetsTypeVO();
        BeanUtils.copyProperties(source,vo);
        return vo;
    }
}
