package com.fls.master.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 单据类型表(BaseBilltype)表实体类
 *
 * <AUTHOR>
 * @since 2024-11-05 14:12:25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_base_billtype")
public class BaseBillType implements Serializable {

    private static final long serialVersionUID = 1L;

     //主键
    @TableId(value = "id_billtype", type = IdType.ASSIGN_UUID)
    private String idBilltype;

     //编码
    private String code;

     //名称
    private String name;

     //显示顺序
    private Integer displayOrder;

     //NC单据类型表
    private String pkBilltypeid;

     //系统类型代码
    private String systemCode;

     //是否封存 0=否，1=是 参见yesorno
    private String lockFlag;

     //时间戳
    private LocalDateTime ts;

     //是否删除：0=否，1=是，默认0，参见yesorno
    private String deleteFlag;

}
