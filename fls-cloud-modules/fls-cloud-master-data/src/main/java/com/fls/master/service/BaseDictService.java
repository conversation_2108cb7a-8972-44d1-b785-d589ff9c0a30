package com.fls.master.service;

import com.fls.master.entity.BaseDictEntity;
import com.fls.master.pojo.query.BaseDictQuery;
import com.fls.master.pojo.vo.BaseDictVO;
import com.github.yulichang.base.MPJBaseService;

import java.util.List;

/**
 * 字典表-service
 *
 * <AUTHOR>
 * @since  2024-11-12
 */
public interface BaseDictService extends MPJBaseService<BaseDictEntity>, BaseArchiveQueryService {

    /**
     * 查询字典信息
     * @param query 查询条件
     * @return 字典信息
     */
    List<BaseDictVO> queryDictValue(BaseDictQuery query);
}
