package com.fls.master.service;

import com.fls.master.pojo.query.AssetsQuery;
import com.fls.master.pojo.query.StatusQuery;
import com.fls.master.pojo.vo.AssetsDetailsVO;
import com.fls.master.pojo.vo.AssetsTypeVO;
import com.fls.master.pojo.vo.AssetsVO;
import com.fls.master.pojo.vo.F4AddressVO;
import com.fls.master.pojo.vo.F4StatusVO;
import com.fls.master.pojo.vo.PageResult;
import org.apache.commons.lang3.tuple.Pair;

import java.util.List;

/**
 * AssetsManageService
 *
 * <AUTHOR>
 */
public interface AssetsManageService {


    List<AssetsTypeVO> assetsTypePublicList(StatusQuery query);

    PageResult<AssetsVO> assetsPublicPage(AssetsQuery query);

    PageResult<AssetsVO> assetsPrivatePage(AssetsQuery query);

    AssetsDetailsVO assetsPrivateDetails(String laCode);

    Pair<String,F4StatusVO> assetsF4PublicStatus(String laCode);

    Pair<String, F4AddressVO> assetsF4PublicAddress(String assetId);
}
