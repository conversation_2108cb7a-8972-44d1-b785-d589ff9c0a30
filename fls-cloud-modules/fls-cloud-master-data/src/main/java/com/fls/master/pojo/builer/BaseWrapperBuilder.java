package com.fls.master.pojo.builer;

import com.fls.common.core.constant.CommonConstants;
import com.fls.master.entity.BaseStatusEntity;
import com.fls.master.pojo.query.ArchiveQuery;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import java.util.List;
import lombok.experimental.Accessors;

/**
 * 通用状态wrapper抽取
 *
 * <AUTHOR>
 * @since 2024-11-18
 */
@Accessors(chain = true, fluent = true)
public abstract class BaseWrapperBuilder<T extends BaseStatusEntity> {

    protected final MPJLambdaWrapper<T> wrapper;

    protected BaseWrapperBuilder() {
        this.wrapper = new MPJLambdaWrapper<>();
    }

    public BaseWrapperBuilder<T> withCommonConditions(ArchiveQuery query) {
        wrapper.eq(T::getStatus, query.getStatus())
            .eq(T::getDeleteFlag, CommonConstants.DELETE_FLAG_NOT_DELETED);
        return this;
    }

    public abstract BaseWrapperBuilder<T> withQueryParameters(ArchiveQuery query);

    public abstract BaseWrapperBuilder<T> withAuthorization(List<String> authIds);

    public MPJLambdaWrapper<T> build() {
        return wrapper;
    }
}
