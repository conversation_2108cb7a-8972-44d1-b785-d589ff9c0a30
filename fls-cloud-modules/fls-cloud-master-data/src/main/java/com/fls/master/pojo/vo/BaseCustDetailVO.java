package com.fls.master.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
* 客户基本档案详情信息
*
* <AUTHOR>
* @since  2024-11-06
*/
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "客户基本档案详情信息")
public class BaseCustDetailVO extends ArchiveDetailVO {

	@Schema(description = "主键")
	private String idCustomer;

	@Schema(description = "编码")
	private String code;

	@Schema(description = "名称")
	private String name;

	@Schema(description = "简称")
	private String shortname;

	@Schema(description = "所属组织")
	private String idOrg;

	@Schema(description = "客户分类")
	private String idCustclass;

	@Schema(description = "上级客户")
	private String idParentcust;

	@Schema(description = "纳税人登记号")
	private String taxpayerid;

	@Schema(description = "法人")
	private String legalbody;

	@Schema(description = "公司地址")
	private String address;

	@Schema(description = "电话")
	private String tel;

	@Schema(description = "行业")
	private String idIndustry;

	@Schema(description = "发掘渠道 01=地推式寻访，02=公共媒体信息，03=政府信息，04=经济组织信息，05=会议活动，06=竞争对手信息，07=专业信息组织，08=社会关系信息，09=老客户信息 参见cust_channel")
	private String channel;

	@Schema(description = "是否供应商 0=否，1=是 参见yesorno")
	private String supplierFlag;

	@Schema(description = "对应供应商")
	private String idSupplier;

	@Schema(description = "客户属性 0=外部单位，1=内部单位 参见cust_prop")
	private String custProp;

	@Schema(description = "对应组织")
	private String idFinanceorg;

	@Schema(description = "客户付款方式 01=月结，03=代收货款，04=款到发货，05=预收订金，06=三月结，07=免费，08=双月结，10=四月结，11=半年结，99=综合 参见cust_paytype")
	private String custPaytype;

	@Schema(description = "客户类型 01=终端，02=同行，03=个体户 参见cust_type")
	private String custType;

	@Schema(description = "客户等级 1=A类客户，2=B类客户，3=C类客户 参见cust_level")
	private String custLevel;

	@Schema(description = "管理水平评级 1=很好，2=好，3=一般，4=差，5=很差 参见management_level")
	private String managementLevel;

	@Schema(description = "管理水平描述")
	private String management;

	@Schema(description = "工况评级 1=很好，2=好，3=一般，4=差，5=很差 参见workcond_level")
	private String workcondLevel;

	@Schema(description = "工况描述")
	private String workCondition;

	@Schema(description = "环境评级 1=很好，2=好，3=一般，4=差，5=很差 参见environment_level")
	private String environmentLevel;

	@Schema(description = "环境描述")
	private String environment;

	@Schema(description = "NC客户基本信息主键")
	private String pkCustomer;

	@Schema(description = "启用状态：1=未启用，2=已启用，3=已停用，默认1，参见basestatus")
	private String status;

	@Schema(description = "创建时间")
	private LocalDateTime createTime;

	@Schema(description = "创建人")
	private String creator;

	@Schema(description = "失效时间")
	private LocalDateTime disableTime;

	@Schema(description = "时间戳")
	private LocalDateTime ts;

}
