package com.fls.master.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * AssetsDetailsVO
 *
 * <AUTHOR>
 */
@Data
public class AssetsDetailsVO{

    @Schema(description = "主键")
    private String idAssets;

    @Schema(description = "资产编码")
    private String laCode;

    @Schema(description = "租赁资产类型")
    private String idAssetType;

    @Schema(description = "车型id")
    private String idModelnumber;

    @Schema(description = "供应商车型")
    private String idForkliftmodelsSupplier;

    @Schema(description = "品牌id")
    private String idBrand;

    @Schema(description = "别名（资产标识名）")
    private String proEname;

    @Schema(description = "存放地址类型")
    private String addressType;

    @Schema(description = "地址")
    private String idAddress;

    @Schema(description = "签收地址id")
    private String idSignAddress;

    @Schema(description = "资产进入签收状态 0未签收，1正常签收，2异常签收")
    private Integer inSignStatus;

    @Schema(description = "签收日期")
    private LocalDate inSignDate;

    @Schema(description = "首次进入地址（基础地址）")
    private String idFirstInAddress;

    @Schema(description = "首次签收状态（字典表签收状态）")
    private String firstInStatus;

    @Schema(description = "首次签收日期（采购/租入-签收 其他-创建）")
    private LocalDate firstInDate;

    @Schema(description = "配置表")
    private String idConfig;

    @Schema(description = "配置文本")
    private String configTxtBak;

    @Schema(description = "资产名称 命名规则：品牌+车型+吨位（系统生成）")
    private String laName;

    @Schema(description = "制造编号")
    private String manufacture;

    @Schema(description = "租赁系统资产ID")
    private String assetsid;

    @Schema(description = "车架号")
    private String carNum;

    @Schema(description = "新旧 J=二手车，X=新车")
    private String neworold;

    @Schema(description = "条码可打印数量")
    private Integer barcodeNum;

    @Schema(description = "租赁资产进入方式")
    private String assetIncrease;

    @Schema(description = "资产退出方式")
    private String assetDecrease;

    @Schema(description = "是否出租 0=否，1=是")
    private String leaseStatus;

    @Schema(description = "是否需要融资  0=否，1=是")
    private String financingFlag;

    @Schema(description = "融资状态")
    private String financingStatus;

    @Schema(description = "融资制造编号")
    private String financingManufacture;

    @Schema(description = "现在哪台叉车上")
    private String idAssetsOn;

    @Schema(description = "购买时是在哪辆车上")
    private String idAssetsOnPurchase;

    @Schema(description = "资产状态 1=正常，2=待检，3=待修，4=待售, 5=再制造")
    private String assetStatus;

    @Schema(description = "所有权公司名")
    private String idOrgOwnerName;

    @Schema(description = "所有权公司")
    private String idOrgOwner;

    @Schema(description = "管理主体")
    private String idOrgManage;

    @Schema(description = "使用主体")
    private String idOrgUse;

    @Schema(description = "服务主体")
    private String idOrgService;

    @Schema(description = "外协单位")
    private String idOrgOut;

    @Schema(description = "内锁 0 自由 1锁定")
    private String lockIn;

    @Schema(description = "外锁 0 自由 1锁定")
    private String lockOut;

    @Schema(description = "在役状态：0=在役，1=退出，2=融资租入退出")
    private String assetActive;

    @Schema(description = "属性  0 内部 1 外部")
    private String assetProperty;

    @Schema(description = "整车购入无税金额")
    private BigDecimal moneyAllNotax;

    @Schema(description = "整车购入含税金额")
    private BigDecimal moneyAllTax;

    @Schema(description = "资产无税原值")
    private BigDecimal moneySplitNotax;

    @Schema(description = "资产含税原值")
    private BigDecimal moneySplitTax;

    @Schema(description = "整车含税运费")
    private BigDecimal moneyFreightTax;

    @Schema(description = "整车无税运费")
    private BigDecimal moneyFreightNotax;

    @Schema(description = "内部调拨价")
    private BigDecimal moneyInnerAllot;

    @Schema(description = "其他费用")
    private BigDecimal moneyOther;

    @Schema(description = "固定资产原值, 整车无税金额+整车无税运费")
    private BigDecimal originvalue;

    @Schema(description = "是否可移动，0:否，1:是")
    private String moveFlag;

    @Schema(description = "年检预约状态1未预约2已约检3已检车4资料登记中5资料登记完成")
    private String inspectStatus;

    @Schema(description = "最新年检日期")
    private LocalDate inspectDate;

    @Schema(description = "车牌预约状")
    private String licenseStatus;

    @Schema(description = "是否上牌0否1是")
    private String licenseFlag;

    @Schema(description = "车牌表")
    private String idLicenseplate;

    @Schema(description = "车牌号码")
    private String licenseNum;

    @Schema(description = "固定资产表主键")
    private String idFixedAssets;

    @Schema(description = "内租核算id")
    private String innerRentCost;

    @Schema(description = "内租开始折旧日期")
    private LocalDate innerRentDepDate;

    @Schema(description = "生产日期")
    private LocalDate productDate;

    @Schema(description = "退出日期（租赁转库存（转销售|转再制造）、同行租入退租、采购退货、盘亏、资产变动（类型：减少））")
    private LocalDate exitDate;

    @Schema(description = "购入日期")
    private LocalDate buyDate;

    @Schema(description = "仪表盘读数")
    private BigDecimal dashboardNum;

    @Schema(description = "工作时长")
    private BigDecimal workHour;

    @Schema(description = "保险状态")
    private String insuranceStatus;

    @Schema(description = "保养月份/保养类型 0=不保养 1=单月保养 2=双月保养,3=未定义")
    private String maintenanceType;

    @Schema(description = "时间戳")
    private LocalDateTime ts;

    @Schema(description = "失效时间")
    private LocalDateTime disableTime;

    @Schema(description = "创建人")
    private String creator;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "附件表id")
    private String idLink;

    @Schema(description = "客户id")
    private String idCustomer;

    @Schema(description = "技术码")
    private String technicalCode;

    @Schema(description = "供应商id")
    private String idSupplier;

    @Schema(description = "开票供应商")
    private String idInvoiceSupplier;

    @Schema(description = "融资公司")
    private String idFinancing;

    @Schema(description = "新融资公司,实时")
    private String idFinancingNew;

    @Schema(description = "保险公司")
    private String idInsurance;

    @Schema(description = "发动机号")
    private String engineCode;

    @Schema(description = "外租租赁单号")
    private String idLeaseOut;

    @Schema(description = "内租租赁单")
    private String idLeaseInner;

    @Schema(description = "融资项目id")
    private String idProjectFinancing;

    @Schema(description = "物流公司")
    private String idLogistics;

    @Schema(description = "运输状态")
    private String transportFlag;

    @Schema(description = "物料编码（存货编码）")
    private String materielCode;

    @Schema(description = "形式买断类型，01=整车买断，02=随车买断，03=大件买断")
    private String virbuyType;

    @Schema(description = "采购发票")
    private String idPurchaseInvoiceB;

    @Schema(description = "累计出租月份初使值")
    private Integer rentMonthsBase;

    @Schema(description = "在租累计月份")
    private Integer rentMonthsAccum;

    @Schema(description = "所属仓库id")
    private String idWarehouse;

    @Schema(description = "货区id")
    private String idRack;

    @Schema(description = "货区记录id（最新）")
    private String idRackLog;

    @Schema(description = "是否配置调整中")
    private String adjustFlag;

    //出租次数
    private Integer rentNum;

    //采购税率
    private Integer taxRate;

    //整机内部调拨价
    private BigDecimal moneyAllInnerAllot;

    //保养小组
    private String idMaintenanceTeam;

    //变动时间
    private String lmpChangeTime;

    //是否处于参数调整中
    private Integer paramChangeFlag;

    //是否处于属性调整中
    private Integer propertyChangeFlag;

    //是否处于内部调拨价调整中
    private Integer allotChangeFlag;

    //是否处于上牌年检中
    private Integer licenseInspectFlag;

    //车牌申请单id（最新）
    private String idLicenseApply;

    //年检申请单id（最新）
    private String idInspectApply;

    //处理标识，用于批量执行内租核算分类更新
    private String actFlag;

    //内锁主体
    private String idBizunitInner;

    //内锁类别
    private String lockInnerType;

    //期初累计租赁收入
    private BigDecimal initMnyAccincome;

    //期初累计维修成本
    private BigDecimal initMnyAccmaintcosts;

    //期初累计出租月份
    private Integer initMonAcclease;

    //FIMS备注
    private String fimsMemo;

    //是否需要融资备注
    private String financingFlagMemo;

    //来源哪个表
    private String idSourceType;

    //来源哪个表
    private String idSourceH;

    //是否进行整机内部调拨价调整
    private Integer interPriceFlag;

    //自检标识，参见yesorno
    private String selfInspectionFlag;

    //最新的运输单（11.18增加）资产发出确认之后修改
    private String idTransport;

    //最新保养日期
    private LocalDate maintenanceLastDate;

    //计划保养时间
    private LocalDate maintenancePlanDate;

    //资产图片
    private String idAssetsImg;

    //是否需要保养
    private String maintainFlag;

    //是否资产转移中
    private Integer transferFlag;

    //资料引用标识id
    private String idLinkDoc;

    //外租处罚
    private String idPunishOut;

    //质保开始时间
    private LocalDate startQaDate;

    //是否有质保：0=否，1=是，默认0，参见yesorno
    private String qaFlag;

    //质保时长，单位：月
    private Integer qaMonths;

    //质保小时
    private Integer qaHours;

    //质保结束时间(根据质保时长推算)
    private LocalDate endQaDate;

    //资产状态变更单B表（最新）
    private String idMaintenanceStateB;

    //1：小修 2：中修 3：大修 4：点检 5：全车翻新（最新）
    private String repairType;

    //是否包含充电机（数据来源为导入）0=否，1=包含充电机，默认0
    private String inchargerFlag;

    //重量 单位(吨)
    private BigDecimal weight;

    //是否推荐
    private Integer recommendFlag;

    //资产主图 id_attachment
    private String idImgMain;

    //新车首保标记 0=否，1=是 参见yesorno
    private String firstMaintenanceFlag;

    //资产备注
    private String memo;

    //车架编号状态,字典frame_number_status
    private String frameNumberStatus;

    //资产默认打印材质（字典值）
    private String printTextureCode;

    //资产默认颜色（字典值）
    private String printColorCode;

    //期初累计出租月份（全）
    private Integer allInitAccrentMonths;

    //静态出租月份（全）
    private Integer allRentMonthsBase;

    //动态出租月份（全）
    private Integer allRentMonthsActive;

    //最新在库保养日期
    private LocalDate storeMaintenanceLastDate;

    //二手车锁 0 自由 1锁定
    private String lockOldcar;

    //二手车锁有效日期
    private LocalDate lockOldcarDate;

//    以下是翻译字段
    @Schema(description = "租赁资产类型")
    private String assetTypeName;

    @Schema(description = "供应商车型名称")
    private String forkliftmodelsSupplierName;

    @Schema(description = "品牌名称")
    private String brandName;

    @Schema(description = "地址名称")
    private String addressName;

    @Schema(description = "车型名称")
    private String modelnumberName;

    @Schema(description = "管理主体名称")
    private String orgManageName;

    @Schema(description = "使用主体名称")
    private String orgUseName;

    @Schema(description = "服务主体名称")
    private String orgServiceName;

    @Schema(description = "外协单位名称")
    private String orgOutName;
}
