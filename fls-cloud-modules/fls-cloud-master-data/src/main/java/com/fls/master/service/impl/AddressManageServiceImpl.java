package com.fls.master.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNodeConfig;
import cn.hutool.core.lang.tree.TreeUtil;
import cn.hutool.core.util.CoordinateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fls.common.core.constant.CommonConstants;
import com.fls.common.core.enums.StatusEnum;
import com.fls.common.core.exception.ServiceException;
import com.fls.master.constant.ResourceCodeConst;
import com.fls.master.entity.BaseAddressEntity;
import com.fls.master.entity.BaseDictEntity;
import com.fls.master.entity.BaseRegion;
import com.fls.master.entity.BaseRescode;
import com.fls.master.mapper.BaseAddressMapper;
import com.fls.master.mapper.BaseRescodeMapper;
import com.fls.master.pojo.dto.AddressDTO;
import com.fls.master.pojo.dto.UpdateAddressDTO;
import com.fls.master.pojo.query.AddressQuery;
import com.fls.master.pojo.query.InnerAddressQuery;
import com.fls.master.pojo.query.RegionLevelQuery;
import com.fls.master.pojo.query.StatusQuery;
import com.fls.master.pojo.vo.AddressTypeVO;
import com.fls.master.pojo.vo.AddressVO;
import com.fls.master.pojo.vo.InnerAddressVo;
import com.fls.master.pojo.vo.RegionLevelVO;
import com.fls.master.service.AddressManageService;
import com.fls.master.service.BaseAddressService;
import com.fls.master.service.BaseDictService;
import com.fls.master.service.BaseRegionService;
import com.fls.master.service.PermissionService;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

/**
 * AddressManageServiceImpl
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class AddressManageServiceImpl implements AddressManageService {

    public static final String ADDRESS_TYPE = "address_type";

    private final BaseRegionService region;

    private final BaseAddressService address;

    private final BaseDictService dictService;

    private final PermissionService permission;

    private final BaseRescodeMapper rescodeMapper;

    private final BaseAddressMapper addressMapper;


    @Override
    public List<Tree<String>> regionPublicTree(StatusQuery query) {
        List<BaseRegion> list = region.lambdaQuery()
            .eq(BaseRegion::getDeleteFlag, CommonConstants.DELETE_FLAG_NOT_DELETED)
            .eq(Objects.nonNull(query.getStatus()), BaseRegion::getStatus, query.getStatus())
            .orderByAsc(BaseRegion::getCode)
            .list();

        return buildTree(list);
    }

    private static List<Tree<String>> buildTree(List<BaseRegion> list) {
        TreeNodeConfig config = new TreeNodeConfig();
        config.setDeep(10);
        config.setIdKey("id");
        config.setParentIdKey("pid");
        config.setNameKey("name");
        config.setChildrenKey("children");
        return TreeUtil.build(list, null, config, (node, tree) -> {
            JSONObject item = JSONUtil.parseObj(node);
            tree.setId(item.getStr("code"));
            tree.setParentId(item.getStr("idParentregion"));
            tree.setName(item.getStr("name"));
        });
    }


    @Override
    public List<RegionLevelVO> regionPublicRegionLevel(RegionLevelQuery query) {
        List<BaseRegion> list = region.lambdaQuery()
            .eq(BaseRegion::getDeleteFlag, CommonConstants.DELETE_FLAG_NOT_DELETED)
            .eq(BaseRegion::getIdParentregion, query.getIdParent())
            .eq(StrUtil.isNotBlank(query.getLevel()), BaseRegion::getRegionLevel, query.getLevel())
            .eq(Objects.nonNull(query.getStatus()), BaseRegion::getStatus, query.getStatus())
            .orderByAsc(BaseRegion::getCode)
            .list();

        return list.stream()
            .map(RegionLevelVO::of)
            .collect(Collectors.toList());
    }


    @Override
    public List<AddressTypeVO> addressPublicType(StatusQuery query) {
        List<BaseDictEntity> list = dictService.lambdaQuery()
            .eq(BaseDictEntity::getDeleteFlag, CommonConstants.DELETE_FLAG_NOT_DELETED)
            .eq(Objects.nonNull(query.getStatus()), BaseDictEntity::getStatus, query.getStatus())
            .eq(BaseDictEntity::getDictType, ADDRESS_TYPE)
            .list();
        return list.stream()
            .map(AddressTypeVO::of)
            .collect(Collectors.toList());
    }


    @Override
    public List<AddressVO> addressPublicList(AddressQuery query) {
        LambdaQueryWrapper<BaseAddressEntity> wrapper = commonQueryWrapper(query);
        List<BaseAddressEntity> list = address.list(wrapper);

        Map<String, String> regionMap = getRegionMap();
        return buildResult(list, regionMap);
    }

    private Map<String, String> getRegionMap() {
        return region.lambdaQuery()
            .select(BaseRegion::getCode, BaseRegion::getName)
            .list()
            .stream()
            .collect(Collectors.toMap(BaseRegion::getCode, BaseRegion::getName, (k1, k2) -> k1));
    }

    private static List<AddressVO> buildResult(List<BaseAddressEntity> list, Map<String, String> regionMap) {
        return list.stream()
            .map(item -> {
                AddressVO vo = new AddressVO();
                BeanUtils.copyProperties(item, vo);
                vo.setProvinceName(regionMap.get(item.getIdProvince()));
                vo.setCityName(regionMap.get(item.getIdCity()));
                vo.setVsectionName(regionMap.get(item.getIdVsection()));
                return vo;
            }).collect(Collectors.toList());
    }

    @Override
    public List<AddressVO> addressPrivateList(AddressQuery query) {
        List<String> resourceId = permission.getPrivateResourceId(query.getUserId(), query.getHref(), ResourceCodeConst.ADDRESS_ARCHIVES_CODE);
        LambdaQueryWrapper<BaseAddressEntity> wrapper = commonQueryWrapper(query);
        wrapper.in(ObjectUtil.isNotEmpty(resourceId), BaseAddressEntity::getIdAddress, resourceId);
        List<BaseAddressEntity> list = address.list(wrapper);
        Map<String, String> regionMap = getRegionMap();
        return buildResult(list, regionMap);
    }

    private static LambdaQueryWrapper<BaseAddressEntity> commonQueryWrapper(AddressQuery query) {
        return Wrappers.<BaseAddressEntity>lambdaQuery()
            .eq(BaseAddressEntity::getDeleteFlag, CommonConstants.DELETE_FLAG_NOT_DELETED)
            .eq(StrUtil.isNotBlank(query.getIdAddress()), BaseAddressEntity::getIdAddress, query.getIdAddress())
            .like(StrUtil.isNotBlank(query.getName()), BaseAddressEntity::getName, query.getName())
            .eq(StrUtil.isNotBlank(query.getAddressType()), BaseAddressEntity::getAddressType, query.getAddressType())
            .eq(Objects.nonNull(query.getStatus()), BaseAddressEntity::getStatus, query.getStatus())
            .eq(StrUtil.isNotBlank(query.getIdProvince()), BaseAddressEntity::getIdProvince, query.getIdProvince())
            .eq(StrUtil.isNotBlank(query.getIdCity()), BaseAddressEntity::getIdCity, query.getIdCity())
            .eq(StrUtil.isNotBlank(query.getIdVsection()), BaseAddressEntity::getIdVsection, query.getIdVsection())
            .like(StrUtil.isNotBlank(query.getFullName()), BaseAddressEntity::getFullName, query.getFullName())
            .like(StrUtil.isNotBlank(query.getCode()), BaseAddressEntity::getCode, query.getCode());
    }


    @Override
    public Boolean removeBatchAddress(List<String> ids) {
        return address.removeBatchByIds(ids);
    }

    @Override
    public Boolean saveBatchAddress(List<AddressDTO> dtos) {
        //获取省市区地址名称
        Map<String, String> regionMap = selectRegionMap(dtos);

        List<BaseAddressEntity> saveList = Lists.newArrayList();
        dtos.forEach(item -> saveList.add(process(regionMap, item)));

        return address.saveBatch(saveList);
    }

    private BaseAddressEntity process(Map<String, String> regionMap, AddressDTO dto) {
        BaseAddressEntity entity = new BaseAddressEntity();
        entity.setIdAddress(IdUtil.fastUUID());
        entity.setIdCountry("CN");
        entity.setStatus(StatusEnum.ENABLE.getCode());
        entity.setIdProvince(dto.getIdProvince());
        entity.setIdCity(dto.getIdCity());
        entity.setIdVsection(dto.getIdVsection());
        entity.setDetail(dto.getDetail());
        entity.setAddressType(dto.getAddressType());
        entity.setLatGaode(dto.getLatGaode());
        entity.setLngGaode(dto.getLngGaode());
        entity.setName(dto.getName());
        entity.setDistrictCode(dto.getDistrictCode());
        entity.setCreator(dto.getUserId());
//        生成其他属性内容
        generateContent(entity, regionMap);
        return entity;
    }

    /**
     * 生成其他属性内容
     *
     * @param entity
     * @param regionMap
     */
    private void generateContent(BaseAddressEntity entity, Map<String, String> regionMap) {
//        生成百度经纬度
        generateBd09(entity);
//        生成地址信息
        generateAddress(entity, regionMap.get(entity.getIdVsection()));
//        生成地址编码
        generateCode(entity);
    }


    private void generateCode(BaseAddressEntity entity) {
        String code = getCode();
        entity.setCode(code);
    }


    private void generateAddress(BaseAddressEntity entity, String regionName) {
        entity.setFullName(regionName + entity.getDetail());
        entity.setRegionName(regionName);
        entity.setAddressMd5(SecureUtil.md5(entity.getFullName()));
    }


    /**
     * 根据高德经纬度转换为百度经纬度
     *
     * @param entity
     */
    private void generateBd09(BaseAddressEntity entity) {
        if (StrUtil.isAllNotBlank(entity.getLngGaode(), entity.getLatGaode())) {
            CoordinateUtil.Coordinate bd09 = CoordinateUtil.gcj02ToBd09(Convert.toDouble(entity.getLngGaode()), Convert.toDouble(entity.getLatGaode()));
            entity.setLng(NumberUtil.roundStr(bd09.getLng(), 6));
            entity.setLat(NumberUtil.roundStr(bd09.getLat(), 6));
        }
    }

    private Map<String, String> selectRegionMap(List<AddressDTO> dtos) {
        List<String> regionIds = dtos.stream()
            .flatMap(dto -> Stream.of(dto.getIdProvince(), dto.getIdCity(), dto.getIdVsection()))
            .filter(StrUtil::isNotBlank)
            .distinct()
            .collect(Collectors.toList());
        return region.lambdaQuery()
            .select(BaseRegion::getCode, BaseRegion::getName)
            .in(BaseRegion::getCode, regionIds)
            .list()
            .stream()
            .collect(Collectors.toMap(BaseRegion::getCode, BaseRegion::getName, (k1, k2) -> k1));
    }

    private String getCode() {
        BaseRescode one = rescodeMapper.selectOne(Wrappers.<BaseRescode>lambdaQuery()
            .eq(BaseRescode::getResourceCode, ResourceCodeConst.ADDRESS_ARCHIVES_CODE)
            .eq(BaseRescode::getDeleteFlag, CommonConstants.DELETE_FLAG_NOT_DELETED)
            .last("limit 1"));
        Optional.ofNullable(one)
            .orElseThrow(() -> new ServiceException("资源编码不存在"));
        int length = one.getLenCode() - one.getPrefix().length();
        String code = one.getPrefix() + StrUtil.padPre(Convert.toStr(one.getFlowNum()), length, '0');
        one.setFlowNum(one.getFlowNum() + 1);
        rescodeMapper.updateById(one);
        return code;
    }


    @Override
    public Boolean updateBatchAddress(List<UpdateAddressDTO> dtos) {
        List<BaseAddressEntity> updateList = Lists.newArrayList();
        List<String> ids = dtos.stream().map(UpdateAddressDTO::getIdAddress).collect(Collectors.toList());
        Map<String, BaseAddressEntity> sourceEntityMap = address.listByIds(ids).stream()
            .collect(Collectors.toMap(BaseAddressEntity::getIdAddress, item -> item));

        dtos.forEach(item -> {
            BaseAddressEntity source = Optional.ofNullable(sourceEntityMap.get(item.getIdAddress()))
                .orElse(new BaseAddressEntity());
            BaseAddressEntity entity = new BaseAddressEntity();
            entity.setIdAddress(item.getIdAddress());
//            修改名称
            checkAndSet(item.getName(), entity::setName);
//            修改状态
            checkAndSet(item.getStatus(), entity::setStatus);
//            修改邮政编码
            checkAndSet(item.getZipCode(), entity::setZipCode);
//            修改地址类型
            checkAndSet(item.getAddressType(), entity::setAddressType);
//            修改百度地图县区编码
            checkAndSet(item.getDistrictCode(), entity::setDistrictCode);
//            修改地址信息
            if (item.isUpdateAddress()) {
//                修改省份
                checkAndSet(item.getIdProvince(), entity::setIdProvince, source.getIdProvince());
//                整改城市
                checkAndSet(item.getIdCity(), entity::setIdCity, source.getIdCity());
//                修改县区
                checkAndSet(item.getIdVsection(), entity::setIdVsection, source.getIdVsection());
//                修改地址详细
                checkAndSet(item.getDetail(), entity::setDetail, source.getDetail());
//                修改其他地址信息
                generateAddress(entity, getRegionVsection(entity.getIdVsection()));
            }
            //修改经纬度
            if (item.isUpdateLatOrLng()) {
//                修改高德维度
                checkAndSet(item.getLatGaode(), entity::setLatGaode, source.getLatGaode());
//                修改高德经度
                checkAndSet(item.getLngGaode(), entity::setLngGaode, source.getLngGaode());
//                修改百度经纬度
                generateBd09(entity);
            }
            updateList.add(entity);
        });

        return address.updateBatchById(updateList);
    }

    private void checkAndSet(String item, Consumer<String> set) {
        if (StrUtil.isNotBlank(item)) {
            set.accept(item);
        }
    }

    private void checkAndSet(String item, Consumer<String> set, String defaultValue) {
        if (StrUtil.isNotBlank(item)) {
            set.accept(item);
        } else {
            set.accept(defaultValue);
        }

    }

    private String getRegionVsection(String idVsection) {
        BaseRegion one = region.lambdaQuery()
            .select(BaseRegion::getCode, BaseRegion::getName)
            .eq(BaseRegion::getCode, idVsection)
            .last("limit 1")
            .one();
        return Optional.ofNullable(one)
            .map(BaseRegion::getName)
            .orElse("");
    }

    @Override
    public List<InnerAddressVo> innerAddressList(InnerAddressQuery query) {
        return addressMapper.queryInnerAddress(query);
    }
}
