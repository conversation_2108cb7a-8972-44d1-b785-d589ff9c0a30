package com.fls.master.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
* 客户联系人基础信息
*
* <AUTHOR>
* @since  2024-11-07
*/
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "客户联系人基础信息")
public class CustLinkmanVO extends ArchiveVO {

	@Schema(description = "主键")
	private String idCustlinkman;

	@Schema(description = "所属客户")
	private String idCustomer;

	@Schema(description = "联系人")
	private String idLinkman;

	@Schema(description = "NC客户联系人主键")
	private String pkCustlinkman;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "称呼")
    private String idAppellation;

    @Schema(description = "性别 1=男，2=女，3=不详 参见sex")
    private String sex;

    @Schema(description = "电话")
    private String tel;

    @Schema(description = "手机")
    private String mobile;

    @Schema(description = "电子邮箱")
    private String email;

    @Schema(description = "传真")
    private String fax;

    @Schema(description = "通讯地址")
    private String address;

    @Schema(description = "NC联系人主键")
    private String pkLinkman;
}
