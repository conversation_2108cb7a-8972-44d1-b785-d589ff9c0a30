package com.fls.master.pojo.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * F4StatusVO
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class F4StatusDTO {

    private Integer isStoreCar;

    private String laCode;

    private List<Problem> problemList;

    private String problemState;

    private String terminalId;

    private Integer warrantyState;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime warrantyTime;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Problem {

        private String code;

        @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
        private LocalDateTime data;

        private String message;

    }
}
