package com.fls.master.pojo.vo;

import com.fls.master.entity.BaseTransMode;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.time.LocalDateTime;

/**
 * TransmodeVO
 *
 * <AUTHOR>
 */
@Data
public class TransModeVO {

    @Schema(description = "主键id")
    private String idTransmode;

    @Schema(description = "编码")
    private String code;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "英文名称")
    private String ename;

    @Schema(description = "显示顺序")
    private Integer sort;

    @Schema(description = "是否快递：0=否，1=是，")
    private String expressFlag;

    @Schema(description = "NC自定义档案主键")
    private String pkDefdoc;

    @Schema(description = "启用状态：1=未启用，2=已启用，3=已停用")
    private String status;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    public static TransModeVO of(BaseTransMode source) {
        TransModeVO target = new TransModeVO();
        BeanUtils.copyProperties(source, target);
        target.setSort(source.getDisplayOrder());
        return target;
    }
}
