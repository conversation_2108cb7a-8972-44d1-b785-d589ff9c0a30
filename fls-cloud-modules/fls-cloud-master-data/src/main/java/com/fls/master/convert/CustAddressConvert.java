package com.fls.master.convert;

import com.fls.master.entity.CustAddressEntity;
import com.fls.master.pojo.vo.CustAddressVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
* 客户基本档案-Mapstruct
*
* <AUTHOR>
* @since  2024-11-06
*/
@Mapper
public interface CustAddressConvert {
    CustAddressConvert INSTANCE = Mappers.getMapper(CustAddressConvert.class);

    CustAddressEntity voToEntity(CustAddressVO vo);

    CustAddressVO entityToVo(CustAddressEntity entity);
}
