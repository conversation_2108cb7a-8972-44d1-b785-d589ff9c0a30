package com.fls.master.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 物料表(BaseInventory)表实体类
 *
 * <AUTHOR>
 * @since 2024-11-08 11:36:48
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_base_inventory")
public class BaseInventory implements Serializable {

    private static final long serialVersionUID = 1L;

     //主键
    @TableId(value = "id_inventory", type = IdType.ASSIGN_UUID)
    private String idInventory;

     //编码
    private String code;

     //名称
    private String name;

     //英文名称
    private String ename;

     //主计量单位
    private String idMeasdoc;

     //物料分类
    private String idInvclass;

     //OEM码
    private String spec;

     //机型
    private String model;

     //标签机型
    private String modelLabel;

     //基本参数
    private String baseParam;

     //技术参数
    private String techParam;

     //技术推荐
    private String techMemo;

     //采购推荐
    private String purchaseMemo;

     //供应商编码
    private String supplierCode;

     //供应商存货编码
    private String supplierInvcode;

     //供货单位所在地
    private String placeOfOrgin;

     //存货属性 01=配件，02=叉车，03=二手叉车，04=电动车，05=其他成型机械，06=二手成型设备，07=手拉车，08=属具，09=电池组，10=轮胎，11=货叉，12=仓储设备 参见inv_type
    private String invType;

     //配件属性 01=原厂件，02=拆车件，03=副厂件，04=代用件，05=进口件，06=台湾件，07=开发件，09=OEM配套件 参见parts_type
    private String partsType;

     //铺货属性 01=铺货，02=非铺货 参见distribution_type
    private String distributionType;

     //是否内销 0=否，1=是 参见yesorno
    private String saleFlag;

     //是否外销 0=否，1=是 参见yesorno
    private String exsaleFlag;

     //无税成本（最新成本）
    private BigDecimal cost;

     //含税成本
    private BigDecimal taxcost;

     //其他费用
    private BigDecimal outlay;

     //无税调拨价
    private BigDecimal tranprice;

     //含税调拨价
    private BigDecimal taxtranprice;

     //销售指导价
    private BigDecimal guideprice;

     //外贸系数
    private BigDecimal exrate;

     //外贸网上价
    private BigDecimal exsaleprice;

     //叉车车型
    private String idForkliftmodels;

     //品牌
    private String idBrand;

     //吨位
    private String idTonnage;

     //新旧 J=二手车，X=新车 参见neworold
    private String newDegree;

     //整车车号
    private String carNum;

     //资产编号
    private String assetCode;

     //整车配置说明
    private String carSets;

     //对应U8编码
    private String u8Code;

     //采购员
    private String idPurman;

     //技术员
    private String idTecman;

     //仓管员
    private String idStman;

     //NC物料PK值
    private String pkMaterial;

     //启用状态：1=未启用，2=已启用，3=已停用，默认1，参见basestatus
    private String status;

     //创建时间
    private LocalDateTime createTime;

     //创建人
    private String creator;

     //失效时间
    private LocalDateTime disableTime;

     //时间戳
    private LocalDateTime ts;

     //是否删除：0=否，1=是，默认0，参见yesorno
    private String deleteFlag;

}

