package com.fls.master.pojo.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
* 资源表查询
*
* <AUTHOR>
* @since  2024-11-12
*/
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "资源表查询")
public class BaseResourceQuery extends ArchiveQuery {
    @Schema(description = "编码")
    private String code;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "所属分类")
    private String idResourceclass;

    @Schema(description = "是否启用审批流：0=否，1=是，默认1，参见yesorno")
    private String needprocFlag;

    @Schema(description = "是否进入综合待审列表：0=否，1=是，默认1，参见yesorno")
    private String incalFlag;

    @Schema(description = "是否启用数据权限控制：0=否，1=是，默认1，参见yesorno")
    private String needauthFlag;

    @Schema(description = "类型，1=档案类，2=单据类")
    private String resourceType;

    @Schema(description = "是否启用资料管理，0=否，1=是，参见yesorno")
    private String needdocFlag;

    @Schema(description = "是否启用申请，0=否，1=是，参见yesorno")
    private String needapplyFlag;

    @Schema(description = "是否启用档案数据管理，0=否，1=是，参见yesorno")
    private String archmanaFlag;

    @Schema(description = "是否启用历史记录，0=否，1=是，参见yesorno")
    private String historyFlag;

    @Schema(description = "是否进入移动APP：0=否，1=是，默认0，参见yesorno")
    private String appFlag;

}
