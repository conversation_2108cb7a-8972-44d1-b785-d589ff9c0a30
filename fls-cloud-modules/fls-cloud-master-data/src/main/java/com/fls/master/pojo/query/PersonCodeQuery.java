package com.fls.master.pojo.query;

import cn.hutool.core.util.StrUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 员工编码查询
 *
 * <AUTHOR>
 * @since 2025-06-09
 */
@Data
public class PersonCodeQuery {

    @Schema(description = "所属组织")
    private String idOrg;

    @Schema(description = "所属经营主体")
    private String idBizunit;

    @Schema(description = "岗位编码")
    private String pCode;

    @Schema(description = "职位编码")
    private String jCode;

    /**
     * 校验参数 idOrg 和 idBizunit 至少有一个不为空
     *
     * @return 校验结果
     */
    public boolean validateOrgOrBizunit() {
        return StrUtil.isBlank(idOrg) && StrUtil.isBlank(idBizunit);
    }

    public void setpCode(String pCode) {
        this.pCode = pCode;
    }

    public void setjCode(String jCode) {
        this.jCode = jCode;
    }
}
