package com.fls.master.convert;

import com.fls.master.api.model.WorkTeamBaseInfo;
import com.fls.master.entity.BaseWorkTeamEntity;
import com.fls.master.pojo.vo.BaseWorkTeamDetailVO;
import com.fls.master.pojo.vo.BaseWorkTeamVO;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
* 工作班组表-Mapstruct
*
* <AUTHOR>
* @since  2024-11-12
*/
@Mapper
public interface BaseWorkTeamConvert {
    BaseWorkTeamConvert INSTANCE = Mappers.getMapper(BaseWorkTeamConvert.class);

    BaseWorkTeamEntity voToEntity(BaseWorkTeamVO vo);

    BaseWorkTeamVO entityToVo(BaseWorkTeamEntity entity);

    BaseWorkTeamDetailVO entityToDetailVo(BaseWorkTeamEntity entity);

    List<BaseWorkTeamVO> toVoList(List<BaseWorkTeamEntity> list);

    List<WorkTeamBaseInfo> toBaseInfoList(List<BaseWorkTeamEntity> list);

}
