package com.fls.master.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 供应商车型表(BaseForkliftmodelsSupplier)表实体类
 *
 * <AUTHOR>
 * @since 2024-11-12 16:02:53
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_base_forkliftmodels_supplier")
public class BaseForkliftmodelsSupplier implements Serializable {

    private static final long serialVersionUID = 1L;


    @TableId(value = "id_forkliftmodels_supplier", type = IdType.ASSIGN_UUID)
    private String idForkliftmodelsSupplier;

     //供应商车型名称
    private String name;

     //供应商id
    private String idSupplier;

     //启用状态：1=未启用，2=已启用，3=已停用，默认1，参见basestatus
    private String status;

     //品牌id
    private String idBrand;

     //车型id
    private String idModelnumber;

     //供应商车型说明
    private String memo;

     //id_asset_type资产类型
    private String idAssetType;

     //创建用户
    private String creator;

     //创建时间
    private LocalDateTime createTime;

     //修改时间
    private LocalDateTime ts;

     //是否删除：0=否，1=是，默认0，参见yesorno
    private String deleteFlag;

     //删除时间
    private LocalDateTime deleteTime;

}

