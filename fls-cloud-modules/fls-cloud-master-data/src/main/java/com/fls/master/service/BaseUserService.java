package com.fls.master.service;

import com.fls.master.entity.BaseUser;
import com.fls.master.pojo.query.UserRoleQuery;
import com.fls.master.pojo.vo.UserVO;
import com.github.yulichang.base.MPJBaseService;
import java.util.List;

/**
 * 用户表(BasePerson)表服务接口
 *
 * <AUTHOR>
 * @since 2024-10-31 15:29:49
 */
public interface BaseUserService extends MPJBaseService<BaseUser> {
    /**
     * 角色用户查询
     * @param roleQuery 查询参数
     * @return 用户列表
     */
    List<UserVO> getUsersByRole(UserRoleQuery roleQuery);
}
