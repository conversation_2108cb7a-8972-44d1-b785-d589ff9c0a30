package com.fls.master.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * LA资料验证器(LeaseDocumentValidator)表实体类
 *
 * <AUTHOR>
 * @since 2024-11-12 09:41:40
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_lease_document_validator")
public class LeaseDocumentValidator implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id_validator", type = IdType.ASSIGN_UUID)
    private String idValidator;

    private String idDocCat;

     //所属组织是否必填
    private Integer idOrgRequireFlag;

     //所属主体是否必填
    private Integer idBizunitRequireFlag;

     //保管组织是否必填
    private Integer idOrgKeepRequireFlag;

     //保管主体是否必填
    private Integer idBizunitKeepRequireFlag;

     //存放地址是否必填
    private Integer placeRequireFlag;

     //责任人是否必填
    private Integer idPersonRequireFlag;

     //创建人
    private String creator;

     //创建时间
    private LocalDateTime createTime;

     //失效时间
    private LocalDateTime disableTime;

     //时间戳
    private LocalDateTime ts;

     //1:删除
    private String deleteFlag;

}
