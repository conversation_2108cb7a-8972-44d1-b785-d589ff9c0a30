package com.fls.master.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 主计量单位表(BaseMeasdoc)表实体类
 *
 * <AUTHOR>
 * @since 2024-11-08 14:07:03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_base_measdoc")
public class BaseMeasdoc implements Serializable {

    private static final long serialVersionUID = 1L;

     //主键
    @TableId(value = "id_measdoc", type = IdType.ASSIGN_UUID)
    private String idMeasdoc;

     //编码
    private String code;

     //名称
    private String name;

     //英文名称
    private String ename;

     //NC计量单位主键
    private String pkMeasdoc;

     //启用状态：1=未启用，2=已启用，3=已停用，默认1，参见basestatus
    private String status;

     //创建时间
    private LocalDateTime createTime;

     //创建人
    private String creator;

     //失效时间
    private LocalDateTime disableTime;

     //时间戳
    private LocalDateTime ts;

     //是否删除：0=否，1=是，默认0，参见yesorno
    private String deleteFlag;

}

