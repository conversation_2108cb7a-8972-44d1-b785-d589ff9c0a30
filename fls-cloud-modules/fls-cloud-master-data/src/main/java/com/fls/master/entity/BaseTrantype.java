package com.fls.master.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 交易类型表(BaseTrantype)表实体类
 *
 * <AUTHOR>
 * @since 2024-11-06 15:08:25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_base_trantype")
public class BaseTrantype implements Serializable {

    private static final long serialVersionUID = 1L;

     //主键
    @TableId(value = "id_trantype", type = IdType.ASSIGN_UUID)
    private String idTrantype;

     //编码
    private String code;

     //名称
    private String name;

     //英文名称
    private String ename;

     //显示顺序
    private Integer displayOrder;

     //NC交易类型表
    private String pkBilltypeid;

     //所属单据
    private String idBilltype;

     //制单节点号
    private String nodeCode;

     //系统类型代码
    private String systemCode;

     //是否封存 0=否，1=是 参见yesorno
    private String lockFlag;

     //时间戳
    private LocalDateTime ts;

     //是否删除：0=否，1=是，默认0，参见yesorno
    private String deleteFlag;

}
