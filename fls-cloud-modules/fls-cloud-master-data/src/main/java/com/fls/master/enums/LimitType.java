package com.fls.master.enums;

import lombok.Getter;

/**
 * 限定类型枚举类
 *
 * <AUTHOR>
 * @since 2025-01-04
 */
@Getter
public enum LimitType {
	// 同组织
	ORG("1"),
	
	// 同部门
    DEP("2"),
    
    // 同经营主体
    BIZ("3");

    private final String type;

    LimitType(String type) {
        this.type = type;
    }

    public static LimitType parse(String type) {
        for (LimitType limitType : LimitType.values()) {
            if (limitType.type.equals(type)) {
                return limitType;
            }
        }
        throw new IllegalArgumentException("Invalid limit type: " + type);
    }
}