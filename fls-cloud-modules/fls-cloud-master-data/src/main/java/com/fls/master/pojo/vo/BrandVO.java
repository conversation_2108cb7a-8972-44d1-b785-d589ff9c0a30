package com.fls.master.pojo.vo;

import com.fls.master.entity.BaseBrand;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.time.LocalDateTime;

/**
 * BrandVO
 *
 * <AUTHOR>
 */
@Data
public class BrandVO {

    @Schema(description = "主键")
    private String id;

    @Schema(description = "编码")
    private String code;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "英文名称")
    private String ename;

    @Schema(description = "显示顺序")
    private Integer sort;

    @Schema(description = "描述")
    private String memo;

    @Schema(description = "NC品牌主键")
    private String pkBrand;

    @Schema(description = "启用状态：1=未启用，2=已启用，3=已停用")
    private String status;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "失效时间")
    private LocalDateTime disableTime;

    @Schema(description = "进口品牌标识：0=否，1=是")
    private String abroadFlag;

    @Schema(description = "原租赁系统品牌ID")
    private String oldSysid;

    @Schema(description = "品牌Logo")
    private String logo;

    @Schema(description = "高端品牌标识 0否 1是")
    private String highEndFlag;

    public static BrandVO of(BaseBrand source) {
        BrandVO target = new BrandVO();
        BeanUtils.copyProperties(source, target);
        target.setId(source.getIdBrand());
        target.setSort(source.getDisplayOrder());
        return target;
    }
}
