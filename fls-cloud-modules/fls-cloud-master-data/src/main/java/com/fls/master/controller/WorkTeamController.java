package com.fls.master.controller;

import com.fls.common.core.domain.ResponseData;
import com.fls.common.core.validate.QueryGroup;
import com.fls.master.pojo.query.ArchiveDetailQuery;
import com.fls.master.pojo.query.BaseWorkTeamMemberQuery;
import com.fls.master.pojo.query.BaseWorkTeamQuery;
import com.fls.master.pojo.query.OwnerWorkTeamQuery;
import com.fls.master.pojo.vo.BaseWorkTeamDetailVO;
import com.fls.master.pojo.vo.BaseWorkTeamMemberVO;
import com.fls.master.pojo.vo.BaseWorkTeamVO;
import com.fls.master.service.BaseWorkTeamService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 工作班组表-web层服务
 *
 * <AUTHOR>
 * @since 2024-11-12
 */
@RestController
@Slf4j
@RequestMapping("/workTeam")
@Tag(name = "工作班组表-web层服务")
@RequiredArgsConstructor
public class WorkTeamController {
    private final BaseWorkTeamService baseWorkTeamService;

    @Operation(summary = "全量工作班组表查询")
    @PostMapping("/list")
    public ResponseData<List<BaseWorkTeamVO>> list(@RequestBody BaseWorkTeamQuery query) {
        return ResponseData.ok(baseWorkTeamService.queryAll(query));
    }

    @Operation(summary = "权限工作班组表查询")
    @PostMapping("/auth/list")
    public ResponseData<List<BaseWorkTeamVO>> authList(@Validated(QueryGroup.class) @RequestBody BaseWorkTeamQuery query) {
        return ResponseData.ok(baseWorkTeamService.queryAuthArchives(query));
    }

    @Operation(summary = "班组详情查询")
    @PostMapping("/detail")
    public ResponseData<BaseWorkTeamDetailVO> detail(@Validated @RequestBody ArchiveDetailQuery query) {
        return ResponseData.ok(baseWorkTeamService.queryArchiveDetail(query.getId()));
    }

    @Operation(summary = "查询工作班组成员列表")
    @PostMapping("/member")
    public ResponseData<List<BaseWorkTeamMemberVO>> member(@RequestBody BaseWorkTeamMemberQuery query) {
        return ResponseData.ok(baseWorkTeamService.getWorkTeamMembers(query));
    }

    @Operation(summary = "查询用户所在班组")
    @PostMapping("/owner/list")
    public ResponseData<List<BaseWorkTeamVO>> ownerList(@Validated @RequestBody OwnerWorkTeamQuery query) {
        return ResponseData.ok(baseWorkTeamService.getOwnerTeams(query));
    }
}
