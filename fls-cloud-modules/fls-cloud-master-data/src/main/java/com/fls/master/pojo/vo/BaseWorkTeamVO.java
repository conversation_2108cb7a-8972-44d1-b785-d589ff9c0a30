package com.fls.master.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 工作班组表基础信息
 *
 * <AUTHOR>
 * @since 2024-11-12
 */
@Data
@Schema(description = "工作班组表基础信息")
public class BaseWorkTeamVO extends ArchiveVO {

    @Schema(description = "主键")
    private String idWorkteam;

    @Schema(description = "班组编码")
    private String code;

    @Schema(description = "班组名称")
    private String name;

    @Schema(description = "所属组织")
    private String idOrg;

    @Schema(description = "组织名称")
    private String orgName;

    @Schema(description = "所属主体")
    private String idBizunit;

    @Schema(description = "经营主体名称")
    private String bizName;

    @Schema(description = "所属部门")
    private String idDepartment;

    @Schema(description = "部门名称")
    private String depName;

    @Schema(description = "班组对应虚拟地点")
    private String idAddress;

    @Schema(description = "班组对应仓库")
    private String idWarehouse;

    @Schema(description = "班组对应仓库名称")
    private String warehouseName;

    @Schema(description = "班组对应货位")
    private String idWhpos;

    @Schema(description = "班组对应货位名称")
    private String whposName;

    @Schema(description = "班组默认归还仓库")
    private String idWhback;

    @Schema(description = "班组默认归还仓库名称")
    private String whbackName;

    @Schema(description = "组长")
    private String idHeadman;

    @Schema(description = "组长名称")
    private String headmanName;

    @Schema(description = "班组成员")
    private String members;

    @Schema(description = "租赁服务标识：0=否，1=是，默认0，参见yesorno")
    private String rentalServiceFlag;

    @Schema(description = "商业维修标识：0=否，1=是，默认0，参见yesorno")
    private String commercialMaintenanceFlag;

    @Schema(description = "默认维修仓库id")
    private String idMainWh;

    @Schema(description = "默认维修仓库名称")
    private String mainWhName;

    @Schema(description = "是否外协：0=否，1=是，默认0，参见yesorno")
    private String ocunitFlag;

    @Schema(description = "外协单位")
    private String idOcunit;

    @Schema(description = "NC主键")
    private String ncPk;

    @Schema(description = "备注")
    private String memo;

    @Schema(description = "启用状态：1=未启用，2=已启用，3=已停用，默认1，参见basestatus")
    private String status;

}
