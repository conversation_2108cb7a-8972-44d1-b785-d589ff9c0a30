package com.fls.master.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.*;

import java.time.LocalDateTime;

/**
 * 字典表表
 *
 * <AUTHOR>
 * @since  2024-11-11
 */
@Data
@TableName("t_base_dict")
public class BaseDictEntity {
	/**
	* 主键
	*/
	@TableId(value = "id_dict", type = IdType.ASSIGN_UUID)
	private String idDict;

	/**
	* 枚举类型
	*/
	private String dictType;

	/**
	* 编码
	*/
	private String dictCode;

	/**
	* 枚举名
	*/
	private String dictName;

	/**
	* 索引号
	*/
	private Integer dictIndex;

	/**
	* 启用状态：1=未启用，2=已启用，3=已停用，默认1，参见basestatus
	*/
	private String status;

	/**
	* 说明
	*/
	private String description;

	/**
	* 父级id
	*/
	private String idParentdict;

	/**
	* 创建时间
	*/
	private LocalDateTime createTime;

	/**
	* 创建人
	*/
	private String creator;

	/**
	* 失效时间
	*/
	private LocalDateTime disableTime;

	/**
	* 时间戳
	*/
	private LocalDateTime ts;

	/**
	* 是否删除：0=否，1=是，默认0，参见yesorno
	*/
	private String deleteFlag;

	/**
	* NC档案主键
	*/
	private String ncpk;

}
