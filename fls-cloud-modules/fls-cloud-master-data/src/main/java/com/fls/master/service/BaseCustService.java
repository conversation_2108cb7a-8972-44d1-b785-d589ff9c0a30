package com.fls.master.service;

import com.fls.master.entity.BaseCustEntity;
import com.fls.master.pojo.query.CustAllocationQuery;
import com.fls.master.pojo.vo.CustAddressVO;
import com.fls.master.pojo.vo.CustAllocationVO;
import com.fls.master.pojo.vo.CustLinkmanVO;
import com.fls.master.pojo.vo.PageResult;
import com.github.yulichang.base.MPJBaseService;
import java.util.List;

/**
 * 客户基本档案-service
 *
 * <AUTHOR>
 * @since 2024-11-06
 */
public interface BaseCustService extends MPJBaseService<BaseCustEntity>, BaseArchiveQueryService {
    /**
     * 查询客户地址信息列表
     * @param custId 客户id
     * @return 对应客户的地址信息列表
     */
    List<CustAddressVO> queryCustAddress(String custId);

    /**
     * 查询客户联系人信息列表
     * @param custId 客户id
     * @return 对应客户的地址信息列表
     */
    List<CustLinkmanVO> queryCustLinkman(String custId);

    /**
     * 查询客户分拨分页信息
     *
     * @param query 客户分拨查询参数
     * @return 客户分拨分页结果
     */
    PageResult<CustAllocationVO> queryCustAllocation(CustAllocationQuery query);
}
