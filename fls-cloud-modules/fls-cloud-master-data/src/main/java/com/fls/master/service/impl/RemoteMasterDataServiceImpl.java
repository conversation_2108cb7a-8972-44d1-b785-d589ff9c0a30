package com.fls.master.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.fls.common.core.constant.CommonConstants;
import com.fls.master.api.RemoteMasterDataService;
import com.fls.master.api.model.BizunitNameInfo;
import com.fls.master.constant.ResourceCodeConst;
import com.fls.master.entity.BaseBizunitEntity;
import com.fls.master.service.BaseBizunitService;
import com.fls.master.service.PermissionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 主数据服务实现
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Slf4j
@Service
@DubboService
@RequiredArgsConstructor
public class RemoteMasterDataServiceImpl implements RemoteMasterDataService {

    private final BaseBizunitService bizunitService;

    private final PermissionService permissionService;

    @Override
    public List<BizunitNameInfo> getBizunitName(Set<String> idsBizunit) {
        List<BaseBizunitEntity> bizunitEntities = bizunitService.lambdaQuery().eq(BaseBizunitEntity::getStatus, CommonConstants.COMMON_STATUS_NORMAL)
            .eq(BaseBizunitEntity::getDeleteFlag, CommonConstants.DELETE_FLAG_NOT_DELETED).in(CollectionUtil.isNotEmpty(idsBizunit), BaseBizunitEntity::getIdBizunit, idsBizunit).list();
        List<BizunitNameInfo> bizunitNameInfos = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(bizunitEntities)) {
            bizunitNameInfos = bizunitEntities.stream().map(entity -> {
                BizunitNameInfo bizunitNameInfo = new BizunitNameInfo();
                bizunitNameInfo.setIdBizunit(entity.getIdBizunit());
                bizunitNameInfo.setName(entity.getName());
                bizunitNameInfo.setCode(entity.getInnercode());
                return bizunitNameInfo;
            }).collect(Collectors.toList());
        }
        return bizunitNameInfos;
    }

    @Override
    public List<String> getAuthBizunitIds(String idUser, String href) {
        List<String> privateBizunitId = permissionService.getPrivateResourceId(idUser, href, ResourceCodeConst.BIZUNIT_ARCHIVES_CODE);
        if (CollUtil.isNotEmpty(privateBizunitId)) {
            return privateBizunitId;
        }
        List<String> privateOrgId = permissionService.getPrivateOrgId(idUser, href);
        return CollUtil.isEmpty(privateOrgId) ? Collections.emptyList() : privateOrgId;
    }
}
