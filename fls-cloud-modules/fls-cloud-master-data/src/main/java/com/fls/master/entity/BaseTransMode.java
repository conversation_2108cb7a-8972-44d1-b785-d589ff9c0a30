package com.fls.master.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 发运方式表(BaseTransmode)表实体类
 *
 * <AUTHOR>
 * @since 2024-11-05 10:37:41
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_base_transmode")
public class BaseTransMode implements Serializable {

    private static final long serialVersionUID = 1L;

     //主键
    @TableId(value = "id_transmode", type = IdType.ASSIGN_UUID)
    private String idTransmode;

     //编码
    private String code;

     //名称
    private String name;

     //英文名称
    private String ename;

     //显示顺序
    private Integer displayOrder;

     //是否快递：0=否，1=是，默认0，参见yesorno
    private String expressFlag;

     //NC自定义档案主键
    private String pkDefdoc;

     //启用状态：1=未启用，2=已启用，3=已停用，默认1，参见basestatus
    private String status;

     //创建时间
    private LocalDateTime createTime;

     //创建人
    private String creator;

     //失效时间
    private LocalDateTime disableTime;

     //时间戳
    private LocalDateTime ts;

     //是否删除：0=否，1=是，默认0，参见yesorno
    private String deleteFlag;

}
