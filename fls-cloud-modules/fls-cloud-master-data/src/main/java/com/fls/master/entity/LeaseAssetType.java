package com.fls.master.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * LA租赁资产类型表(LeaseAssetType)表实体类
 *
 * <AUTHOR>
 * @since 2024-11-06 14:17:07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_lease_asset_type")
public class LeaseAssetType implements Serializable {

    private static final long serialVersionUID = 1L;

     //主键
    @TableId(value = "id_asset_type", type = IdType.ASSIGN_UUID)
    private String idAssetType;

     //编码
    private String code;

     //名称
    private String name;

     //产品分类名称
    private String proename;

     //资产分类
    private String idAssetClass;

     //启用状态：1=未启用，2=已启用，3=已停用，默认1，参见basestatus
    private String status;

     //创建时间
    private LocalDateTime createTime;

     //创建人
    private String creator;

     //失效时间
    private LocalDateTime disableTime;

     //时间戳
    private LocalDateTime ts;

     //是否删除：0=否，1=是，默认0，参见yesorno
    private String deleteFlag;

     //可捆绑大件
    private String parts;

     //可装载大件
    private String partsOn;

     //包含参数
    private String params;

     //标签数量 0为不需要标签
    private Integer barcodeNum;

     //扫码控制，条码是否必须扫, 参照yesorno
    private String scanBarcodeFlag;

     //是否需要上牌
    private String licenseFlag;

     //是否需要融资
    private String financingFlag;

     //是否启用制造编号
    private String manufactureFlag;

     //是否需要保养
    private String maintainFlag;

     //燃油类型 id_dict
    private String fuelType;

     //动力类型 id_dict
    private String powerType;

     //采购员
    private String idPurchasePerson;

     //资产财务报表统计分类参见basestatus assets_financeclass
    private String assetsFinanceclass;

     //维修积分
    private BigDecimal maintenancePoints;

     //关注等级：0最低，1初级，默认1，暂分0和1，用于物管中心统计分类用
    private String attentionLevel;

     //维保基金分类
    private String maintenanceFundType;

     //维保基金系数
    private BigDecimal maintenanceFundMoney;

     //资产使用分类
    private String assetsUseClass;

     //在库资产保养周期，单位：天
    private Integer storeMaintenanceInterval;

}
