package com.fls.master.service.impl;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fls.common.core.exception.ServiceException;
import com.fls.master.entity.BaseResTranType;
import com.fls.master.entity.BaseRescode;
import com.fls.master.entity.BaseResourceClassEntity;
import com.fls.master.entity.BaseResourceEntity;
import com.fls.master.entity.BaseRestableEntity;
import com.fls.master.mapper.BaseRescodeMapper;
import com.fls.master.mapper.BaseResourceMapper;
import com.fls.master.pojo.builer.ResourceWrapperBuilder;
import com.fls.master.pojo.query.ArchiveDetailQuery;
import com.fls.master.pojo.query.ArchiveQuery;
import com.fls.master.pojo.query.BaseResourceQuery;
import com.fls.master.pojo.vo.BaseResTransTypeVO;
import com.fls.master.pojo.vo.BaseResourceDetailVO;
import com.fls.master.pojo.vo.BaseResourceVO;
import com.fls.master.pojo.vo.ResourceCodeVo;
import com.fls.master.service.BaseResTranTypeService;
import com.fls.master.service.BaseResourceService;
import com.fls.master.service.PermissionService;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 资源表-service
 *
 * <AUTHOR>
 * @since 2024-11-12
 */
@Service
@Slf4j
public class BaseResourceServiceImpl extends MPJBaseServiceImpl<BaseResourceMapper, BaseResourceEntity> implements BaseResourceService {

    @Resource
    private PermissionService permissionService;

    @Resource
    private BaseRescodeMapper resCodeMapper;

    @Resource
    private BaseResTranTypeService resTranTypeService;

    @Override
    public List<BaseResourceVO> queryAll(ArchiveQuery query) {
        BaseResourceQuery tansQuery = (BaseResourceQuery) query;
        MPJLambdaWrapper<BaseResourceEntity> wrapper = ResourceWrapperBuilder.builder().withCommonConditions(tansQuery).withQueryParameters(tansQuery).build();
        return baseMapper.selectJoinList(BaseResourceVO.class, wrapper);
    }

    @Override
    public List<BaseResourceVO> queryAuthArchives(ArchiveQuery query) {
        BaseResourceQuery tansQuery = (BaseResourceQuery) query;
        List<String> privateArhIds = permissionService.getPrivateResourceId(query.getUserId(), query.getHref(), CharSequenceUtil.EMPTY);
        MPJLambdaWrapper<BaseResourceEntity> wrapper = ResourceWrapperBuilder.builder().withCommonConditions(tansQuery).withQueryParameters(tansQuery).withAuthorization(privateArhIds).build();
        return baseMapper.selectJoinList(BaseResourceVO.class, wrapper);
    }

    @Override
    public BaseResourceDetailVO queryArchiveDetail(String archiveId) {
        MPJLambdaWrapper<BaseResourceEntity> wrapper = new MPJLambdaWrapper<>();
        wrapper.selectAll(BaseResourceEntity.class)
            .selectAs(BaseResourceClassEntity::getCode, BaseResourceDetailVO::getClassCode)
            .selectAs(BaseResourceClassEntity::getName, BaseResourceDetailVO::getClassName)
            .selectAs(BaseRestableEntity::getTblName, BaseResourceDetailVO::getTblName)
            .selectAs(BaseRestableEntity::getTblIdName, BaseResourceDetailVO::getTblIdName)
            .leftJoin(BaseResourceClassEntity.class, BaseResourceClassEntity::getIdResourceclass, BaseResourceEntity::getIdResourceclass)
            .leftJoin(BaseRestableEntity.class, BaseRestableEntity::getIdResource, BaseResourceEntity::getIdResourceclass)
            .eq(ObjectUtil.isNotEmpty(archiveId), BaseResourceEntity::getIdResource, archiveId);
        return baseMapper.selectJoinOne(BaseResourceDetailVO.class, wrapper);
    }

    @Override
    public BaseResourceDetailVO queryArchiveDetail(ArchiveDetailQuery query) {
        String id = query.getId();
        String code = query.getCode();
        MPJLambdaWrapper<BaseResourceEntity> wrapper = new MPJLambdaWrapper<>();
        wrapper.selectAll(BaseResourceEntity.class)
                .selectAs(BaseResourceClassEntity::getName, BaseResourceDetailVO::getClassName)
                .selectAs(BaseResourceClassEntity::getCode, BaseResourceDetailVO::getClassCode)
                .selectAs(BaseRestableEntity::getTblIdName, BaseResourceDetailVO::getTblIdName)
                .selectAs(BaseRestableEntity::getTblName, BaseResourceDetailVO::getTblName)
                .leftJoin(BaseResourceClassEntity.class, BaseResourceClassEntity::getIdResourceclass, BaseResourceEntity::getIdResourceclass)
                .leftJoin(BaseRestableEntity.class, BaseRestableEntity::getIdResource, BaseResourceEntity::getIdResource)
                .eq(ObjectUtil.isNotEmpty(code), BaseResourceEntity::getCode, code)
                .eq(ObjectUtil.isNotEmpty(id), BaseResourceEntity::getIdResource, id);
        BaseResourceDetailVO resourceDetailVO = baseMapper.selectJoinOne(BaseResourceDetailVO.class, wrapper);
        if (ObjectUtil.isNull(resourceDetailVO)) {
            throw new ServiceException("单据资源信息不存在");
        }
        //查询资源交易类型信息列表
        List<BaseResTranType> resTranTypes = resTranTypeService.lambdaQuery().eq(BaseResTranType::getIdResource, resourceDetailVO.getIdResource()).list();
        if (ObjectUtil.isNotEmpty(resTranTypes)) {
            List<BaseResTransTypeVO> transVOList =
                    resTranTypes.stream().map(item -> new BaseResTransTypeVO(item.getIdResTrantype(), item.getTranType(), item.getTranCode())).collect(Collectors.toList());
            resourceDetailVO.setTransList(transVOList);
        }
        return resourceDetailVO;
    }

    @Override
    public ResourceCodeVo queryResourceCode(String code) {
        ResourceCodeVo resourceCodeVo = baseMapper.queryResCode(code);
        if (ObjectUtil.isNull(resourceCodeVo)) {
            throw new ServiceException("资源编码信息不存在");
        }
        return resourceCodeVo;
    }

    @Override
    public void updateResourceFlowNum(String code,Integer flowNum) {
        // 查询资源编码对应的 BaseResCode 实体
        LambdaQueryWrapper<BaseRescode> query = new LambdaQueryWrapper<>();
        query.eq(BaseRescode::getResourceCode, code);
        BaseRescode baseRescode = resCodeMapper.selectOne(query);
        if (ObjectUtil.isNull(baseRescode)) {
            throw new ServiceException("资源编码信息不存在");
        }
        // 设置新的 flowNum 并根据主键更新
        baseRescode.setFlowNum(flowNum);
        resCodeMapper.updateById(baseRescode);
    }
}
