package com.fls.master.service.impl;

import com.fls.master.mapper.BaseResFiletypeMapper;
import com.fls.master.entity.BaseResFiletype;
import com.fls.master.service.BaseResFiletypeService;
import com.github.yulichang.base.MPJBaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 资源文件类型表(BaseResFiletype)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-11-05 14:53:55
 */
@Service
@Slf4j
public class BaseResFiletypeServiceImpl extends MPJBaseServiceImpl<BaseResFiletypeMapper, BaseResFiletype> implements BaseResFiletypeService {
}
