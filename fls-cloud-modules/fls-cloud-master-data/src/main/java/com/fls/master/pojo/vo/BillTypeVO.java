package com.fls.master.pojo.vo;

import com.fls.master.entity.BaseBillType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.beans.BeanUtils;

/**
 * BillTypeVO
 *
 * <AUTHOR>
 */
@Data
public class BillTypeVO {

    @Schema(description = "主键")
    private String idBilltype;

    @Schema(description = "编码")
    private String code;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "显示顺序")
    private Integer displayOrder;

    @Schema(description = "NC单据类型表")
    private String pkBilltypeid;

    @Schema(description = "系统类型代码")
    private String systemCode;

    @Schema(description = "是否封存 0=否，1=是")
    private String lockFlag;

    public static BillTypeVO of(BaseBillType source) {
        BillTypeVO target = new BillTypeVO();
        BeanUtils.copyProperties(source, target);
        return target;
    }
}
