package com.fls.master.pojo.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * AddressQuery
 *
 * <AUTHOR>
 */
@Data
public class AddressQuery extends ArchiveQuery{

    @Schema(description = "名称")
    private String name;

    @Schema(description = "主键")
    private String idAddress;

    @Schema(description = "地址类型")
    private String addressType;

    @Schema(description = "地址全称")
    private String fullName;

    @Schema(description = "地址编码")
    private String code;

    @Schema(description = "省份id")
    private String idProvince;

    @Schema(description = "城市id")
    private String idCity;

    @Schema(description = "县区id")
    private String idVsection;
}
