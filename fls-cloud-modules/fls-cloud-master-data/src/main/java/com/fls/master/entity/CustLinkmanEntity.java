package com.fls.master.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 客户联系人表
 *
 * <AUTHOR>
 * @since  2024-11-07
 */
@Data
@TableName("t_cust_linkman")
public class CustLinkmanEntity {
	/**
	* 主键
	*/
	@TableId(value = "id_custlinkman", type = IdType.ASSIGN_UUID)
	private String idCustlinkman;

	/**
	* 所属客户
	*/
	private String idCustomer;

	/**
	* 联系人
	*/
	private String idLinkman;

	/**
	* 是否默认 0=否，1=是 参见yesorno
	*/
	private String defaultFlag;

	/**
	* NC客户联系人主键
	*/
	private String pkCustlinkman;

	/**
	* 启用状态：1=未启用，2=已启用，3=已停用，默认1，参见basestatus
	*/
	private String status;

	/**
	* 创建时间
	*/
	private LocalDateTime createTime;

	/**
	* 创建人
	*/
	private String creator;

	/**
	* 失效时间
	*/
	private LocalDateTime disableTime;

	/**
	* 时间戳
	*/
	private LocalDateTime ts;

	/**
	* 是否删除：0=否，1=是，默认0，参见yesorno
	*/
	private String deleteFlag;

	/**
	* 期初标识，默认0
	*/
	private Integer initFlag;

}
