package com.fls.master.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.fls.common.core.exception.ServiceException;
import com.fls.master.api.RemoteWorkTeamService;
import com.fls.master.api.model.WorkTeamBaseInfo;
import com.fls.master.convert.BaseWorkTeamConvert;
import com.fls.master.entity.BaseWorkTeamEntity;
import com.fls.master.mapper.BaseWorkTeamMapper;
import com.github.yulichang.base.MPJBaseServiceImpl;
import java.util.Collections;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

/**
 * 工作班组表-service
 *
 * <AUTHOR>
 * @since 2024-11-12
 */
@Slf4j
@Service
@DubboService
@RequiredArgsConstructor
public class RemoteWorkTeamServiceImpl extends MPJBaseServiceImpl<BaseWorkTeamMapper, BaseWorkTeamEntity> implements RemoteWorkTeamService {

    @Override
    public List<WorkTeamBaseInfo> getWorkTeamsByUserId(String userId) {
        List<BaseWorkTeamEntity> workTeamEntities = baseMapper.selectWorkTeamsByUserId(userId);
        return BaseWorkTeamConvert.INSTANCE.toBaseInfoList(workTeamEntities);
    }

    @Override
    public WorkTeamBaseInfo getWorkTeamById(String teamId) {
        WorkTeamBaseInfo baseInfo = baseMapper.getBaseInfoByWorkTeamId(teamId);
        if (baseInfo == null) {
            throw new ServiceException("班组不存在");
        }
        return baseInfo;
    }

    @Override
    public List<WorkTeamBaseInfo> getWorkTeamsByIds(List<String> idsWorkteam) {
        if (CollectionUtil.isEmpty(idsWorkteam)) {
            return Collections.emptyList();
        }
        try {
            return baseMapper.getBaseInfoByWorkTeamIds(idsWorkteam);
        } catch (Exception e) {
            log.error("查询班组信息失败", e);
        }
        return Collections.emptyList();
    }
}
