package com.fls.master.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 基础地址信息表
 *
 * <AUTHOR>
 * @since 2024-11-07
 */
@Data
@TableName("t_base_address")
public class BaseAddressEntity {
	/**
	* 主键
	*/
	@TableId(value = "id_address", type = IdType.ASSIGN_UUID)
	private String idAddress;

	/**
	* NC地址簿主键
	*/
	private String pkAddress;

	/**
	* 国家地区
	*/
	private String idCountry;

	/**
	* 省份
	*/
	private String idProvince;

	/**
	* 城市
	*/
	private String idCity;

	/**
	* 县区
	*/
	private String idVsection;

	/**
	* 地址详细
	*/
	private String detail;

	/**
	* 地址MD5值
	*/
	private String addressMd5;

	/**
	* 地址类型
	*/
	private String addressType;

	/**
	* 启用状态：1=未启用，2=已启用，3=已停用，默认1，参见basestatus
	*/
	private String status;

	/**
	* 创建时间
	*/
	private LocalDateTime createTime;

	/**
	* 创建人
	*/
	private String creator;

	/**
	* 失效时间
	*/
	private LocalDateTime disableTime;

	/**
	* 时间戳
	*/
	private LocalDateTime ts;

	/**
	* 是否删除：0=否，1=是，默认0，参见yesorno
	*/
    @TableLogic
	private String deleteFlag;

	/**
	* 纬度，百度bd09ll坐标系
	*/
	private String lat;

	/**
	* 经度，百度bd09ll坐标系
	*/
	private String lng;

	/**
	* 邮政编码
	*/
	private String zipCode;

	/**
	* 名称
	*/
	private String name;

	/**
	* 地址全称
	*/
	private String fullName;

	/**
	* 原租赁系统地址ID
	*/
	private String oldSysid;

	/**
	* 百度地图县区编码
	*/
	private String districtCode;

	/**
	* 行政区划名
	*/
	private String regionName;

	/**
	* 地点编码
	*/
	private String code;

	/**
	* 纬度-高德
	*/
	private String latGaode;

	/**
	* 经度-高德
	*/
	private String lngGaode;

}
