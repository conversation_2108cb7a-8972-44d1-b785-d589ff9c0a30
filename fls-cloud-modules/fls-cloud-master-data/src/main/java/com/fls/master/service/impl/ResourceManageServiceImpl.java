package com.fls.master.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fls.common.core.constant.CommonConstants;
import com.fls.master.entity.BaseResdoccate;
import com.fls.master.entity.BaseResourceClassEntity;
import com.fls.master.entity.LeaseDocumentCategory;
import com.fls.master.entity.LeaseDocumentParameters;
import com.fls.master.entity.LeaseDocumentValidator;
import com.fls.master.mapper.LeaseDocumentCategoryMapper;
import com.fls.master.mapper.LeaseDocumentParametersMapper;
import com.fls.master.mapper.LeaseDocumentValidatorMapper;
import com.fls.master.pojo.query.StatusQuery;
import com.fls.master.pojo.vo.ResDocCateVO;
import com.fls.master.pojo.vo.ResourceClassVO;
import com.fls.master.service.BaseResdoccateService;
import com.fls.master.service.BaseResourceService;
import com.fls.master.service.BaseResourceclassService;
import com.fls.master.service.ResourceManageService;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * ResourceManageServiceImpl
 *
 * <AUTHOR>
 */
@Data
@RequiredArgsConstructor
@Slf4j
@Service
public class ResourceManageServiceImpl implements ResourceManageService {

    private final BaseResourceclassService resourceClass;

    private final BaseResdoccateService resDocCate;

    private final LeaseDocumentCategoryMapper documentCategoryMapper;

    private final LeaseDocumentValidatorMapper documentValidatorMapper;

    private final BaseResourceService baseResource;

    private final LeaseDocumentParametersMapper documentParametersMapper;

    @Override
    public List<ResourceClassVO> rcPublicList(StatusQuery query) {
        List<BaseResourceClassEntity> list = resourceClass.lambdaQuery()
            .eq(BaseResourceClassEntity::getDeleteFlag, CommonConstants.DELETE_FLAG_NOT_DELETED)
            .eq(Objects.nonNull(query.getStatus()), BaseResourceClassEntity::getStatus, query.getStatus())
            .list();

        return list.stream()
            .map(ResourceClassVO::of)
            .collect(Collectors.toList());
    }

    @Override
    public List<ResDocCateVO> resDocCatePublicList(StatusQuery query) {
        List<ResDocCateVO> result = Lists.newArrayList();
        List<BaseResdoccate> list = resDocCate.lambdaQuery()
            .eq(BaseResdoccate::getDeleteFlag, CommonConstants.DELETE_FLAG_NOT_DELETED)
            .eq(Objects.nonNull(query.getStatus()), BaseResdoccate::getStatus, query.getStatus())
            .list();

        List<String> idDocates = list.stream()
            .map(BaseResdoccate::getIdDoccate)
            .distinct()
            .collect(Collectors.toList());

        Map<String, LeaseDocumentValidator> validatorMap = documentValidatorMapper.selectList(Wrappers.<LeaseDocumentValidator>lambdaQuery()
                .eq(LeaseDocumentValidator::getDeleteFlag, CommonConstants.DELETE_FLAG_NOT_DELETED)
                .in(LeaseDocumentValidator::getIdDocCat, idDocates))
            .stream()
            .collect(Collectors.toMap(LeaseDocumentValidator::getIdDocCat, i -> i, (k1, k2) -> k1));

        Map<String, LeaseDocumentCategory> categoryMap = documentCategoryMapper.selectList(Wrappers.<LeaseDocumentCategory>lambdaQuery()
                .eq(LeaseDocumentCategory::getDeleteFlag, CommonConstants.DELETE_FLAG_NOT_DELETED)
                .in(LeaseDocumentCategory::getIdDocCat, idDocates))
            .stream()
            .collect(Collectors.toMap(LeaseDocumentCategory::getIdDocCat, i -> i, (k1, k2) -> k1));

        Map<String, LeaseDocumentParameters> parametersMap = documentParametersMapper.selectList(Wrappers.<LeaseDocumentParameters>lambdaQuery()
                .eq(LeaseDocumentParameters::getDeleteFlag, CommonConstants.DELETE_FLAG_NOT_DELETED)
                .in(LeaseDocumentParameters::getIdDocCat, idDocates))
            .stream()
            .collect(Collectors.toMap(LeaseDocumentParameters::getIdDocCat, i -> i, (k1, k2) -> k1));

        list.forEach(i -> {
            ResDocCateVO vo = new ResDocCateVO();
            Optional.ofNullable(categoryMap.get(i.getIdDoccate()))
                .ifPresent(category -> BeanUtils.copyProperties(category, vo));
            Optional.ofNullable(validatorMap.get(i.getIdDoccate()))
                .ifPresent(v -> vo.setValidators(ResDocCateVO.Validators.of(v)));
            Optional.ofNullable(parametersMap.get(i.getIdDoccate()))
                .ifPresent(p -> vo.setExts(ResDocCateVO.Exts.of(p)));
            result.add(vo);
        });
        return result;
    }
}
