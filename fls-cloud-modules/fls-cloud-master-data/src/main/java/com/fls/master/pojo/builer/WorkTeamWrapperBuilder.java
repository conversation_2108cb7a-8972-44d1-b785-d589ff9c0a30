package com.fls.master.pojo.builer;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.fls.master.entity.BaseBizunitEntity;
import com.fls.master.entity.BaseDepartment;
import com.fls.master.entity.BaseOrg;
import com.fls.master.entity.BasePerson;
import com.fls.master.entity.BaseWarehouseEntity;
import com.fls.master.entity.BaseWhposEntity;
import com.fls.master.entity.BaseWorkTeamEntity;
import com.fls.master.pojo.query.ArchiveQuery;
import com.fls.master.pojo.query.BaseWorkTeamQuery;
import com.fls.master.pojo.vo.BaseWorkTeamDetailVO;
import java.util.List;

public class WorkTeamWrapperBuilder extends BaseWrapperBuilder<BaseWorkTeamEntity> {
    /**
     * 构造器放在子类实现
     *
     * @return 构造器
     */
    public static WorkTeamWrapperBuilder builder() {
        return new WorkTeamWrapperBuilder();
    }

    /**
     * 添加所有业务条件
     */
    @Override
    public WorkTeamWrapperBuilder withQueryParameters(ArchiveQuery query) {
        BaseWorkTeamQuery transQuery = (BaseWorkTeamQuery)query;
        wrapper.selectAll(BaseWorkTeamEntity.class).selectAs(BaseOrg::getName, BaseWorkTeamDetailVO::getOrgName)
                .selectAs(BaseBizunitEntity::getName, BaseWorkTeamDetailVO::getBizName).selectAs(BaseBizunitEntity::getIdWarehouseCmaint, BaseWorkTeamDetailVO::getIdMainWh)
                .selectAs(BaseDepartment::getName, BaseWorkTeamDetailVO::getDepName)
                .selectAs("wh.name", BaseWorkTeamDetailVO::getWarehouseName)
                .selectAs("wh_back.name", BaseWorkTeamDetailVO::getWhbackName)
                .selectAs("wh_main.name", BaseWorkTeamDetailVO::getMainWhName)
                .selectAs(BaseWhposEntity::getName, BaseWorkTeamDetailVO::getWhposName)
                .selectAs(BasePerson::getName, BaseWorkTeamDetailVO::getHeadmanName)
                .leftJoin(BaseOrg.class, BaseOrg::getIdOrg, BaseWorkTeamEntity::getIdOrg)
                .leftJoin(BaseBizunitEntity.class, BaseBizunitEntity::getIdBizunit, BaseWorkTeamEntity::getIdBizunit)
                .leftJoin(BaseDepartment.class, BaseDepartment::getIdDepartment, BaseWorkTeamEntity::getIdDepartment)
                .leftJoin(BaseWhposEntity.class, BaseWhposEntity::getIdWhpos, BaseWorkTeamEntity::getIdWhpos)
                .leftJoin(BasePerson.class, BasePerson::getIdPerson, BaseWorkTeamEntity::getIdHeadman)
                // 使用别名关联主仓库
                .leftJoin(BaseWarehouseEntity.class, "wh", BaseWarehouseEntity::getIdWarehouse, BaseWorkTeamEntity::getIdWarehouse)
                // 关联归还仓库
                .leftJoin(BaseWarehouseEntity.class, "wh_back", BaseWarehouseEntity::getIdWarehouse, BaseWorkTeamEntity::getIdWhback)
                // 关联商业维修仓库
                .leftJoin(BaseWarehouseEntity.class, "wh_main", BaseWarehouseEntity::getIdWarehouse, BaseBizunitEntity::getIdWarehouseCmaint)
                .like(ObjectUtil.isNotEmpty(transQuery.getName()), BaseWorkTeamEntity::getName, transQuery.getName())
                .eq(ObjectUtil.isNotEmpty(transQuery.getCode()), BaseWorkTeamEntity::getCode, transQuery.getCode())
                .eq(ObjectUtil.isNotEmpty(transQuery.getIdOrg()), BaseWorkTeamEntity::getIdOrg, transQuery.getIdOrg())
                .eq(ObjectUtil.isNotEmpty(transQuery.getIdBizunit()), BaseWorkTeamEntity::getIdBizunit, transQuery.getIdBizunit())
                .eq(ObjectUtil.isNotEmpty(transQuery.getIdDepartment()), BaseWorkTeamEntity::getIdDepartment, transQuery.getIdDepartment())
                .eq(ObjectUtil.isNotEmpty(transQuery.getRentalServiceFlag()), BaseWorkTeamEntity::getRentalServiceFlag, transQuery.getRentalServiceFlag())
                .eq(ObjectUtil.isNotEmpty(transQuery.getCommercialMaintenanceFlag()), BaseWorkTeamEntity::getCommercialMaintenanceFlag, transQuery.getCommercialMaintenanceFlag())
                .eq(ObjectUtil.isNotEmpty(transQuery.getOcunitFlag()), BaseWorkTeamEntity::getOcunitFlag, transQuery.getOcunitFlag());
        return this;
    }

    /**
     * 添加权限条件
     */
    @Override
    public WorkTeamWrapperBuilder withAuthorization(List<String> authIds) {
        if (CollectionUtil.isNotEmpty(authIds)) {
            wrapper.in(BaseWorkTeamEntity::getIdOrg, authIds);
        }
        return this;
    }
}
