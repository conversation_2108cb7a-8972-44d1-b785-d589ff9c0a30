package com.fls.master.service.impl;

import com.fls.master.entity.BaseAddressEntity;
import com.fls.master.mapper.BaseAddressMapper;
import com.fls.master.service.BaseAddressService;
import com.github.yulichang.base.MPJBaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 地址表(BaseAddress)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-11-08 17:52:36
 */
@Service
@Slf4j
public class BaseAddressServiceImpl extends MPJBaseServiceImpl<BaseAddressMapper, BaseAddressEntity> implements BaseAddressService {

}
