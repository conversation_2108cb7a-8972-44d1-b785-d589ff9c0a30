package com.fls.master.service;

import com.fls.master.entity.BaseResourceEntity;
import com.fls.master.pojo.vo.ResourceCodeVo;
import com.github.yulichang.base.MPJBaseService;

/**
 * 资源表-service
 *
 * <AUTHOR>
 * @since  2024-11-12
 */
public interface BaseResourceService extends MPJBaseService<BaseResourceEntity>, BaseArchiveQueryService {

    /**
     * 查询资源编码
     * @param code 资源编码
     * @return 资源编码VO
     */
    ResourceCodeVo queryResourceCode(String code);

    /**
     * 更新资源流水号
     *
     * @param code 资源编码
     * @param flowNum 资源流水号
     */
    void updateResourceFlowNum(String code, Integer flowNum);
}
