package com.fls.master.pojo.vo;

import com.fls.master.entity.BaseBizType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.time.LocalDateTime;

/**
 * BizTypeVO
 *
 * <AUTHOR>
 */
@Data
public class BizTypeVO {

    @Schema(description = "主键")
    private String idBiztype;

    @Schema(description = "编码")
    private String code;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "级次")
    private Integer grade;

    @Schema(description = "是否末级 0=否，1=是")
    private String endFlag;

    @Schema(description = "父级业务类型")
    private String idParenttype;

    @Schema(description = "启用状态：1=未启用，2=已启用，3=已停用")
    private String status;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "失效时间")
    private LocalDateTime disableTime;


    public static BizTypeVO of(BaseBizType source) {
        BizTypeVO target = new BizTypeVO();
        BeanUtils.copyProperties(source, target);
        return target;
    }
}
