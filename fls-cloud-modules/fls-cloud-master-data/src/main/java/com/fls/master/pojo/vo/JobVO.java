package com.fls.master.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


/**
 * JobVO
 *
 * <AUTHOR>
 */
@Data
public class JobVO {

    @Schema(description = "主键")
    private String idJob;

    @Schema(description = "职务类别")
    private String idJobtype;

    @Schema(description = "编码")
    private String code;

    @Schema(description = "名称")
    private String name;

//    @Schema(description = "概要描述")
//    private String memo;

//    @Schema(description = "学历要求")
//    private String reqEducation;
//
//    @Schema(description = "性别要求")
//    private String reqSex;
//
//    @Schema(description = "年龄要求")
//    private String reqAge;
//
//    @Schema(description = "工作经验要求")
//    private String reqExperience;

//    @Schema(description = "最低工作年限")
//    private String reqWorklimit;
//
//    @Schema(description = "专业背景要求")
//    private String reqProskills;

//    @Schema(description = "其他要求")
//    private String reqOther;

    @Schema(description = "NC职务pk值")
    private String pkJob;

    @Schema(description = "状态，1=未启用，2=已启用，3=已停用")
    private String status;

//    @Schema(description = "创建时间")
//    private LocalDateTime createTime;

//    @Schema(description = "iHR职务id")
//    private String idIhr;

    @Schema(description = "职务类别名称")
    private String jobtypeName;

}
