package com.fls.master.convert;

import com.fls.master.entity.BaseDictEntity;
import com.fls.master.pojo.vo.BaseDictVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 字典表-Mapstruct
*
* <AUTHOR>
* @since  2024-11-12
*/
@Mapper
public interface BaseDictConvert {
    BaseDictConvert INSTANCE = Mappers.getMapper(BaseDictConvert.class);

    BaseDictEntity voToEntity(BaseDictVO vo);

    BaseDictVO entityToVo(BaseDictEntity entity);

    List<BaseDictVO> toVoList(List<BaseDictEntity> list);

}
