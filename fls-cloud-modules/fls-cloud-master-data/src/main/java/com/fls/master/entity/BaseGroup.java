package com.fls.master.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 集团表(BaseGroup)表实体类
 *
 * <AUTHOR>
 * @since 2024-11-02 11:12:46
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_base_group")
public class BaseGroup implements Serializable {

    private static final long serialVersionUID = 1L;

     //集团表主键
    @TableId(value = "id_group", type = IdType.ASSIGN_UUID)
    private String idGroup;

     //编码
    private String code;

     //内部编码
    private String innercode;

     //名称
    private String name;

     //简称
    private String shortname;

     //NC集团PK值
    private String pkGroup;

     //父级集团
    private String idParentgroup;

     //主营业务
    private String service;

     //简介
    private String introduction;

     //备注
    private String memo;

     //启用状态：1=未启用，2=已启用，3=已停用，默认1，参见basestatus
    private String status;

     //创建时间
    private LocalDateTime createTime;

     //创建人
    private String creator;

     //失效时间
    private LocalDateTime disableTime;

     //时间戳
    private LocalDateTime ts;

     //是否删除：0=否，1=是，默认0，参见yesorno
    private String deleteFlag;

}

