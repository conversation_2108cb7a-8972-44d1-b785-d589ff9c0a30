package com.fls.master.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;

/**
* 资源表详情信息
*
* <AUTHOR>
* @since  2024-11-12
*/
@Data
@Schema(description = "资源表详情信息")
public class BaseResourceDetailVO extends ArchiveDetailVO {

	@Schema(description = "主键")
	private String idResource;

	@Schema(description = "编码")
	private String code;

	@Schema(description = "名称")
	private String name;

	@Schema(description = "所属分类")
	private String idResourceclass;

    @Schema(description = "分类编码")
    private String classCode;

    @Schema(description = "分类名称")
    private String className;

	@Schema(description = "业务模块代码")
	private String moduleCode;

	@Schema(description = "是否启用审批流：0=否，1=是，默认1，参见yesorno")
	private String needprocFlag;

	@Schema(description = "是否进入综合待审列表：0=否，1=是，默认1，参见yesorno")
	private String incalFlag;

	@Schema(description = "是否启用数据权限控制：0=否，1=是，默认1，参见yesorno")
	private String needauthFlag;

	@Schema(description = "资源审核页url地址")
	private String incalLink;

	@Schema(description = "类型，1=档案类，2=单据类")
	private String resourceType;

	@Schema(description = "是否启用资料管理，0=否，1=是，参见yesorno")
	private String needdocFlag;

	@Schema(description = "是否启用申请，0=否，1=是，参见yesorno")
	private String needapplyFlag;

	@Schema(description = "是否启用档案数据管理，0=否，1=是，参见yesorno")
	private String archmanaFlag;

	@Schema(description = "是否启用历史记录，0=否，1=是，参见yesorno")
	private String historyFlag;

	@Schema(description = "是否进入移动APP：0=否，1=是，默认0，参见yesorno")
	private String appFlag;

	@Schema(description = "移动端动态路由url")
	private String appLink;

	@Schema(description = "域名判断标识")
	private String domainFlag;

	@Schema(description = "资源资料是否开启引用")
	private String quoteFlag;

    @Schema(description = "启用状态：1=未启用，2=已启用，3=已停用，默认1，参见basestatus")
    private String status;

	@Schema(description = "数据表名")
	private String tblName;

	@Schema(description = "数据表主键名")
	private String tblIdName;

	@Schema(description = "交易类型列表")
	private List<BaseResTransTypeVO> transList;
}
