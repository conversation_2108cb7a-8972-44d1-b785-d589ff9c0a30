package com.fls.master.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
* 客户基本档案基础信息
*
* <AUTHOR>
* @since  2024-11-06
*/
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "客户基本档案基础信息")
public class BaseCustVO extends ArchiveVO {

	@Schema(description = "主键")
	private String idCustomer;

	@Schema(description = "编码")
	private String code;

	@Schema(description = "名称")
	private String name;

	@Schema(description = "简称")
	private String shortname;

	@Schema(description = "所属组织")
	private String idOrg;

    @Schema(description = "组织名称")
    private String orgName;

    @Schema(description = "客户类型 01=终端，02=同行，03=个体户 参见cust_type")
    private String custType;

    @Schema(description = "客户等级 1=A类客户，2=B类客户，3=C类客户 参见cust_level")
    private String custLevel;

	@Schema(description = "客户分类")
	private String idCustclass;

    @Schema(description = "分类名称")
    private String className;

	@Schema(description = "上级客户")
	private String idParentcust;

	@Schema(description = "行业")
	private String idIndustry;

    @Schema(description = "行业名称")
    private String industryName;

	@Schema(description = "客户属性 0=外部单位，1=内部单位 参见cust_prop")
	private String custProp;

	@Schema(description = "对应组织")
	private String idFinanceorg;

	@Schema(description = "NC客户基本信息主键")
	private String pkCustomer;

	@Schema(description = "启用状态：1=未启用，2=已启用，3=已停用，默认1，参见basestatus")
	private String status;

}
