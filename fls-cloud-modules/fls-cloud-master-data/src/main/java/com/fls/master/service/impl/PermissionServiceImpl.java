package com.fls.master.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import com.fls.master.mapper.PermissionMapper;
import com.fls.master.service.PermissionService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * PermissionServiceImpl
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class PermissionServiceImpl implements PermissionService {

    private final PermissionMapper permissionMapper;


    @Override
    public List<String> getPrivateDepartmentId(String userId, String href) {
        List<String> roles = permissionMapper.getRoleIdsFromUserIdAndHref(userId, href);
        if (CollUtil.isNotEmpty(roles)) {
            return permissionMapper.getPrivateDepartmentId(roles);
        }
        return ListUtil.empty();
    }

    @Override
    public List<String> getPrivateOrgId(String userId, String href) {
        List<String> roles = permissionMapper.getRoleIdsFromUserIdAndHref(userId, href);
        if (CollUtil.isNotEmpty(roles)) {
            return permissionMapper.getPrivateOrgId(roles);
        }
        return ListUtil.empty();
    }

    @Override
    public List<String> getPrivatePersonId(String userId, String href) {
        List<String> roles = permissionMapper.getRoleIdsFromUserIdAndHref(userId, href);
        if (CollUtil.isNotEmpty(roles)) {
            return permissionMapper.getPrivatePersonId(roles);
        }
        return ListUtil.empty();
    }

    @Override
    public List<String> getPrivateBizunitId(String userId, String href) {
        return permissionMapper.getPrivateBizunitId(userId, href);
    }

    @Override
    public List<String> getPrivateResourceId(String userId, String href, String code) {
        return permissionMapper.getPrivateResourceId(userId, href, code);
    }

}
