package com.fls.master.pojo.vo;

import com.fls.master.entity.BaseResFiletype;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.time.LocalDateTime;

/**
 * ResFiletypeVO
 *
 * <AUTHOR>
 */
@Data
public class ResFiletypeVO {

    @Schema(description = "主键")
    private String idResFiletype;

    @Schema(description = "资源主键")
    private String idResource;

    @Schema(description = "文件类型MIME类型")
    private String fileType;

    @Schema(description = "状态，1=未启用，2=已启用，3=已停用")
    private String status;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "作废时间")
    private LocalDateTime invalidTime;

    @Schema(description = "资源编码")
    private String resourceCode;

    @Schema(description = "资源名称")
    private String resourceName;

    public static ResFiletypeVO of(BaseResFiletype source) {
        ResFiletypeVO target = new ResFiletypeVO();
        BeanUtils.copyProperties(source,target);
        return target;
    }
}
