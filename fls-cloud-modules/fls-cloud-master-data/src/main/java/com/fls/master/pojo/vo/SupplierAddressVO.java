package com.fls.master.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 客户收货地址表基础信息
 *
 * <AUTHOR>
 * @since 2024-11-07
 */
@Data
@Schema(description = "客户收货地址表基础信息")
public class SupplierAddressVO implements Serializable {
	private static final long serialVersionUID = 1L;

	@Schema(description = "主键")
	private String idSuppaddress;

    @Schema(description = "所属供应商")
    private String idSupplier;

	@Schema(description = "收货地址")
	private String idAddress;

	@Schema(description = "收货单位")
	private String receivingUnit;

	@Schema(description = "联系人")
	private String idLinkman;

	@Schema(description = "F4云围栏id")
	private String fenceId;

	@Schema(description = "服务主体")
	private String idService;

	@Schema(description = "外协单位")
	private String idOcunit;

	@Schema(description = "客户分拨")
	private String idCustallocation;

	@Schema(description = "开票客户")
	private String idCustinvoice;

	@Schema(description = "期初标识，默认0")
	private Integer initFlag;

	@Schema(description = "地址映射关系表主键值关联:t_cust_address_map")
	private String idCustAddrmap;

    @Schema(description = "详细地址")
    private String detail;

    @Schema(description = "启用状态：1=未启用，2=已启用，3=已停用，默认1，参见basestatus")
    private String status;

}
