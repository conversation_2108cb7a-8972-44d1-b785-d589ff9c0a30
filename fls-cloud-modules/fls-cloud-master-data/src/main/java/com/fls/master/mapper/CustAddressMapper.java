package com.fls.master.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fls.master.entity.CustAddressEntity;
import com.fls.master.pojo.query.CustAllocationQuery;
import com.fls.master.pojo.vo.CustAllocationVO;
import com.github.yulichang.base.MPJBaseMapper;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * 客户基本档案-mapper
 *
 * <AUTHOR>
 * @since 2024-11-06
 */
public interface CustAddressMapper extends MPJBaseMapper<CustAddressEntity> {

    Page<CustAllocationVO> selectAllocPAge(@Param("query") CustAllocationQuery query,@Param("privateIdOrg") List<String> privateIdOrg, Page<CustAllocationVO> page);

}
