package com.fls.master.pojo.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

/**
 * 客户分拨信息查询
 *
 * <AUTHOR>
 * @since 2024-11-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "客户分拨信息查询")
public class CustAllocationQuery extends ArchiveQuery {
    @Schema(description = "客户名称/客户/编码地址/地址编码")
    private String keyword;

    @Schema(description = "分拨名称")
    private String name;

    @Schema(description = "组织id")
    private String idOrg;

    @Schema(description = "当前页")
    @Min(value = 1, message = "当前页不能小于1")
    private Long pageNo = 1L;

    @Schema(description = "分页大小")
    @Min(value = 1, message = "当前分页大小不能小于1")
    @Max(value = 5000, message = "分页大小最大不能超过5000")
    private Long pageSize = 10L;
}
