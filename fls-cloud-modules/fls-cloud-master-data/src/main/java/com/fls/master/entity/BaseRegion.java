package com.fls.master.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 行政区划表(BaseRegion)表实体类
 *
 * <AUTHOR>
 * @since 2024-11-08 16:23:50
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_base_region")
public class BaseRegion implements Serializable {

    private static final long serialVersionUID = 1L;

     //主键
    @TableId(value = "id_region", type = IdType.ASSIGN_UUID)
    private String idRegion;

     //NC行政区划主键
    private String pkRegion;

     //编码
    private String code;

     //名称
    private String name;

     //简称
    private String shortname;

     //行政区划类别：01=省，02=自治区，03=直辖市，04=地级市，05=地区，06=自治州，07=盟，08=市辖区，09=县级市，10=县，11=旗，12=自治县，参见region_type
    private String regionType;

     //行政区划级次：1=省，2=市，3=县，参见region_level
    private String regionLevel;

     //区号
    private String areaCode;

     //邮编
    private String zipCode;

     //国家地区
    private String idCountry;

     //上级行政区划
    private String idParentregion;

     //启用状态：1=未启用，2=已启用，3=已停用，默认1，参见basestatus
    private String status;

     //创建时间
    private LocalDateTime createTime;

     //创建人
    private String creator;

     //失效时间
    private LocalDateTime disableTime;

     //时间戳
    private LocalDateTime ts;

     //是否删除：0=否，1=是，默认0，参见yesorno
    private String deleteFlag;

     //保养小组id
    private String idMaintenanceTeam;

}

