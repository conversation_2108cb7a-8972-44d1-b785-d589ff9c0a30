package com.fls.master.pojo.vo;

import com.fls.master.entity.BaseProductCategory;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.time.LocalDateTime;

/**
 * ProductVO
 *
 * <AUTHOR>
 */
@Data
public class ProductVO {

    @Schema(description = "主键")
    private String id;

    @Schema(description = "编码")
    private String code;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "标识名")
    private String ename;

    @Schema(description = "前缀名")
    private String prefix;

//    @Schema(description = "编码长度")
//    private Integer lenCode;

    @Schema(description = "流水号")
    private Integer flowNum;

//    @Schema(description = "备注")
//    private String memo;

    @Schema(description = "状态，1=未启用，2=已启用，3=已停用")
    private String status;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "作废时间")
    private LocalDateTime invalidTime;

    @Schema(description = "作废人")
    private String invalider;

    @Schema(description = "产品类型，1=整机，2=大件,3=附件")
    private String productType;

    @Schema(description = "适用整机")
    private String vehicleLimits;

    @Schema(description = "排序")
    private Integer sort;

    public static ProductVO of(BaseProductCategory source) {
        ProductVO target = new ProductVO();
        BeanUtils.copyProperties(source,target);
        target.setId(source.getIdProductcate());
        return target;
    }
}
