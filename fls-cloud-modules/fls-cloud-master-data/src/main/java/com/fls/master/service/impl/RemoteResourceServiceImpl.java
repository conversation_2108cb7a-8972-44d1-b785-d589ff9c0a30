package com.fls.master.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.fls.common.core.exception.ServiceException;
import com.fls.master.api.RemoteResourceService;
import com.fls.master.api.model.ResourceInfo;
import com.fls.master.convert.BaseResourceConvert;
import com.fls.master.entity.BaseResourceEntity;
import com.fls.master.mapper.BaseResourceMapper;
import com.fls.master.pojo.vo.ResourceCodeVo;
import com.fls.master.service.BaseResourceService;
import com.github.yulichang.base.MPJBaseServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 工作班组表-service
 *
 * <AUTHOR>
 * @since 2024-11-12
 */
@Slf4j
@Service
@DubboService
@RequiredArgsConstructor
public class RemoteResourceServiceImpl extends MPJBaseServiceImpl<BaseResourceMapper, BaseResourceEntity> implements RemoteResourceService {

    public static final String CODE_CATALOG = "fls:code:cache";

    private final BaseResourceService resourceService;

    private final RedissonClient redissonClient;

    @Override
    public ResourceInfo getResourceById(String resourceId) {
        BaseResourceEntity resourceEntity = getById(resourceId);
        return BaseResourceConvert.INSTANCE.entityToInfo(resourceEntity);
    }

    @Override
    public List<ResourceInfo> getResourcesByIds(List<String> idsResource) {
        if (CollectionUtil.isEmpty(idsResource)) {
            return Collections.emptyList();
        }
        List<ResourceInfo> resources = listByIds(idsResource).stream().map(BaseResourceConvert.INSTANCE::entityToInfo).collect(Collectors.toList());
        return CollUtil.isEmpty(resources) ? Collections.emptyList() : resources;
    }

    @Override
    public String genBIllCode(String resourceId) {
        ResourceCodeVo resourceCodeInfo = baseMapper.queryResCodeById(resourceId);
        if (ObjectUtil.isNull(resourceCodeInfo)) {
            throw new ServiceException("资源编码信息不存在");
        }
        String code = resourceCodeInfo.getCode();
        Integer codeLen = resourceCodeInfo.getLenCode() - resourceCodeInfo.getPrefix().length();
        String busKey = resourceCodeInfo.getPrefix();
        RLock rLock = redissonClient.getLock(code);
        try {
            rLock.tryLock();
            RAtomicLong atomicLong = redissonClient.getAtomicLong(CODE_CATALOG + "::" + busKey);
            long num = atomicLong.incrementAndGet();
            String coverStr = fillLeftZero(String.valueOf(num), codeLen);
            resourceService.updateResourceFlowNum(code, (int) num);
            // 拼接
            return busKey + coverStr;
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        } finally {
            rLock.unlock();
        }
    }

    /**
     * 左补位
     *
     * @param str    字符串
     * @param length 总位数
     * @return Result 补位后的字符串
     */
    public static String fillLeftZero(String str, Integer length) {
        int strLen = str.length();
        if (strLen < length) {
            while (strLen < length) {
                str = "0" + str;
                strLen++;
            }
        }
        return str;
    }
}
