package com.fls.master.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * OrgVO
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrgVO{

    @Schema(description = "组织主键")
    private String idOrg;

    @Schema(description = "组织编码")
    private String code;

    @Schema(description = "内部编码")
    private String innercode;

    @Schema(description = "组织名称")
    private String name;

    @Schema(description = "组织简称")
    private String shortname;

    @Schema(description = "NC组织pk值")
    private String pkOrg;

//    @Schema(description = "是否采购 0=否，1=是")
//    private String purchaseFlag;

//    @Schema(description = "是否销售 0=否，1=是")
//    private String salesFlag;
//
//    @Schema(description = "是否物流 0=否，1=是")
//    private String trafficFlag;
//
//    @Schema(description = "是否财务 0=否，1=是")
//    private String financeFlag;
//
//    @Schema(description = "是否库存 0=否，1=是")
//    private String stockFlag;
//
//    @Schema(description = "是否人力资源 0=否，1=是 ")
//    private String hrFlag;
//
//    @Schema(description = "是否行政 0=否，1=是 ")
//    private String adminFlag;
//
//    @Schema(description = "是否独立法人 0=否，1=是 ")
//    private String companyFlag;

    @Schema(description = "组织类型  0=总公司， 1=分公司，2=子公司，3=孙公司，4=营业部，9=虚拟公司")
    private String orgType;

    @Schema(description = "父级组织")
    private String idParentorg;
//
//    @Schema(description = "负责人")
//    private String orgManager;
//
//    @Schema(description = "分管领导")
//    private String orgLeader;
//
//    @Schema(description = "经纬度")
//    private String latLongAlt;
//
    @Schema(description = "所属集团id")
    private String idGroup;

    @Schema(description = "所属集团名称")
    private String groupName;

    @Schema(description = "启用状态：1=未启用，2=已启用，3=已停用，默认1")
    private String status;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "创建人")
    private String creator;

    @Schema(description = "失效时间")
    private LocalDateTime disableTime;

    @Schema(description = "开票组织")
    private String idOrgInvoice;

    @Schema(description = "注销标识")
    private String cancelFlag;

//    @Schema(description = "iHR组织id")
//    private String idIhr;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "付款银行账户融资请款用")
    private String myPaymentAccount;

}
