package com.fls.master;

import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.metrics.buffering.BufferingApplicationStartup;

/**
 * 主数据启动入口
 *
 * <AUTHOR>
 * @since 2024-10-31
 */
@EnableDubbo
@SpringBootApplication
public class FlsMasterDataApplication {
    public static void main(String[] args) {
        SpringApplication application = new SpringApplication(FlsMasterDataApplication.class);
        application.setApplicationStartup(new BufferingApplicationStartup(2048));
        application.run(args);
    }
}
