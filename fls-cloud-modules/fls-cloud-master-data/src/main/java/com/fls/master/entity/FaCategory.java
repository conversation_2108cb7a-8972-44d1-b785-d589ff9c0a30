package com.fls.master.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 固定资产类别表(FaCategory)表实体类
 *
 * <AUTHOR>
 * @since 2024-11-06 14:57:57
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_fa_category")
public class FaCategory implements Serializable {

    private static final long serialVersionUID = 1L;

     //主键
    @TableId(value = "id_facategory", type = IdType.ASSIGN_UUID)
    private String idFacategory;

     //编码
    private String code;

     //名称
    private String name;

     //级次
    private Integer grade;

     //是否末级 0=否，1=是 参见yesorno
    private String endFlag;

     //NC资产类别主键
    private String pkCategory;

     //折旧方法
    private String idDepmethod;

     //父级资产类别
    private String idParentcate;

     //交易类型
    private String idTrantype;

     //净残值率(%)
    private BigDecimal netResidualRate;

     //使用月限
    private Integer serviceMonth;

     //计量单位
    private String meaUnit;

     //启用状态 1=未启用，2=已启用，3=已停用 参见basestatus
    private String status;

     //创建时间
    private LocalDateTime createTime;

     //创建人
    private String creator;

     //失效时间
    private LocalDateTime disableTime;

     //时间戳
    private LocalDateTime ts;

     //是否删除：0=否，1=是，默认0，参见yesorno
    private String deleteFlag;

}
