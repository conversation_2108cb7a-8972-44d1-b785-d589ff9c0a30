package com.fls.master.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 部门表(BaseDepartment)表实体类
 *
 * <AUTHOR>
 * @since 2024-10-31 15:29:49
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_base_department")
public class BaseDepartment implements Serializable {

    private static final long serialVersionUID = 1L;

     //部门主键
    @TableId(value = "id_department", type = IdType.ASSIGN_UUID)
    private String idDepartment;

     //多版本部门主键
    private String idDeptversions;

     //编码
    private String code;

     //名称
    private String name;

     //部门类型：0=普通部门，1=虚拟部门，默认0，参见dept_type
    private String deptType;

     //所属组织
    private String idOrg;

     //所属集团
    private String idGroup;

     //上级部门
    private String idParentdept;

     //NC部门PK
    private String pkDept;

     //成立日期 格式yyyy-mm-dd
    private String buildDate;

     //撤销日期 格式yyyy-mm-dd
    private String abortDate;

     //版本号 格式yyyymmdd
    private String version;

     //启用状态：1=未启用，2=已启用，3=已停用，默认1，参见basestatus
    private String status;

     //创建时间
    private LocalDateTime createTime;

     //创建人
    private String creator;

     //失效时间
    private LocalDateTime disableTime;

     //时间戳
    private LocalDateTime ts;

     //是否删除：0=否，1=是，默认0，参见yesorno
    private String deleteFlag;

     //iHR部门id
    private String idIhr;

}

