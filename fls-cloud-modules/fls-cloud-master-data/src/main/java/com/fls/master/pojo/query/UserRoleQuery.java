package com.fls.master.pojo.query;

import cn.hutool.core.util.StrUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 员工角色查询
 *
 * <AUTHOR>
 * @since 2025-06-09
 */
@Data
public class UserRoleQuery {

    @Schema(description = "所属组织")
    private String idOrg;

    @Schema(description = "所属经营主体")
    private String idBizunit;

    @Schema(description = "角色id")
    private String idRole;

    @Schema(description = "角色编码")
    private String roleCode;

    /**
     * 校验参数 idRole 和 roleCode 至少有一个不为空
     *
     * @return 校验结果
     */
    public boolean validateRoleParam() {
        return StrUtil.isBlank(idRole) && StrUtil.isBlank(roleCode);
    }
}
