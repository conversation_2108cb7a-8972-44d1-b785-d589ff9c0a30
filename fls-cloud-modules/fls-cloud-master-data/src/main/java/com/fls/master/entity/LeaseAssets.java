package com.fls.master.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * LA租赁资产表(LeaseAssets)表实体类
 *
 * <AUTHOR>
 * @since 2024-11-12 11:35:26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_lease_assets")
public class LeaseAssets implements Serializable {

    private static final long serialVersionUID = 1L;

     //主键
    @TableId(value = "id_assets", type = IdType.ASSIGN_UUID)
    private String idAssets;

     //资产编码
    private String laCode;

     //租赁资产类型
    private String idAssetType;

     //车型id
    private String idModelnumber;

     //供应商车型
    private String idForkliftmodelsSupplier;

     //品牌id
    private String idBrand;

     //别名（资产标识名）
    private String proEname;

     //存放地址类型
    private String addressType;

     //地址
    private String idAddress;

     //签收地址id
    private String idSignAddress;

     //资产进入签收状态 0未签收，1正常签收，2异常签收
    private Integer inSignStatus;

     //签收日期
    private LocalDate inSignDate;

     //首次进入地址（基础地址）
    private String idFirstInAddress;

     //首次签收状态（字典表签收状态）
    private String firstInStatus;

     //首次签收日期（采购/租入-签收 其他-创建）
    private LocalDate firstInDate;

     //配置表
    private String idConfig;

     //配置文本
    private String configTxtBak;

     //资产名称 命名规则：品牌+车型+吨位（系统生成）
    private String laName;

     //制造编号
    private String manufacture;

     //租赁系统资产ID
    private String assetsid;

     //车架号
    private String carNum;

     //新旧 J=二手车，X=新车 参见neworold
    private String neworold;

     //条码可打印数量
    private Integer barcodeNum;

     //租赁资产进入方式
    private String assetIncrease;

     //资产退出方式
    private String assetDecrease;

     //是否出租 0=否，1=是
    private String leaseStatus;

     //是否需要融资  0=否，1=是 参见yesorno
    private String financingFlag;

     //融资状态
    private String financingStatus;

     //融资制造编号
    private String financingManufacture;

     //现在哪台叉车上
    private String idAssetsOn;

     //购买时是在哪辆车上
    private String idAssetsOnPurchase;

     //资产状态 1=正常，2=待检，3=待修，4=待售, 5=再制造 参见assets_type
    private String assetStatus;

     //所有权公司名
    private String idOrgOwnerName;

     //所有权公司
    private String idOrgOwner;

     //管理主体
    private String idOrgManage;

     //使用主体
    private String idOrgUse;

     //服务主体
    private String idOrgService;

     //外协单位
    private String idOrgOut;

     //内锁 0 自由 1锁定
    private String lockIn;

     //外锁 0 自由 1锁定
    private String lockOut;

     //在役状态：0=在役，1=退出，2=融资租入退出
    private String assetActive;

     //属性  0 内部 1 外部
    private String assetProperty;

     //整车购入无税金额
    private BigDecimal moneyAllNotax;

     //整车购入含税金额
    private BigDecimal moneyAllTax;

     //资产无税原值
    private BigDecimal moneySplitNotax;

     //资产含税原值
    private BigDecimal moneySplitTax;

     //整车含税运费
    private BigDecimal moneyFreightTax;

     //整车无税运费
    private BigDecimal moneyFreightNotax;

     //内部调拨价
    private BigDecimal moneyInnerAllot;

     //其他费用
    private BigDecimal moneyOther;

     //固定资产原值, 整车无税金额+整车无税运费
    private BigDecimal originvalue;

     //是否可移动0否1是
    private String moveFlag;

     //年检预约状态1未预约2已约检3已检车4资料登记中5资料登记完成
    private String inspectStatus;

     //最新年检日期
    private LocalDate inspectDate;

     //车牌预约状（字典表	license_plate_status）
    private String licenseStatus;

     //是否上牌0否1是
    private String licenseFlag;

     //车牌表
    private String idLicenseplate;

     //车牌号码
    private String licenseNum;

     //固定资产表主键
    private String idFixedAssets;

     //内租核算id
    private String innerRentCost;

     //内租开始折旧日期
    private LocalDate innerRentDepDate;

     //生产日期
    private LocalDate productDate;

     //退出日期（租赁转库存（转销售|转再制造）、同行租入退租、采购退货、盘亏、资产变动（类型：减少））
    private LocalDate exitDate;

     //购入日期
    private LocalDate buyDate;

     //仪表盘读数
    private BigDecimal dashboardNum;

     //工作时长
    private BigDecimal workHour;

     //保险状态
    private String insuranceStatus;

     //保养月份/保养类型 0=不保养 1=单月保养 2=双月保养,3=未定义
    private String maintenanceType;

     //时间戳
    private LocalDateTime ts;

     //是否删除：0=否，1=是，默认0，参见yesorno
    private String deleteFlag;

     //失效时间
    private LocalDateTime disableTime;

     //创建人
    private String creator;

     //创建时间
    private LocalDateTime createTime;

     //附件表id
    private String idLink;

     //客户id
    private String idCustomer;

     //技术码
    private String technicalCode;

     //供应商id
    private String idSupplier;

     //开票供应商
    private String idInvoiceSupplier;

     //融资公司
    private String idFinancing;

     //新融资公司,实时
    private String idFinancingNew;

     //保险公司
    private String idInsurance;

     //发动机号
    private String engineCode;

     //外租租赁单号
    private String idLeaseOut;

     //内租租赁单
    private String idLeaseInner;

     //融资项目id
    private String idProjectFinancing;

     //物流公司
    private String idLogistics;

     //运输状态
    private String transportFlag;

     //物料编码（存货编码）
    private String materielCode;

     //形式买断类型，01=整车买断，02=随车买断，03=大件买断
    private String virbuyType;

     //采购发票
    private String idPurchaseInvoiceB;

     //累计出租月份初使值
    private Integer rentMonthsBase;

     //在租累计月份
    private Integer rentMonthsAccum;

     //所属仓库id
    private String idWarehouse;

     //货区id
    private String idRack;

     //货区记录id（最新）
    private String idRackLog;

     //是否配置调整中（反向调整也算）
    private String adjustFlag;

     //出租次数
    private Integer rentNum;

     //采购税率
    private Integer taxRate;

     //整机内部调拨价
    private BigDecimal moneyAllInnerAllot;

     //保养小组
    private String idMaintenanceTeam;

     //变动时间
    private String lmpChangeTime;

     //是否处于参数调整中
    private Integer paramChangeFlag;

     //是否处于属性调整中
    private Integer propertyChangeFlag;

     //是否处于内部调拨价调整中
    private Integer allotChangeFlag;

     //是否处于上牌年检中
    private Integer licenseInspectFlag;

     //车牌申请单id（最新）
    private String idLicenseApply;

     //年检申请单id（最新）
    private String idInspectApply;

     //处理标识，用于批量执行内租核算分类更新
    private String actFlag;

     //内锁主体
    private String idBizunitInner;

     //内锁类别
    private String lockInnerType;

     //期初累计租赁收入
    private BigDecimal initMnyAccincome;

     //期初累计维修成本
    private BigDecimal initMnyAccmaintcosts;

     //期初累计出租月份
    private Integer initMonAcclease;

     //FIMS备注
    private String fimsMemo;

     //是否需要融资备注
    private String financingFlagMemo;

     //来源哪个表
    private String idSourceType;

     //来源哪个表
    private String idSourceH;

     //是否进行整机内部调拨价调整
    private Integer interPriceFlag;

     //自检标识，参见yesorno
    private String selfInspectionFlag;

     //最新的运输单（11.18增加）资产发出确认之后修改
    private String idTransport;

     //最新保养日期
    private LocalDate maintenanceLastDate;

     //计划保养时间
    private LocalDate maintenancePlanDate;

     //资产图片
    private String idAssetsImg;

     //是否需要保养
    private String maintainFlag;

     //是否资产转移中
    private Integer transferFlag;

     //资料引用标识id
    private String idLinkDoc;

     //外租处罚
    private String idPunishOut;

     //质保开始时间
    private LocalDate startQaDate;

     //是否有质保：0=否，1=是，默认0，参见yesorno
    private String qaFlag;

     //质保时长，单位：月
    private Integer qaMonths;

     //质保小时
    private Integer qaHours;

     //质保结束时间(根据质保时长推算)
    private LocalDate endQaDate;

     //资产状态变更单B表（最新）
    private String idMaintenanceStateB;

     //1：小修 2：中修 3：大修 4：点检 5：全车翻新（最新）
    private String repairType;

     //是否包含充电机（数据来源为导入）0=否，1=包含充电机，默认0
    private String inchargerFlag;

     //重量 单位(吨)
    private BigDecimal weight;

     //是否推荐
    private Integer recommendFlag;

     //资产主图 id_attachment
    private String idImgMain;

     //新车首保标记 0=否，1=是 参见yesorno
    private String firstMaintenanceFlag;

     //资产备注
    private String memo;

     //车架编号状态,字典frame_number_status
    private String frameNumberStatus;

     //资产默认打印材质（字典值）
    private String printTextureCode;

     //资产默认颜色（字典值）
    private String printColorCode;

     //期初累计出租月份（全）
    private Integer allInitAccrentMonths;

     //静态出租月份（全）
    private Integer allRentMonthsBase;

     //动态出租月份（全）
    private Integer allRentMonthsActive;

     //最新在库保养日期
    private LocalDate storeMaintenanceLastDate;

     //二手车锁 0 自由 1锁定
    private String lockOldcar;

     //二手车锁有效日期
    private LocalDate lockOldcarDate;

}

