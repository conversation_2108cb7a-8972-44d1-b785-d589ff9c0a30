package com.fls.master.convert;

import com.fls.master.entity.BaseSupplierEntity;
import com.fls.master.pojo.vo.BaseSupplierDetailVO;
import com.fls.master.pojo.vo.BaseSupplierVO;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
* 供应商基本档案-Mapstruct
*
* <AUTHOR>
* @since  2024-11-07
*/
@Mapper
public interface BaseSupplierConvert {
    BaseSupplierConvert INSTANCE = Mappers.getMapper(BaseSupplierConvert.class);

    BaseSupplierEntity voToEntity(BaseSupplierVO vo);

    BaseSupplierVO entityToVo(BaseSupplierEntity entity);

    BaseSupplierDetailVO entityToDetailVo(BaseSupplierEntity entity);

    List<BaseSupplierVO> toVoList(List<BaseSupplierEntity> list);
}
