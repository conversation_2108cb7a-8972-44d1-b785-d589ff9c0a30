package com.fls.master.pojo.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
* 供应商基本档案查询
*
* <AUTHOR>
* @since  2024-11-07
*/
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "供应商基本档案查询")
public class BaseSupplierQuery extends ArchiveQuery {
    @Schema(description = "编码")
    private String code;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "所属组织")
    private String idOrg;

    @Schema(description = "供应商分类")
    private String idSupplierclass;

    @Schema(description = "上级供应商")
    private String idParentsupplier;

    @Schema(description = "供应商属性 0=外部单位，1=内部单位 参见supplier_prop")
    private String supplierProp;

    @Schema(description = "供应商类型 01=工厂，02=同行，03=小作坊 参见supplier_type")
    private String supplierType;

    @Schema(description = "供应商结算方式 01=月结，02=现金，03=代收货款，04=款到发货，05=预付订金，06=货到付款，07=分期付款，08=现金支票，09=约定分期付款 参见supplier_settletype")
    private String settleType;

    @Schema(description = "租赁外协服务标识：0=否，1=是，参见yesorno")
    private String leaseAssistFlag;

    @Schema(description = "物流供应商标识，0=否，1=是")
    private String logisticsFlag;

    @Schema(description = "页码")
    private Integer pageNo = 1;

    @Schema(description = "页大小")
    private Integer pageSize = 20;
}
