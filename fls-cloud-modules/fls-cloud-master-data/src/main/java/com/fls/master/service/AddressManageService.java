package com.fls.master.service;

import cn.hutool.core.lang.tree.Tree;
import com.fls.master.pojo.dto.AddressDTO;
import com.fls.master.pojo.dto.UpdateAddressDTO;
import com.fls.master.pojo.query.AddressQuery;
import com.fls.master.pojo.query.InnerAddressQuery;
import com.fls.master.pojo.query.RegionLevelQuery;
import com.fls.master.pojo.query.StatusQuery;
import com.fls.master.pojo.vo.AddressTypeVO;
import com.fls.master.pojo.vo.AddressVO;
import com.fls.master.pojo.vo.InnerAddressVo;
import com.fls.master.pojo.vo.RegionLevelVO;
import java.util.List;

/**
 * AddressManageService
 *
 * <AUTHOR>
 */
public interface AddressManageService {

    List<Tree<String>> regionPublicTree(StatusQuery query);

    List<RegionLevelVO> regionPublicRegionLevel(RegionLevelQuery query);

    List<AddressTypeVO> addressPublicType(StatusQuery query);

    List<AddressVO> addressPublicList(AddressQuery query);

    List<AddressVO> addressPrivateList(AddressQuery query);

    Boolean removeBatchAddress(List<String> ids);

    Boolean saveBatchAddress(List<AddressDTO> dtos);

    Boolean updateBatchAddress(List<UpdateAddressDTO> dtos);

    /**
     * 获取内部地址列表
     *
     * @param query 内部地址查询参数
     * @return 内部地址列表
     */
    List<InnerAddressVo> innerAddressList(InnerAddressQuery query);
}
