package com.fls.master.pojo.builer;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.fls.master.entity.BaseResourceClassEntity;
import com.fls.master.entity.BaseResourceEntity;
import com.fls.master.pojo.query.ArchiveQuery;
import com.fls.master.pojo.query.BaseResourceQuery;
import com.fls.master.pojo.vo.BaseResourceVO;
import java.util.List;

public class ResourceWrapperBuilder extends BaseWrapperBuilder<BaseResourceEntity> {
    /**
     * 构造器放在子类实现
     *
     * @return 构造器
     */
    public static ResourceWrapperBuilder builder() {
        return new ResourceWrapperBuilder();
    }

    /**
     * 添加所有业务条件
     */
    @Override
    public ResourceWrapperBuilder withQueryParameters(ArchiveQuery query) {
        BaseResourceQuery transQuery = (BaseResourceQuery) query;
        wrapper.selectAll(BaseResourceEntity.class)
            .selectAs(BaseResourceClassEntity::getCode, BaseResourceVO::getTypeCode)
            .selectAs(BaseResourceClassEntity::getName, BaseResourceVO::getTypeName)
            .leftJoin(BaseResourceClassEntity.class, BaseResourceClassEntity::getIdResourceclass, BaseResourceEntity::getIdResourceclass)
            .eq(ObjectUtil.isNotEmpty(transQuery.getCode()), BaseResourceEntity::getCode, transQuery.getCode())
            .like(ObjectUtil.isNotEmpty(transQuery.getName()), BaseResourceEntity::getName, transQuery.getName())
            .eq(ObjectUtil.isNotEmpty(transQuery.getIdResourceclass()), BaseResourceEntity::getIdResourceclass, transQuery.getIdResourceclass())
            .eq(ObjectUtil.isNotEmpty(transQuery.getNeedprocFlag()), BaseResourceEntity::getNeedprocFlag, transQuery.getNeedprocFlag())
            .eq(ObjectUtil.isNotEmpty(transQuery.getIncalFlag()), BaseResourceEntity::getIncalFlag, transQuery.getIncalFlag())
            .eq(ObjectUtil.isNotEmpty(transQuery.getNeedauthFlag()), BaseResourceEntity::getNeedauthFlag, transQuery.getNeedauthFlag())
            .eq(ObjectUtil.isNotEmpty(transQuery.getResourceType()), BaseResourceEntity::getResourceType, transQuery.getResourceType())
            .eq(ObjectUtil.isNotEmpty(transQuery.getNeeddocFlag()), BaseResourceEntity::getNeeddocFlag, transQuery.getNeeddocFlag())
            .eq(ObjectUtil.isNotEmpty(transQuery.getNeedapplyFlag()), BaseResourceEntity::getNeedapplyFlag, transQuery.getNeedapplyFlag())
            .eq(ObjectUtil.isNotEmpty(transQuery.getArchmanaFlag()), BaseResourceEntity::getArchmanaFlag, transQuery.getArchmanaFlag())
            .eq(ObjectUtil.isNotEmpty(transQuery.getHistoryFlag()), BaseResourceEntity::getHistoryFlag, transQuery.getHistoryFlag())
            .eq(ObjectUtil.isNotEmpty(transQuery.getAppFlag()), BaseResourceEntity::getAppFlag, transQuery.getAppFlag());
        return this;
    }

    /**
     * 添加权限条件
     */
    @Override
    public ResourceWrapperBuilder withAuthorization(List<String> authIds) {
        if (CollectionUtil.isNotEmpty(authIds)) {
            wrapper.in(BaseResourceEntity::getIdResource, authIds);
        }
        return this;
    }
}
