package com.fls.master.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 岗位表(BasePost)表实体类
 *
 * <AUTHOR>
 * @since 2024-10-31 17:15:40
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_base_post")
public class BasePost implements Serializable {

    private static final long serialVersionUID = 1L;

     //主键
    @TableId(value = "id_post", type = IdType.ASSIGN_UUID)
    private String idPost;

     //编码
    private String code;

     //名称
    private String name;

     //所属组织
    private String idOrg;

     //所属集团
    private String idGroup;

     //所属部门
    private String idDepartment;

     //岗位序列
    private String idPostseries;

     //上级岗位
    private String idParentpost;

     //所属职务
    private String idJob;

     //成立日期 格式yyyy-mm-dd
    private String buildDate;

     //撤销日期 格式yyyy-mm-dd
    private String abortDate;

     //NC岗位PK
    private String pkPost;

     //启用状态：1=未启用，2=已启用，3=已停用，默认1，参见basestatus
    private String status;

     //创建时间
    private LocalDateTime createTime;

     //创建人
    private String creator;

     //失效时间
    private LocalDateTime disableTime;

     //时间戳
    private LocalDateTime ts;

     //是否删除：0=否，1=是，默认0，参见yesorno
    private String deleteFlag;

     //iHR岗位id
    private String idIhr;

}

