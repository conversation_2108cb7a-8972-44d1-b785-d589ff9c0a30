package com.fls.master.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 基础联系人表
 *
 * <AUTHOR>
 * @since  2024-11-07
 */

@Data
@TableName("t_base_linkman")
public class BaseLinkmanEntity {
	/**
	* 主键
	*/
	@TableId(value = "id_linkman", type = IdType.ASSIGN_UUID)
	private String idLinkman;

	/**
	* 名称
	*/
	private String name;

	/**
	* 称呼
	*/
	private String idAppellation;

	/**
	* 性别 1=男，2=女，3=不详 参见sex
	*/
	private String sex;

	/**
	* 电话
	*/
	private String tel;

	/**
	* 手机
	*/
	private String mobile;

	/**
	* 电子邮箱
	*/
	private String email;

	/**
	* 传真
	*/
	private String fax;

	/**
	* 生日
	*/
	private String birthday;

	/**
	* 纪念日
	*/
	private String anniversary;

	/**
	* 通讯地址
	*/
	private String address;

	/**
	* NC联系人主键
	*/
	private String pkLinkman;

	/**
	* 启用状态：1=未启用，2=已启用，3=已停用，默认1，参见basestatus
	*/
	private String status;

	/**
	* 创建时间
	*/
	private LocalDateTime createTime;

	/**
	* 创建人
	*/
	private String creator;

	/**
	* 失效时间
	*/
	private LocalDateTime disableTime;

	/**
	* 时间戳
	*/
	private LocalDateTime ts;

	/**
	* 是否删除：0=否，1=是，默认0，参见yesorno
	*/
	private String deleteFlag;

	/**
	* 身份证号
	*/
	private String idCard;

	/**
	* 手机号2
	*/
	private String mobile2;

	/**
	* 手机号3
	*/
	private String mobile3;

	/**
	* 联系人md5值
	*/
	private String md5Linkman;

}
