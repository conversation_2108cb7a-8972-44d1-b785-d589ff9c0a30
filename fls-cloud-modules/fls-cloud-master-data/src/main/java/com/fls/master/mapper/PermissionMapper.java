package com.fls.master.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * PermissionMapper
 *
 * <AUTHOR>
 */
public interface PermissionMapper {

    /**
     * 根据用户id和url查询当前用户对应的角色id
     *
     * @param userId
     * @param href
     * @return
     */
    List<String> getRoleIdsFromUserIdAndHref(@Param("userId") String userId, @Param("href") String href);

    /**
     * 根据角色id查询有权限的部门id
     *
     * @param roles
     * @return
     */
    List<String> getPrivateDepartmentId(@Param("roles") List<String> roles);

    /**
     * 根据角色id查询有权限的组织id
     *
     * @param roles
     * @return
     */
    List<String> getPrivateOrgId(@Param("roles") List<String> roles);

    /**
     * 根据角色id查询有权限的员工id
     *
     * @param roles
     * @return
     */
    List<String> getPrivatePersonId(@Param("roles") List<String> roles);

    /**
     * 根据角色id查询有权限的经营主体id
     *
     * @param userId
     * @param href
     * @return
     */
    List<String> getPrivateBizunitId(@Param("userId") String userId, @Param("href") String href);

    /**
     * 根据角色id查询有权限的经营主体id
     *
     * @param userId
     * @param href
     * @return
     */
    List<String> getPrivateResourceId(@Param("userId") String userId, @Param("href") String href, @Param("code") String code);
}
