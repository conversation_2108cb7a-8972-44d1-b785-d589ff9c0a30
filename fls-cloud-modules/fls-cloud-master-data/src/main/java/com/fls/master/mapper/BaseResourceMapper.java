package com.fls.master.mapper;

import com.fls.master.entity.BaseResourceEntity;
import com.fls.master.pojo.vo.ResourceCodeVo;
import com.github.yulichang.base.MPJBaseMapper;
import org.apache.ibatis.annotations.Param;

/**
* 资源表-mapper
*
* <AUTHOR>
* @since  2024-11-12
*/
public interface BaseResourceMapper extends MPJBaseMapper<BaseResourceEntity> {

    /**
     * 查询资源编码信息
     * @param code 资源编码
     * @return 资源编码VO
     */
    ResourceCodeVo queryResCode(@Param("code") String code);

    /**
     * 通过id查询资源编码信息
     * @param idRes 资源id
     * @return 资源编码信息
     */
    ResourceCodeVo queryResCodeById(@Param("idRes") String idRes);
}
