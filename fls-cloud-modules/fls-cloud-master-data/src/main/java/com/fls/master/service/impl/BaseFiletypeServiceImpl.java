package com.fls.master.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fls.master.mapper.BaseFiletypeMapper;
import com.fls.master.entity.BaseFiletype;
import com.fls.master.service.BaseFiletypeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 文件类型表(BaseFiletype)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-11-05 15:30:44
 */
@Service
@Slf4j
public class BaseFiletypeServiceImpl extends ServiceImpl<BaseFiletypeMapper, BaseFiletype> implements BaseFiletypeService {
}
