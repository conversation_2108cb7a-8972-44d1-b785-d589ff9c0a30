package com.fls.master.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 职务表(BaseJob)表实体类
 *
 * <AUTHOR>
 * @since 2024-10-31 17:30:22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_base_job")
public class BaseJob implements Serializable {

    private static final long serialVersionUID = 1L;

     //主键
    @TableId(value = "id_job", type = IdType.ASSIGN_UUID)
    private String idJob;

     //职务类别
    private String idJobtype;

     //编码
    private String code;

     //名称
    private String name;

     //概要/描述
    private String memo;

     //学历要求
    private String reqEducation;

     //性别要求
    private String reqSex;

     //年龄要求
    private String reqAge;

     //工作经验要求
    private String reqExperience;

     //最低工作年限
    private String reqWorklimit;

     //专业背景要求
    private String reqProskills;

     //其他要求
    private String reqOther;

     //NC职务pk值
    private String pkJob;

     //状态，1=未启用，2=已启用，3=已停用，参见basestatus
    private String status;

     //创建时间
    private LocalDateTime createTime;

     //创建人
    private String creator;

     //时间戳
    private LocalDateTime ts;

     //是否删除，默认0，参见yesorno
    private String deleteFlag;

     //iHR职务id
    private String idIhr;

}

