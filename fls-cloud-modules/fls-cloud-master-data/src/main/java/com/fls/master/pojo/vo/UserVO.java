package com.fls.master.pojo.vo;

import lombok.Data;

/**
 * UserVo
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@Data
public class UserVO {

    /**
     * 主键
     */
    private String idUser;

    /**
     * 用户名
     */
    private String code;

    /**
     * 昵称
     */
    private String name;

    /**
     * 组织id
     */
    private String idOrg;

    /**
     * 经营主体id
     */
    private String idBizunit;

    /**
     * 角色id
     */
    private String idRole;

    /**
     * 角色编码
     */
    private String roleCode;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 身份类型：0=员工，1=客户，2=供应商，3=审计，4=外部系统，5=开发者，6=合作伙伴，默认0，参见identity_type
     */
    private String identityType;

    /**
     * 身份id
     */
    private String idIdentity;

    /**
     * NC用户pk值
     */
    private String pkUser;

    /**
     * NC身份pk值
     */
    private String pkBaseDoc;
}
