package com.fls.master.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * AddressVO
 *
 * <AUTHOR>
 */
@Data
public class AddressVO {

    @Schema(description = "主键")
    private String idAddress;

    /**
     * NC地址簿主键
     */
    @Schema(description = "NC地址簿主键")
    private String pkAddress;

    /**
     * 国家地区
     */
    @Schema(description = "国家地区id")
    private String idCountry;

    /**
     * 省份
     */
    @Schema(description = "省份id")
    private String idProvince;

    /**
     * 城市
     */
    @Schema(description = "城市id")
    private String idCity;

    /**
     * 县区
     */
    @Schema(description = "县区")
    private String idVsection;

    /**
     * 地址详细
     */
    @Schema(description = "地址详细")
    private String detail;

    /**
     * 地址MD5值
     */
    @Schema(description = "地址MD5值")
    private String addressMd5;

    /**
     * 地址类型
     */
    @Schema(description = "地址类型")
    private String addressType;

    /**
     * 启用状态：1=未启用，2=已启用，3=已停用，默认1，参见basestatus
     */
    @Schema(description = "启用状态：1=未启用，2=已启用，3=已停用")
    private String status;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private String creator;

    /**
     * 失效时间
     */
    @Schema(description = "失效时间")
    private LocalDateTime disableTime;


    /**
     * 纬度，百度bd09ll坐标系
     */
    @Schema(description = "纬度，百度bd09ll坐标系")
    private String lat;

    /**
     * 经度，百度bd09ll坐标系
     */
    @Schema(description = "经度，百度bd09ll坐标系")
    private String lng;

    /**
     * 邮政编码
     */
    @Schema(description = "邮政编码")
    private String zipCode;

    /**
     * 名称
     */
    @Schema(description = "名称")
    private String name;

    /**
     * 地址全称
     */
    @Schema(description = "地址全称")
    private String fullName;

    /**
     * 原租赁系统地址ID
     */
    @Schema(description = "原租赁系统地址ID")
    private String oldSysid;

    /**
     * 百度地图县区编码
     */
    @Schema(description = "百度地图县区编码")
    private String districtCode;

    /**
     * 行政区划名
     */
    @Schema(description = "行政区划名")
    private String regionName;

    /**
     * 地点编码
     */
    @Schema(description = "地点编码")
    private String code;

    /**
     * 纬度-高德
     */
    @Schema(description = "纬度-高德")
    private String latGaode;

    /**
     * 经度-高德
     */
    @Schema(description = "经度-高德")
    private String lngGaode;

    @Schema(description = "省份名称")
    private String provinceName;

    @Schema(description = "城市名称")
    private String cityName;

    @Schema(description = "县区名称")
    private String vsectionName;
}
