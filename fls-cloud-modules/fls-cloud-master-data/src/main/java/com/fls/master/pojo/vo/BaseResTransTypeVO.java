package com.fls.master.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 资源交易类型Vo
 *
 * <AUTHOR>
 * @since 2025-06-21
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "资源交易类型表基础信息")
public class BaseResTransTypeVO {

	@Schema(description = "主键")
	private String idTransType;

	@Schema(description = "交易类型名称")
	private String transType;

	@Schema(description = "编码")
	private String code;
}
