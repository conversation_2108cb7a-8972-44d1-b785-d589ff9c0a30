package com.fls.master.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * InventoryVO
 *
 * <AUTHOR>
 */
@Data
public class InventoryVO {

    @Schema(description = "主键")
    private String idInventory;

    //编码
    @Schema(description = "编码")
    private String code;

    //名称
    @Schema(description = "名称")
    private String name;

    //英文名称
    @Schema(description = "英文名称")
    private String ename;

    //主计量单位
    @Schema(description = "计量单位id")
    private String idMeasdoc;

    //物料分类
    @Schema(description = "物料分类id")
    private String idInvclass;

    //OEM码
    @Schema(description = "OEM码")
    private String spec;

    //机型
    @Schema(description = "机型")
    private String model;

    //标签机型
    @Schema(description = "标签机型")
    private String modelLabel;

    //基本参数
    @Schema(description = "基本参数")
    private String baseParam;

    //技术参数
    @Schema(description = "技术参数")
    private String techParam;

    //技术推荐
    @Schema(description = "技术推荐")
    private String techMemo;

    //采购推荐
    @Schema(description = "采购推荐")
    private String purchaseMemo;

    //供应商编码
    @Schema(description = "供应商编码")
    private String supplierCode;

    //供应商存货编码
    @Schema(description = "供应商存货编码")
    private String supplierInvcode;

    //供货单位所在地
    @Schema(description = "供货单位所在地")
    private String placeOfOrgin;

    //存货属性 01=配件，02=叉车，03=二手叉车，04=电动车，05=其他成型机械，06=二手成型设备，07=手拉车，08=属具，09=电池组，10=轮胎，11=货叉，12=仓储设备 参见inv_type
    @Schema(description = "存货属性 01=配件，02=叉车，03=二手叉车，04=电动车，05=其他成型机械，06=二手成型设备，07=手拉车，08=属具，09=电池组，10=轮胎，11=货叉，12=仓储设备")
    private String invType;

    //配件属性 01=原厂件，02=拆车件，03=副厂件，04=代用件，05=进口件，06=台湾件，07=开发件，09=OEM配套件 参见parts_type
    @Schema(description = "配件属性 01=原厂件，02=拆车件，03=副厂件，04=代用件，05=进口件，06=台湾件，07=开发件，09=OEM配套件")
    private String partsType;

    //铺货属性 01=铺货，02=非铺货 参见distribution_type
    @Schema(description = "铺货属性 01=铺货，02=非铺货")
    private String distributionType;

    //是否内销 0=否，1=是 参见yesorno
    @Schema(description = "是否内销 0=否，1=是 ")
    private String saleFlag;

    //是否外销 0=否，1=是 参见yesorno
    @Schema(description = "是否外销 0=否，1=是")
    private String exsaleFlag;

    //无税成本（最新成本）
    @Schema(description = "无税成本（最新成本）")
    private BigDecimal cost;

    //含税成本
    @Schema(description = "含税成本")
    private BigDecimal taxcost;

    //其他费用
    @Schema(description = "其他费用")
    private BigDecimal outlay;

    //无税调拨价
    @Schema(description = "无税调拨价")
    private BigDecimal tranprice;

    //含税调拨价
    @Schema(description = "含税调拨价")
    private BigDecimal taxtranprice;

    //销售指导价
    @Schema(description = "销售指导价")
    private BigDecimal guideprice;

    //外贸系数
    @Schema(description = "外贸系数")
    private BigDecimal exrate;

    //外贸网上价
    @Schema(description = "外贸网上价")
    private BigDecimal exsaleprice;

    //叉车车型
    @Schema(description = "叉车车型id")
    private String idForkliftmodels;

    //品牌
    @Schema(description = "品牌id")
    private String idBrand;

    //吨位
    @Schema(description = "吨位id")
    private String idTonnage;

    //新旧 J=二手车，X=新车 参见neworold
    @Schema(description = "新旧 J=二手车，X=新车")
    private String newDegree;

    //整车车号
    @Schema(description = "整车车号")
    private String carNum;

    //资产编号
    @Schema(description = "资产编号")
    private String assetCode;

    //整车配置说明
    @Schema(description = "整车配置说明")
    private String carSets;

    //对应U8编码
    @Schema(description = "对应U8编码")
    private String u8Code;

    //采购员
    @Schema(description = "采购员id")
    private String idPurman;

    //技术员
    @Schema(description = "技术员id")
    private String idTecman;

    //仓管员
    @Schema(description = "仓管员id")
    private String idStman;

    //NC物料PK值
    @Schema(description = "NC物料PK值")
    private String pkMaterial;

    //启用状态：1=未启用，2=已启用，3=已停用，默认1，参见basestatus
    @Schema(description = "启用状态：1=未启用，2=已启用，3=已停用")
    private String status;

    //创建时间
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    //创建人
    @Schema(description = "创建人")
    private String creator;

    //失效时间
    @Schema(description = "失效时间")
    private LocalDateTime disableTime;

//    翻译字段
    @Schema(description = "物料分类名称")
    private String invclassName;

    @Schema(description = "主计量单位名称")
    private String measdocName;

    @Schema(description = "叉车车型名称")
    private String forkliftmodelsName;

    @Schema(description = "品牌名称")
    private String brandName;

    @Schema(description = "吨位名称")
    private String tonnageName;

    @Schema(description = "采购员名称")
    private String purmanName;

    @Schema(description = "技术员名称")
    private String tecmanName;

    @Schema(description = "仓管员名称")
    private String stmanName;
}
