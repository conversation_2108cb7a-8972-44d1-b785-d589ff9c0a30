package com.fls.master.controller;

import com.fls.common.core.domain.ResponseData;
import com.fls.common.core.validate.QueryGroup;
import com.fls.master.pojo.query.DepartmentQuery;
import com.fls.master.pojo.query.OrgQuery;
import com.fls.master.pojo.query.PersonCodeQuery;
import com.fls.master.pojo.query.PersonQuery;
import com.fls.master.pojo.query.PostQuery;
import com.fls.master.pojo.query.StatusQuery;
import com.fls.master.pojo.query.UserRoleQuery;
import com.fls.master.pojo.query.WhposQuery;
import com.fls.master.pojo.vo.DepartmentVO;
import com.fls.master.pojo.vo.JobVO;
import com.fls.master.pojo.vo.OrgVO;
import com.fls.master.pojo.vo.PersonVO;
import com.fls.master.pojo.vo.PostVO;
import com.fls.master.pojo.vo.UserVO;
import com.fls.master.pojo.vo.WhposVO;
import com.fls.master.service.OrgStructManageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 组织架构管理
 *
 * <AUTHOR>
 */

@Tag(name = "组织架构管理")
@Slf4j
@RestController
@RequestMapping("/org-struct-manage")
@RequiredArgsConstructor
public class OrgStructManageController {

    private final OrgStructManageService orgStructManage;

    @Operation(summary = "全量组织列表查询")
    @PostMapping("/org/public/list")
    public ResponseData<List<OrgVO>> orgPublicList(@RequestBody OrgQuery query) {
        return ResponseData.ok(orgStructManage.orgPublicList(query));
    }


    @Operation(summary = "查询有权限的组织列表")
    @PostMapping("/org/private/list")
    public ResponseData<List<OrgVO>> orgPrivateList(@Validated(value = QueryGroup.class) @RequestBody OrgQuery query) {
        return ResponseData.ok(orgStructManage.orgPrivateList(query));
    }


    @Operation(summary = "全量部门列表查询")
    @PostMapping("/department/public/list")
    public ResponseData<List<DepartmentVO>> departmentPublicList(@RequestBody DepartmentQuery query) {
        return ResponseData.ok(orgStructManage.departmentPublicList(query));
    }


    @Operation(summary = "权限部门列表查询")
    @PostMapping("/department/private/list")
    public ResponseData<List<DepartmentVO>> departmentPrivateList(@Validated(value = QueryGroup.class) @RequestBody DepartmentQuery query) {
        return ResponseData.ok(orgStructManage.departmentPrivateList(query));
    }


    @Operation(summary = "全量岗位列表查询")
    @PostMapping("/post/public/list")
    public ResponseData<List<PostVO>> postPublicList(@RequestBody PostQuery query) {
        return ResponseData.ok(orgStructManage.postPublicList(query));
    }

    @Operation(summary = "全量职务列表查询")
    @PostMapping("/job/public/list")
    public ResponseData<List<JobVO>> jobPublicList(@RequestBody StatusQuery query) {
        return ResponseData.ok(orgStructManage.jobPublicList(query));
    }


    @Operation(summary = "全量员工列表查询")
    @PostMapping("/person/public/list")
    public ResponseData<List<PersonVO>> personPublicList(@RequestBody PersonQuery query) {
        return ResponseData.ok(orgStructManage.personPublicList(query));
    }


    @Operation(summary = "权限员工列表查询")
    @PostMapping("/person/private/list")
    public ResponseData<List<PersonVO>> personPrivateList(@Validated(value = QueryGroup.class) @RequestBody PersonQuery query) {
        return ResponseData.ok(orgStructManage.personPrivateList(query));
    }

    @Operation(summary = "岗位员工列表查询")
    @PostMapping("/person/list-by-code")
    public ResponseData<List<PersonVO>> personsByCode(@RequestBody PersonCodeQuery personCodeQuery) {
        return ResponseData.ok(orgStructManage.personListByCode(personCodeQuery));
    }

    @Operation(summary = "角色员工列表查询")
    @PostMapping("/user/list-by-role")
    public ResponseData<List<UserVO>> personsByRole(@RequestBody UserRoleQuery roleQuery) {
        return ResponseData.ok(orgStructManage.userListByRole(roleQuery));
    }

    @Operation(summary = "货位列表查询")
    @PostMapping("/whpos/list")
    public ResponseData<List<WhposVO>> whposList(@Validated @RequestBody WhposQuery whposQuery) {
        return ResponseData.ok(orgStructManage.whposList(whposQuery));
    }
}
