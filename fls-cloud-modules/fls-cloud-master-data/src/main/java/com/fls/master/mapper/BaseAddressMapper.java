package com.fls.master.mapper;

import com.fls.master.entity.BaseAddressEntity;
import com.fls.master.pojo.query.InnerAddressQuery;
import com.fls.master.pojo.vo.InnerAddressVo;
import com.github.yulichang.base.MPJBaseMapper;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
* 客户基本档案-mapper
*
* <AUTHOR>
* @since  2024-11-06
*/
public interface BaseAddressMapper extends MPJBaseMapper<BaseAddressEntity> {

    /**
     * 查询内部收货地址列表
     * @param query 查询参数
     * @return 内部收货地址列表
     */
    List<InnerAddressVo> queryInnerAddress(@Param("query") InnerAddressQuery query);
}
