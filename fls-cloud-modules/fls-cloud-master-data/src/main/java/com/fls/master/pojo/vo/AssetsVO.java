package com.fls.master.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * AssetsVO
 *
 * <AUTHOR>
 */
@Data
public class AssetsVO {

    @Schema(description = "主键")
    private String idAssets;

    @Schema(description = "资产编码")
    private String laCode;

    @Schema(description = "租赁资产类型")
    private String idAssetType;

    @Schema(description = "地址")
    private String idAddress;

    @Schema(description = "别名（资产标识名）")
    private String proEname;

    @Schema(description = "资产名称")
    private String laName;

    @Schema(description = "制造编号")
    private String manufacture;

    @Schema(description = "租赁系统资产ID")
    private String assetsid;

    @Schema(description = "车型id")
    private String idModelnumber;

    @Schema(description = "车型名称")
    private String modelnumberName;

    @Schema(description = "供应商车型")
    private String idForkliftmodelsSupplier;

    @Schema(description = "新旧 J=二手车，X=新车")
    private String neworold;

    @Schema(description = "资产状态 1=正常，2=待检，3=待修，4=待售, 5=再制造")
    private String assetStatus;

    @Schema(description = "车牌表")
    private String idLicenseplate;

    @Schema(description = "车牌号码")
    private String licenseNum;

    @Schema(description = "所有权公司名")
    private String idOrgOwnerName;

    @Schema(description = "所有权公司")
    private String idOrgOwner;

    @Schema(description = "资产图片")
    private String idAssetsImg;

    @Schema(description = "品牌")
    private String brandName;

    @Schema(description = "品牌id")
    private String idBrand;

    @Schema(description = "租赁资产类型")
    private String assetTypeName;

    @Schema(description = "附件表id")
    private String idLink;

    @Schema(description = "发动机号")
    private String engineCode;

    @Schema(description = "地址名称")
    private String addressName;

    @Schema(description = "生产日期")
    private LocalDate productDate;

    @Schema(description = "购入日期")
    private LocalDate buyDate;

    @Schema(description = "工作时长")
    private BigDecimal workHour;

    @Schema(description = "技术码")
    private String technicalCode;

    @Schema(description = "重量")
    private BigDecimal weight;

}
