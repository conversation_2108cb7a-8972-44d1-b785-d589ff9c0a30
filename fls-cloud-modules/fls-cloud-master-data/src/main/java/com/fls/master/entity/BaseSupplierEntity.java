package com.fls.master.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 供应商基本档案表
 *
 * <AUTHOR>
 * @since 2024-11-07
 */

@Data
@TableName("t_supplier_baseinfo")
public class BaseSupplierEntity extends BaseStatusEntity {
    /**
     * 主键
     */
    @TableId(value = "id_supplier", type = IdType.ASSIGN_UUID)
    private String idSupplier;

    /**
     * 编码
     */
    private String code;

    /**
     * 名称
     */
    private String name;

    /**
     * 简称
     */
    private String shortname;

    /**
     * 所属组织
     */
    private String idOrg;

    /**
     * 供应商分类
     */
    private String idSupplierclass;

    /**
     * 上级供应商
     */
    private String idParentsupplier;

    /**
     * 纳税人登记号
     */
    private String taxpayerid;

    /**
     * 法人
     */
    private String legalbody;

    /**
     * 公司地址
     */
    private String address;

    /**
     * 电话
     */
    private String tel;

    /**
     * 是否客户 0=否，1=是 参见yesorno
     */
    private String customerFlag;

    /**
     * 对应客户
     */
    private String idCustomer;

    /**
     * 供应商属性 0=外部单位，1=内部单位 参见supplier_prop
     */
    private String supplierProp;

    /**
     * 对应组织
     */
    private String idFinanceorg;

    /**
     * 供应商类型 01=工厂，02=同行，03=小作坊 参见supplier_type
     */
    private String supplierType;

    /**
     * 供应商结算方式 01=月结，02=现金，03=代收货款，04=款到发货，05=预付订金，06=货到付款，07=分期付款，08=现金支票，09=约定分期付款 参见supplier_settletype
     */
    private String settleType;

    /**
     * 备注
     */
    private String memo;

    /**
     * NC供应商档案主键
     */
    private String pkSupplier;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 失效时间
     */
    private LocalDateTime disableTime;

    /**
     * 时间戳
     */
    private LocalDateTime ts;

    /**
     * 租赁外协服务标识：0=否，1=是，参见yesorno
     */
    private String leaseAssistFlag;

    /**
     * 物流供应商标识，0=否，1=是
     */
    private String logisticsFlag;

    /**
     * 固定租期标识
     */
    private String rentLongFixedFlag;

    /**
     * 固定租期期数
     */
    private Integer rentLong;

}
