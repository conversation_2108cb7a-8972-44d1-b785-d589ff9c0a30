package com.fls.master.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
* 工作班组成员表基础信息
*
* <AUTHOR>
* @since  2024-11-12
*/
@Data
@Schema(description = "工作班组成员表基础信息")
public class BaseWorkTeamMemberVO extends ArchiveVO {

	@Schema(description = "主键")
	private String idWorkteamMember;

	@Schema(description = "工作班组")
	private String idWorkteam;

	@Schema(description = "班组名称")
	private String workteamName;

	@Schema(description = "班组成员")
	private String idMember;

    @Schema(description = "成员名称")
    private String name;

	@Schema(description = "班组成员对应虚拟地点")
	private String idAddress;

    @Schema(description = "地址详情")
    private String detail;

	@Schema(description = "班组成员对应货位")
	private String idWhpos;

	@Schema(description = "班组成员默认归还仓库")
	private String idWhback;

	@Schema(description = "NC档案主键")
	private String ncPk;

    @Schema(description = "启用状态：1=未启用，2=已启用，3=已停用，默认1，参见basestatus")
    private String status;

	@Schema(description = "用户id")
	private String userId;
}
