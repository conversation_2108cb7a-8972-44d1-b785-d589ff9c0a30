package com.fls.master.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
* 字典表基础信息
*
* <AUTHOR>
* @since  2024-11-12
*/
@Data
@Schema(description = "字典表基础信息")
public class BaseDictVO extends ArchiveVO {

	@Schema(description = "主键")
	private String idDict;

	@Schema(description = "枚举类型")
	private String dictType;

	@Schema(description = "编码")
	private String dictCode;

	@Schema(description = "枚举名")
	private String dictName;

	@Schema(description = "索引号")
	private Integer dictIndex;

	@Schema(description = "说明")
	private String description;

	@Schema(description = "父级id")
	private String idParentdict;

	@Schema(description = "NC档案主键")
	private String ncpk;

    @Schema(description = "启用状态：1=未启用，2=已启用，3=已停用，默认1，参见basestatus")
    private String status;
}
