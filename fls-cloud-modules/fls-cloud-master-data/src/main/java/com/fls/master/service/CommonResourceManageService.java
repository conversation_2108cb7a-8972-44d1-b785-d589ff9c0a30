package com.fls.master.service;

import com.fls.master.pojo.query.ArchiveQuery;
import com.fls.master.pojo.query.BillTypeQuery;
import com.fls.master.pojo.query.InvclassQuery;
import com.fls.master.pojo.query.ProductQuery;
import com.fls.master.pojo.query.ResTranTypeQuery;
import com.fls.master.pojo.query.StatusQuery;
import com.fls.master.pojo.vo.BaseMeasDocVO;
import com.fls.master.pojo.vo.BillTypeVO;
import com.fls.master.pojo.vo.BizTypeVO;
import com.fls.master.pojo.vo.BrandVO;
import com.fls.master.pojo.vo.FaCategoryVO;
import com.fls.master.pojo.vo.FiletypeVO;
import com.fls.master.pojo.vo.ForkliftModelsVO;
import com.fls.master.pojo.vo.InvClassVO;
import com.fls.master.pojo.vo.LeaseAssetTypeVO;
import com.fls.master.pojo.vo.ProductVO;
import com.fls.master.pojo.vo.ResFiletypeVO;
import com.fls.master.pojo.vo.ResTranTypeVO;
import com.fls.master.pojo.vo.TransModeVO;

import java.util.List;

/**
 * CommonResourceManageService
 *
 * <AUTHOR>
 */
public interface CommonResourceManageService {

    List<TransModeVO> rcPublicTransMode(StatusQuery query);

    List<ProductVO> rcPublicProduct(ProductQuery query);

    List<InvClassVO> rcPublicInvclass(InvclassQuery query);

    List<BrandVO> rcPublicBrand(StatusQuery query);

    List<ForkliftModelsVO> rcPublicForkliftModels(StatusQuery query);

    List<BaseMeasDocVO> measDocList(ArchiveQuery query);

    List<BillTypeVO> billPublicBillType(BillTypeQuery query);

    List<BizTypeVO> bizPublicBizType(StatusQuery query);

    List<ResTranTypeVO> resTranPublicResTranType(ResTranTypeQuery query);

    List<ResFiletypeVO> resFilePublicResFiletype(StatusQuery query);

    List<FiletypeVO> filePublicFiletype(StatusQuery query);

    List<LeaseAssetTypeVO> assetPublicLeaseAssetType(StatusQuery query);

    List<FaCategoryVO> assetPublicFaCategory(StatusQuery query);
}
