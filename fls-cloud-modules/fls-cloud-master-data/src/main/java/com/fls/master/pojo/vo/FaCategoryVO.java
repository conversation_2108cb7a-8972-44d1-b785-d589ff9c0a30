package com.fls.master.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * FaCategoryVO
 *
 * <AUTHOR>
 */
@Data
public class FaCategoryVO {

    @Schema(description = "主键")
    private String idFacategory;

    @Schema(description = "编码")
    private String code;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "级次")
    private Integer grade;

    @Schema(description = "是否末级 0=否，1=是")
    private String endFlag;

    @Schema(description = "NC资产类别主键")
    private String pkCategory;

    @Schema(description = "折旧方法")
    private String idDepmethod;

    @Schema(description = "父级资产类别")
    private String idParentcate;

    @Schema(description = "交易类型")
    private String idTrantype;

    @Schema(description = "净残值率(%)")
    private BigDecimal netResidualRate;

    @Schema(description = "使用月限")
    private Integer serviceMonth;

    @Schema(description = "计量单位")
    private String meaUnit;

    @Schema(description = "启用状态 1=未启用，2=已启用，3=已停用")
    private String status;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "创建人")
    private String creator;

    @Schema(description = "失效时间")
    private LocalDateTime disableTime;

    @Schema(description = "交易类型名称")
    private String trantypeName;

}
