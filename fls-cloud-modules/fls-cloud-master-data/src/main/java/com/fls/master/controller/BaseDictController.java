package com.fls.master.controller;

import com.fls.common.core.domain.ResponseData;
import com.fls.common.core.validate.QueryGroup;
import com.fls.master.pojo.query.BaseDictQuery;
import com.fls.master.pojo.vo.BaseDictVO;
import com.fls.master.service.BaseDictService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 字典表-web层服务
 *
 * <AUTHOR>
 * @since 2024-11-12
 */
@RestController
@Slf4j
@RequestMapping("/dict")
@Tag(name = "字典表-web层服务")
@RequiredArgsConstructor
public class BaseDictController {
    private final BaseDictService baseDictService;

    @Operation(summary = "全量字典表查询")
    @PostMapping("/list")
    public ResponseData<List<BaseDictVO>> list(@RequestBody BaseDictQuery query) {
        return ResponseData.ok(baseDictService.queryAll(query));
    }

    @Operation(summary = "字典值查询")
    @PostMapping("/value")
    public ResponseData<List<BaseDictVO>> authList(@RequestBody BaseDictQuery query) {
        return ResponseData.ok(baseDictService.queryDictValue(query));
    }
}
