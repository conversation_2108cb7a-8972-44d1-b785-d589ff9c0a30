package com.fls.master.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 资源资料分类表(BaseResdoccate)表实体类
 *
 * <AUTHOR>
 * @since 2024-11-12 08:45:54
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_base_resdoccate")
public class BaseResdoccate implements Serializable {

    private static final long serialVersionUID = 1L;

     //主键
    @TableId(value = "id_resdoccate", type = IdType.ASSIGN_UUID)
    private String idResdoccate;

     //资源主键
    private String idResource;

     //资料分类主键
    private String idDoccate;

     //开启引用标识，0=否，1，是，默认0，参见yesorno
    private String quoteFlag;

     //状态，1=未启用，2=已启用，3=已停用，参见basestatus
    private String status;

     //创建时间
    private LocalDateTime createTime;

     //创建人
    private String creator;

     //作废时间
    private LocalDateTime invalidTime;

     //作废人
    private String invalider;

     //时间戳
    private LocalDateTime ts;

     //是否删除，默认0，参见yesorno
    private String deleteFlag;

     //资源编码
    private String resourceCode;

}
