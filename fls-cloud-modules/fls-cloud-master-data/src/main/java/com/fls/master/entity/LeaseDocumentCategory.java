package com.fls.master.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * LA资料分类(LeaseDocumentCategory)表实体类
 *
 * <AUTHOR>
 * @since 2024-11-12 09:42:08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_lease_document_category")
public class LeaseDocumentCategory implements Serializable {

    private static final long serialVersionUID = 1L;


    @TableId(value = "id_doc_cat", type = IdType.ASSIGN_UUID)
    private String idDocCat;

     //编码
    private String code;

     //名称
    private String dcName;

     //资产类型
    private String idDocType;

     //资料引用所属资源
    private String idResource;

     //是否启用资料维护申请
    private Integer applyFlag;

     //是否启用审批
    private Integer needprocFlag;

     //资料维护申请资源（默认固定）
    private String idResourceApply;

     //资料维护申请资源交易类型
    private String idResTrantypeApply;

     //是否启用引用功能
    private Integer quoteFlag;

     //是否启用有效时间
    private String activetimeFlag;

     //备注
    private String dcRemark;

     //显示顺序
    private Integer sort;

     //创建时间
    private LocalDateTime createTime;

     //失效时间
    private LocalDateTime disableTime;

     //创建人
    private String creator;

     //时间戳
    private LocalDateTime ts;

     //1：删除
    private String deleteFlag;

     //1：未启用，2：已启用，3：已停用
    private String status;

     //菜单id
    private String idAuth;

     //菜单状态
    private String authStatus;
}
