package com.fls.master.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * AssetCategoryVO
 *
 * <AUTHOR>
 */
@Data
public class LeaseAssetTypeVO {

    @Schema(description = "主键")
    private String idAssetType;

    @Schema(description = "编码")
    private String code;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "产品分类名称")
    private String proename;

    @Schema(description = "资产分类")
    private String idAssetClass;

    @Schema(description = "启用状态：1=未启用，2=已启用，3=已停用")
    private String status;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "创建人")
    private String creator;

    @Schema(description = "失效时间")
    private LocalDateTime disableTime;

    @Schema(description = "可捆绑大件")
    private String parts;

    @Schema(description = "可装载大件")
    private String partsOn;

    @Schema(description = "包含参数")
    private String params;

    @Schema(description = "标签数量 0为不需要标签")
    private Integer barcodeNum;

    @Schema(description = "扫码控制，条码是否必须扫")
    private String scanBarcodeFlag;

    @Schema(description = "是否需要上牌")
    private String licenseFlag;

    @Schema(description = "是否需要融资")
    private String financingFlag;

    @Schema(description = "是否启用制造编号")
    private String manufactureFlag;

    @Schema(description = "是否需要保养")
    private String maintainFlag;

    @Schema(description = "燃油类型")
    private String fuelType;

    @Schema(description = "动力类型")
    private String powerType;

    @Schema(description = "采购员")
    private String idPurchasePerson;

    @Schema(description = "资产财务报表统计分类")
    private String assetsFinanceclass;

    @Schema(description = "维修积分")
    private BigDecimal maintenancePoints;

    @Schema(description = "关注等级：0最低，1初级，默认1，暂分0和1，用于物管中心统计分类用")
    private String attentionLevel;

    @Schema(description = "维保基金分类")
    private String maintenanceFundType;

    @Schema(description = "维保基金系数")
    private BigDecimal maintenanceFundMoney;

    @Schema(description = "资产使用分类")
    private String assetsUseClass;

    @Schema(description = "在库资产保养周期，单位：天")
    private Integer storeMaintenanceInterval;

    @Schema(description = "资产分类名称")
    private String assetClassName;
}
