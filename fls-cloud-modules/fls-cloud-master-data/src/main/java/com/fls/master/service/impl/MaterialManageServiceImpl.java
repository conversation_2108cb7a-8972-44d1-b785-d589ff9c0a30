package com.fls.master.service.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fls.common.core.constant.CommonConstants;
import com.fls.master.entity.BaseBrand;
import com.fls.master.entity.BaseForkliftModels;
import com.fls.master.entity.BaseInvClass;
import com.fls.master.entity.BaseInventory;
import com.fls.master.entity.BaseMeasdoc;
import com.fls.master.entity.BasePerson;
import com.fls.master.entity.BaseTonnage;
import com.fls.master.pojo.query.MaterialQuery;
import com.fls.master.pojo.vo.InventoryVO;
import com.fls.master.pojo.vo.PageResult;
import com.fls.master.service.BaseInventoryService;
import com.fls.master.service.MaterialManageService;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * InventoryManageServiceImpl
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
@SuppressWarnings("unchecked")
public class MaterialManageServiceImpl implements MaterialManageService {

    private final BaseInventoryService inventory;


    @Override
    public PageResult<InventoryVO> materialPrivatePage(MaterialQuery query) {
        // 暂无权限物料列表,返回空数组
        return new PageResult(query.getPageInfo(), ListUtil.empty());
    }

    @Override
    public PageResult<InventoryVO> materialPublicPage(MaterialQuery query) {
        MPJLambdaWrapper<BaseInventory> wrapper = new MPJLambdaWrapper<>();
        wrapper.selectAll(BaseInventory.class)
            .selectAs(BaseInvClass::getName, InventoryVO::getInvclassName)
            .selectAs(BaseMeasdoc::getName, InventoryVO::getMeasdocName)
            .selectAs(BaseForkliftModels::getName, InventoryVO::getForkliftmodelsName)
            .selectAs(BaseBrand::getName, InventoryVO::getBrandName)
            .selectAs(BaseTonnage::getName, InventoryVO::getTonnageName)
            .selectAs(BasePerson::getName, InventoryVO::getPurmanName)
            .selectAs(BasePerson::getName, InventoryVO::getTecmanName)
            .selectAs(BasePerson::getName, InventoryVO::getStmanName)
            .leftJoin(BaseInvClass.class, BaseInvClass::getIdInvclass, BaseInventory::getIdInvclass)
            .leftJoin(BaseMeasdoc.class, BaseMeasdoc::getIdMeasdoc, BaseInventory::getIdMeasdoc)
            .leftJoin(BaseForkliftModels.class, BaseForkliftModels::getIdForkliftmodels, BaseInventory::getIdForkliftmodels)
            .leftJoin(BaseBrand.class, BaseBrand::getIdBrand, BaseInventory::getIdBrand)
            .leftJoin(BaseTonnage.class, BaseTonnage::getIdTonnage, BaseInventory::getIdTonnage)
            .leftJoin(BasePerson.class, BasePerson::getIdPerson, BaseInventory::getIdPurman)
            .leftJoin(BasePerson.class, BasePerson::getIdPerson, BaseInventory::getIdTecman)
            .leftJoin(BasePerson.class, BasePerson::getIdPerson, BaseInventory::getIdStman)
            .eq(BaseInventory::getDeleteFlag, CommonConstants.DELETE_FLAG_NOT_DELETED)
            .eq(Objects.nonNull(query.getStatus()), BaseInventory::getStatus, query.getStatus())
            .eq(query.getIsAccurateCode(),BaseInventory::getCode, query.getCode())
            .like(StrUtil.isNotBlank(query.getName()), BaseInventory::getName, query.getName())
            .like(StrUtil.isNotBlank(query.getModel()), BaseInventory::getModel, query.getModel())
            .like(StrUtil.isNotBlank(query.getSpec()), BaseInventory::getSpec, query.getSpec())
            .like(!query.getIsAccurateCode() && StrUtil.isNotBlank(query.getCode()), BaseInventory::getCode, query.getCode())
            .orderByDesc(BaseInventory::getTs);

        Page page = inventory.selectJoinListPage(query.getPageInfo(), InventoryVO.class, wrapper);
        return new PageResult(page, page.getRecords());
    }
}
