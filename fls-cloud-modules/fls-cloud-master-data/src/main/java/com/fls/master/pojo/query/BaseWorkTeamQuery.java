package com.fls.master.pojo.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 工作班组表查询
 *
 * <AUTHOR>
 * @since 2024-11-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "工作班组表查询")
public class BaseWorkTeamQuery extends ArchiveQuery {
    @Schema(description = "班组编码")
    private String code;

    @Schema(description = "班组名称")
    private String name;

    @Schema(description = "所属组织")
    private String idOrg;

    @Schema(description = "所属主体")
    private String idBizunit;

    @Schema(description = "所属部门")
    private String idDepartment;

    @Schema(description = "租赁服务标识：0=否，1=是，默认0，参见yesorno")
    private String rentalServiceFlag;

    @Schema(description = "商业维修标识：0=否，1=是，默认0，参见yesorno")
    private String commercialMaintenanceFlag;

    @Schema(description = "是否外协：0=否，1=是，默认0，参见yesorno")
    private String ocunitFlag;

}
