package com.fls.master.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * 更新资源流水号
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
@Data
public class UpdateResFlowDTO {

    @Schema(description = "资源编码")
    @NotBlank(message = "请求资源编码不能为空")
    private String code;

    @Schema(description = "流水号")
    @NotNull(message = "更新资源流水号不能为空")
    private Integer flowNum;
}
