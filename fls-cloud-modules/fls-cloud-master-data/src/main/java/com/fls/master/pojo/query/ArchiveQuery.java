package com.fls.master.pojo.query;

import com.fls.common.core.validate.QueryGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 基础查询
 *
 * <AUTHOR>
 * @since 2024-11-18
 */
@Data
public class ArchiveQuery {

    @Schema(description = "菜单权限href")
    @NotBlank(groups = QueryGroup.class, message = "菜单href不能为空")
    private String href;

    @Schema(description = "用户id")
    @NotBlank(groups = QueryGroup.class, message = "用户id不能为空")
    private String userId;

    @Schema(description = "状态")
    private String status = "2";
}
