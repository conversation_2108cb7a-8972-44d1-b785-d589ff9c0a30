package com.fls.master.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * DepartmentVO
 *
 * <AUTHOR>
 */
@Data
public class DepartmentVO {

    @Schema(description = "主键")
    private String idDepartment;

//    @Schema(description = "多版本部门主键")
//    private String idDeptversions;

    @Schema(description = "编码")
    private String code;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "部门类型：0=普通部门，1=虚拟部门，默认0")
    private String deptType;

    @Schema(description = "所属组织")
    private String idOrg;

    @Schema(description = "所属集团")
    private String idGroup;

    @Schema(description = "上级部门")
    private String idParentdept;

    @Schema(description = "NC部门PK")
    private String pkDept;

    @Schema(description = "成立日期 格式yyyy-mm-dd")
    private String buildDate;

    @Schema(description = "撤销日期 格式yyyy-mm-dd")
    private String abortDate;

    @Schema(description = "版本号 格式yyyymmdd")
    private String version;

    @Schema(description = "启用状态：1=未启用，2=已启用，3=已停用，默认1")
    private String status;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "失效时间")
    private LocalDateTime disableTime;

//    @Schema(description = "iHR部门id")
//    private String idIhr;

//    字段id翻译
    @Schema(description = "所属集团名称")
    private String groupName;

    @Schema(description = "所属集团名称")
    private String orgName;

}
