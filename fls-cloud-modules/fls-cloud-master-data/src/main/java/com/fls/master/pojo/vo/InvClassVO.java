package com.fls.master.pojo.vo;

import com.fls.master.entity.BaseInvClass;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.time.LocalDateTime;

/**
 * InvclassVO
 *
 * <AUTHOR>
 */
@Data
public class InvClassVO {

    @Schema(description = "主键")
    private String id;

    @Schema(description = "编码")
    private String code;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "级次")
    private Integer grade;

    @Schema(description = "是否末级 0=否，1=是")
    private String endFlag;

    @Schema(description = "父级分类")
    private String idParentclass;

    @Schema(description = "NC物料基本分类主键")
    private String pkMarbasclass;

    @Schema(description = "启用状态：1=未启用，2=已启用，3=已停用")
    private String status;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "失效时间")
    private LocalDateTime disableTime;

    public static InvClassVO of(BaseInvClass source) {
        InvClassVO target = new InvClassVO();
        BeanUtils.copyProperties(source, target);
        target.setId(source.getIdInvclass());
        return target;
    }
}
