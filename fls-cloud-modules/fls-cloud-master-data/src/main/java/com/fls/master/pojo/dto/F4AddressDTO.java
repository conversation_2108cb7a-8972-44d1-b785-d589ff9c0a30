package com.fls.master.pojo.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * F4AddressDTO
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class F4AddressDTO {

    private String address;

    private String assetId;

    private String carId;

    private Long lastReportTime;

    private String latitude;

    private String longitude;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime messageTime;

    private String terminalId;

}
