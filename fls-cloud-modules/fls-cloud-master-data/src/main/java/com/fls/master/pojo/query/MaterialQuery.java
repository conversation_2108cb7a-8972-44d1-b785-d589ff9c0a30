package com.fls.master.pojo.query;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

/**
 * MaterialQuery
 *
 * <AUTHOR>
 */
@Data
public class MaterialQuery extends ArchiveQuery {

    @Schema(description = "物料编码code")
    private String code;

    @Schema(description = "物料名称")
    private String name;

    @Schema(description = "机型")
    private String model;

    @Schema(description = "OEM码")
    private String spec;

    @Min(value = 1, message = "当前页不能小于1")
    @Schema(description = "当前页")
    private Long pageNo = 1L;

    @Schema(description = "分页大小")
    @Min(value = 1, message = "当前分页大小不能小于1")
    @Max(value = 5000, message = "分页大小最大不能超过5000")
    private Long pageSize = 10L;

    @Schema(description = "是否精确匹配code")
    private Boolean isAccurateCode = Boolean.FALSE;


    public Page getPageInfo() {
        return new Page<>(this.pageNo, this.pageSize);
    }

    public void setMaterialDetailQueryInfo(String code) {
        this.code = code;
        this.pageSize = 1L;
        this.isAccurateCode = Boolean.TRUE;
    }
}
