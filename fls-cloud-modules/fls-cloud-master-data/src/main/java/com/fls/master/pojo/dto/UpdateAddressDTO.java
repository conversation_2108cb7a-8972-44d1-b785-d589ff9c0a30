package com.fls.master.pojo.dto;

import cn.hutool.core.util.StrUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * UpdateAddressDTO
 *
 * <AUTHOR>
 */
@Data
public class UpdateAddressDTO {

    @Schema(description = "主键id")
    private String idAddress;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "状态")
    private String status;

    @Schema(description = "邮政编码")
    private String zipCode;

    @Schema(description = "地址类型")
    private String addressType;

    @Schema(description = "百度地图县区编码")
    private String districtCode;

    @Schema(description = "省份")
    private String idProvince;

    @Schema(description = "城市")
    private String idCity;

    @Schema(description = "县区")
    private String idVsection;

    @Schema(description = "地址详细")
    private String detail;

    @Schema(description = "纬度-高德")
    private String latGaode;

    @Schema(description = "经度-高德")
    private String lngGaode;


    public Boolean isUpdateLatOrLng() {
        if (StrUtil.isAllBlank(this.latGaode, this.lngGaode)) {
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    public Boolean isUpdateAddress() {
        if (StrUtil.isAllBlank(this.idAddress, this.name, this.idProvince, this.idCity, this.idVsection, this.detail)) {
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

}
