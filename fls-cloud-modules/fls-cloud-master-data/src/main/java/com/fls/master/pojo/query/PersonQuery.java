package com.fls.master.pojo.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * PersonQuery
 *
 * <AUTHOR>
 */
@Data
public class PersonQuery extends ArchiveQuery {

    @Schema(description = "所属组织")
    @NotBlank(message = "所属组织id不能为空")
    private String idOrg;

    @Schema(description = "岗位id")
    private String idPost;

    @Schema(description = "部门ID")
    private String idDepartment;

    @Schema(description = "工作班组id")
    private String idWorkteam;

    @Schema(description = "名称")
    private String name;
}
