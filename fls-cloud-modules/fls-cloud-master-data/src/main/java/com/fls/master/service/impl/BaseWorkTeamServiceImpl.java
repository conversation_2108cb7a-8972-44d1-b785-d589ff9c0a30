package com.fls.master.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.fls.common.core.constant.CommonConstants;
import com.fls.master.api.RemoteWorkTeamService;
import com.fls.master.api.model.WorkTeamBaseInfo;
import com.fls.master.entity.BaseAddressEntity;
import com.fls.master.entity.BaseBizunitEntity;
import com.fls.master.entity.BaseDepartment;
import com.fls.master.entity.BaseOrg;
import com.fls.master.entity.BasePerson;
import com.fls.master.entity.BaseUser;
import com.fls.master.entity.BaseWarehouseEntity;
import com.fls.master.entity.BaseWhposEntity;
import com.fls.master.entity.BaseWorkTeamEntity;
import com.fls.master.entity.BaseWorkTeamMemberEntity;
import com.fls.master.enums.LimitType;
import com.fls.master.mapper.BaseWarehouseMapper;
import com.fls.master.mapper.BaseWhposMapper;
import com.fls.master.mapper.BaseWorkTeamMapper;
import com.fls.master.mapper.BaseWorkTeamMemberMapper;
import com.fls.master.pojo.builer.WorkTeamWrapperBuilder;
import com.fls.master.pojo.query.ArchiveQuery;
import com.fls.master.pojo.query.BaseWorkTeamMemberQuery;
import com.fls.master.pojo.query.BaseWorkTeamQuery;
import com.fls.master.pojo.query.OwnerWorkTeamQuery;
import com.fls.master.pojo.vo.BaseWorkTeamDetailVO;
import com.fls.master.pojo.vo.BaseWorkTeamMemberVO;
import com.fls.master.pojo.vo.BaseWorkTeamVO;
import com.fls.master.service.BasePersonService;
import com.fls.master.service.BaseWorkTeamService;
import com.fls.master.service.PermissionService;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 工作班组表-service
 *
 * <AUTHOR>
 * @since 2024-11-12
 */
@Service
@Slf4j
@SuppressWarnings("unchecked")
public class BaseWorkTeamServiceImpl extends MPJBaseServiceImpl<BaseWorkTeamMapper, BaseWorkTeamEntity> implements BaseWorkTeamService {

    @Resource
    private PermissionService permissionService;

    @Resource
    private BaseWorkTeamMemberMapper baseWorkTeamMemberMapper;

    @Resource
    private BaseWarehouseMapper baseWarehouseMapper;

    @Resource
    private BaseWhposMapper baseWhposMapper;

    @Autowired
    private BasePersonService basePersonService;

    @DubboReference
    private RemoteWorkTeamService remoteWorkTeamService;

    @Override
    public List<BaseWorkTeamVO> queryAll(ArchiveQuery query) {
        BaseWorkTeamQuery tansQuery = (BaseWorkTeamQuery)query;
        MPJLambdaWrapper<BaseWorkTeamEntity> wrapper = WorkTeamWrapperBuilder.builder().withCommonConditions(tansQuery).withQueryParameters(tansQuery).build();
        return baseMapper.selectJoinList(BaseWorkTeamVO.class, wrapper);
    }

    @Override
    public List<BaseWorkTeamVO> queryAuthArchives(ArchiveQuery query) {
        BaseWorkTeamQuery tansQuery = (BaseWorkTeamQuery)query;
        List<String> privateArhIds = permissionService.getPrivateOrgId(query.getUserId(), query.getHref());
        if (CollectionUtil.isEmpty(privateArhIds)) {
            return Collections.emptyList();
        }
        MPJLambdaWrapper<BaseWorkTeamEntity> wrapper =
                WorkTeamWrapperBuilder.builder().withCommonConditions(tansQuery).withQueryParameters(tansQuery).withAuthorization(privateArhIds).build();
        return baseMapper.selectJoinList(BaseWorkTeamVO.class, wrapper);
    }

    @Override
    public BaseWorkTeamDetailVO queryArchiveDetail(String archiveId) {
        MPJLambdaWrapper<BaseWorkTeamEntity> wrapper = new MPJLambdaWrapper<>();
        wrapper.selectAll(BaseWorkTeamEntity.class)
                .selectAs(BaseOrg::getName, BaseWorkTeamDetailVO::getOrgName)
                .selectAs(BaseBizunitEntity::getName, BaseWorkTeamDetailVO::getBizName)
                .selectAs(BaseBizunitEntity::getIdWarehouseCmaint, BaseWorkTeamDetailVO::getIdMainWh)
                .selectAs(BaseDepartment::getName, BaseWorkTeamDetailVO::getDepName)
                .selectAs(BaseWarehouseEntity::getName, BaseWorkTeamDetailVO::getWarehouseName)
                .leftJoin(BaseOrg.class, BaseOrg::getIdOrg, BaseWorkTeamEntity::getIdOrg)
                .selectAs(BaseWhposEntity::getName, BaseWorkTeamDetailVO::getWhposName)
                .leftJoin(BaseBizunitEntity.class, BaseBizunitEntity::getIdBizunit, BaseWorkTeamEntity::getIdBizunit)
                .leftJoin(BaseDepartment.class, BaseDepartment::getIdDepartment, BaseWorkTeamEntity::getIdDepartment)
                .leftJoin(BaseWarehouseEntity.class, BaseWarehouseEntity::getIdWarehouse, BaseWorkTeamEntity::getIdWarehouse)
                .leftJoin(BaseWhposEntity.class, BaseWhposEntity::getIdWhpos, BaseWorkTeamEntity::getIdWhpos)
                .eq(BaseWorkTeamEntity::getStatus, CommonConstants.COMMON_STATUS_NORMAL)
                .eq(BaseWorkTeamEntity::getDeleteFlag, CommonConstants.DELETE_FLAG_NOT_DELETED)
                .eq(BaseWorkTeamEntity::getIdWorkteam, archiveId);
        BaseWorkTeamDetailVO detailVO = baseMapper.selectJoinOne(BaseWorkTeamDetailVO.class, wrapper);
        //设置默认归还仓库名称
        if (StrUtil.isNotBlank(detailVO.getIdWhback())) {
            BaseWarehouseEntity backWhEntity = baseWarehouseMapper.selectById(detailVO.getIdWhback());
            detailVO.setWhbackName(backWhEntity.getName());
        }
        //设置商业维修仓库信息
        if (StrUtil.isNotBlank(detailVO.getIdMainWh())) {
            BaseWarehouseEntity mainWhEntity = baseWarehouseMapper.selectById(detailVO.getIdMainWh());
            detailVO.setMainWhName(mainWhEntity.getName());
        }
        //设置商业维修仓库信息
        if (StrUtil.isNotBlank(detailVO.getIdHeadman())) {
            BasePerson personEntity = basePersonService.getById(detailVO.getIdHeadman());
            detailVO.setHeadmanName(personEntity.getName());
        }
        return detailVO;
    }

    @Override
    public List<BaseWorkTeamMemberVO> getWorkTeamMembers(BaseWorkTeamMemberQuery query) {
        MPJLambdaWrapper<BaseWorkTeamMemberEntity> wrapper = new MPJLambdaWrapper<>();
        wrapper.selectAll(BaseWorkTeamMemberEntity.class)
                .selectAs(BasePerson::getName, BaseWorkTeamMemberVO::getName)
                .selectAs(BaseUser::getIdUser, BaseWorkTeamMemberVO::getUserId)
                .selectAs(BaseWorkTeamEntity::getName, BaseWorkTeamMemberVO::getWorkteamName)
                .select(BaseAddressEntity::getDetail)
                .leftJoin(BasePerson.class, BasePerson::getIdPerson, BaseWorkTeamMemberEntity::getIdMember)
                .leftJoin(BaseAddressEntity.class, BaseAddressEntity::getIdAddress, BaseWorkTeamMemberEntity::getIdAddress)
                .leftJoin(BaseUser.class, BaseUser::getCode, BasePerson::getCode)
                .leftJoin(BaseWorkTeamEntity.class, BaseWorkTeamEntity::getIdWorkteam, BaseWorkTeamMemberEntity::getIdWorkteam)
                .eq(BaseWorkTeamMemberEntity::getStatus, CommonConstants.COMMON_STATUS_NORMAL)
                .eq(BaseWorkTeamMemberEntity::getDeleteFlag, CommonConstants.DELETE_FLAG_NOT_DELETED)
                .eq(ObjectUtil.isNotEmpty(query.getIdWorkteam()), BaseWorkTeamMemberEntity::getIdWorkteam, query.getIdWorkteam());
        String limitType = query.getLimitType();
        String limitVal = query.getLimitVal();
        if (StrUtil.isNotEmpty(limitType) && StrUtil.isNotEmpty(limitVal)) {
            switch (LimitType.parse(limitType)) {
                case ORG:
                    wrapper.leftJoin(BaseWorkTeamEntity.class, BaseWorkTeamEntity::getIdWorkteam, BaseWorkTeamMemberEntity::getIdWorkteam)
                            .eq(BaseWorkTeamEntity::getIdOrg, limitVal);
                    break;
                case DEP:
                    wrapper.leftJoin(BaseWorkTeamEntity.class, BaseWorkTeamEntity::getIdWorkteam, BaseWorkTeamMemberEntity::getIdWorkteam)
                            .eq(BaseWorkTeamEntity::getIdDepartment, limitVal);
                    break;
                case BIZ:
                    wrapper.leftJoin(BaseWorkTeamEntity.class, BaseWorkTeamEntity::getIdWorkteam, BaseWorkTeamMemberEntity::getIdWorkteam)
                            .eq(BaseWorkTeamEntity::getIdBizunit, limitVal);
                    break;
                default:
                    log.warn("未知的权限限制类型: {}", limitType);
                    break;
            }
        }
        return baseWorkTeamMemberMapper.selectJoinList(BaseWorkTeamMemberVO.class, wrapper);
    }

    @Override
    public List<BaseWorkTeamVO> getOwnerTeams(OwnerWorkTeamQuery query) {
        List<WorkTeamBaseInfo> workTeamsByUserId = remoteWorkTeamService.getWorkTeamsByUserId(query.getUserId());
        if (ObjectUtil.isEmpty(workTeamsByUserId)) {
            return Collections.emptyList();
        }
        BaseWorkTeamQuery tansQuery = new BaseWorkTeamQuery();
        BeanUtil.copyProperties(query, tansQuery);
        List<String> idsWorkTeam = workTeamsByUserId.stream().map(WorkTeamBaseInfo::getIdWorkteam).collect(Collectors.toList());
        MPJLambdaWrapper<BaseWorkTeamEntity> wrapper =
                WorkTeamWrapperBuilder.builder().withCommonConditions(tansQuery).withQueryParameters(tansQuery).build();
        wrapper.in(BaseWorkTeamEntity::getIdWorkteam, idsWorkTeam);
        return baseMapper.selectJoinList(BaseWorkTeamVO.class, wrapper);
    }
}
