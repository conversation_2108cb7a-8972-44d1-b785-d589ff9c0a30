package com.fls.master.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 业务类型表(BaseBiztype)表实体类
 *
 * <AUTHOR>
 * @since 2024-11-05 14:36:53
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_base_biztype")
public class BaseBizType implements Serializable {

    private static final long serialVersionUID = 1L;

     //主键
    @TableId(value = "id_biztype", type = IdType.ASSIGN_UUID)
    private String idBiztype;

     //编码
    private String code;

     //名称
    private String name;

     //级次
    private Integer grade;

     //是否末级 0=否，1=是 参见yesorno
    private String endFlag;

     //父级业务类型
    private String idParenttype;

     //启用状态：1=未启用，2=已启用，3=已停用，默认1，参见basestatus
    private String status;

     //创建时间
    private LocalDateTime createTime;

     //创建人
    private String creator;

     //失效时间
    private LocalDateTime disableTime;

     //时间戳
    private LocalDateTime ts;

     //是否删除：0=否，1=是，默认0，参见yesorno
    private String deleteFlag;

}
