package com.fls.master.pojo.vo;

import com.fls.master.entity.BaseFiletype;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.time.LocalDateTime;

/**
 * FiletypeVO
 *
 * <AUTHOR>
 */
@Data
public class FiletypeVO {

    @Schema(description = "主键")
    private String idFiletype;

    @Schema(description = "后缀名")
    private String suffix;

    @Schema(description = "mime类型")
    private String mimeType;

    @Schema(description = "文件类型")
    private String fileType;

    @Schema(description = "状态，1=未启用，2=已启用，3=已停用")
    private String status;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "创建人")
    private String creator;

    @Schema(description = "作废时间")
    private LocalDateTime invalidTime;

    @Schema(description = "作废人")
    private String invalider;

    public static FiletypeVO of(BaseFiletype source){
        FiletypeVO target = new FiletypeVO();
        BeanUtils.copyProperties(source,target);
        return target;
    }
}
