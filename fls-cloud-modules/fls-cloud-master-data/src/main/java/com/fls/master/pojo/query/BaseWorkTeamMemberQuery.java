package com.fls.master.pojo.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
* 工作班组成员表查询
*
* <AUTHOR>
* @since  2024-11-12
*/
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "工作班组成员表查询")
public class BaseWorkTeamMemberQuery extends ArchiveQuery {
    @Schema(description = "工作班组")
    private String idWorkteam;

    @Schema(description = "限定类型")
    private String limitType;

    @Schema(description = "限定条件")
    private String limitVal;
}