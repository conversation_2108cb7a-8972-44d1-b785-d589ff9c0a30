package com.fls.master.pojo.vo;

import com.fls.master.entity.BaseDictEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * AddressTypeVO
 *
 * <AUTHOR>
 */
@Data
public class AddressTypeVO {

    @Schema(description = "主体id")
    private String idBizUnit;

    @Schema(description = "内部编码")
    private String innerCode;

    @Schema(description = "名称")
    private String name;

    public static AddressTypeVO of(BaseDictEntity source) {
        AddressTypeVO vo = new AddressTypeVO();
        vo.setName(source.getDictName());
        vo.setInnerCode(source.getDictCode());
        vo.setIdBizUnit(source.getIdDict());
        return vo;
    }
}
