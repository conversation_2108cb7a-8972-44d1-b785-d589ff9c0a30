package com.fls.master.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 资源表表
 *
 * <AUTHOR>
 * @since 2024-11-12
 */

@Data
@TableName("t_base_resource")
public class BaseResourceEntity extends BaseStatusEntity {
    /**
     * 主键
     */
    @TableId(value = "id_resource", type = IdType.ASSIGN_UUID)
    private String idResource;

    /**
     * 编码
     */
    private String code;

    /**
     * 名称
     */
    private String name;

    /**
     * 所属分类
     */
    private String idResourceclass;

    /**
     * 业务模块代码
     */
    private String moduleCode;

    /**
     * 是否启用审批流：0=否，1=是，默认1，参见yesorno
     */
    private String needprocFlag;

    /**
     * 是否进入综合待审列表：0=否，1=是，默认1，参见yesorno
     */
    private String incalFlag;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 失效时间
     */
    private LocalDateTime disableTime;

    /**
     * 时间戳
     */
    private LocalDateTime ts;

    /**
     * 是否启用数据权限控制：0=否，1=是，默认1，参见yesorno
     */
    private String needauthFlag;

    /**
     * 资源审核页url地址
     */
    private String incalLink;

    /**
     * 类型，1=档案类，2=单据类
     */
    private String resourceType;

    /**
     * 是否启用资料管理，0=否，1=是，参见yesorno
     */
    private String needdocFlag;

    /**
     * 是否启用申请，0=否，1=是，参见yesorno
     */
    private String needapplyFlag;

    /**
     * 是否启用档案数据管理，0=否，1=是，参见yesorno
     */
    private String archmanaFlag;

    /**
     * 是否启用历史记录，0=否，1=是，参见yesorno
     */
    private String historyFlag;

    /**
     * 是否进入移动APP：0=否，1=是，默认0，参见yesorno
     */
    private String appFlag;

    /**
     * 移动端动态路由url
     */
    private String appLink;

    /**
     * 域名判断标识
     */
    private String domainFlag;

    /**
     * 资源资料是否开启引用
     */
    private String quoteFlag;

}
