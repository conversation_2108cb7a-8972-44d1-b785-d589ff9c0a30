package com.fls.master.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fls.master.mapper.BaseTransModeMapper;
import com.fls.master.entity.BaseTransMode;
import com.fls.master.service.BaseTransModeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 发运方式表(BaseTransmode)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-11-05 10:37:41
 */
@Service
@Slf4j
public class BaseTransModeServiceImpl extends ServiceImpl<BaseTransModeMapper, BaseTransMode> implements BaseTransModeService {
}
