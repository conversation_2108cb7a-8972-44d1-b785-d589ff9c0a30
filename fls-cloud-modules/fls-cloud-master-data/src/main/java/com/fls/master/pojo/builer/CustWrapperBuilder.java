package com.fls.master.pojo.builer;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.fls.master.entity.BaseCustEntity;
import com.fls.master.entity.BaseIndustryEntity;
import com.fls.master.entity.BaseOrg;
import com.fls.master.entity.CustClassEntity;
import com.fls.master.pojo.query.ArchiveQuery;
import com.fls.master.pojo.query.BaseCustQuery;
import com.fls.master.pojo.vo.BaseCustVO;
import java.util.List;

public class CustWrapperBuilder extends BaseWrapperBuilder<BaseCustEntity> {
    /**
     * 构造器放在子类实现
     *
     * @return 构造器
     */
    public static CustWrapperBuilder builder() {
        return new CustWrapperBuilder();
    }

    /**
     * 添加所有业务条件
     */
    @Override
    public CustWrapperBuilder withQueryParameters(ArchiveQuery query) {
        BaseCustQuery transQuery = (BaseCustQuery) query;
        wrapper.selectAll(BaseCustEntity.class)
            .selectAs(CustClassEntity::getName, BaseCustVO::getClassName)
            .selectAs(BaseIndustryEntity::getName, BaseCustVO::getIndustryName)
            .selectAs(BaseOrg::getName, BaseCustVO::getOrgName)
            .leftJoin(BaseOrg.class,BaseOrg::getIdOrg,BaseCustEntity::getIdOrg)
            .leftJoin(CustClassEntity.class, CustClassEntity::getIdCustclass,BaseCustEntity::getIdCustclass)
            .leftJoin(BaseIndustryEntity.class,BaseIndustryEntity::getIdIndustry,BaseCustEntity::getIdIndustry)
            .like(ObjectUtil.isNotEmpty(transQuery.getCode()), BaseCustEntity::getCode, transQuery.getCode())
            .like(ObjectUtil.isNotEmpty(transQuery.getName()), BaseCustEntity::getName, transQuery.getName())
            .eq(ObjectUtil.isNotEmpty(transQuery.getIdOrg()), BaseCustEntity::getIdOrg, transQuery.getIdOrg())
            .eq(ObjectUtil.isNotEmpty(transQuery.getIdCustclass()), BaseCustEntity::getIdCustclass, transQuery.getIdCustclass())
            .eq(ObjectUtil.isNotEmpty(transQuery.getIdParentcust()), BaseCustEntity::getIdParentcust, transQuery.getIdParentcust())
            .eq(ObjectUtil.isNotEmpty(transQuery.getCustProp()), BaseCustEntity::getCustProp, transQuery.getCustProp())
            .eq(ObjectUtil.isNotEmpty(transQuery.getCustType()), BaseCustEntity::getCustType, transQuery.getCustType())
            .eq(ObjectUtil.isNotEmpty(transQuery.getCustLevel()), BaseCustEntity::getCustLevel, transQuery.getCustLevel());
        return this;
    }

    /**
     * 添加权限条件
     */
    @Override
    public CustWrapperBuilder withAuthorization(List<String> authIds) {
        if (CollectionUtil.isNotEmpty(authIds)) {
            wrapper.in(BaseCustEntity::getIdOrg, authIds);
        }
        return this;
    }
}
