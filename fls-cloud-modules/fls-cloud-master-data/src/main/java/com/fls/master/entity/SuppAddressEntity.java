package com.fls.master.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 供应商地址表
 *
 * <AUTHOR>
 * @since  2024-11-07
 */

@Data
@TableName("t_supp_address")
public class SuppAddressEntity {
	/**
	* 主键
	*/
	@TableId(value = "id_suppaddress", type = IdType.ASSIGN_UUID)
	private String idSuppaddress;

	/**
	* 所属供应商
	*/
	private String idSupplier;

	/**
	* 收货地址
	*/
	private String idAddress;

	/**
	* 收货单位
	*/
	private String receivingUnit;

	/**
	* 联系人
	*/
	private String idLinkman;

	/**
	* 是否默认：0=否，1=是，参见yesorno
	*/
	private String defaultFlag;

	/**
	* 状态，1=未启用，2=已启用，3=已停用，参见basestatus
	*/
	private String status;

	/**
	* 创建时间
	*/
	private LocalDateTime createTime;

	/**
	* 创建人
	*/
	private String creator;

	/**
	* 作废时间
	*/
	private LocalDateTime invalidTime;

	/**
	* 作废人
	*/
	private String invalider;

	/**
	* 时间戳
	*/
	private LocalDateTime ts;

	/**
	* 是否删除，默认0，参见yesorno
	*/
	private String deleteFlag;

	/**
	* 期初标识，默认0
	*/
	private Integer initFlag;

}
