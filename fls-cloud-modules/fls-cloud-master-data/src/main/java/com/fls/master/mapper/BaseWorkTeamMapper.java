package com.fls.master.mapper;

import com.fls.master.api.model.WorkTeamBaseInfo;
import com.fls.master.entity.BaseWorkTeamEntity;
import com.github.yulichang.base.MPJBaseMapper;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
* 工作班组表-mapper
*
* <AUTHOR>
* @since  2024-11-12
*/
public interface BaseWorkTeamMapper extends MPJBaseMapper<BaseWorkTeamEntity> {
    List<BaseWorkTeamEntity> selectWorkTeamsByUserId(@Param("userId") String userId);

    WorkTeamBaseInfo getBaseInfoByWorkTeamId(@Param("workTeamId") String workTeamId);

    List<WorkTeamBaseInfo> getBaseInfoByWorkTeamIds(@Param("workTeamIds") List<String> workTeamIds);
}
