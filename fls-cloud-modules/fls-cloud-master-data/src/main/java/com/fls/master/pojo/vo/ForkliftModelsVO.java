package com.fls.master.pojo.vo;

import com.fls.master.entity.BaseForkliftModels;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.time.LocalDateTime;

/**
 * ForkliftmodelsVO
 *
 * <AUTHOR>
 */
@Data
public class ForkliftModelsVO {

    @Schema(description = "主键")
    private String id;

    @Schema(description = "编码")
    private String code;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "NC自定义档案主键")
    private String pkDefdoc;

    @Schema(description = "启用状态：1=未启用，2=已启用，3=已停用")
    private String status;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "失效时间")
    private LocalDateTime disableTime;

    public static ForkliftModelsVO of(BaseForkliftModels source) {
        ForkliftModelsVO target = new ForkliftModelsVO();
        BeanUtils.copyProperties(source, target);
        target.setId(source.getIdForkliftmodels());
        return target;
    }
}
