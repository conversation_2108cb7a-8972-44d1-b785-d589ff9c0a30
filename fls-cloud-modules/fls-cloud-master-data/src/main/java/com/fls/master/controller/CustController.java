package com.fls.master.controller;

import com.fls.common.core.domain.ResponseData;
import com.fls.common.core.validate.QueryGroup;
import com.fls.master.pojo.query.ArchiveDetailQuery;
import com.fls.master.pojo.query.BaseCustQuery;
import com.fls.master.pojo.query.CustAllocationQuery;
import com.fls.master.pojo.vo.BaseCustDetailVO;
import com.fls.master.pojo.vo.BaseCustVO;
import com.fls.master.pojo.vo.CustAddressVO;
import com.fls.master.pojo.vo.CustAllocationVO;
import com.fls.master.pojo.vo.CustLinkmanVO;
import com.fls.master.pojo.vo.PageResult;
import com.fls.master.service.BaseCustService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 客户基本档案-web层服务
 *
 * <AUTHOR>
 * @since 2024-11-06
 */
@RestController
@Slf4j
@RequestMapping("/cust")
@Tag(name = "客户基本档案-web层服务")
@RequiredArgsConstructor
public class CustController {
    private final BaseCustService baseCustService;

    @Operation(summary = "全量客户基本档案查询")
    @PostMapping("/list")
    public ResponseData<PageResult<BaseCustVO>> list(@RequestBody BaseCustQuery query) {
        return ResponseData.ok(baseCustService.page(query));
    }

    @Operation(summary = "权限客户基本档案查询")
    @PostMapping("/auth/list")
    public ResponseData<List<BaseCustVO>> authList(@Validated(QueryGroup.class) @RequestBody BaseCustQuery query) {
        return ResponseData.ok(baseCustService.queryAuthArchives(query));
    }

    @Operation(summary = "客户基本档案详情查询")
    @PostMapping("/detail")
    public ResponseData<BaseCustDetailVO> detail(@RequestBody ArchiveDetailQuery query) {
        return ResponseData.ok(baseCustService.queryArchiveDetail(query.getId()));
    }

    @Operation(summary = "客户地址查询")
    @PostMapping("/address")
    public ResponseData<List<CustAddressVO>> address(@RequestBody ArchiveDetailQuery query) {
        return ResponseData.ok(baseCustService.queryCustAddress(query.getId()));
    }

    @Operation(summary = "客户联系人查询")
    @PostMapping("/linkman")
    public ResponseData<List<CustLinkmanVO>> linkman(@RequestBody ArchiveDetailQuery query) {
        return ResponseData.ok(baseCustService.queryCustLinkman(query.getId()));
    }

    @Operation(summary = "权限客户分拨地址查询")
    @PostMapping("/auth/allocation/list")
    public ResponseData<PageResult<CustAllocationVO>> allocationPage(@Validated @RequestBody CustAllocationQuery query) {
        return ResponseData.ok(baseCustService.queryCustAllocation(query));
    }
}
