package com.fls.master.pojo.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

/**
* 客户基本档案查询
*
* <AUTHOR>
* @since  2024-11-06
*/
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "客户基本档案查询")
public class BaseCustQuery extends ArchiveQuery{
    @Schema(description = "编码")
    private String code;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "所属组织")
    private String idOrg;

    @Schema(description = "客户分类")
    private String idCustclass;

    @Schema(description = "上级客户")
    private String idParentcust;

    @Schema(description = "客户属性 0=外部单位，1=内部单位 参见cust_prop")
    private String custProp;

    @Schema(description = "客户类型 01=终端，02=同行，03=个体户 参见cust_type")
    private String custType;

    @Schema(description = "客户等级 1=A类客户，2=B类客户，3=C类客户 参见cust_level")
    private String custLevel;

    @Schema(description = "当前页")
    @Min(value = 1, message = "当前页不能小于1")
    private Long pageNo = 1L;

    @Schema(description = "分页大小")
    @Min(value = 1, message = "当前分页大小不能小于1")
    @Max(value = 5000, message = "分页大小最大不能超过5000")
    private Long pageSize = 10L;
}
