package com.fls.master.controller;

import com.fls.common.core.domain.ResponseData;
import com.fls.common.core.validate.QueryGroup;
import com.fls.master.pojo.query.DetailQuery;
import com.fls.master.pojo.query.MaterialQuery;
import com.fls.master.pojo.vo.InventoryVO;
import com.fls.master.pojo.vo.PageResult;
import com.fls.master.service.MaterialManageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * InventoryManageController
 *
 * <AUTHOR>
 */
@Tag(name = "物料管理")
@Slf4j
@RestController
@RequestMapping("/material-manage")
@RequiredArgsConstructor
public class MaterialManageController {


    private final MaterialManageService materialManage;

    /**
     * 暂无权限物料列表,返回空数组
     *
     * @param query
     * @return
     */
    @Operation(summary = "分页-权限物料列表查询")
    @PostMapping("/material/private/page")
    public ResponseData<PageResult<InventoryVO>> materialPrivatePage(@Validated(QueryGroup.class) @RequestBody MaterialQuery query) {
        return ResponseData.ok(materialManage.materialPrivatePage(query));
    }


    @Operation(summary = "分页-物料列表查询")
    @PostMapping("/material/public/page")
    public ResponseData<PageResult<InventoryVO>> invPublicPage(@RequestBody MaterialQuery query) {
        query.setIsAccurateCode(Boolean.FALSE);
        return ResponseData.ok(materialManage.materialPublicPage(query));
    }

    @Operation(summary = "物料基础信息查询")
    @PostMapping("/material/public/detail")
    public ResponseData<InventoryVO> invPublicDetail(@RequestBody DetailQuery code) {
        MaterialQuery query = new MaterialQuery();
        query.setMaterialDetailQueryInfo(code.getCode());
        List<InventoryVO> records = materialManage.materialPublicPage(query).getRecords();
        return ResponseData.ok(records.isEmpty() ? null : records.get(0));
    }
}
