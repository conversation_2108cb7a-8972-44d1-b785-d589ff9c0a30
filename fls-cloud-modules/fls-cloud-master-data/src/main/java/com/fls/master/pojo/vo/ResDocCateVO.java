package com.fls.master.pojo.vo;

import com.fls.master.entity.LeaseDocumentParameters;
import com.fls.master.entity.LeaseDocumentValidator;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import java.time.LocalDateTime;

/**
 * ResDocCateVO
 *
 * <AUTHOR>
 */
@Data
public class ResDocCateVO {

    @Schema(description = "资料分类id")
    private String idDocCat;

    @Schema(description = "编码")
    private String code;

    @Schema(description = "名称")
    private String dcName;

    @Schema(description = "资料类型")
    private String idDocType;

    @Schema(description = "资料引用所属资源")
    private String idResource;

    @Schema(description = "是否启用资料维护申请")
    private Integer applyFlag;

    @Schema(description = "是否启用审批")
    private Integer needprocFlag;

    @Schema(description = "资料维护申请资源（默认固定）")
    private String idResourceApply;

    @Schema(description = "资料维护申请资源交易类型")
    private String idResTrantypeApply;

    @Schema(description = "是否启用引用功能")
    private Integer quoteFlag;

    @Schema(description = "是否启用有效时间")
    private String activetimeFlag;

    @Schema(description = "备注")
    private String dcRemark;

    @Schema(description = "显示顺序")
    private Integer sort;

    @Schema(description = "失效时间")
    private LocalDateTime disableTime;

    @Schema(description = "菜单id")
    private String idAuth;

    @Schema(description = "菜单状态")
    private String authStatus;

    @Schema(description = "1：未启用，2：已启用，3：已停用")
    private String status;

    @Schema(description = "资料分类的额外参数")
    private Exts exts;

    @Schema(description = "资料验证器")
    private Validators validators;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Exts {
        @Schema(description = "资料分类的额外参数id")
        private String idAdditionParem;

        @Schema(description = "分类id")
        private String idDocCat;

        @Schema(description = "参数标识")
        private String ename;

        @Schema(description = "名称")
        private String name;

        @Schema(description = "数据类型")
        private String valstr;

        @Schema(description = "自定义序号, 与资料分类构成联合唯一键")
        private String serialNum;

        @Schema(description = "输入长度")
        private Integer vallen;

        @Schema(description = "精度")
        private Integer valacc;

        @Schema(description = "1：必选")
        private String seltiveFlag;

        @Schema(description = "状态")
        private String status;

        public static Exts of(LeaseDocumentParameters source) {
            Exts vo = new Exts();
            BeanUtils.copyProperties(source, vo);
            return vo;
        }
    }

    @Data
    public static class Validators {

        @Schema(description = "资料验证器id")
        private String idValidator;

        @Schema(description = "资料分类id")
        private String idDocCat;

        @Schema(description = "所属组织是否必填")
        private Integer idOrgRequireFlag;

        @Schema(description = "所属主体是否必填")
        private Integer idBizunitRequireFlag;

        @Schema(description = "保管组织是否必填")
        private Integer idOrgKeepRequireFlag;

        @Schema(description = "保管主体是否必填")
        private Integer idBizunitKeepRequireFlag;

        @Schema(description = "存放地址是否必填")
        private Integer placeRequireFlag;

        @Schema(description = "责任人是否必填")
        private Integer idPersonRequireFlag;

        @Schema(description = "失效时间")
        private LocalDateTime disableTime;

        public static Validators of(LeaseDocumentValidator source) {
            Validators vo = new Validators();
            BeanUtils.copyProperties(source, vo);
            return vo;
        }
    }
}
