package com.fls.master.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 经营主体表基础信息
 *
 * <AUTHOR>
 * @since  2024-11-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "经营主体表基础信息")
public class BaseBizunitVO extends ArchiveVO {

    @Schema(description = "主键")
    private String idBizunit;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "显示顺序")
    private Integer displayOrder;

    @Schema(description = "内部编号")
    private String innercode;

    @Schema(description = "经营主体类型 1=终端，2=批发，3=外贸 参见bizunit_type")
    private Integer bizunitType;

    @Schema(description = "经营主体NC PK")
    private String bizPK;

    @Schema(description = "所属集团")
    private String idGroup;

    @Schema(description = "所属组织")
    private String idOrg;

    @Schema(description = "组织名称")
    private String orgName;

    @Schema(description = "集团编码")
    private String groupCode;

    @Schema(description = "集团内部编码")
    private String groupInnerCode;

    @Schema(description = "集团名称")
    private String groupName;

    @Schema(description = "集团pk")
    private String pkGroup;

    @Schema(description = "启用状态：1=未启用，2=已启用，3=已停用，默认1，参见basestatus")
    private Integer status;

}


