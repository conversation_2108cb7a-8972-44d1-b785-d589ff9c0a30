package com.fls.master.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 客户分拨信息
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "客户分拨信息")
public class CustAllocationVO extends ArchiveVO {

    @Schema(description = "客户分拨主键id")
    private String idAllocation;

    @Schema(description = "客户id")
    private String idCust;

    @Schema(description = "客户名称")
    private String custName;

    @Schema(description = "客户编码")
    private String custCode;

    @Schema(description = "客户地址")
    private String custAddress;

    @Schema(description = "客户地址编码")
    private String custAddressCode;

    @Schema(description = "客户分拨名称")
    private String allocationName;

    @Schema(description = "客户分拨地址id")
    private String idAllocationAddress;

    @Schema(description = "客户分拨地址")
    private String allocationAddress;

    @Schema(description = "客户分拨地址编码")
    private String allocationAddressCode;
}
