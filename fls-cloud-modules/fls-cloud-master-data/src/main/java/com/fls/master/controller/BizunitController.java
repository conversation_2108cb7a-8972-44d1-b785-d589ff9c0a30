package com.fls.master.controller;

import com.fls.common.core.domain.ResponseData;
import com.fls.common.core.validate.QueryGroup;
import com.fls.master.pojo.query.ArchiveDetailQuery;
import com.fls.master.pojo.query.BaseBizunitQuery;
import com.fls.master.pojo.vo.BaseBizunitDetailVO;
import com.fls.master.pojo.vo.BaseBizunitVO;
import com.fls.master.service.BaseBizunitService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 经营主体-web层服务
 *
 * <AUTHOR>
 * @since 2024-11-06
 */
@RestController
@Slf4j
@RequestMapping("/bizunit")
@Tag(name = "经营主体-web层服务")
@RequiredArgsConstructor
public class BizunitController {
    private final BaseBizunitService baseBizunitService;

    @Operation(summary = "全量经营主体查询")
    @PostMapping("/list")
    public ResponseData<List<BaseBizunitVO>> list(@RequestBody BaseBizunitQuery query) {
        return ResponseData.ok(baseBizunitService.queryAll(query));
    }

    @Operation(summary = "权限经营主体查询")
    @PostMapping("/auth/list")
    public ResponseData<List<BaseBizunitVO>> authList(@Validated(QueryGroup.class) @RequestBody BaseBizunitQuery query) {
        return ResponseData.ok(baseBizunitService.queryAuthArchives(query));
    }

    @Operation(summary = "经营主体详情查询")
    @PostMapping("/detail")
    public ResponseData<List<BaseBizunitDetailVO>> detail(@RequestBody ArchiveDetailQuery query) {
        return ResponseData.ok(baseBizunitService.queryArchiveDetail(query.getId()));
    }
}
