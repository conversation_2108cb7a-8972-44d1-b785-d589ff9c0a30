package com.fls.master.pojo.vo;

import com.fls.master.entity.BaseResourceClassEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.time.LocalDateTime;

/**
 * ResourceClassVO
 *
 * <AUTHOR>
 */
@Data
public class ResourceClassVO {

    @Schema(description = "主键")
    private String idResourceclass;

    @Schema(description = "编码")
    private String code;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "级次")
    private Integer grade;

    @Schema(description = "上级分类")
    private String idParentclass;

    @Schema(description = "是否末级 0=否，1=是")
    private String endFlag;

    @Schema(description = "启用状态：1=未启用，2=已启用，3=已停用")
    private String status;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "创建人")
    private String creator;

    @Schema(description = "失效时间")
    private LocalDateTime disableTime;

    @Schema(description = "时间戳")
    private LocalDateTime ts;

    @Schema(description = "应用工程名称")
    private String projectName;

    public static ResourceClassVO of(BaseResourceClassEntity source) {
        ResourceClassVO vo = new ResourceClassVO();
        BeanUtils.copyProperties(source, vo);
        return vo;
    }
}
