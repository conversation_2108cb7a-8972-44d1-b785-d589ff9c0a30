package com.fls.master.controller;

import com.fls.common.core.domain.ResponseData;
import com.fls.common.core.validate.QueryGroup;
import com.fls.master.pojo.query.ArchiveDetailQuery;
import com.fls.master.pojo.query.BaseSupplierQuery;
import com.fls.master.pojo.vo.BaseCustDetailVO;
import com.fls.master.pojo.vo.BaseSupplierVO;
import com.fls.master.pojo.vo.PageResult;
import com.fls.master.pojo.vo.SupplierAddressVO;
import com.fls.master.pojo.vo.SupplierLinkmanVO;
import com.fls.master.service.BaseSupplierService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 供应商基本档案-web层服务
 *
 * <AUTHOR>
 * @since 2024-11-07
 */
@RestController
@Slf4j
@RequestMapping("/supplier")
@Tag(name = "供应商基本档案-web层服务")
@RequiredArgsConstructor
public class SupplierController {
    private final BaseSupplierService baseSupplierService;

    @Operation(summary = "全量供应商基本档案查询")
    @PostMapping("/list")
    public ResponseData<PageResult<BaseSupplierVO>> list(@RequestBody BaseSupplierQuery query) {
        return ResponseData.ok(baseSupplierService.page(query));
    }

    @Operation(summary = "权限供应商基本档案查询")
    @PostMapping("/auth/list")
    public ResponseData<List<BaseSupplierVO>> authList(@Validated(QueryGroup.class) @RequestBody BaseSupplierQuery query) {
        return ResponseData.ok(baseSupplierService.queryAuthArchives(query));
    }


    @Operation(summary = "供应商基本档案详情查询")
    @PostMapping("/detail")
    public ResponseData<BaseCustDetailVO> detail(@RequestBody ArchiveDetailQuery query) {
        return ResponseData.ok(baseSupplierService.queryArchiveDetail(query.getId()));
    }

    @Operation(summary = "供应商基本档案地址查询")
    @PostMapping("/address")
    public ResponseData<List<SupplierAddressVO>> address(@RequestBody ArchiveDetailQuery query) {
        return ResponseData.ok(baseSupplierService.querySupplierAddress(query.getId()));
    }

    @Operation(summary = "供应商基本档案联系人查询")
    @PostMapping("/linkman")
    public ResponseData<List<SupplierLinkmanVO>> linkman(@RequestBody ArchiveDetailQuery query) {
        return ResponseData.ok(baseSupplierService.querySupplierLinkman(query.getId()));
    }
}
