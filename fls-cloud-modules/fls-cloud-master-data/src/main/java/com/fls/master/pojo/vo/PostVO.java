package com.fls.master.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * PostVO
 *
 * <AUTHOR>
 */
@Data
public class PostVO {

    @Schema(description = "主键")
    private String idPost;

    @Schema(description = "编码")
    private String code;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "所属组织")
    private String idOrg;

    @Schema(description = "所属集团")
    private String idGroup;

    @Schema(description = "所属部门")
    private String idDepartment;

//    @Schema(description = "岗位序列")
//    private String idPostseries;

    @Schema(description = "上级岗位")
    private String idParentpost;

    @Schema(description = "所属职务")
    private String idJob;

    @Schema(description = "成立日期 格式yyyy-mm-dd")
    private String buildDate;

    @Schema(description = "撤销日期 格式yyyy-mm-dd")
    private String abortDate;

    @Schema(description = "NC岗位PK")
    private String pkPost;

    @Schema(description = "启用状态：1=未启用，2=已启用，3=已停用")
    private String status;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "失效时间")
    private LocalDateTime disableTime;

//    @Schema(description = "iHR岗位id")
//    private String idIhr;

//    字段id翻译
    @Schema(description = "所属集团名称")
    private String groupName;

    @Schema(description = "所属集团名称")
    private String orgName;

    @Schema(description = "所属部门名称")
    private String departmentName;

    @Schema(description = "所属职务名称")
    private String jobName;

}
