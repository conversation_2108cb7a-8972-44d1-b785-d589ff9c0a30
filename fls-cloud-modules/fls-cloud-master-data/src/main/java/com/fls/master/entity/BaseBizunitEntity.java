package com.fls.master.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 经营主体表
 *
 * <AUTHOR>
 * @since  2024-11-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_base_bizunit")
public class BaseBizunitEntity extends BaseStatusEntity{
    /**
     * 主键
     */
    @TableId(value = "id_bizunit", type = IdType.ASSIGN_UUID)
    private String idBizunit;

    /**
     * 名称
     */
    private String name;

    /**
     * 显示顺序
     */
    private Integer displayOrder;

    /**
     * 内部编号
     */
    private String innercode;

    /**
     * 经营主体类型 1=终端，2=批发，3=外贸 参见bizunit_type
     */
    private Integer bizunitType;

    /**
     * 开始日期
     */
    private LocalDate beginDate;

    /**
     * 结束日期
     */
    private LocalDate endDate;

    /**
     * 所属集团
     */
    private String idGroup;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 失效时间
     */
    private LocalDateTime disableTime;

    /**
     * 时间戳
     */
    private LocalDateTime ts;


    /**
     * 核算归属主体
     */
    private String idAccunit;

    /**
     * 所属组织
     */
    private String idOrg;

    /**
     * 所属地区名称
     */
    private String areaName;

    /**
     * 经理
     */
    private String idManager;

    /**
     * 维修经理
     */
    private String idManagerMaintenance;

    /**
     * 服务支持人员
     */
    private String idsServiceSpporter;

    /**
     * 业务支持人员
     */
    private String idsBusinessSpporter;

    /**
     * 商业维修库
     */
    private String idWarehouseCmaint;

    /**
     * 自行维修库
     */
    private String idWarehouseSrepair;

    /**
     * 租赁维修库
     */
    private String idWarehouseLmaint;

    /**
     * 直运库
     */
    private String idWarehouseDirect;

    /**
     * 配件库
     */
    private String idWarehouseParts;

    /**
     * 自检登记机关 checking_city_dep
     */
    private String checkingCityDep;

    /**
     * 社会信用代码
     */
    private String appunitcode;

    /**
     * 资料关联id
     */
    private String idLinkDoc;

    /**
     * 单据编码，没用的值，仅用于资料上传不报错
     */
    private String billCode;

    /**
     * 上牌单位邮政编码
     */
    private String usecompostcode;

    /**
     * 上牌单位地址
     */
    private String usecomplace;

    /**
     * 上牌单位固定电话
     */
    private String usecomtel;

    /**
     * 上牌单位地址行政编码
     */
    private String usecomregion;

    /**
     * 安全管理员
     */
    private String checkSecurity;

    /**
     * 安全管理员电话
     */
    private String checkSecurityTel;

    /**
     * 自检人员
     */
    private String checkSelf;

    /**
     * 中转库
     */
    private String idWarehouseTransfer;

    /**
     * 管理部门id
     */
    private String idManageDept;

    /**
     * 使用部门pk
     */
    private String pkUseDept;

}
