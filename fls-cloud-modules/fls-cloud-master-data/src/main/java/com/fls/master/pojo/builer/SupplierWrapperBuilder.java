package com.fls.master.pojo.builer;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.fls.master.entity.BaseOrg;
import com.fls.master.entity.BaseSupplierEntity;
import com.fls.master.entity.SupplierClassEntity;
import com.fls.master.pojo.query.ArchiveQuery;
import com.fls.master.pojo.query.BaseSupplierQuery;
import com.fls.master.pojo.vo.BaseSupplierVO;
import java.util.List;

public class SupplierWrapperBuilder extends BaseWrapperBuilder<BaseSupplierEntity> {
    /**
     * 构造器放在子类实现
     *
     * @return 构造器
     */
    public static SupplierWrapperBuilder builder() {
        return new SupplierWrapperBuilder();
    }

    /**
     * 添加所有业务条件
     */
    @Override
    public SupplierWrapperBuilder withQueryParameters(ArchiveQuery query) {
        BaseSupplierQuery transQuery = (BaseSupplierQuery) query;
        wrapper.selectAll(BaseSupplierEntity.class)
            .selectAs(BaseOrg::getName, BaseSupplierVO::getOrgName)
            .selectAs(SupplierClassEntity::getName, BaseSupplierVO::getClassName)
            .leftJoin(BaseOrg.class,BaseOrg::getIdOrg,BaseSupplierEntity::getIdOrg)
            .leftJoin(SupplierClassEntity.class,SupplierClassEntity::getIdSupplierclass,BaseSupplierEntity::getIdSupplier)
            .eq(ObjectUtil.isNotEmpty(transQuery.getCode()), BaseSupplierEntity::getCode, transQuery.getCode())
            .like(ObjectUtil.isNotEmpty(transQuery.getName()), BaseSupplierEntity::getName, transQuery.getName())
            .eq(ObjectUtil.isNotEmpty(transQuery.getIdOrg()), BaseSupplierEntity::getIdOrg, transQuery.getIdOrg())
            .eq(ObjectUtil.isNotEmpty(transQuery.getIdSupplierclass()), BaseSupplierEntity::getIdSupplierclass, transQuery.getIdSupplierclass())
            .eq(ObjectUtil.isNotEmpty(transQuery.getIdParentsupplier()), BaseSupplierEntity::getIdParentsupplier, transQuery.getIdParentsupplier())
            .eq(ObjectUtil.isNotEmpty(transQuery.getSupplierProp()), BaseSupplierEntity::getSupplierProp, transQuery.getSupplierProp())
            .eq(ObjectUtil.isNotEmpty(transQuery.getSupplierType()), BaseSupplierEntity::getSupplierType, transQuery.getSupplierType())
            .eq(ObjectUtil.isNotEmpty(transQuery.getSettleType()), BaseSupplierEntity::getSettleType, transQuery.getSettleType())
            .eq(ObjectUtil.isNotEmpty(transQuery.getLeaseAssistFlag()), BaseSupplierEntity::getLeaseAssistFlag, transQuery.getLeaseAssistFlag())
            .eq(ObjectUtil.isNotEmpty(transQuery.getLogisticsFlag()), BaseSupplierEntity::getLogisticsFlag, transQuery.getLogisticsFlag());
        return this;
    }

    /**
     * 添加权限条件
     */
    @Override
    public SupplierWrapperBuilder withAuthorization(List<String> authIds) {
        if (CollectionUtil.isNotEmpty(authIds)) {
            wrapper.in(BaseSupplierEntity::getIdOrg, authIds);
        }
        return this;
    }
}
