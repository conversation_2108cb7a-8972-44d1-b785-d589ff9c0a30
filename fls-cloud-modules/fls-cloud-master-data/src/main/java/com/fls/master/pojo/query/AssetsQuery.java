package com.fls.master.pojo.query;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

/**
 * AssetsQuery
 *
 * <AUTHOR>
 */

@Data
public class AssetsQuery extends ArchiveQuery{

    @Schema(description = "当前页")
    @Min(value = 1, message = "当前页不能小于1")
    private Long pageNo = 1L;

    @Schema(description = "分页大小")
    @Min(value = 1, message = "当前分页大小不能小于1")
    @Max(value = 5000, message = "分页大小最大不能超过5000")
    private Long pageSize = 10L;

    @Schema(description = "资产编码")
    private String code;

    @Schema(description = "资产名称")
    private String name;

    @Schema(description = "资产类型")
    private String idAssetType;

    @Schema(description = "资产状态")
    private String assetStatus;

    public Page getPageInfo() {
        return new Page<>(pageNo, pageSize);
    }
}
