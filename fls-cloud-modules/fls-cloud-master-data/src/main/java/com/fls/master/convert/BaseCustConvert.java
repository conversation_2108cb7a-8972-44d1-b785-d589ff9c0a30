package com.fls.master.convert;

import com.fls.master.entity.BaseCustEntity;
import com.fls.master.pojo.vo.BaseCustDetailVO;
import com.fls.master.pojo.vo.BaseCustVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 客户基本档案-Mapstruct
*
* <AUTHOR>
* @since  2024-11-06
*/
@Mapper
public interface BaseCustConvert {
    BaseCustConvert INSTANCE = Mappers.getMapper(BaseCustConvert.class);

    BaseCustEntity voToEntity(BaseCustVO vo);

    BaseCustVO entityToVo(BaseCustEntity entity);

    BaseCustDetailVO entityToDetailVo(BaseCustEntity entity);

    List<BaseCustVO> toVoList(List<BaseCustEntity> list);

}
