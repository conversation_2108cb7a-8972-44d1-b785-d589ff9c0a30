package com.fls.master.pojo.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
* 字典表查询
*
* <AUTHOR>
* @since  2024-11-12
*/
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "字典表查询")
public class BaseDictQuery extends ArchiveQuery {
    @Schema(description = "枚举类型")
    private String dictType;

    @Schema(description = "编码")
    private String dictCode;

    @Schema(description = "枚举名")
    private String dictName;

    @Schema(description = "NC档案主键")
    private String ncpk;

}
