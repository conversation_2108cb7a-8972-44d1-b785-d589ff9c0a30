package com.fls.master.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.*;

import java.time.LocalDateTime;

/**
 * 工作班组表表
 *
 * <AUTHOR>
 * @since  2024-11-12
 */

@Data
@TableName("t_base_workteam")
public class BaseWorkTeamEntity extends BaseStatusEntity{
	/**
	* 主键
	*/
	@TableId(value = "id_workteam", type = IdType.ASSIGN_UUID)
	private String idWorkteam;

	/**
	* 班组编码
	*/
	private String code;

	/**
	* 班组名称
	*/
	private String name;

	/**
	* 所属组织
	*/
	private String idOrg;

	/**
	* 所属主体
	*/
	private String idBizunit;

	/**
	* 所属部门
	*/
	private String idDepartment;

	/**
	* 班组对应虚拟地点
	*/
	private String idAddress;

	/**
	* 班组对应仓库
	*/
	private String idWarehouse;

	/**
	* 班组对应货位
	*/
	private String idWhpos;

	/**
	* 班组默认归还仓库
	*/
	private String idWhback;

	/**
	* 组长
	*/
	private String idHeadman;

	/**
	* 班组成员
	*/
	private String members;

	/**
	* 租赁服务标识：0=否，1=是，默认0，参见yesorno
	*/
	private String rentalServiceFlag;

	/**
	* 商业维修标识：0=否，1=是，默认0，参见yesorno
	*/
	private String commercialMaintenanceFlag;

	/**
	* 是否外协：0=否，1=是，默认0，参见yesorno
	*/
	private String ocunitFlag;

	/**
	* 外协单位
	*/
	private String idOcunit;

	/**
	* NC主键
	*/
	private String ncPk;

	/**
	* 备注
	*/
	private String memo;

	/**
	* 创建时间
	*/
	private LocalDateTime createTime;

	/**
	* 创建人
	*/
	private String creator;

	/**
	* 修改时间
	*/
	private LocalDateTime modifyTime;

	/**
	* 修改人
	*/
	private String modifier;

	/**
	* 作废时间
	*/
	private LocalDateTime invalidTime;

	/**
	* 作废人
	*/
	private String invalider;

	/**
	* 时间戳
	*/
	private LocalDateTime ts;
}
