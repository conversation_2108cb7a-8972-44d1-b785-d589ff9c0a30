package com.fls.master.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
* 资源表基础信息
*
* <AUTHOR>
* @since  2024-11-12
*/
@Data
@Schema(description = "资源表基础信息")
public class BaseResourceVO extends ArchiveVO {

	@Schema(description = "主键")
	private String idResource;

	@Schema(description = "编码")
	private String code;

	@Schema(description = "名称")
	private String name;

	@Schema(description = "所属分类")
	private String idResourceclass;

	@Schema(description = "业务模块代码")
	private String moduleCode;

	@Schema(description = "类型，1=档案类，2=单据类")
	private String resourceType;

    @Schema(description = "类型编码")
    private String typeCode;

    @Schema(description = "类型名称")
    private String typeName;

	@Schema(description = "是否启用资料管理，0=否，1=是，参见yesorno")
	private String needdocFlag;

	@Schema(description = "是否启用申请，0=否，1=是，参见yesorno")
	private String needapplyFlag;

	@Schema(description = "是否启用档案数据管理，0=否，1=是，参见yesorno")
	private String archmanaFlag;

	@Schema(description = "是否启用历史记录，0=否，1=是，参见yesorno")
	private String historyFlag;

	@Schema(description = "是否进入移动APP：0=否，1=是，默认0，参见yesorno")
	private String appFlag;

    @Schema(description = "启用状态：1=未启用，2=已启用，3=已停用，默认1，参见basestatus")
    private String status;
}
