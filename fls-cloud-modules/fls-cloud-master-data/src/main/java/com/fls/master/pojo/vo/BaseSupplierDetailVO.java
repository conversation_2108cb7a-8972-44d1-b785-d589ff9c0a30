package com.fls.master.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
* 供应商基本档案详情信息
*
* <AUTHOR>
* @since  2024-11-07
*/
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "供应商基本档案详情信息")
public class BaseSupplierDetailVO extends ArchiveDetailVO {

	@Schema(description = "主键")
	private String idSupplier;

	@Schema(description = "编码")
	private String code;

	@Schema(description = "名称")
	private String name;

	@Schema(description = "简称")
	private String shortname;

	@Schema(description = "所属组织")
	private String idOrg;

	@Schema(description = "供应商分类")
	private String idSupplierclass;

	@Schema(description = "上级供应商")
	private String idParentsupplier;

	@Schema(description = "纳税人登记号")
	private String taxpayerid;

	@Schema(description = "法人")
	private String legalbody;

	@Schema(description = "公司地址")
	private String address;

	@Schema(description = "电话")
	private String tel;

	@Schema(description = "是否客户 0=否，1=是 参见yesorno")
	private String customerFlag;

	@Schema(description = "对应客户")
	private String idCustomer;

	@Schema(description = "供应商属性 0=外部单位，1=内部单位 参见supplier_prop")
	private String supplierProp;

	@Schema(description = "对应组织")
	private String idFinanceorg;

	@Schema(description = "供应商类型 01=工厂，02=同行，03=小作坊 参见supplier_type")
	private String supplierType;

	@Schema(description = "供应商结算方式 01=月结，02=现金，03=代收货款，04=款到发货，05=预付订金，06=货到付款，07=分期付款，08=现金支票，09=约定分期付款 参见supplier_settletype")
	private String settleType;

	@Schema(description = "备注")
	private String memo;

	@Schema(description = "NC供应商档案主键")
	private String pkSupplier;

	@Schema(description = "启用状态：1=未启用，2=已启用，3=已停用，默认1，参见basestatus")
	private String status;

	@Schema(description = "创建时间")
	private LocalDateTime createTime;

	@Schema(description = "创建人")
	private String creator;

	@Schema(description = "失效时间")
	private LocalDateTime disableTime;

	@Schema(description = "时间戳")
	private LocalDateTime ts;

	@Schema(description = "租赁外协服务标识：0=否，1=是，参见yesorno")
	private String leaseAssistFlag;

	@Schema(description = "物流供应商标识，0=否，1=是")
	private String logisticsFlag;

	@Schema(description = "固定租期标识")
	private String rentLongFixedFlag;

	@Schema(description = "固定租期期数")
	private Integer rentLong;

}
