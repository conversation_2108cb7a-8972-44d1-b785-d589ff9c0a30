package com.fls.master.service;

import com.fls.master.pojo.query.ArchiveDetailQuery;
import com.fls.master.pojo.query.ArchiveQuery;
import com.fls.master.pojo.vo.ArchiveDetailVO;
import com.fls.master.pojo.vo.ArchiveVO;
import com.fls.master.pojo.vo.PageResult;
import java.util.List;

/**
 * 经营主体表
 *
 * <AUTHOR>
 * @since 2024-11-05
 */
public interface BaseArchiveQueryService {
    /**
     * 查询全量档案数据
     *
     * @param query 查询参数
     * @return 档案列表
     */
    default <Q extends ArchiveQuery, R extends ArchiveVO> List<R> queryAll(Q query) {
        return null;
    }

    /**
     * 查询全量档案数据-分页
     *
     * @param query 查询参数
     * @return 档案列表
     */
    default <Q extends ArchiveQuery, R extends ArchiveVO> PageResult<R> page(Q query) {
        return null;
    }

    /**
     * 查询全量档案数据
     *
     * @param query 查询参数
     * @return 档案列表
     */
    default <Q extends ArchiveQuery, R extends ArchiveVO> PageResult<R> pageAuthArchives(Q query) {
        return null;
    }

    /**
     * 查询权限过滤档案数据
     *
     * @param query 查询参数
     * @return 档案列表
     */
    default <Q extends ArchiveQuery, R extends ArchiveVO> List<R> queryAuthArchives(Q query) {
        return null;
    }

    /**
     * 查询档案详情,默认default返回空，详情查询不是所有档案都有的方法
     *
     * @param archiveId 档案主键id
     * @return 档案详情
     */
    default <R extends ArchiveDetailVO> R queryArchiveDetail(String archiveId) {
        return null;
    }

    /**
     * 扩展方法，将查询参数进行扩展
     *
     * @param detailQuery 档案详情查询参数
     * @return 档案详情
     */
    default <Q extends ArchiveDetailQuery, R extends ArchiveDetailVO> R queryArchiveDetail(ArchiveDetailQuery detailQuery) {
        return null;
    }
}
