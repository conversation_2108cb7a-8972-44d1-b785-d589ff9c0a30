package com.fls.master.pojo.vo;

import com.fls.master.entity.BaseMeasdoc;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.beans.BeanUtils;


/**
 * 计量单位
 *
 * <AUTHOR>
 * @since 2024-11-12
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "计量单位")
public class BaseMeasDocVO extends ArchiveVO {

    /**
     * 主键
     */
    private String idMeasDoc;

    /**
     * 编码
     */
    private String code;

    /**
     * 名称
     */
    private String name;

    /**
     * 英文名称
     */
    private String enName;

    /**
     * NC计量单位主键
     */
    private String pkMeasDoc;

    public static BaseMeasDocVO of(BaseMeasdoc source) {
        BaseMeasDocVO target = new BaseMeasDocVO();
        BeanUtils.copyProperties(source, target);
        target.setIdMeasDoc(source.getIdMeasdoc());
        target.setEnName(source.getEname());
        target.setPkMeasDoc(source.getPkMeasdoc());
        return target;
    }
}
