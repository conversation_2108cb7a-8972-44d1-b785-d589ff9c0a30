package com.fls.master.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 资源分类表表
 *
 * <AUTHOR>
 * @since  2024-11-13
 */

@Data
@TableName("t_base_resourceclass")
public class BaseResourceClassEntity {
	/**
	* 主键
	*/
	@TableId(value = "id_resourceclass", type = IdType.ASSIGN_UUID)
	private String idResourceclass;

	/**
	* 编码
	*/
	private String code;

	/**
	* 名称
	*/
	private String name;

	/**
	* 级次
	*/
	private Integer grade;

	/**
	* 上级分类
	*/
	private String idParentclass;

	/**
	* 是否末级 0=否，1=是 参见yesorno
	*/
	private String endFlag;

	/**
	* 启用状态：1=未启用，2=已启用，3=已停用，默认1，参见basestatus
	*/
	private String status;

	/**
	* 创建时间
	*/
	private LocalDateTime createTime;

	/**
	* 创建人
	*/
	private String creator;

	/**
	* 失效时间
	*/
	private LocalDateTime disableTime;

	/**
	* 时间戳
	*/
	private LocalDateTime ts;

	/**
	* 是否删除：0=否，1=是，默认0，参见yesorno
	*/
	private String deleteFlag;

	/**
	* 应用工程名
	*/
	private String projectName;

}
