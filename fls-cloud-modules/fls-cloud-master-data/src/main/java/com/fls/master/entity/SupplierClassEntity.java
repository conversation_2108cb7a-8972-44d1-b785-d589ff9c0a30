package com.fls.master.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>
 * 供应商基本分类表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_supplier_class")
public class SupplierClassEntity {
    /**
     * 主键
     */
    @TableId(value = "id_supplierclass", type = IdType.ASSIGN_UUID)
    private String idSupplierclass;

    /**
     * 编码
     */
    private String code;

    /**
     * 名称
     */
    private String name;

    /**
     * 级次
     */
    private Integer grade;

    /**
     * 是否末级 0=否，1=是 参见yesorno
     */
    private String endFlag;

    /**
     * 父级分类
     */
    private String idParentclass;

    /**
     * NC供应商基本分类主键
     */
    private String pkSupplierclass;

    /**
     * 启用状态：1=未启用，2=已启用，3=已停用，默认1，参见basestatus
     */
    private String status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 失效时间
     */
    private LocalDateTime disableTime;

    /**
     * 时间戳
     */
    private LocalDateTime ts;

    /**
     * 是否删除：0=否，1=是，默认0，参见yesorno
     */
    private String deleteFlag;


}
