package com.fls.master.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 角色数据权限表(BaseRoledata)表实体类
 *
 * <AUTHOR>
 * @since 2024-11-01 16:13:57
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_base_roledata")
public class BaseRoledata implements Serializable {

    private static final long serialVersionUID = 1L;

     //主键
    @TableId(value = "id_roledata", type = IdType.ASSIGN_UUID)
    private String idRoledata;

     //所属组织
    private String idOrg;

     //所属部门
    private String idDepartment;

     //所属岗位序列
    private String idPostseries;

     //所属岗位
    private String idPost;

     //角色id
    private String idRole;

     //启用状态：1=未启用，2=已启用，3=已停用，默认1，参见basestatus
    private String status;

     //来源申请子单ID
    private String sourceId;

     //创建时间
    private LocalDateTime createTime;

     //创建人
    private String creator;

     //失效时间
    private LocalDateTime disableTime;

     //时间戳
    private LocalDateTime ts;

     //是否删除：0=否，1=是，默认0，参见yesorno
    private String deleteFlag;

}

