package com.fls.master.pojo.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * RegionLevelQuery
 *
 * <AUTHOR>
 */
@Data
public class RegionLevelQuery extends StatusQuery{

    @Schema(description = "父级id")
    @NotBlank(message = "idParent不能为空")
    private String idParent;

    @Schema(description = "行政区划等级")
    private String level;

}
