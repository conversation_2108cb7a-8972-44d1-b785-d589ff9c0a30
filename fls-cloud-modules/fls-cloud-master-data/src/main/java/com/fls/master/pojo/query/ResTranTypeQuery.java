package com.fls.master.pojo.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * ResTranTypeQuery
 *
 * <AUTHOR>
 */
@Data
public class ResTranTypeQuery extends StatusQuery{

    @Schema(description = "名称")
    private String tranType;

    @Schema(description = "资源编码")
    private String resourceCode;

    @Schema(description = "交易类型编码")
    private String tranCode;

}
