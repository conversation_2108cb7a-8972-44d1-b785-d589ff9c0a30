package com.fls.master.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fls.master.entity.BaseUser;
import com.fls.master.pojo.query.UserRoleQuery;
import com.fls.master.pojo.vo.UserVO;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * 基础用户mapper
 *
 * <AUTHOR>
 * @since 2025-04-29
 */
public interface BaseUserMapper extends BaseMapper<BaseUser> {

    /**
     * 通过角色查询用户列表
     *
     * @param roleQuery 角色查询参数
     * @return 用户列表
     */
    List<UserVO> listByRole(@Param("query") UserRoleQuery roleQuery);
}
