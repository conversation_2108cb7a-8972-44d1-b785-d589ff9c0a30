package com.fls.master.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.*;

import java.time.LocalDateTime;

/**
 * 工作班组成员表表
 *
 * <AUTHOR>
 * @since  2024-11-12
 */

@Data
@TableName("t_base_workteam_member")
public class BaseWorkTeamMemberEntity {
	/**
	* 主键
	*/
	@TableId(value = "id_workteam_member", type = IdType.ASSIGN_UUID)
	private String idWorkteamMember;

	/**
	* 工作班组
	*/
	private String idWorkteam;

	/**
	* 班组成员
	*/
	private String idMember;

	/**
	* 班组成员对应虚拟地点
	*/
	private String idAddress;

	/**
	* 班组成员对应货位
	*/
	private String idWhpos;

	/**
	* 班组成员默认归还仓库
	*/
	private String idWhback;

	/**
	* NC档案主键
	*/
	private String ncPk;

	/**
	* 启用状态：1=未启用，2=已启用，3=已停用，默认1，参见basestatus
	*/
	private String status;

	/**
	* 创建时间
	*/
	private LocalDateTime createTime;

	/**
	* 创建人
	*/
	private String creator;

	/**
	* 修改时间
	*/
	private LocalDateTime modifyTime;

	/**
	* 修改人
	*/
	private String modifier;

	/**
	* 作废时间
	*/
	private LocalDateTime invalidTime;

	/**
	* 作废人
	*/
	private String invalider;

	/**
	* 时间戳
	*/
	private LocalDateTime ts;

	/**
	* 是否删除：0=否，1=是，默认0，参见yesorno
	*/
	private String deleteFlag;

}
