package com.fls.master.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fls.master.convert.BaseDictConvert;
import com.fls.master.entity.BaseDictEntity;
import com.fls.master.mapper.BaseDictMapper;
import com.fls.master.pojo.query.ArchiveQuery;
import com.fls.master.pojo.query.BaseDictQuery;
import com.fls.master.pojo.vo.BaseDictVO;
import com.fls.master.service.BaseDictService;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 字典表-service
 *
 * <AUTHOR>
 * @since 2024-11-12
 */
@Service
@Slf4j
public class BaseDictServiceImpl extends MPJBaseServiceImpl<BaseDictMapper, BaseDictEntity> implements BaseDictService {

    @Override
    public List<BaseDictVO> queryAll(ArchiveQuery query) {
        BaseDictQuery tansQuery = (BaseDictQuery) query;
        List<BaseDictEntity> entities = baseMapper.selectList(getWrapper(tansQuery));
        return BaseDictConvert.INSTANCE.toVoList(entities);
    }

    private MPJLambdaWrapper<BaseDictEntity> getWrapper(BaseDictQuery query) {
        MPJLambdaWrapper<BaseDictEntity> wrapper = new MPJLambdaWrapper<>();
        wrapper.selectAll(BaseDictEntity.class)
            .eq(ObjectUtil.isNotEmpty(query.getDictType()), BaseDictEntity::getDictType, query.getDictType())
            .like(ObjectUtil.isNotEmpty(query.getDictCode()), BaseDictEntity::getDictCode, query.getDictCode())
            .like(ObjectUtil.isNotEmpty(query.getDictName()), BaseDictEntity::getDictName, query.getDictName())
            .like(ObjectUtil.isNotEmpty(query.getNcpk()), BaseDictEntity::getNcpk, query.getNcpk());
        return wrapper;
    }

    @Override
    public List<BaseDictVO> queryDictValue(BaseDictQuery query) {
        LambdaQueryWrapper<BaseDictEntity> wrapper = Wrappers.lambdaQuery(BaseDictEntity.class).
            eq(ObjectUtil.isNotEmpty(query.getDictCode()), BaseDictEntity::getDictCode, query.getDictCode()).
            eq(ObjectUtil.isNotEmpty(query.getDictType()), BaseDictEntity::getDictType, query.getDictType());
        List<BaseDictEntity> baseDictEntities = this.getBaseMapper().selectList(wrapper);
        return BaseDictConvert.INSTANCE.toVoList(baseDictEntities);
    }
}
