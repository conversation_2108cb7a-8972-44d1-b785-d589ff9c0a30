package com.fls.master.pojo.vo;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.Collections;
import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PageResult<T> {

    private int current;

    private int size;

    private int total;

    private List<T> records;

    public PageResult(Page page, List<T> records) {
        if (CollUtil.isEmpty(records)) {
            records = Collections.emptyList();
        }
        this.current = (int) page.getCurrent();
        this.size = (int) page.getSize();
        this.total = (int) page.getTotal();
        this.records = records;
    }
}
