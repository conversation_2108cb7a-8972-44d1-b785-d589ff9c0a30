package com.fls.master.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 员工工作信息表(BasePsnjob)表实体类
 *
 * <AUTHOR>
 * @since 2025-07-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_base_psnjob")
public class BasePsnjobEntity implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 主键
         */
        @TableId(value = "id_psnjob", type = IdType.ASSIGN_UUID)
        private String idPsnjob;

        /**
         * 所属集团
         */
        private String idGroup;

        /**
         * 任职组织
         */
        private String idOrg;

        /**
         * 任职部门
         */
        private String idDepartment;

        /**
         * 任职岗位
         */
        private String idPost;

        /**
         * 所属职务
         */
        private String idJob;

        /**
         * 人员类别
         */
        private String idPsncl;

        /**
         * 员工
         */
        private String idPerson;

        /**
         * 任职开始日期
         */
        private LocalDate beginDate;

        /**
         * 任职结束日期
         */
        private LocalDate endDate;

        /**
         * 是否主职，参见yesorno
         */
        private String mainjobFlag;

        /**
         * 员工号
         */
        private String clerkCode;

        /**
         * NC人员工作信息PK
         */
        private String pkPsnjob;

        /**
         * 创建时间
         */
        private LocalDateTime createTime;

        /**
         * 创建人
         */
        private String creator;

        /**
         * 时间戳
         */
        private LocalDateTime ts;

        /**
         * 是否删除，默认0，参见yesorno
         */
        private String deleteFlag;

        /**
         * iHR兼任信息id
         */
        private String idIhr;
}
