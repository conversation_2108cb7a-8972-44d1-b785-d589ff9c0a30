package com.fls.master.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 产品分类表(BaseProductCategory)表实体类
 *
 * <AUTHOR>
 * @since 2024-11-05 11:15:02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_base_product_category")
public class BaseProductCategory implements Serializable {

    private static final long serialVersionUID = 1L;

     //主键
    @TableId(value = "id_productcate", type = IdType.ASSIGN_UUID)
    private String idProductcate;

     //编码
    private String code;

     //名称
    private String name;

     //标识名
    private String ename;

     //前缀名
    private String prefix;

     //编码长度
    private Integer lenCode;

     //流水号
    private Integer flowNum;

     //备注
    private String memo;

     //状态，1=未启用，2=已启用，3=已停用，参见basestatus
    private String status;

     //创建时间
    private LocalDateTime createTime;

     //创建人
    private String creator;

     //作废时间
    private LocalDateTime invalidTime;

     //作废人
    private String invalider;

     //时间戳
    private LocalDateTime ts;

     //是否删除，默认0，参见yesorno
    private String deleteFlag;

     //产品类型，1=整机，2=大件,3=附件
    private String productType;

     //适用整机
    private String vehicleLimits;

     //排序
    private Integer sort;

}
