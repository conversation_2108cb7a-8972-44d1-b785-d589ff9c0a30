package com.fls.master.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 资源交易类型表(BaseResTrantype)表实体类
 *
 * <AUTHOR>
 * @since 2024-11-05 15:12:28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_base_res_trantype")
public class BaseResTranType implements Serializable {

    private static final long serialVersionUID = 1L;

     //主键
    @TableId(value = "id_res_trantype", type = IdType.ASSIGN_UUID)
    private String idResTrantype;

     //资源主键
    private String idResource;

     //资源编码
    private String resourceCode;

     //交易类型
    private String tranType;

     //是否启用审批流，0=假，1=真，参见trueorfalse
    private String needprocFlag;

     //启用状态：1=未启用，2=已启用，3=已停用，默认1，参见basestatus
    private String status;

     //创建时间
    private LocalDateTime createTime;

     //创建人
    private String creator;

     //作废时间
    private LocalDateTime invalidTime;

     //作废人
    private String invalider;

     //时间戳
    private LocalDateTime ts;

     //是否删除：0=否，1=是，默认0，参见yesorno
    private String deleteFlag;

     //交易类型编码
    private String tranCode;

     //是否系统预置=否，1=是，参见yesorno
    private String presetFlag;

     //显示顺序
    private Integer displayOrder;

     //资源审核页url地址
    private String incalLink;

     //NC交易类型主键
    private String pkBilltypeid;

}
