package com.fls.master.service;

import com.fls.master.pojo.query.DepartmentQuery;
import com.fls.master.pojo.query.OrgQuery;
import com.fls.master.pojo.query.PersonCodeQuery;
import com.fls.master.pojo.query.PersonQuery;
import com.fls.master.pojo.query.PostQuery;
import com.fls.master.pojo.query.StatusQuery;
import com.fls.master.pojo.query.UserRoleQuery;
import com.fls.master.pojo.query.WhposQuery;
import com.fls.master.pojo.vo.DepartmentVO;
import com.fls.master.pojo.vo.JobVO;
import com.fls.master.pojo.vo.OrgVO;
import com.fls.master.pojo.vo.PersonVO;
import com.fls.master.pojo.vo.PostVO;
import com.fls.master.pojo.vo.UserVO;
import com.fls.master.pojo.vo.WhposVO;

import java.util.List;

/**
 * OrgStructManageService
 *
 * <AUTHOR>
 */
public interface OrgStructManageService {

    List<OrgVO> orgPublicList(OrgQuery query);

    List<OrgVO> orgPrivateList(OrgQuery query);

    List<DepartmentVO> departmentPublicList(DepartmentQuery query);

    List<DepartmentVO> departmentPrivateList(DepartmentQuery query);

    List<PostVO> postPublicList(PostQuery query);

    List<JobVO> jobPublicList(StatusQuery query);

    List<PersonVO> personPublicList(PersonQuery query);

    List<PersonVO> personPrivateList(PersonQuery query);

    List<PersonVO> personListByCode(PersonCodeQuery query);

    List<UserVO> userListByRole(UserRoleQuery query);

    /**
     * 查询货位列表
     * @param query 查询条件
     * @return 货位列表
     */
    List<WhposVO> whposList(WhposQuery query);
}
