package com.fls.master.service;

import java.util.List;

/**
 * PermissionService
 *
 * <AUTHOR>
 */
public interface PermissionService {

    /**
     * 根据用户id和菜单获取有权限的部门id
     *
     * @param userId
     * @param href
     * @return
     */
    List<String> getPrivateDepartmentId(String userId, String href);

    /**
     * 根据用户id和菜单获取有权限的员工id
     *
     * @param userId
     * @param href
     * @return
     */
    List<String> getPrivateOrgId(String userId, String href);

    /**
     * 根据用户id和菜单获取有权限的员工id
     *
     * @param userId
     * @param href
     * @return
     */
    List<String> getPrivatePersonId(String userId, String href);

    /**
     * 根据用户id和菜单获取有权限的员工id
     *
     * @param userId
     * @param href
     * @return
     */
    List<String> getPrivateBizunitId(String userId, String href);

    /**
     * 根据用户id、菜单、档案编码获取档案id
     *
     * @param userId 用户id
     * @param href 菜单
     * @param code 档案编码
     * @return 档案id
     */
    List<String> getPrivateResourceId(String userId, String href, String code);
}
