package com.fls.master.controller;

import com.fls.common.core.domain.ResponseData;
import com.fls.master.pojo.query.ArchiveQuery;
import com.fls.master.pojo.query.BillTypeQuery;
import com.fls.master.pojo.query.InvclassQuery;
import com.fls.master.pojo.query.ProductQuery;
import com.fls.master.pojo.query.ResTranTypeQuery;
import com.fls.master.pojo.query.StatusQuery;
import com.fls.master.pojo.vo.BaseMeasDocVO;
import com.fls.master.pojo.vo.BillTypeVO;
import com.fls.master.pojo.vo.BizTypeVO;
import com.fls.master.pojo.vo.BrandVO;
import com.fls.master.pojo.vo.FaCategoryVO;
import com.fls.master.pojo.vo.FiletypeVO;
import com.fls.master.pojo.vo.ForkliftModelsVO;
import com.fls.master.pojo.vo.InvClassVO;
import com.fls.master.pojo.vo.LeaseAssetTypeVO;
import com.fls.master.pojo.vo.ProductVO;
import com.fls.master.pojo.vo.ResFiletypeVO;
import com.fls.master.pojo.vo.ResTranTypeVO;
import com.fls.master.pojo.vo.TransModeVO;
import com.fls.master.service.CommonResourceManageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * CommonResourceManageController
 *
 * <AUTHOR>
 */
@Tag(name = "公共资源管理")
@Slf4j
@RestController
@RequestMapping("/common-resource-manage")
@RequiredArgsConstructor
public class CommonResourceManageController {

    private final CommonResourceManageService commonResourceManage;

    @Operation(summary = "公共资源类型查询-全量-发运方式列表查询")
    @PostMapping("/resource-category/public/trans-mode")
    public ResponseData<List<TransModeVO>> resourceCategoryPublicTransMode(@RequestBody StatusQuery query) {
        return ResponseData.ok(commonResourceManage.rcPublicTransMode(query));
    }

    @Operation(summary = "公共资源类型查询-全量-产品分类查询")
    @PostMapping("/resource-category/public/product")
    public ResponseData<List<ProductVO>> resourceCategoryPublicProduct(@RequestBody ProductQuery query) {
        return ResponseData.ok(commonResourceManage.rcPublicProduct(query));
    }


    @Operation(summary = "公共资源类型查询-全量-物料基本分类查询")
    @PostMapping("/resource-category/public/inv-class")
    public ResponseData<List<InvClassVO>> resourceCategoryPublicInvClass(@RequestBody InvclassQuery query) {
        return ResponseData.ok(commonResourceManage.rcPublicInvclass(query));
    }

    @Operation(summary = "公共资源类型查询-全量-品牌列表查询")
    @PostMapping("/resource-category/public/brand")
    public ResponseData<List<BrandVO>> resourceCategoryPublicBrand(@RequestBody StatusQuery query) {
        return ResponseData.ok(commonResourceManage.rcPublicBrand(query));
    }

    @Operation(summary = "公共资源类型查询-全量-叉车车型列表查询")
    @PostMapping("/resource-category/public/forklift-models")
    public ResponseData<List<ForkliftModelsVO>> resourceCategoryPublicForkliftModels(@RequestBody StatusQuery query) {
        return ResponseData.ok(commonResourceManage.rcPublicForkliftModels(query));
    }

    @Operation(summary = "公共资源类型查询-全量-计量单位查询")
    @PostMapping("/resource-category/public/meas-doc")
    public ResponseData<List<BaseMeasDocVO>> measDocList(@RequestBody ArchiveQuery query) {
        return ResponseData.ok(commonResourceManage.measDocList(query));
    }

    @Operation(summary = "单据类型-全量-单据类型列表查询")
    @PostMapping("/bill/public/bill-type")
    public ResponseData<List<BillTypeVO>> billPublicBillType(@RequestBody BillTypeQuery query) {
        return ResponseData.ok(commonResourceManage.billPublicBillType(query));
    }


    @Operation(summary = "业务类型-全量-业务类型列表查询")
    @PostMapping("/biz/public/biz-type")
    public ResponseData<List<BizTypeVO>> bizPublicBizType(@RequestBody StatusQuery query) {
        return ResponseData.ok(commonResourceManage.bizPublicBizType(query));
    }


    @Operation(summary = "资源交易类型-全量-资源交易类型查询")
    @PostMapping("/res-tran/public/res-tran-type")
    public ResponseData<List<ResTranTypeVO>> resTranPublicResTranType(@RequestBody ResTranTypeQuery query) {
        return ResponseData.ok(commonResourceManage.resTranPublicResTranType(query));
    }


    @Operation(summary = "资源文件类型-全量-资源文件类型查询")
    @PostMapping("/res-file/public/res-filetype")
    public ResponseData<List<ResFiletypeVO>> resFilePublicResFiletype(@RequestBody StatusQuery query) {
        return ResponseData.ok(commonResourceManage.resFilePublicResFiletype(query));
    }


    @Operation(summary = "资产类型-全量-租赁资产类型查询")
    @PostMapping("/asset/public/lease-asset-type")
    public ResponseData<List<LeaseAssetTypeVO>> assetPublicLeaseAssetType(@RequestBody StatusQuery query) {
        return ResponseData.ok(commonResourceManage.assetPublicLeaseAssetType(query));
    }


    @Operation(summary = "资产类型-全量-固定资产类型查询")
    @PostMapping("/asset/public/fa-category")
    public ResponseData<List<FaCategoryVO>> assetPublicFaCategory(@RequestBody StatusQuery query) {
        return ResponseData.ok(commonResourceManage.assetPublicFaCategory(query));
    }


    @Operation(summary = "文件类型-全量-文件类型查询")
    @PostMapping("/file/public/filetype")
    public ResponseData<List<FiletypeVO>> filePublicFiletype(@RequestBody StatusQuery query) {
        return ResponseData.ok(commonResourceManage.filePublicFiletype(query));
    }

}
