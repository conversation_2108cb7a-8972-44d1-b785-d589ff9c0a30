package com.fls.master.service;

import com.fls.master.entity.BaseSupplierEntity;
import com.fls.master.pojo.vo.SupplierAddressVO;
import com.fls.master.pojo.vo.SupplierLinkmanVO;
import com.github.yulichang.base.MPJBaseService;

import java.util.List;

/**
 * 供应商基本档案-service
 *
 * <AUTHOR>
 * @since  2024-11-07
 */
public interface BaseSupplierService extends MPJBaseService<BaseSupplierEntity>, BaseArchiveQueryService {
    /**
     * 查询客户地址信息列表
     * @param SupplierId 客户id
     * @return 对应客户的地址信息列表
     */
    List<SupplierAddressVO> querySupplierAddress(String SupplierId);

    /**
     * 查询客户联系人信息列表
     * @param SupplierId 客户id
     * @return 对应客户的地址信息列表
     */
    List<SupplierLinkmanVO> querySupplierLinkman(String SupplierId);
}
