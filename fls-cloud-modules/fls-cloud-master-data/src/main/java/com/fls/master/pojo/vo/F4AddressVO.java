package com.fls.master.pojo.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * F4AddressVO
 *
 * <AUTHOR>
 */
@Data
public class F4AddressVO {

    private String carId;

    private String address;

    private Long lastReportTime;

    private String latitude;

    private String longitude;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime messageTime;
}
