package com.fls.master.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * LA资料分类的额外参数表(LeaseDocumentParameters)表实体类
 *
 * <AUTHOR>
 * @since 2024-11-12 10:14:20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_lease_document_parameters")
public class LeaseDocumentParameters implements Serializable {

    private static final long serialVersionUID = 1L;

     //主键
    private String idAdditionParem;

     //分类id
    private String idDocCat;

     //参数标识
    private String ename;

     //名称
    private String name;

     //数据类型
    private String valstr;

     //输入长度
    private Integer vallen;

     //精度
    private Integer valacc;

     //1：必选
    private String seltiveFlag;

     //创建时间
    private LocalDateTime createTime;

     //创建人
    private String creator;

     //失效时间
    private LocalDateTime disableTime;

     //1:删除
    private String deleteFlag;

     //时间戳
    private LocalDateTime ts;

     //1:未启用，2：已启用，3：已停用
    private String status;

     //作废时间
    private LocalDateTime invalidTime;

     //作废人
    private String invalider;

     //自定义序号,与资料分类构成联合唯一键
    private String serialNum;

}
