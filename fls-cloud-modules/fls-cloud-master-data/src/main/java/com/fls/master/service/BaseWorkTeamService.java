package com.fls.master.service;

import com.fls.master.entity.BaseWorkTeamEntity;
import com.fls.master.pojo.query.BaseWorkTeamMemberQuery;
import com.fls.master.pojo.query.OwnerWorkTeamQuery;
import com.fls.master.pojo.vo.BaseWorkTeamMemberVO;
import com.fls.master.pojo.vo.BaseWorkTeamVO;
import com.github.yulichang.base.MPJBaseService;
import java.util.List;

/**
 * 工作班组表-service
 *
 * <AUTHOR>
 * @since  2024-11-12
 */
public interface BaseWorkTeamService extends MPJBaseService<BaseWorkTeamEntity>, BaseArchiveQueryService {
    /**
     * 获取工作班组成员
     * @param query 查询条件
     * @return 工作班组成员列表
     */
    List<BaseWorkTeamMemberVO> getWorkTeamMembers(BaseWorkTeamMemberQuery query);

    /**
     * 查询用户所在班组列表
     *
     * @param query 所在班组查询参数
     * @return 班组信息列表
     */
    List<BaseWorkTeamVO> getOwnerTeams(OwnerWorkTeamQuery query);
}
