package com.fls.master.convert;

import com.fls.master.entity.BaseBizunitEntity;
import com.fls.master.pojo.vo.BaseBizunitDetailVO;
import com.fls.master.pojo.vo.BaseBizunitVO;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
* 经营主体
*
* <AUTHOR>
* @since  2024-11-06
*/
@Mapper()
public interface BaseBizunitConvert {
    BaseBizunitConvert INSTANCE = Mappers.getMapper(BaseBizunitConvert.class);

    BaseBizunitEntity voToEntity(BaseBizunitVO vo);

    BaseBizunitVO entityToVo(BaseBizunitEntity entity);

    BaseBizunitDetailVO entityToDetailVo(BaseBizunitEntity entity);

    List<BaseBizunitVO> toVoList(List<BaseBizunitEntity> list);

}
