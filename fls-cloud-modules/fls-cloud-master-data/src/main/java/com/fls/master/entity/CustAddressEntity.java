package com.fls.master.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 客户收货地址表表
 *
 * <AUTHOR>
 * @since 2024-11-07
 */
@Data
@TableName("t_cust_address")
public class CustAddressEntity {
	/**
	* 主键
	*/
	@TableId(value = "id_custaddress", type = IdType.ASSIGN_UUID)
	private String idCustaddress;

	/**
	* NC客户收货地址主键
	*/
	private String pkAddress;

	/**
	* 所属客户
	*/
	private String idCustomer;

	/**
	* 客户销售组织
	*/
	private String idCustsaleorg;

	/**
	* 收货地址
	*/
	private String idAddress;

	/**
	* 收货单位
	*/
	private String receivingUnit;

	/**
	* 联系人
	*/
	private String idLinkman;

	/**
	* 是否为默认 0=否，1=是 参见yesorno
	*/
	private String defaultFlag;

	/**
	* 启用状态：1=未启用，2=已启用，3=已停用，默认1，参见basestatus
	*/
	private String status;

	/**
	* 创建时间
	*/
	private LocalDateTime createTime;

	/**
	* 创建人
	*/
	private String creator;

	/**
	* 失效时间
	*/
	private LocalDateTime disableTime;

	/**
	* 时间戳
	*/
	private LocalDateTime ts;

	/**
	* 是否删除：0=否，1=是，默认0，参见yesorno
	*/
	private String deleteFlag;

	/**
	* F4云围栏id
	*/
	private String fenceId;

	/**
	* 服务主体
	*/
	private String idService;

	/**
	* 外协单位
	*/
	private String idOcunit;

	/**
	* 客户分拨
	*/
	private String idCustallocation;

	/**
	* 开票客户
	*/
	private String idCustinvoice;

	/**
	* 期初标识，默认0
	*/
	private Integer initFlag;

	/**
	* 地址映射关系表主键值关联:t_cust_address_map
	*/
	private String idCustAddrmap;

}
