package com.fls.master.pojo.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;


/**
 * F4StatusVO
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class F4StatusVO {

    @Schema(description = "资产编码")
    private String laCode;

    @Schema(description = "故障状态,1-正常，0-异常")
    private String state;

    @Schema(description = "消息内容")
    private String message;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime data;;
}
