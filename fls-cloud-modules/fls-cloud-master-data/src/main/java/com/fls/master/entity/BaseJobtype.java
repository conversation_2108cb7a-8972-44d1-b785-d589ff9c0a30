package com.fls.master.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 职务类别表(BaseJobtype)表实体类
 *
 * <AUTHOR>
 * @since 2024-11-06 11:53:11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_base_jobtype")
public class BaseJobtype implements Serializable {

    private static final long serialVersionUID = 1L;

     //主键
    @TableId(value = "id_jobtype", type = IdType.ASSIGN_UUID)
    private String idJobtype;

     //上级职务类别
    private String idParentjobtype;

     //编码
    private String code;

     //名称
    private String name;

     //描述
    private String memo;

     //NC职务类别pk值
    private String pkJobtype;

     //状态，1=未启用，2=已启用，3=已停用，参见basestatus
    private String status;

     //创建时间
    private LocalDateTime createTime;

     //创建人
    private String creator;

     //时间戳
    private LocalDateTime ts;

     //是否删除，默认0，参见yesorno
    private String deleteFlag;

     //iHR职务类别id
    private String idIhr;

}
