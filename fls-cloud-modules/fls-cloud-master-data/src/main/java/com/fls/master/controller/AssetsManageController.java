package com.fls.master.controller;

import cn.hutool.core.bean.BeanUtil;
import com.fls.common.core.domain.ResponseData;
import com.fls.master.pojo.query.AssetsDetailsQuery;
import com.fls.master.pojo.query.AssetsQuery;
import com.fls.master.pojo.query.F4AddressQuery;
import com.fls.master.pojo.query.F4StatusQuery;
import com.fls.master.pojo.query.StatusQuery;
import com.fls.master.pojo.vo.AssetsDetailsVO;
import com.fls.master.pojo.vo.AssetsTypeVO;
import com.fls.master.pojo.vo.AssetsVO;
import com.fls.master.pojo.vo.F4AddressVO;
import com.fls.master.pojo.vo.F4StatusVO;
import com.fls.master.pojo.vo.PageResult;
import com.fls.master.service.AssetsManageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * AssetsManageController
 *
 * <AUTHOR>
 */
@Tag(name = "资产管理")
@Slf4j
@RestController
@RequestMapping("/assets-manage")
@RequiredArgsConstructor
public class AssetsManageController {

    private final AssetsManageService assetsManage;

    @Operation(summary = "全量-资产类型查询")
    @PostMapping("/assets-type/public/list")
    public ResponseData<List<AssetsTypeVO>> assetsTypePublicList(@RequestBody StatusQuery query) {
        return ResponseData.ok(assetsManage.assetsTypePublicList(query));
    }


    @Operation(summary = "全量-资产列表查询")
    @PostMapping("/assets/public/page")
    public ResponseData<PageResult<AssetsVO>> assetsPublicPage(@RequestBody AssetsQuery query) {
        return ResponseData.ok(assetsManage.assetsPublicPage(query));
    }


    @Operation(summary = "权限-资产列表查询")
    @PostMapping("/assets/private/page")
    public ResponseData<PageResult<AssetsVO>> assetsPrivatePage(@Validated @RequestBody AssetsQuery query) {
        return ResponseData.ok(assetsManage.assetsPrivatePage(query));
    }

    @Operation(summary = "资产详情查询")
    @PostMapping("/assets/public/details")
    public ResponseData<AssetsDetailsVO> assetsPrivateDetails(@RequestBody AssetsDetailsQuery query) {
        return ResponseData.ok(assetsManage.assetsPrivateDetails(query.getLaCode()));
    }

    @Operation(summary = "资产F4状态查询")
    @PostMapping("/assetsF4/public/status")
    public ResponseData<?> assetsF4PublicStatus(@RequestBody F4StatusQuery query) {
        Pair<String,F4StatusVO> result =  assetsManage.assetsF4PublicStatus(query.getLaCode());
        return ResponseData.ok(result.getLeft(),BeanUtil.beanToMap(result.getRight(), false, true));
    }

    @Operation(summary = "资产实时地址查询")
    @PostMapping("/assetsF4/public/address")
    public ResponseData<?> assetsF4PublicAddress(@RequestBody F4AddressQuery query) {
        Pair<String, F4AddressVO> result = assetsManage.assetsF4PublicAddress(query.getAssetId());
        return ResponseData.ok(result.getLeft(), BeanUtil.beanToMap(result.getRight(), false, true));
    }
}
