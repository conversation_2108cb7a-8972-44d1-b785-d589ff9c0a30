package com.fls.master.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 组织表(BaseOrg)表实体类
 *
 * <AUTHOR>
 * @since 2024-10-31 15:29:49
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_base_org")
public class BaseOrg implements Serializable {

    private static final long serialVersionUID = 1L;

     //组织主键
    @TableId(value = "id_org", type = IdType.ASSIGN_UUID)
    private String idOrg;

     //组织编码
    private String code;

     //内部编码
    private String innercode;

     //组织名称
    private String name;

     //组织简称
    private String shortname;

     //NC组织pk值
    private String pkOrg;

     //是否采购 0=否，1=是 参见yesorno
    private String purchaseFlag;

     //是否销售 0=否，1=是 参见yesorno
    private String salesFlag;

     //是否物流 0=否，1=是 参见yesorno
    private String trafficFlag;

     //是否财务 0=否，1=是 参见yesorno
    private String financeFlag;

     //是否库存 0=否，1=是 参见yesorno
    private String stockFlag;

     //是否人力资源 0=否，1=是 参见yesorno
    private String hrFlag;

     //是否行政 0=否，1=是 参见yesorno
    private String adminFlag;

     //是否独立法人 0=否，1=是 参见yesorno
    private String companyFlag;

     //组织类型  0=总公司， 1=分公司，2=子公司，3=孙公司，4=营业部，9=虚拟公司 参见org_type
    private String orgType;

     //父级组织
    private String idParentorg;

     //负责人
    private String orgManager;

     //分管领导
    private String orgLeader;

     //经纬度
    private String latLongAlt;

     //所属集团
    private String idGroup;

     //启用状态：1=未启用，2=已启用，3=已停用，默认1，参见basestatus
    private String status;

     //创建时间
    private LocalDateTime createTime;

     //创建人
    private String creator;

     //失效时间
    private LocalDateTime disableTime;

     //时间戳
    private LocalDateTime ts;

     //是否删除：0=否，1=是，默认0，参见yesorno
    private String deleteFlag;

     //开票组织
    private String idOrgInvoice;

     //注销标识
    private String cancelFlag;

     //iHR组织id
    private String idIhr;

     //排序
    private Integer sort;

     //付款银行账户融资请款用
    private String myPaymentAccount;

}

