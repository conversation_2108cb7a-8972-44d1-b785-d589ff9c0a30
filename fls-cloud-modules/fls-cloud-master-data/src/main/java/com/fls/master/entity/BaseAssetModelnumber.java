package com.fls.master.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * LA车型表(BaseAssetModelnumber)表实体类
 *
 * <AUTHOR>
 * @since 2024-11-12 15:17:33
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_base_asset_modelnumber")
public class BaseAssetModelnumber implements Serializable {

    private static final long serialVersionUID = 1L;


    @TableId(value = "id_modelnumber", type = IdType.ASSIGN_UUID)
    private String idModelnumber;

     //id_asset_type资产类型
    private String idAssetType;

     //品牌ID
    private String idBrand;

     //吨位ID
    private String idTonnage;

     //编号
    private String code;

     //名称
    private String name;

     //参数
    private String configJson;

     //启用状态：1=未启用，2=已启用，3=已停用，默认1，参见basestatus
    private String status;

     //创建时间
    private LocalDateTime createTime;


    private String creator;


    private LocalDateTime disableTime;

     //时间戳
    private LocalDateTime ts;

     //是否删除：0=否，1=是，默认0，参见yesorno
    private String deleteFlag;

     //原租赁系统车型ID
    private String oldSysid;

     //技术推荐
    private String techParam;

     //车型说明
    private String memo;

     //财务统计分类，参见assets_financeclass
    private String assetsFinanceclass;

     //是否有质保：0=否，1=是，默认0，参见yesorno
    private String qaFlag;

     //质保时长，单位：月
    private Integer qaMonths;

     //质保小时
    private Integer qaHours;

     //是否锂电：0=否，1=是，默认0，参见yesorno
    private String lithiumBatteryFlag;

}

