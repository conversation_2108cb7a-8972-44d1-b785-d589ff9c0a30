package com.fls.master.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * PersonVO
 *
 * <AUTHOR>
 */
@Data
public class PersonVO {

    @Schema(description = "主键")
    private String idPerson;

    @Schema(description = "编码")
    private String code;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "曾用名")
    private String usedname;

    @Schema(description = "用户id")
    private String idUser;

    @Schema(description = "性别 1=男，2=女，3=不详")
    private String sex;

    @Schema(description = "民族")
    private String nation;

    @Schema(description = "办公电话")
    private String officephone;

    @Schema(description = "手机")
    private String mobile;

    @Schema(description = "所属组织")
    private String idOrg;

    @Schema(description = "所属部门")
    private String idDepartment;

    @Schema(description = "所属岗位")
    private String idPost;

    @Schema(description = "NC人员基本信息pk值")
    private String pkPsndoc;

    @Schema(description = "启用状态：1=未启用，2=已启用，3=已停用")
    private String status;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "失效时间")
    private LocalDateTime disableTime;

    @Schema(description = "是否在岗")
    private String postFlag;

    @Schema(description = "所属职务")
    private String idJob;

    @Schema(description = "邮箱")
    private String email;

//    @Schema(description = "钉钉识别码id")
//    private String dingdid;

    @Schema(description = "企业邮箱")
    private String enterpriseMail;

//    @Schema(description = "iHR员工id")
//    private String idIhr;

//    字段id翻译
    @Schema(description = "所属集团名称")
    private String orgName;

    @Schema(description = "所属部门名称")
    private String departmentName;

    @Schema(description = "所属职务名称")
    private String jobName;

    @Schema(description = "所属职务名称")
    private String postName;
}
