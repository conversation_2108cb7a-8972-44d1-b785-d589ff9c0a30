package com.fls.master.pojo.vo;

import com.fls.master.entity.BaseRegion;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * RegionLevel
 *
 * <AUTHOR>
 */
@Data
public class RegionLevelVO {

    @Schema(description = "行政区划id")
    private String id;

    @Schema(description = "行政区划编码")
    private String pid;

    @Schema(description = "行政区划名称")
    private String name;

    public static RegionLevelVO of(BaseRegion source) {
        RegionLevelVO vo = new RegionLevelVO();
        vo.setId(source.getCode());
        vo.setPid(source.getIdParentregion());
        vo.setName(source.getName());
        return vo;
    }
}
