package com.fls.master.controller;

import com.fls.common.core.domain.ResponseData;
import com.fls.common.core.validate.QueryGroup;
import com.fls.master.pojo.dto.UpdateResFlowDTO;
import com.fls.master.pojo.query.ArchiveDetailQuery;
import com.fls.master.pojo.query.BaseResourceQuery;
import com.fls.master.pojo.query.ResCodeQuery;
import com.fls.master.pojo.query.StatusQuery;
import com.fls.master.pojo.vo.BaseResourceDetailVO;
import com.fls.master.pojo.vo.BaseResourceVO;
import com.fls.master.pojo.vo.ResDocCateVO;
import com.fls.master.pojo.vo.ResourceClassVO;
import com.fls.master.pojo.vo.ResourceCodeVo;
import com.fls.master.service.BaseResourceService;
import com.fls.master.service.ResourceManageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 资源表-web层服务
 *
 * <AUTHOR>
 * @since 2024-11-12
 */
@RestController
@Slf4j
@RequestMapping("/resource")
@Tag(name = "资源表-web层服务")
@RequiredArgsConstructor
public class ResourceController {

    private final BaseResourceService baseResourceService;

    private final ResourceManageService resourceManage;

    @Operation(summary = "全量资源表查询")
    @PostMapping("/list")
    public ResponseData<List<BaseResourceVO>> list(@RequestBody BaseResourceQuery query) {
        return ResponseData.ok(baseResourceService.queryAll(query));
    }

    @Operation(summary = "权限资源表查询")
    @PostMapping("/auth/list")
    public ResponseData<List<BaseResourceVO>> authList(@Validated(QueryGroup.class) @RequestBody BaseResourceQuery query) {
        return ResponseData.ok(baseResourceService.queryAuthArchives(query));
    }

    @Operation(summary = "资源表详情查询")
    @PostMapping("/detail")
    public ResponseData<BaseResourceDetailVO> detail(@RequestBody ArchiveDetailQuery query) {
        return ResponseData.ok(baseResourceService.queryArchiveDetail(query));
    }

    @Operation(summary = "全量-资源分类查询")
    @PostMapping("/resource-class/public/list")
    public ResponseData<List<ResourceClassVO>> resourceClassPublicList(@RequestBody StatusQuery query) {
        return ResponseData.ok(resourceManage.rcPublicList(query));
    }


    @Operation(summary = "全量-资源资料分类列表")
    @PostMapping("/resDocCate/public/list")
    public ResponseData<List<ResDocCateVO>> resDocCatePublicList(@RequestBody StatusQuery query) {
        return ResponseData.ok(resourceManage.resDocCatePublicList(query));
    }

    @Operation(summary = "获取资源编码信息")
    @PostMapping("/code")
    public ResponseData<ResourceCodeVo> queryResCode(@RequestBody ResCodeQuery query) {
        return ResponseData.ok(baseResourceService.queryResourceCode(query.getCode()));
    }

    @Operation(summary = "更新资源流水号")
    @PostMapping("/flow/update")
    public ResponseData<Void> updateFlowNum(@RequestBody UpdateResFlowDTO updateFlowDTO) {
        baseResourceService.updateResourceFlowNum(updateFlowDTO.getCode(), updateFlowDTO.getFlowNum());
        return ResponseData.ok();
    }
}
