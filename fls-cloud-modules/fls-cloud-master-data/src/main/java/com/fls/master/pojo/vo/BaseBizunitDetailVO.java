package com.fls.master.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 经营主体表详情信息
 *
 * <AUTHOR>
 * @since  2024-11-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "经营主体表详情信息")
public class BaseBizunitDetailVO extends ArchiveDetailVO {

    @Schema(description = "主键")
    private String idBizunit;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "显示顺序")
    private Integer displayOrder;

    @Schema(description = "内部编号")
    private String innercode;

    @Schema(description = "经营主体类型 1=终端，2=批发，3=外贸 参见bizunit_type")
    private Integer bizunitType;

    @Schema(description = "开始日期")
    private LocalDate beginDate;

    @Schema(description = "结束日期")
    private LocalDate endDate;

    @Schema(description = "所属集团")
    private String idGroup;

    @Schema(description = "启用状态：1=未启用，2=已启用，3=已停用，默认1，参见basestatus")
    private Integer status;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "创建人")
    private String creator;

    @Schema(description = "失效时间")
    private LocalDateTime disableTime;

    @Schema(description = "时间戳")
    private LocalDateTime ts;

    @Schema(description = "核算归属主体")
    private String idAccunit;

    @Schema(description = "所属组织")
    private String idOrg;

    @Schema(description = "所属地区名称")
    private String areaName;

    @Schema(description = "经理")
    private String idManager;

    @Schema(description = "维修经理")
    private String idManagerMaintenance;

    @Schema(description = "服务支持人员")
    private String idsServiceSpporter;

    @Schema(description = "业务支持人员")
    private String idsBusinessSpporter;

    @Schema(description = "商业维修库")
    private String idWarehouseCmaint;

    @Schema(description = "自行维修库")
    private String idWarehouseSrepair;

    @Schema(description = "租赁维修库")
    private String idWarehouseLmaint;

    @Schema(description = "直运库")
    private String idWarehouseDirect;

    @Schema(description = "配件库")
    private String idWarehouseParts;

    @Schema(description = "自检登记机关 checking_city_dep")
    private String checkingCityDep;

    @Schema(description = "社会信用代码")
    private String appunitcode;

    @Schema(description = "资料关联id")
    private String idLinkDoc;

    @Schema(description = "单据编码，没用的值，仅用于资料上传不报错")
    private String billCode;

    @Schema(description = "上牌单位邮政编码")
    private String usecompostcode;

    @Schema(description = "上牌单位地址")
    private String usecomplace;

    @Schema(description = "上牌单位固定电话")
    private String usecomtel;

    @Schema(description = "上牌单位地址行政编码")
    private String usecomregion;

    @Schema(description = "安全管理员")
    private String checkSecurity;

    @Schema(description = "安全管理员电话")
    private String checkSecurityTel;

    @Schema(description = "自检人员")
    private String checkSelf;

    @Schema(description = "中转库")
    private String idWarehouseTransfer;

    @Schema(description = "管理部门id")
    private String idManageDept;

    @Schema(description = "使用部门pk")
    private String pkUseDept;

}
