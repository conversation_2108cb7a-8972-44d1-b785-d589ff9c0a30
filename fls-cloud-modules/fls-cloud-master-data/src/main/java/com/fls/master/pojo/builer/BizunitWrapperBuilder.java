package com.fls.master.pojo.builer;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.fls.master.entity.BaseBizunitEntity;
import com.fls.master.entity.BaseGroup;
import com.fls.master.entity.BaseOrg;
import com.fls.master.pojo.query.ArchiveQuery;
import com.fls.master.pojo.query.BaseBizunitQuery;
import com.fls.master.pojo.vo.BaseBizunitVO;
import java.util.List;

public class BizunitWrapperBuilder extends BaseWrapperBuilder<BaseBizunitEntity> {
    /**
     * 构造器放在子类实现
     *
     * @return 构造器
     */
    public static BizunitWrapperBuilder builder() {
        return new BizunitWrapperBuilder();
    }

    /**
     * 添加所有业务条件
     */
    @Override
    public BizunitWrapperBuilder withQueryParameters(ArchiveQuery query) {
        BaseBizunitQuery tansQuery = (BaseBizunitQuery) query;
        wrapper.selectAll(BaseBizunitEntity.class)
            .selectAs(BaseGroup::getCode, BaseBizunitVO::getGroupCode)
            .selectAs(BaseGroup::getInnercode, BaseBizunitVO::getGroupInnerCode)
            .selectAs(BaseGroup::getName, BaseBizunitVO::getGroupName)
            .selectAs(BaseGroup::getPkGroup, BaseBizunitVO::getPkGroup)
            .selectAs(BaseOrg::getPkOrg, BaseBizunitVO::getBizPK)
            .selectAs(BaseOrg::getName, BaseBizunitVO::getOrgName)
            .leftJoin(BaseGroup.class, BaseGroup::getIdGroup,BaseBizunitEntity::getIdGroup)
            .leftJoin(BaseOrg.class, BaseOrg::getIdOrg,BaseBizunitEntity::getIdOrg)
            .like(ObjectUtil.isNotEmpty(tansQuery.getName()), BaseBizunitEntity::getName, tansQuery.getName())
            .like(ObjectUtil.isNotEmpty(tansQuery.getInnercode()), BaseBizunitEntity::getInnercode, tansQuery.getInnercode())
            .eq(ObjectUtil.isNotEmpty(tansQuery.getBizunitType()), BaseBizunitEntity::getBizunitType, tansQuery.getBizunitType())
            .eq(ObjectUtil.isNotEmpty(tansQuery.getIdOrg()), BaseBizunitEntity::getIdOrg, tansQuery.getIdOrg());
        return this;
    }

    /**
     * 添加权限条件
     */
    @Override
    public BizunitWrapperBuilder withAuthorization(List<String> authIds) {
        if (CollectionUtil.isNotEmpty(authIds)) {
            wrapper.in(BaseBizunitEntity::getIdBizunit, authIds);
        }
        return this;
    }
}
