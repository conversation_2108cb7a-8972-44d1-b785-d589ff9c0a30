package com.fls.master.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.fls.master.constant.ResourceCodeConst;
import com.fls.master.convert.BaseBizunitConvert;
import com.fls.master.entity.BaseBizunitEntity;
import com.fls.master.mapper.BaseBizunitMapper;
import com.fls.master.pojo.builer.BizunitWrapperBuilder;
import com.fls.master.pojo.query.ArchiveQuery;
import com.fls.master.pojo.query.BaseBizunitQuery;
import com.fls.master.pojo.vo.BaseBizunitDetailVO;
import com.fls.master.pojo.vo.BaseBizunitVO;
import com.fls.master.service.BaseBizunitService;
import com.fls.master.service.PermissionService;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import java.util.Collections;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 经营主体-service
 *
 * <AUTHOR>
 * @since 2024-11-06
 */
@Service
@Slf4j
@SuppressWarnings("unchecked")
public class BaseBizunitServiceImpl extends MPJBaseServiceImpl<BaseBizunitMapper, BaseBizunitEntity> implements BaseBizunitService {

    @Resource
    private PermissionService permissionService;

    @Override
    public List<BaseBizunitVO> queryAll(ArchiveQuery query) {
        BaseBizunitQuery tansQuery = (BaseBizunitQuery)query;
        MPJLambdaWrapper<BaseBizunitEntity> wrapper = BizunitWrapperBuilder.builder().withCommonConditions(tansQuery).withQueryParameters(tansQuery).build();
        return baseMapper.selectJoinList(BaseBizunitVO.class, wrapper);
    }

    @Override
    public List<BaseBizunitVO> queryAuthArchives(ArchiveQuery query) {
        String userId = query.getUserId();
        String href = query.getHref();
        List<String> privateOrgId = permissionService.getPrivateOrgId(userId, href);
        List<String> privateBizunitId = permissionService.getPrivateResourceId(userId, href, ResourceCodeConst.BIZUNIT_ARCHIVES_CODE);
        BaseBizunitQuery tansQuery = (BaseBizunitQuery)query;
        MPJLambdaWrapper<BaseBizunitEntity> wrapper = BizunitWrapperBuilder.builder().withCommonConditions(tansQuery).withQueryParameters(tansQuery).build();
        if (CollectionUtil.isNotEmpty(privateBizunitId) && CollectionUtil.isNotEmpty(privateOrgId)) {
            wrapper.and(w -> w.in(BaseBizunitEntity::getIdBizunit, privateBizunitId).or().in(BaseBizunitEntity::getIdOrg, privateOrgId));
        }
        else if (CollectionUtil.isNotEmpty(privateBizunitId)) {
            wrapper.in(BaseBizunitEntity::getIdBizunit, privateBizunitId);
        }
        else if (CollectionUtil.isNotEmpty(privateOrgId)) {
            wrapper.in(BaseBizunitEntity::getIdOrg, privateOrgId);
        }
        else {
            return Collections.emptyList();
        }
        return baseMapper.selectJoinList(BaseBizunitVO.class, wrapper);
    }

    @Override
    public BaseBizunitDetailVO queryArchiveDetail(String archiveId) {
        BaseBizunitEntity entity = baseMapper.selectById(archiveId);
        return BaseBizunitConvert.INSTANCE.entityToDetailVo(entity);
    }
}
