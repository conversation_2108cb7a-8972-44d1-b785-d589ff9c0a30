package com.fls.master.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
* 供应商基本档案基础信息
*
* <AUTHOR>
* @since  2024-11-07
*/
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "供应商基本档案基础信息")
public class BaseSupplierVO extends ArchiveVO {

	@Schema(description = "主键")
	private String idSupplier;

	@Schema(description = "编码")
	private String code;

	@Schema(description = "名称")
	private String name;

	@Schema(description = "简称")
	private String shortname;

	@Schema(description = "所属组织")
	private String idOrg;

    @Schema(description = "组织名称")
    private String orgName;

	@Schema(description = "供应商分类")
	private String idSupplierclass;

    @Schema(description = "供应商分类名称")
    private String className;

	@Schema(description = "上级供应商")
	private String idParentsupplier;

	@Schema(description = "对应组织")
	private String idFinanceorg;

	@Schema(description = "NC供应商档案主键")
	private String pkSupplier;

    @Schema(description = "启用状态：1=未启用，2=已启用，3=已停用，默认1，参见basestatus")
    private String status;
}
