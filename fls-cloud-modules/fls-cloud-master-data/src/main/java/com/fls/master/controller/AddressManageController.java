package com.fls.master.controller;

import cn.hutool.core.lang.tree.Tree;
import com.fls.common.core.domain.ResponseData;
import com.fls.master.pojo.dto.RemoveAddressDTO;
import com.fls.master.pojo.dto.SaveAddressDTO;
import com.fls.master.pojo.dto.UpdateAddressDTOS;
import com.fls.master.pojo.query.AddressQuery;
import com.fls.master.pojo.query.InnerAddressQuery;
import com.fls.master.pojo.query.RegionLevelQuery;
import com.fls.master.pojo.query.StatusQuery;
import com.fls.master.pojo.vo.AddressTypeVO;
import com.fls.master.pojo.vo.AddressVO;
import com.fls.master.pojo.vo.InnerAddressVo;
import com.fls.master.pojo.vo.RegionLevelVO;
import com.fls.master.service.AddressManageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * AddressManageController
 *
 * <AUTHOR>
 */

@Tag(name = "地址管理")
@Slf4j
@RestController
@RequestMapping("/address-manage")
@RequiredArgsConstructor
public class AddressManageController {

    private final AddressManageService addressManage;


    @Operation(summary = "全量-行政区划列表查询")
    @PostMapping("/region/public/tree")
    public ResponseData<List<Tree<String>>> regionPublicTree(@RequestBody StatusQuery query) {
        return ResponseData.ok(addressManage.regionPublicTree(query));
    }


    @Operation(summary = "级联行政区划查询")
    @PostMapping("/region/public/region-level")
    public ResponseData<List<RegionLevelVO>> regionPublicRegionLevel(@Validated @RequestBody RegionLevelQuery query) {
        return ResponseData.ok(addressManage.regionPublicRegionLevel(query));
    }

    @Operation(summary = "获取地址类型")
    @PostMapping("/address/public/type")
    public ResponseData<List<AddressTypeVO>> addressPublicType(@RequestBody StatusQuery query) {
        return ResponseData.ok(addressManage.addressPublicType(query));
    }


    @Operation(summary = "全量地址列表")
    @PostMapping("/address/public/list")
    public ResponseData<List<AddressVO>> addressPublicList(@RequestBody AddressQuery query) {
        return ResponseData.ok(addressManage.addressPublicList(query));
    }

    @Operation(summary = "权限地址列表")
    @PostMapping("/address/private/list")
    public ResponseData<List<AddressVO>> addressPrivateList(@Validated @RequestBody AddressQuery query) {
        return ResponseData.ok(addressManage.addressPrivateList(query));
    }

    @Operation(summary = "批量新增地址信息")
    @PostMapping("/address/saveBatch")
    public ResponseData<Boolean> saveBatchAddress(@Validated @RequestBody SaveAddressDTO dto) {
        return ResponseData.ok(addressManage.saveBatchAddress(dto.getAddressDto()));
    }

    @Operation(summary = "批量删除地址信息")
    @PostMapping("/address/removeBatch")
    public ResponseData<Boolean> removeBatchAddress(@RequestBody RemoveAddressDTO dto) {
        return ResponseData.ok(addressManage.removeBatchAddress(dto.getIds()));
    }


    @Operation(description = "批量更新收获地址")
    @PostMapping("/address/updateBatch")
    public ResponseData<Boolean> updateBatchAddress(@Validated @RequestBody UpdateAddressDTOS dto ) {
        return ResponseData.ok(addressManage.updateBatchAddress(dto.getAddressDto()));
    }

    @Operation(summary = "内部地址列表")
    @PostMapping("/address/inner/list")
    public ResponseData<List<InnerAddressVo>> innerAddressList(@RequestBody InnerAddressQuery query) {
        return ResponseData.ok(addressManage.innerAddressList(query));
    }
}
