package com.fls.master.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 权限表(BaseAuth)表实体类
 *
 * <AUTHOR>
 * @since 2024-10-31 15:26:40
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_base_auth")
public class BaseAuth implements Serializable {

    private static final long serialVersionUID = 1L;

     //主键
    @TableId(value = "id_auth", type = IdType.ASSIGN_UUID)
    private String idAuth;

     //编码
    private String code;

     //名称
    private String name;

     //备注
    private String memo;

     //类型 1=系统管理，2=业务 参见auth_type
    private String authType;

     //启用状态：1=未启用，2=已启用，3=已停用，默认1，参见basestatus
    private String status;

     //创建时间
    private LocalDateTime createTime;

     //创建人
    private String creator;

     //失效时间
    private LocalDateTime disableTime;

     //时间戳
    private LocalDateTime ts;

     //是否删除：0=否，1=是，默认0，参见yesorno
    private String deleteFlag;

     //url地址
    private String href;

     //图标
    private String icon;

     //父级权限
    private String idParentauth;

     //应用工程名
    private String projectName;

     //排序
    private Integer sort;

     //菜单类型： M目录 C菜单 F按钮
    private String menuType;

     //组件地址
    private String component;

     //打开方式： 0无 1组件 2内链 3外链
    private String openType;

     //链接地址
    private String link;

     //标签
    private String label;

     //标签名称
    private String labelName;

}

