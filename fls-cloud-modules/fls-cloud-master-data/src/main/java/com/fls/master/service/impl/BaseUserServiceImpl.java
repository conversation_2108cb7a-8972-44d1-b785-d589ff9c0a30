package com.fls.master.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fls.master.entity.BaseUser;
import com.fls.master.mapper.BaseUserMapper;
import com.fls.master.pojo.query.UserRoleQuery;
import com.fls.master.pojo.vo.UserVO;
import com.fls.master.service.BaseUserService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 员工表(BasePerson)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-10-31 15:29:49
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BaseUserServiceImpl extends ServiceImpl<BaseUserMapper, BaseUser> implements BaseUserService {

    private final BaseUserMapper baseUserMapper;

    @Override
    public List<UserVO> getUsersByRole(UserRoleQuery roleQuery) {
        return baseUserMapper.listByRole(roleQuery);
    }
}
