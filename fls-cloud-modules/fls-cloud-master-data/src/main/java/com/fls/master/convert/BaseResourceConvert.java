package com.fls.master.convert;

import com.fls.master.api.model.ResourceInfo;
import com.fls.master.entity.BaseResourceEntity;
import com.fls.master.pojo.vo.BaseResourceDetailVO;
import com.fls.master.pojo.vo.BaseResourceVO;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
* 资源表-Mapstruct
*
* <AUTHOR>
* @since  2024-11-12
*/
@Mapper
public interface BaseResourceConvert {
    BaseResourceConvert INSTANCE = Mappers.getMapper(BaseResourceConvert.class);

    BaseResourceEntity voToEntity(BaseResourceVO vo);

    BaseResourceVO entityToVo(BaseResourceEntity entity);

    BaseResourceDetailVO entityToDetailVo(BaseResourceEntity entity);

    List<BaseResourceVO> toVoList(List<BaseResourceEntity> list);

    ResourceInfo entityToInfo(BaseResourceEntity entity);
}
