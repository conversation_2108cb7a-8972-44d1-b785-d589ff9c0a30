package com.fls.master.service.impl;

import cn.hutool.core.util.StrUtil;
import com.fls.common.core.constant.CommonConstants;
import com.fls.master.entity.BaseBillType;
import com.fls.master.entity.BaseBizType;
import com.fls.master.entity.BaseBrand;
import com.fls.master.entity.BaseFiletype;
import com.fls.master.entity.BaseForkliftModels;
import com.fls.master.entity.BaseInvClass;
import com.fls.master.entity.BaseMeasdoc;
import com.fls.master.entity.BaseProductCategory;
import com.fls.master.entity.BaseResFiletype;
import com.fls.master.entity.BaseResTranType;
import com.fls.master.entity.BaseResourceEntity;
import com.fls.master.entity.BaseTransMode;
import com.fls.master.entity.BaseTrantype;
import com.fls.master.entity.FaCategory;
import com.fls.master.entity.LeaseAssetClass;
import com.fls.master.entity.LeaseAssetType;
import com.fls.master.pojo.query.ArchiveQuery;
import com.fls.master.pojo.query.BillTypeQuery;
import com.fls.master.pojo.query.InvclassQuery;
import com.fls.master.pojo.query.ProductQuery;
import com.fls.master.pojo.query.ResTranTypeQuery;
import com.fls.master.pojo.query.StatusQuery;
import com.fls.master.pojo.vo.BaseMeasDocVO;
import com.fls.master.pojo.vo.BillTypeVO;
import com.fls.master.pojo.vo.BizTypeVO;
import com.fls.master.pojo.vo.BrandVO;
import com.fls.master.pojo.vo.FaCategoryVO;
import com.fls.master.pojo.vo.FiletypeVO;
import com.fls.master.pojo.vo.ForkliftModelsVO;
import com.fls.master.pojo.vo.InvClassVO;
import com.fls.master.pojo.vo.LeaseAssetTypeVO;
import com.fls.master.pojo.vo.ProductVO;
import com.fls.master.pojo.vo.ResFiletypeVO;
import com.fls.master.pojo.vo.ResTranTypeVO;
import com.fls.master.pojo.vo.TransModeVO;
import com.fls.master.service.BaseBillTypeService;
import com.fls.master.service.BaseBizTypeService;
import com.fls.master.service.BaseBrandService;
import com.fls.master.service.BaseFiletypeService;
import com.fls.master.service.BaseForkliftModelsService;
import com.fls.master.service.BaseInvClassService;
import com.fls.master.service.BaseMeasDocService;
import com.fls.master.service.BaseProductCategoryService;
import com.fls.master.service.BaseResFiletypeService;
import com.fls.master.service.BaseResTranTypeService;
import com.fls.master.service.BaseTransModeService;
import com.fls.master.service.CommonResourceManageService;
import com.fls.master.service.FaCategoryService;
import com.fls.master.service.LeaseAssetTypeService;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * CommonResourceManageServiceImpl
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class CommonResourceManageServiceImpl implements CommonResourceManageService {

    private final BaseTransModeService transMode;

    private final BaseProductCategoryService productCategory;

    private final BaseInvClassService invClass;

    private final BaseBrandService brand;

    private final BaseForkliftModelsService forkliftModels;

    private final BaseBillTypeService billType;

    private final BaseBizTypeService bizType;

    private final BaseResFiletypeService resFiletype;

    private final BaseResTranTypeService resTranType;

    private final BaseFiletypeService filetype;

    private final LeaseAssetTypeService leaseAssetType;

    private final FaCategoryService faCategory;

    private final BaseMeasDocService baseMeasDocService;

    @Override
    public List<TransModeVO> rcPublicTransMode(StatusQuery query) {
        List<BaseTransMode> list = transMode.lambdaQuery()
            .eq(BaseTransMode::getDeleteFlag, CommonConstants.DELETE_FLAG_NOT_DELETED)
            .eq(Objects.nonNull(query.getStatus()), BaseTransMode::getStatus, query.getStatus())
            .list();
        return list.stream()
            .map(TransModeVO::of)
            .collect(Collectors.toList());
    }


    @Override
    public List<ProductVO> rcPublicProduct(ProductQuery query) {
        List<BaseProductCategory> list = productCategory.lambdaQuery()
            .eq(BaseProductCategory::getDeleteFlag, CommonConstants.DELETE_FLAG_NOT_DELETED)
            .eq(Objects.nonNull(query.getStatus()), BaseProductCategory::getStatus, query.getStatus())
            .like(StrUtil.isNotBlank(query.getName()), BaseProductCategory::getName, query.getName())
            .list();
        return list.stream()
            .map(ProductVO::of)
            .collect(Collectors.toList());
    }

    @Override
    public List<InvClassVO> rcPublicInvclass(InvclassQuery query) {
        List<BaseInvClass> list = invClass.lambdaQuery()
            .eq(BaseInvClass::getDeleteFlag, CommonConstants.DELETE_FLAG_NOT_DELETED)
            .eq(Objects.nonNull(query.getStatus()), BaseInvClass::getStatus, query.getStatus())
            .like(StrUtil.isNotBlank(query.getName()), BaseInvClass::getName, query.getName())
            .list();
        return list.stream()
            .map(InvClassVO::of)
            .collect(Collectors.toList());
    }

    @Override
    public List<BrandVO> rcPublicBrand(StatusQuery query) {
        List<BaseBrand> list = brand.lambdaQuery()
            .eq(BaseBrand::getDeleteFlag, CommonConstants.DELETE_FLAG_NOT_DELETED)
            .eq(Objects.nonNull(query.getStatus()), BaseBrand::getStatus, query.getStatus())
            .list();
        return list.stream()
            .map(BrandVO::of)
            .collect(Collectors.toList());
    }

    @Override
    public List<ForkliftModelsVO> rcPublicForkliftModels(StatusQuery query) {
        List<BaseForkliftModels> list = forkliftModels.lambdaQuery()
            .eq(BaseForkliftModels::getDeleteFlag, CommonConstants.DELETE_FLAG_NOT_DELETED)
            .eq(Objects.nonNull(query.getStatus()), BaseForkliftModels::getStatus, query.getStatus())
            .list();
        return list.stream()
            .map(ForkliftModelsVO::of)
            .collect(Collectors.toList());
    }

    @Override
    public List<BaseMeasDocVO> measDocList(ArchiveQuery query) {
        List<BaseMeasdoc> list = baseMeasDocService.lambdaQuery()
            .eq(BaseMeasdoc::getDeleteFlag, CommonConstants.DELETE_FLAG_NOT_DELETED)
            .eq(Objects.nonNull(query.getStatus()), BaseMeasdoc::getStatus, query.getStatus())
            .list();
        return list.stream()
            .map(BaseMeasDocVO::of)
            .collect(Collectors.toList());
    }

    @Override
    public List<BillTypeVO> billPublicBillType(BillTypeQuery query) {
        List<BaseBillType> list = billType.lambdaQuery()
            .eq(BaseBillType::getDeleteFlag, CommonConstants.DELETE_FLAG_NOT_DELETED)
            .like(StrUtil.isNotBlank(query.getName()), BaseBillType::getName, query.getName())
            .list();
        return list.stream()
            .map(BillTypeVO::of)
            .collect(Collectors.toList());
    }


    @Override
    public List<BizTypeVO> bizPublicBizType(StatusQuery query) {
        List<BaseBizType> list = bizType.lambdaQuery()
            .eq(BaseBizType::getDeleteFlag, CommonConstants.DELETE_FLAG_NOT_DELETED)
            .eq(Objects.nonNull(query.getStatus()), BaseBizType::getStatus, query.getStatus())
            .list();
        return list.stream()
            .map(BizTypeVO::of)
            .collect(Collectors.toList());
    }

    @Override
    public List<ResTranTypeVO> resTranPublicResTranType(ResTranTypeQuery query) {
        List<BaseResTranType> list = resTranType.lambdaQuery()
            .eq(BaseResTranType::getDeleteFlag, CommonConstants.DELETE_FLAG_NOT_DELETED)
            .eq(Objects.nonNull(query.getStatus()), BaseResTranType::getStatus, query.getStatus())
            .eq(StrUtil.isNotEmpty(query.getResourceCode()), BaseResTranType::getResourceCode, query.getResourceCode())
            .eq(StrUtil.isNotEmpty(query.getTranCode()), BaseResTranType::getTranCode, query.getTranCode())
            .like(StrUtil.isNotBlank(query.getTranType()), BaseResTranType::getTranType, query.getTranType())
            .orderByAsc(BaseResTranType::getDisplayOrder)
            .list();
        return list.stream()
            .map(ResTranTypeVO::of)
            .collect(Collectors.toList());
    }

    @Override
    public List<ResFiletypeVO> resFilePublicResFiletype(StatusQuery query) {
        MPJLambdaWrapper<BaseResFiletype> wrapper = new MPJLambdaWrapper<>();
        wrapper.selectAll(BaseResFiletype.class)
            .selectAs(BaseResourceEntity::getName, ResFiletypeVO::getResourceName)
            .leftJoin(BaseResourceEntity.class, BaseResourceEntity::getIdResource, BaseResFiletype::getIdResource)
            .eq(BaseResFiletype::getDeleteFlag, CommonConstants.DELETE_FLAG_NOT_DELETED)
            .eq(BaseResourceEntity::getDeleteFlag, CommonConstants.DELETE_FLAG_NOT_DELETED)
            .eq(Objects.nonNull(query.getStatus()), BaseResFiletype::getStatus, query.getStatus());

        return resFiletype.selectJoinList(ResFiletypeVO.class, wrapper);
    }

    @Override
    public List<FiletypeVO> filePublicFiletype(StatusQuery query) {
        List<BaseFiletype> list = filetype.lambdaQuery()
            .eq(BaseFiletype::getDeleteFlag, CommonConstants.DELETE_FLAG_NOT_DELETED)
            .eq(Objects.nonNull(query.getStatus()), BaseFiletype::getStatus, query.getStatus())
            .list();
        return list.stream()
            .map(FiletypeVO::of)
            .collect(Collectors.toList());
    }


    @Override
    public List<LeaseAssetTypeVO> assetPublicLeaseAssetType(StatusQuery query) {
        MPJLambdaWrapper<LeaseAssetType> wrapper = new MPJLambdaWrapper<>();
        wrapper.selectAll(LeaseAssetType.class)
            .selectAs(LeaseAssetClass::getName, LeaseAssetTypeVO::getAssetClassName)
            .leftJoin(LeaseAssetClass.class, LeaseAssetClass::getIdAssetClass, LeaseAssetType::getIdAssetClass)
            .eq(LeaseAssetType::getDeleteFlag, CommonConstants.DELETE_FLAG_NOT_DELETED)
            .eq(LeaseAssetClass::getDeleteFlag, CommonConstants.DELETE_FLAG_NOT_DELETED)
            .eq(Objects.nonNull(query.getStatus()), LeaseAssetType::getStatus, query.getStatus());

        return leaseAssetType.selectJoinList(LeaseAssetTypeVO.class, wrapper);
    }


    @Override
    public List<FaCategoryVO> assetPublicFaCategory(StatusQuery query) {
        MPJLambdaWrapper<FaCategory> wrapper = new MPJLambdaWrapper<>();
        wrapper.selectAll(FaCategory.class)
            .selectAs(BaseTrantype::getName, FaCategoryVO::getTrantypeName)
            .leftJoin(BaseTrantype.class, BaseTrantype::getIdTrantype, FaCategory::getIdTrantype)
            .eq(FaCategory::getDeleteFlag, CommonConstants.DELETE_FLAG_NOT_DELETED)
            .eq(BaseTrantype::getDeleteFlag, CommonConstants.DELETE_FLAG_NOT_DELETED)
            .eq(Objects.nonNull(query.getStatus()), FaCategory::getStatus, query.getStatus());

        return faCategory.selectJoinList(FaCategoryVO.class, wrapper);
    }
}
