package com.fls.master.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * AddressDTO
 *
 * <AUTHOR>
 */
@Data
public class AddressDTO {

    @Schema(description = "名称")
    @NotBlank(message = "名称不能为空")
    private String name;

    @Schema(description = "省份")
    @NotBlank(message = "省份不能为空")
    private String idProvince;

    @Schema(description = "城市")
    @NotBlank(message = "城市不能为空")
    private String idCity;

    @Schema(description = "县区")
    @NotBlank(message = "县区不能为空")
    private String idVsection;

    @Schema(description = "地址详细")
    @NotBlank(message = "详细地址不能为空")
    private String detail;

    @Schema(description = "纬度")
    @NotBlank(message = "纬度不能为空")
    private String latGaode;

    @Schema(description = "经度")
    @NotBlank(message = "经度不能为空")
    private String lngGaode;

    @Schema(description = "百度地图县区编码")
    private String districtCode;

    @Schema(description = "邮政编码")
    private String zipCode;

    @Schema(description = "地址类型编码")
    @NotBlank(message = "地址类型编码不能为空")
    private String addressType;

    @Schema(description = "用户Id")
    @NotBlank(message = "用户Id不能为空")
    private String userId;

}
