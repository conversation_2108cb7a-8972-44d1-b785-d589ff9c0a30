<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fls.master.mapper.BaseResourceMapper">

    <select id="queryResCode" resultType="com.fls.master.pojo.vo.ResourceCodeVo">
        SELECT tbs.flow_num AS flowNum,
               tbs.prefix,
               tbs.len_code AS lenCode,
               tbs.id_resource sourceId,
               tbs.resource_code AS code
        FROM t_base_resource tbr
                 INNER JOIN t_base_resourceclass tbrc ON tbrc.id_resourceclass = tbr.id_resourceclass
                 LEFT JOIN t_base_rescode tbs ON tbr.id_resource = tbs.id_resource
        WHERE
          tbr.code = #{code}
          AND tbr.STATUS = '2'
          AND tbr.delete_flag = '0'
          AND tbs.STATUS = '2'
    </select>

    <select id="queryResCodeById" resultType="com.fls.master.pojo.vo.ResourceCodeVo">
        SELECT tbs.flow_num AS flowNum,
               tbs.prefix,
               tbs.len_code AS lenCode,
               tbs.id_resource sourceId,
               tbs.resource_code AS code
        FROM t_base_resource tbr
                 INNER JOIN t_base_resourceclass tbrc ON tbrc.id_resourceclass = tbr.id_resourceclass
                 LEFT JOIN t_base_rescode tbs ON tbr.id_resource = tbs.id_resource
        WHERE
          tbr.id_resource = #{idRes}
          AND tbr.STATUS = '2'
          AND tbr.delete_flag = '0'
          AND tbs.STATUS = '2'
    </select>
</mapper>
