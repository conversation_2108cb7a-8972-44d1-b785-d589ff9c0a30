<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace= "com.fls.master.mapper.BaseAddressMapper">
    <select id="queryInnerAddress" resultType="com.fls.master.pojo.vo.InnerAddressVo">
        SELECT
        tia.id_address AS idAddress,
        tia.id_inneraddress as idInnerAddress,
        tia.receiving_unit AS receivingUnit,
        tia.id_linkman AS idLinkman,
        tia.id_org AS idOrg,
        tia.id_bizunit AS idBizunit,
        CONCAT(
            IFNULL(tia.receiving_unit, ''), '/',
            IFNULL(tbl.name, ''), '/',
            IFNULL(COALESCE(tbl.mobile, tbl.tel), ''), '/',
            IFNULL(tba.detail, '')
        ) AS detail,
        tbl.tel,
        tbl.mobile,
        tbl.name AS linkmanName
        FROM
        t_inner_address tia
        JOIN t_base_address tba ON tia.id_address = tba.id_address
        JOIN t_base_linkman tbl ON tia.id_linkman = tbl.id_linkman
        <where>
            <if test="query.idOrg != null and query.idOrg != ''">
                AND tia.id_org = #{query.idOrg}
            </if>
            <if test="query.idBizunit != null and query.idBizunit != ''">
                AND tia.id_bizunit = #{query.idBizunit}
            </if>
            <if test="query.keyword != null and query.keyword != ''">
                AND (
                tia.receiving_unit LIKE CONCAT('%', #{query.keyword}, '%')
                OR tbl.name LIKE CONCAT('%', #{query.keyword}, '%')
                OR tba.detail LIKE CONCAT('%', #{query.keyword}, '%')
                OR tbl.tel LIKE CONCAT('%', #{query.keyword}, '%')
                OR tbl.mobile LIKE CONCAT('%', #{query.keyword}, '%')
                )
            </if>
            AND tia.status = '2'
            AND tia.delete_flag = '0'
        </where>
    </select>
</mapper>
