<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fls.master.mapper.BaseWorkTeamMapper">

    <select id="selectWorkTeamsByUserId" resultType="com.fls.master.entity.BaseWorkTeamEntity">
        SELECT tbwt.*
        FROM t_base_workteam tbwt,
             t_base_workteam_member tbwm,
             t_base_person tbp,
             t_base_user tbu
        where tbp.id_person = tbwm.id_member
          and tbu.id_user = #{userId}
          and tbp.delete_flag = '0'
          AND tbwm.id_workteam = tbwt.id_workteam
          and tbp.status = '2'
          and tbwm.status = '2'
          and tbwt.delete_flag = '0'
          AND tbp.code = tbu.code
        UNION ALL
        SELECT tbwt.*
        FROM t_base_workteam tbwt,
            t_base_person tbp,
             t_base_user tbu
        WHERE tbp.id_person = tbwt.id_headman
          and tbu.id_identity = tbp.id_person
         and tbu.id_user = #{userId}
    </select>
    <select id="getBaseInfoByWorkTeamId" resultType="com.fls.master.api.model.WorkTeamBaseInfo"
            parameterType="java.lang.String">
        SELECT team.*,
               wh.pk_stordoc as pkWarehouse,
               wh.name as warehouseName,
               whpos.pk_rack as pkWhpos,
               whpos.name as whposName,
               org.name      as orgName,
               bizunit.name  as bizunitName
        FROM t_base_workteam team,
             t_base_warehouse wh,
             t_base_whpos whpos,
             t_base_org org,
             t_base_bizunit bizunit
        where team.id_warehouse = wh.id_warehouse
          AND team.id_org = org.id_org
          AND team.id_bizunit = bizunit.id_bizunit
          AND wh.id_warehouse = whpos.id_warehouse
          AND team.id_whpos = whpos.id_whpos
          AND team.id_workteam = #{workTeamId}
    </select>
    <select id="getBaseInfoByWorkTeamIds" resultType="com.fls.master.api.model.WorkTeamBaseInfo">
        SELECT team.*,
        wh.pk_stordoc as pkWarehouse,
        wh.code as warehouseCode,
        wh.name as warehouseName,
        whpos.pk_rack as pkWhpos,
        whpos.code as whposCode,
        whpos.name as whposName,
        tbp.name as headmanName,
        org.name as orgName,
        bizunit.name as bizunitName
        FROM t_base_workteam team,
        t_base_warehouse wh,
        t_base_whpos whpos,
        t_base_org org,
        t_base_bizunit bizunit,
        t_base_person tbp
        where team.id_warehouse = wh.id_warehouse
        AND team.id_org = org.id_org
        AND team.id_bizunit = bizunit.id_bizunit
        AND wh.id_warehouse = whpos.id_warehouse
        AND team.id_whpos = whpos.id_whpos
        AND tbp.id_person = team.id_headman
        <if test="workTeamIds != null and workTeamIds.size() > 0">
            AND team.id_workteam IN
            <foreach item="workTeamId" collection="workTeamIds" open="(" separator="," close=")">
                #{workTeamId}
            </foreach>
        </if>
        AND team.delete_flag = '0'
    </select>
</mapper>
