<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fls.master.mapper.BaseUserMapper">
    <select id="listByRole" resultType="com.fls.master.pojo.vo.UserVO"
            parameterType="com.fls.master.pojo.query.UserRoleQuery">
        SELECT tbu.*,tbp.id_org,tbp.id_bizunit,tbr.id_role,tbr.code as role_code,tbr.name as role_name from
        t_base_userrole tbur
        LEFT JOIN t_base_user tbu on tbu.id_user=tbur.id_user
        LEFT JOIN t_base_role tbr on tbr.id_role=tbur.id_role
        LEFT JOIN t_base_person tbp on tbp.code = tbu.code
        <where>
            <if test="query.idOrg != null and query.idOrg != ''">
                AND tbp.id_org = #{query.idOrg}
            </if>
            <if test="query.idBizunit != null and query.idBizunit != ''">
                AND tbp.id_bizunit = #{query.idBizunit}
            </if>
            <if test="query.idRole != null and query.idRole != ''">
                AND tbur.id_role = #{query.idRole}
            </if>
            <if test="query.roleCode != null and query.roleCode != ''">
                AND tbr.code = #{query.roleCode}
            </if>
            AND tbu.status = '2'
            AND tbu.delete_flag = '0'
        </where>
    </select>
</mapper>
