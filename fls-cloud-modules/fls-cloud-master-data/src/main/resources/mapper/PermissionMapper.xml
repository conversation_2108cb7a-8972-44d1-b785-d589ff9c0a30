<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fls.master.mapper.PermissionMapper">

    <select id="getPrivateDepartmentId" resultType="java.lang.String">
        SELECT DISTINCT(roledata.id_department)
        FROM t_base_roledata roledata
        where roledata.delete_flag = '0' and roledata.id_department is not null
        <if test="roles != null and roles.size() > 0">
            AND roledata.id_role IN
            <foreach item="role" collection="roles" open="(" separator="," close=")">
                #{role}
            </foreach>
        </if>
    </select>

    <select id="getPrivateOrgId" resultType="java.lang.String">
        SELECT DISTINCT(roledata.id_org)
        FROM t_base_roledata roledata
        where roledata.delete_flag = '0' and roledata.id_org is not null
        <if test="roles != null and roles.size() > 0">
            AND roledata.id_role IN
            <foreach item="role" collection="roles" open="(" separator="," close=")">
                #{role}
            </foreach>
        </if>
    </select>


    <select id="getPrivatePersonId" resultType="java.lang.String">
        SELECT DISTINCT(bu.id_identity)
        from t_base_user bu
                 join t_base_userrole userrole on bu.id_user = userrole .id_user
        where bu.delete_flag = '0' and userrole.delete_flag = '0' and bu.id_identity is not null
        <if test="roles != null and roles.size() > 0">
            AND userrole.id_role IN
            <foreach item="role" collection="roles" open="(" separator="," close=")">
                #{role}
            </foreach>
        </if>
    </select>


    <select id="getRoleIdsFromUserIdAndHref" resultType="java.lang.String">
        SELECT distinct(roleauth.id_role)
        from t_base_roleauth roleauth
                 JOIN t_base_auth auth on roleauth.id_auth = auth.id_auth
                 join t_base_userrole userrole on userrole.id_role = roleauth.id_role
        where roleauth.delete_flag = '0' and auth.delete_flag = '0' and userrole.delete_flag = '0'
            and roleauth.id_role is not null
            and auth.href = #{href} and userrole.id_user = #{userId}
    </select>

    <select id="getPrivateBizunitId" resultType="java.lang.String">
        SELECT DISTINCT
            (bizauth.id_bizunit)
        FROM
            t_base_roledata_bizunit bizauth
                JOIN t_base_roleauth roleauth ON bizauth.id_role = roleauth.id_role
                JOIN t_base_auth auth ON roleauth.id_auth = auth.id_auth
                JOIN t_base_userrole userrole ON userrole.id_role = roleauth.id_role
        WHERE
            roleauth.delete_flag = '0'
          AND auth.delete_flag = '0'
          AND userrole.delete_flag = '0'
          AND roleauth.id_role IS NOT NULL
          AND auth.href = #{href}
          AND userrole.id_user = #{userId}
    </select>

    <select id="getPrivateResourceId" resultType="java.lang.String">
        SELECT
        DISTINCT
        (docdata.id_resdoc)
        FROM
        t_base_role_docdata docdata
        JOIN t_base_roleauth roleauth ON docdata.id_role = roleauth.id_role
        JOIN t_base_auth auth ON roleauth.id_auth = auth.id_auth
        JOIN t_base_userrole userrole ON userrole.id_role = roleauth.id_role
        JOIN t_base_resource resource ON docdata.id_resource = resource.id_resource
        WHERE
        roleauth.delete_flag = '0'
        AND auth.delete_flag = '0'
        AND userrole.delete_flag = '0'
        AND roleauth.id_role IS NOT NULL
        AND auth.href = #{href}
        AND userrole.id_user = #{userId}
        <if test="code!=null and code!=''">
            AND resource. CODE = #{code}
        </if>
    </select>
</mapper>
