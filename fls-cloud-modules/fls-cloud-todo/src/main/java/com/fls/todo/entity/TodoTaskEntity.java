package com.fls.todo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>
 * 待办任务
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_base_todo_task")
public class TodoTaskEntity {
    /**
     * 待办任务主键id
     */
    @TableId(value = "id_todo_task", type = IdType.ASSIGN_UUID)
    private String idTodoTask;

    /**
     * 来源系统标识
     */
    private String sourceProjectName;

    /**
     * 来源系统地址
     */
    private String sourceProjectUrl;

    /**
     * 资源访问菜单路径
     */
    private String visitPath;

    /**
     * 资源访问参数
     */
    private String visitParam;

    /**
     * 小程序资源访问菜单路径
     */
    private String appletVisitPath;

    /**
     * 小程序资源访问参数
     */
    private String appletVisitParam;

    /**
     * 来源系统任务id
     */
    private String idSourceRecord;

    /**
     * 来源单据编码
     */
    private String sourceRecordCode;

    /**
     * 任务编号
     */
    private String taskCode;


    /**
     * 交易类型编号
     */
    private String transCode;

    /**
     * 交易类型名称
     */
    private String transName;

    /**
     * 资源id
     */
    private String idResource;

    /**
     * 资源名称
     */
    private String resourceName;

    /**
     * 来源任务名称
     */
    private String recordName;

    /**
     * 待办任务状态，0：待审批，1：待办理，2：进行中，3：已办理，4：已关闭
     */
    private String taskStatus;

    /**
     * 处理状态，0：未阅，1：已阅，2：待处理，3：已处理
     */
    private String handleStatus;

    /**
     * 任务类型
     */
    private String taskType;

    /**
     * 分配类型
     */
    private String assigneeType;

    /**
     * 任务办理人
     */
    private String idAssignee;

    /**
     * 经营主体id
     */
    private String idBizunit;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 发起人
     */
    private String initiator;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 删除标记: 0-正常, 1-删除
     */
    private String deleteFlag;

    /**
     * 创建时间戳
     */
    private LocalDateTime ts;
}
