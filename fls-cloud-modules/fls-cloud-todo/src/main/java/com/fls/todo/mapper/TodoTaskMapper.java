package com.fls.todo.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fls.todo.api.model.TodoTaskQuery;
import com.fls.todo.api.model.TodoTaskVo;
import com.fls.todo.entity.TodoTaskEntity;
import com.fls.todo.pojo.query.ApproveTaskQuery;
import com.fls.todo.pojo.vo.ApproveTaskVo;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 待办任务 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-27
 */
public interface TodoTaskMapper extends BaseMapper<TodoTaskEntity> {

    /**
     * 分页查询待办任务
     *
     * @param page  分页参数
     * @param query 查询条件
     * @return 待办任务列表
     */
    Page<TodoTaskVo> selectTodoTaskPage(Page<TodoTaskVo> page, @Param("query") TodoTaskQuery query);

    /**
     * 分页查询待审任务分页
     *
     * @param page 分页参数
     * @param query 查询参数
     * @return 分页解雇
     */
    Page<ApproveTaskVo> selectApproveTaskPage(Page<ApproveTaskVo> page, @Param("query") ApproveTaskQuery query);
}
