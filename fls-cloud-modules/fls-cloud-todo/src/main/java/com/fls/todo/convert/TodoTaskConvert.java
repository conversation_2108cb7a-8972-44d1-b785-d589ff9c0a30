package com.fls.todo.convert;

import com.fls.todo.api.model.TodoMessage;
import com.fls.todo.api.model.TodoTaskVo;
import com.fls.todo.entity.TodoTaskEntity;
import com.fls.todo.pojo.dto.TodoTaskDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * TodoTaskConvert
 *
 * <AUTHOR>
 * @since 2025-01-02
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface TodoTaskConvert {

    TodoTaskConvert INSTANCE = Mappers.getMapper(TodoTaskConvert.class);

    @Mappings({
        @Mapping(source = "sourceProjectName", target = "sourceProjectName"),
        @Mapping(source = "sourceProjectUrl", target = "sourceProjectUrl"),
        @Mapping(source = "visitPath", target = "visitPath"),
        @Mapping(source = "visitParam", target = "visitParam"),
        @Mapping(source = "appletVisitPath", target = "appletVisitPath"),
        @Mapping(source = "appletVisitParam", target = "appletVisitParam"),
        @Mapping(source = "idSourceRecord", target = "idSourceRecord"),
        @Mapping(source = "todoTaskName", target = "recordName"),
        @Mapping(source = "taskStatus", target = "taskStatus"),
        @Mapping(source = "assignees", target = "idAssignee"),
        @Mapping(source = "initiator", target = "initiator"),
        @Mapping(source = "createBy", target = "createBy"),
        @Mapping(source = "updateBy", target = "updateBy"),
    })
    TodoTaskEntity messageToTodoTaskEntity(TodoMessage message);

    @Mappings({
        @Mapping(source = "sourceProjectName", target = "sourceProjectName"),
        @Mapping(source = "sourceProjectUrl", target = "sourceProjectUrl"),
        @Mapping(source = "visitPath", target = "visitPath"),
        @Mapping(source = "visitParam", target = "visitParam"),
        @Mapping(source = "appletVisitPath", target = "appletVisitPath"),
        @Mapping(source = "appletVisitParam", target = "appletVisitParam"),
        @Mapping(source = "idSourceRecord", target = "idSourceRecord"),
        @Mapping(source = "todoTaskName", target = "recordName"),
        @Mapping(source = "taskStatus", target = "taskStatus"),
        @Mapping(source = "assignees", target = "idAssignee"),
        @Mapping(source = "initiator", target = "initiator"),
        @Mapping(source = "createBy", target = "createBy"),
        @Mapping(source = "updateBy", target = "updateBy"),
        @Mapping(target = "sourceRecordCode", ignore = true),
        @Mapping(target = "updateTime", expression = "java(java.time.LocalDateTime.now())"),
    })
    void updateTodoTask(TodoMessage message, @MappingTarget TodoTaskEntity todoTask);

    @Mappings({

    })
    TodoTaskVo entityToVo(TodoTaskEntity entity);

    List<TodoTaskVo> entityListToVoList(List<TodoTaskEntity> entityList);

    @Mappings({
        @Mapping(source = "sourceProjectName", target = "sourceProjectName"),
        @Mapping(source = "sourceProjectUrl", target = "sourceProjectUrl"),
        @Mapping(source = "visitPath", target = "visitPath"),
        @Mapping(source = "visitParam", target = "visitParam"),
        @Mapping(source = "appletVisitPath", target = "appletVisitPath"),
        @Mapping(source = "appletVisitParam", target = "appletVisitParam"),
        @Mapping(source = "idInstance", target = "idSourceRecord"),
        @Mapping(source = "todoTaskName", target = "recordName"),
        @Mapping(source = "assignees", target = "idAssignee"),
    })
    TodoTaskEntity dtoToTodoTaskEntity(TodoTaskDto dto);
}
