package com.fls.todo.pojo.vo;

import lombok.Data;

/**
 * 待审任务查询结果
 *
 * <AUTHOR>
 * @since 2025-08-06
 */
@Data
public class ApproveTaskVo {

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 资源id
     */
    private String idResource;

    /**
     * 资源编号
     */
    private String resCode;

    /**
     * 资源名称
     */
    private String resName;

    /**
     * 交易类型编号
     */
    private String transCode;

    /**
     * 交易类型名称
     */
    private String transName;

    /**
     * 单据id
     */
    private String billId;

    /**
     * 单据编号
     */
    private String billCode;

    /**
     * 经营主体id
     */
    private String idBizunit;

    /**
     * 经营主体名称
     */
    private String bizunitName;

    /**
     * 发起人用户id
     */
    private String initiator;

    /**
     * 发起人用户名称
     */
    private String initiatorName;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 流程名称
     */
    private String processName;

    /**
     * 任务定义key
     */
    private String taskDefinitionKey;

    /**
     * 待办任务状态
     */
    private String taskStatus;

    /**
     * 任务状态描述
     */
    private String taskStatusDesc;

    /**
     * 待办任务处理状态
     */
    private String handleStatus;

    /**
     * 待办任务处理状态描述
     */
    private String handleStatusDesc;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 小程序资源访问参数
     */
    private String appletVisitParam;
    /**
     * 小程序资源访问菜单路径
     */
    private String appletVisitPath;

    /**
     * 资源访问参数
     */
    private String visitParam;
    /**
     * 资源访问菜单路径
     */
    private String visitPath;
}
