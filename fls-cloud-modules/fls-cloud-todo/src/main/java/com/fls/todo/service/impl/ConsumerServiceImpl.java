package com.fls.todo.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.fls.common.core.constant.CommonConstants;
import com.fls.master.api.RemoteResourceService;
import com.fls.master.api.model.ResourceInfo;
import com.fls.todo.api.enums.TodoTaskStatusEnum;
import com.fls.todo.api.model.TodoMessage;
import com.fls.todo.convert.TodoTaskConvert;
import com.fls.todo.entity.TodoTaskEntity;
import com.fls.todo.service.IConsumerService;
import com.fls.todo.service.ITodoTaskService;
import com.fls.workorder.enums.AssigneeTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * ConsumerServiceImpl
 *
 * <AUTHOR>
 * @since 2025-01-02
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ConsumerServiceImpl implements IConsumerService {

    private final ITodoTaskService todoTaskService;

    private final TodoTaskConvert todoTaskConvert;

    @DubboReference
    private RemoteResourceService remoteResourceService;

    @Override
    public void consumeMsg(TodoMessage todoMessage) {
        String assignees = todoMessage.getAssignees();
        if (StrUtil.isNotBlank(assignees) && assignees.contains(StrUtil.COMMA)) {
            // 拆分assignees并递归处理每个办理人
            List<String> assigneesList = StrUtil.split(assignees, ",");
            for (String assignee : assigneesList) {
                // 创建新的消息对象避免修改原对象
                TodoMessage singleAssigneeMessage = new TodoMessage();
                BeanUtil.copyProperties(todoMessage, singleAssigneeMessage);
                singleAssigneeMessage.setAssignees(assignee.trim());
                consumeMsg(singleAssigneeMessage);
            }
            return;
        }
        String idSourceRecord = todoMessage.getIdSourceRecord();
        // 待办任务记录校验
        TodoTaskEntity todoTaskBySourceId = todoTaskService.getTodoTaskBySourceId(idSourceRecord, assignees);

        if (ObjectUtil.isNotNull(todoTaskBySourceId) && !TodoTaskStatusEnum.isComplete(todoTaskBySourceId.getTaskStatus())) {
            // 更新现有任务
            todoTaskConvert.updateTodoTask(todoMessage, todoTaskBySourceId);
        } else {
            // 创建新任务
            todoTaskBySourceId = todoTaskConvert.messageToTodoTaskEntity(todoMessage);
        }

        // 设置资源名称
        String resourceName = getResourceName(todoTaskBySourceId);
        todoTaskBySourceId.setResourceName(resourceName);

        // 更新或创建主办人任务
        todoTaskService.saveOrUpdate(todoTaskBySourceId);

        // 处理协办人任务
        handleCoOperators(todoMessage, resourceName);
    }

    private void handleCoOperators(TodoMessage todoMessage, String resourceName) {
        String idSourceRecord = todoMessage.getIdSourceRecord();
        String coOperators = todoMessage.getCoOperators();
        if (StrUtil.isBlank(coOperators)) {
            // 如果协办人为空，需要删除所有现有的协办人任务
            List<TodoTaskEntity> existingCoTasks = todoTaskService.getCoOptTasksBySourceId(idSourceRecord);
            if (!existingCoTasks.isEmpty()) {
                existingCoTasks.forEach(task -> {
                    task.setDeleteFlag(CommonConstants.DELETE_FLAG_IS_DELETED);
                    task.setUpdateTime(LocalDateTime.now());
                });
                todoTaskService.updateBatchById(existingCoTasks);
            }
            return;
        }

        // 构建新的协办人任务列表
        List<TodoTaskEntity> coTodoTaskEntities = new ArrayList<>();
        List<String> idsCoOperators = StrUtil.split(coOperators, ",");

        for (String idsCoOperator : idsCoOperators) {
            idsCoOperator = idsCoOperator.trim();
            if (StrUtil.isBlank(idsCoOperator)) {
                continue;
            }

            // 创建协办人消息模板
            TodoMessage coOperatorMessage = new TodoMessage();
            BeanUtil.copyProperties(todoMessage, coOperatorMessage);
            coOperatorMessage.setAssignees(idsCoOperator);

            // 转换为实体并设置资源名称
            TodoTaskEntity todoTaskEntity = todoTaskConvert.messageToTodoTaskEntity(coOperatorMessage);
            todoTaskEntity.setResourceName(resourceName);
            todoTaskEntity.setAssigneeType(AssigneeTypeEnum.COOPERATE.getType());
            coTodoTaskEntities.add(todoTaskEntity);
        }

        // 获取现有的协办人任务
        List<TodoTaskEntity> coOptTasksBySourceId = todoTaskService.getCoOptTasksBySourceId(idSourceRecord);

        // 比对并处理任务变更
        processTaskChanges(coTodoTaskEntities, coOptTasksBySourceId);
    }

    private void processTaskChanges(List<TodoTaskEntity> newTasks, List<TodoTaskEntity> existingTasks) {
        // 构建现有任务的Map，key为办理人ID，value为任务实体
        Map<String, TodoTaskEntity> existTaskMap = existingTasks.stream()
            .collect(Collectors.toMap(TodoTaskEntity::getIdAssignee, task -> task));

        // 构建新任务的Map，key为办理人ID，value为任务实体
        Map<String, TodoTaskEntity> newTaskMap = newTasks.stream()
            .collect(Collectors.toMap(TodoTaskEntity::getIdAssignee, task -> task));

        List<TodoTaskEntity> toSaveOrUpdate = new ArrayList<>();
        List<TodoTaskEntity> toDelete = new ArrayList<>();

        // 处理新任务列表
        newTasks.forEach(newTask -> {
            String assigneeId = newTask.getIdAssignee();
            if (existTaskMap.containsKey(assigneeId)) {
                // 两个都存在，做更新操作
                TodoTaskEntity existTask = existTaskMap.get(assigneeId);
                newTask.setIdTodoTask(existTask.getIdTodoTask());
                toSaveOrUpdate.add(newTask);
            } else {
                // 新任务列表特有的，做新增操作
                toSaveOrUpdate.add(newTask);
            }
        });

        // 处理原有任务中不在新任务列表中的，做软删除操作
        existingTasks.forEach(existTask -> {
            String assigneeId = existTask.getIdAssignee();
            if (!newTaskMap.containsKey(assigneeId)) {
                // 原有任务列表特有的，做软删除操作
                existTask.setDeleteFlag("1");
                existTask.setUpdateTime(LocalDateTime.now());
                toDelete.add(existTask);
            }
        });

        // 批量保存或更新
        if (!toSaveOrUpdate.isEmpty()) {
            todoTaskService.saveOrUpdateBatch(toSaveOrUpdate);
        }

        // 批量软删除
        if (!toDelete.isEmpty()) {
            todoTaskService.updateBatchById(toDelete);
        }
    }

    private String getResourceName(TodoTaskEntity todoTask) {
        if (StrUtil.isNotBlank(todoTask.getResourceName())) {
            return todoTask.getResourceName();
        }
        if (StrUtil.isBlank(todoTask.getIdResource())) {
            return "";
        }
        try {
            ResourceInfo resourceById = remoteResourceService.getResourceById(todoTask.getIdResource());
            return resourceById == null ? "" : resourceById.getName();
        } catch (Exception e) {
            log.warn("获取资源名称失败，资源ID: {}, 错误: {}", todoTask.getIdResource(), e.getMessage());
            return "";
        }
    }
}
