package com.fls.todo;

import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.metrics.buffering.BufferingApplicationStartup;

/**
 * 待办模块启动入口
 *
 * <AUTHOR>
 * @since 2025-01-02
 */
@SpringBootApplication
@EnableDubbo
public class FlsTodoApplication {
    public static void main(String[] args) {
        SpringApplication application = new SpringApplication(FlsTodoApplication.class);
        application.setApplicationStartup(new BufferingApplicationStartup(2048));
        application.run(args);
    }
}
