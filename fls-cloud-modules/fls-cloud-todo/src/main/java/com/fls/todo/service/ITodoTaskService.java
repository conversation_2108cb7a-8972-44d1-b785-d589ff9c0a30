package com.fls.todo.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fls.common.mybatis.core.page.PageResult;
import com.fls.todo.pojo.dto.TaskResumeDto;
import com.fls.todo.api.model.TodoTaskQuery;
import com.fls.todo.entity.TodoTaskEntity;
import com.fls.todo.pojo.dto.TaskCancelDto;
import com.fls.todo.pojo.dto.TaskCompleteDto;
import com.fls.todo.pojo.dto.TaskHandleDto;
import com.fls.todo.pojo.dto.TaskSuspendDto;
import com.fls.todo.pojo.dto.TaskTransferDto;
import com.fls.todo.pojo.dto.TodoTaskDto;
import com.fls.todo.pojo.query.ApproveTaskQuery;
import com.fls.todo.pojo.query.StaticQuery;
import com.fls.todo.pojo.vo.TaskMonthStaticVo;
import com.fls.todo.pojo.vo.TodoTaskStaticVo;

import java.util.List;

/**
 * 待办任务服务接口
 *
 * <AUTHOR>
 * @since 2025-01-02
 */
public interface ITodoTaskService extends IService<TodoTaskEntity> {
    /**
     * 根据源ID获取待办任务
     *
     * @param idSource 源ID
     * @param assignee 办理人用户id
     * @return 待办任务实体
     */
    TodoTaskEntity getTodoTaskBySourceId(String idSource, String assignee);

    /**
     * 根据查询条件获取待办任务列表
     *
     * @param query 查询条件
     * @return 待办任务列表
     */
    PageResult<?> queryTodoTasks(TodoTaskQuery query);

    /**
     * 查询待审任务列表
     *
     * @param query 查询参数
     * @return 响应结果
     */
    PageResult<?> queryApproveTasks(ApproveTaskQuery query);

    /**
     * 添加待办任务
     *
     * @param todoTaskDto 待办任务DTO
     * @return 待办任务id
     */
    String addTodoTask(TodoTaskDto todoTaskDto);

    /**
     * 标记待处理操作
     *
     * @param taskHandleDto 处理DTO
     */
    void handle(TaskHandleDto taskHandleDto);

    /**
     * 转办待办任务
     *
     * @param taskTransferDto 任务转办信息
     */
    void transfer(TaskTransferDto taskTransferDto);

    /**
     * 完成待办任务
     *
     * @param finishDto 任务完成信息
     */
    void completeTodoTask(TaskCompleteDto finishDto);

    /**
     * 取消待办任务
     *
     * @param cancelDto 任务取消信息
     */
    void cancelTodoTask(TaskCancelDto cancelDto);

    /**
     * 任务
     *
     * @param suspendDto
     */
    void suspend(TaskSuspendDto suspendDto);

    /**
     * 根据源ID获取协办任务
     *
     * @param idSource 来源系统任务id
     * @return 待办任务列表
     */
    List<TodoTaskEntity> getCoOptTasksBySourceId(String idSource);

    /**
     * 获取待办任务统计概览
     *
     * @param query 统计查询条件
     * @return 待办任务统计概览
     */
    TodoTaskStaticVo getTaskStaticOverview(StaticQuery query);

    /**
     * 获取月度任务统计
     *
     * @param year 年份
     * @return 月度任务统计
     */
    List<TaskMonthStaticVo> getTaskMonthStatic(Integer year, String userId);

    /**
     * 恢复任务
     *
     * @param resumeDto 恢复任务请求
     */
    void resumeTask(TaskResumeDto resumeDto);
}
