package com.fls.todo.pojo.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 任务取消DTO
 *
 * <AUTHOR>
 * @since 2025-01-17
 */
@Data
public class TaskTransferDto {
    /**
     * 待办任务id (按单条任务取消时使用)
     */
    private String idTodoTask;

    /**
     * 来源系统任务id (按业务单据取消时使用)
     */
    private String idSourceRecord;

    /**
     * 操作人id
     */
    @NotBlank(message = "操作人用户id不能为空")
    private String operator;

    /**
     * 转办人id
     */
    @NotBlank(message = "转办人用户id不能为空")
    private String idTransfer;
}
