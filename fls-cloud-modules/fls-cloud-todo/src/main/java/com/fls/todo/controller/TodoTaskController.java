package com.fls.todo.controller;

import cn.hutool.core.lang.Dict;
import com.fls.common.core.domain.ResponseData;
import com.fls.common.mybatis.core.page.PageResult;
import com.fls.todo.api.model.TodoTaskQuery;
import com.fls.todo.pojo.dto.TaskCancelDto;
import com.fls.todo.pojo.dto.TaskCompleteDto;
import com.fls.todo.pojo.dto.TaskHandleDto;
import com.fls.todo.pojo.dto.TaskResumeDto;
import com.fls.todo.pojo.dto.TaskSuspendDto;
import com.fls.todo.pojo.dto.TaskTransferDto;
import com.fls.todo.pojo.dto.TodoTaskDto;
import com.fls.todo.pojo.query.ApproveTaskQuery;
import com.fls.todo.pojo.query.StaticQuery;
import com.fls.todo.pojo.vo.TaskMonthStaticVo;
import com.fls.todo.pojo.vo.TodoTaskStaticVo;
import com.fls.todo.service.ITodoTaskService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.time.LocalDate;
import java.util.List;

/**
 * TodoTaskController
 *
 * <AUTHOR>
 * @since 2025-01-17
 */
@Tag(name = "待办任务")
@Slf4j
@RestController
@RequestMapping("/todo/task")
@RequiredArgsConstructor
public class TodoTaskController {

    private final ITodoTaskService todoTaskService;

    @PostMapping("/list")
    public ResponseData<PageResult<?>> queryTodoTasks(@Valid @RequestBody TodoTaskQuery idQuery) {
        return ResponseData.ok(todoTaskService.queryTodoTasks(idQuery));
    }

    @PostMapping("/approve/list")
    public ResponseData<PageResult<?>> queryApproveTasks(@Valid @RequestBody ApproveTaskQuery query) {
        return ResponseData.ok(todoTaskService.queryApproveTasks(query));
    }

    @PostMapping("/add")
    public ResponseData<Dict> addTodoTask(@Valid @RequestBody TodoTaskDto todoTaskDto) {
        String idTodoTask = todoTaskService.addTodoTask(todoTaskDto);
        return ResponseData.ok(Dict.create().set("idTodoTask", idTodoTask));
    }

    @PostMapping("/transfer")
    public ResponseData<Dict> transfer(@Valid @RequestBody TaskTransferDto taskTransferDto) {
        todoTaskService.transfer(taskTransferDto);
        return ResponseData.ok();
    }

    @PostMapping("/handle")
    public ResponseData<Dict> handle(@Valid @RequestBody TaskHandleDto taskHandleDto) {
        todoTaskService.handle(taskHandleDto);
        return ResponseData.ok();
    }

    @PostMapping("/complete")
    public ResponseData<Void> completeTodoTask(@Valid @RequestBody TaskCompleteDto completeDto) {
        todoTaskService.completeTodoTask(completeDto);
        return ResponseData.ok();
    }

    @PostMapping("/cancel")
    public ResponseData<Void> cancelTodoTask(@Valid @RequestBody TaskCancelDto cancelDto) {
        todoTaskService.cancelTodoTask(cancelDto);
        return ResponseData.ok();
    }

    @PostMapping("/suspend")
    public ResponseData<Void> suspend(@RequestBody TaskSuspendDto suspendDto) {
        todoTaskService.suspend(suspendDto);
        return ResponseData.ok();
    }

    @PostMapping("/resume")
    public ResponseData<Void> resumeTask(@Valid @RequestBody TaskResumeDto resumeDto) {
        todoTaskService.resumeTask(resumeDto);
        return ResponseData.ok();
    }

    @PostMapping("/static/overview")
    public ResponseData<TodoTaskStaticVo> overview(@Valid @RequestBody StaticQuery query) {
        TodoTaskStaticVo result = todoTaskService.getTaskStaticOverview(query);
        return ResponseData.ok(result);
    }

    @PostMapping("/static/month")
    public ResponseData<List<TaskMonthStaticVo>> month(@RequestBody Dict query) {
        Integer year = LocalDate.now().getYear();
        String userId = query.getStr("userId");
        List<TaskMonthStaticVo> result = todoTaskService.getTaskMonthStatic(year, userId);
        return ResponseData.ok(result);
    }
}
