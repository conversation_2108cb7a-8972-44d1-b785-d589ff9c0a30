package com.fls.todo.pojo.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 任务恢复DTO
 *
 * <AUTHOR>
 * @since 2025-01-17
 */
@Data
public class TaskResumeDto implements Serializable {
    private static final long serialVersionUID = 1L;
    
    /**
     * 来源单据id
     */
    private String idSourceBill;

    /**
     * 操作人id
     */
    @NotBlank(message = "操作人id不能为空")
    private String operator;
}
