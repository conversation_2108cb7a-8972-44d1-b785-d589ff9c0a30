package com.fls.todo.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.fls.todo.api.RemoteTodoService;
import com.fls.todo.api.model.TaskCancelReq;
import com.fls.todo.api.model.TaskCompleteReq;
import com.fls.todo.api.model.TaskResumeReq;
import com.fls.todo.api.model.TaskTransferReq;
import com.fls.todo.pojo.dto.TaskCancelDto;
import com.fls.todo.pojo.dto.TaskCompleteDto;
import com.fls.todo.pojo.dto.TaskResumeDto;
import com.fls.todo.pojo.dto.TaskTransferDto;
import com.fls.todo.service.ITodoTaskService;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * RemoteTodoTaskServiceImpl
 *
 * <AUTHOR>
 * @since 2025/8/9
 */
@Service
@DubboService
public class RemoteTodoTaskServiceImpl implements RemoteTodoService {

    @Resource
    private ITodoTaskService todoTaskService;

    @Override
    public void completeTask(TaskCompleteReq completeReq) {
        TaskCompleteDto taskCompleteDto = new TaskCompleteDto();
        BeanUtil.copyProperties(completeReq, taskCompleteDto);
        todoTaskService.completeTodoTask(taskCompleteDto);
    }

    @Override
    public void cancelTask(TaskCancelReq cancelReq) {
        TaskCancelDto taskCancelDto = new TaskCancelDto();
        taskCancelDto.setIdSourceRecord(cancelReq.getIdSourceBill());
        taskCancelDto.setOperator(cancelReq.getOperator());
        todoTaskService.cancelTodoTask(taskCancelDto);
    }

    @Override
    public void transfer(TaskTransferReq transferReq) {
        TaskTransferDto taskTransferDto = new TaskTransferDto();
        taskTransferDto.setIdSourceRecord(transferReq.getIdSourceBill());
        taskTransferDto.setOperator(transferReq.getOperator());
        taskTransferDto.setIdTransfer(transferReq.getIdTransfer());
        todoTaskService.transfer(taskTransferDto);
    }

    @Override
    public void resume(TaskResumeReq resumeReq) {
        TaskResumeDto taskResumeDto = new TaskResumeDto();
        taskResumeDto.setIdSourceBill(resumeReq.getIdSourceBill());
        taskResumeDto.setOperator(resumeReq.getOperator());
        todoTaskService.resumeTask(taskResumeDto);
    }
}
