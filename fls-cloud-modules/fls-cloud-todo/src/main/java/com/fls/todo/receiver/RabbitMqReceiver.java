package com.fls.todo.receiver;

import cn.hutool.json.JSONUtil;
import com.fls.todo.api.constant.MessageMqConstant;
import com.fls.todo.api.model.TodoMessage;
import com.fls.todo.service.IConsumerService;
import com.rabbitmq.client.Channel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import java.io.IOException;

@Slf4j
@Component
@RequiredArgsConstructor
public class RabbitMqReceiver {

    private final IConsumerService consumerService;

    @RabbitListener(bindings = @QueueBinding(value = @Queue(value = MessageMqConstant.TODO_MESSAGE_QUEUE, durable = "true"),
            exchange = @Exchange(value = MessageMqConstant.TODO_MESSAGE_EXCHANGE),
            key = MessageMqConstant.TODO_MESSAGE_TOPIC))
    public void onMessage(Message message, Channel channel) {
        long deliveryTag = message.getMessageProperties().getDeliveryTag();
        try {
            TodoMessage msg = JSONUtil.toBean(new String(message.getBody()), TodoMessage.class);
            consumerService.consumeMsg(msg);
            log.info("received task message consumer success, project: {},taskId: {}", msg.getSourceProjectName(), msg.getIdSourceRecord());
            channel.basicAck(deliveryTag, false);
        }
        catch (Exception e) {
            try {
                // 拒绝消息并直接丢弃消息，暂时未引入死信队列，后续可以考虑使用死信队列进行处理
                channel.basicNack(deliveryTag, false, false);
            }
            catch (IOException ioException) {
                log.error("Failed to nack message: {}", ioException.getMessage());
            }
            log.error("received task message consumer failed, deliveryTag: {},error msg: {}", deliveryTag, e.getMessage());
        }
    }
}
