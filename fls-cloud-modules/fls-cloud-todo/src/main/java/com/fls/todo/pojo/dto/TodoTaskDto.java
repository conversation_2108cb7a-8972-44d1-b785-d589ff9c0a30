package com.fls.todo.pojo.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * TodoTaskDto待办任务信息
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@Data
public class TodoTaskDto {
    /**
     * 来源系统标识
     */
    @NotBlank(message = "来源系统标识不能为空")
    private String sourceProjectName;

    /**
     * 来源系统地址
     */
    @NotBlank(message = "来源系统地址不能为空")
    private String sourceProjectUrl;

    /**
     * 资源访问菜单路径
     */
    private String visitPath;

    /**
     * 资源访问参数
     */
    private String visitParam;

    /**
     * 小程序资源访问菜单路径
     */
    private String appletVisitPath;

    /**
     * 小程序资源访问参数
     */
    private String appletVisitParam;

    /**
     * 来源实例id
     */
    @NotBlank(message = "来源实例id不能为空")
    private String idInstance;

    /**
     * 实例单号
     */
    @NotBlank(message = "来源实例单号不能为空")
    private String instanceCode;

    /**
     * 经营主体id
     */
    @NotBlank(message = "经营主体id不能为空")
    private String idBizunit;

    /**
     * 资源id
     */
    @NotBlank(message = "资源id不能为空")
    private String idResource;

    /**
     * 待办任务名称
     */
    @NotBlank(message = "待办任务名称不能为空")
    private String todoTaskName;

    /**
     * 代办人id
     */
    @NotBlank(message = "代办人id不能为空")
    private String assignees;

    /**
     * 发起人id
     */
    @NotBlank(message = "发起人id不能为空")
    private String initiator;
}
