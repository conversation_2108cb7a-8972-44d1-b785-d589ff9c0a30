package com.fls.todo.pojo.query;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 待审任务查询请求
 *
 * <AUTHOR>
 * @since 2025-07-17
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ApproveTaskQuery {
    /**
     * 单据编号
     */
    private String billCode;

    /**
     * 用户id
     */
    private String idUser;

    /**
     * 经营主体id
     */
    private String idBizunit;

    /**
     * 任务状态：1-待办任务，0-已办任务
     */
    @NotNull
    private Integer isActive = 1;

    /**
     * 资源id
     */
    private String idResource;

    /**
     * 页码，校验规则：页码最小不能小于1
     */
    @Min(value = 1, message = "页码最小不能小于1")
    private Integer pageNo = 1;
    /**
     * 页记录大小，校验规则：非必填，默认20，取值范围1-5000
     */
    @Min(value = 1, message = "页记录大小最小不能小于1")
    @Max(value = 5000, message = "页记录大小最大不能超过5000")
    private Integer pageSize = 20;
}
