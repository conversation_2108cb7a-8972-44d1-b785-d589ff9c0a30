package com.fls.todo.constant;

/**
 * 待办任务常量定义
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
public interface TodoTaskConstant {
    /**
     * 系统操作员标识
     */
    String SYSTEM_OPERATOR = "system";

    /**
     * 业务待办任务类型
     */
    String BIZ_TASK_TYPE = "1";

    /**
     * 待办任务完成锁前缀
     */
    String LOCK_TODO_TASK_COMPLETE_PREFIX = "fls:todo:task:complete:";

    /**
     * 待处理操作锁前缀
     */
    String LOCK_TODO_TASK_HANDLE_PREFIX = "fls:todo:task:handle:";

    /**
     * 待办任务取消锁前缀
     */
    String LOCK_TODO_TASK_CANCEL_PREFIX = "fls:todo:task:cancel:";

    /**
     * 待办任务转办锁前缀
     */
    String LOCK_TODO_TASK_TRANSFER_PREFIX = "fls:todo:task:transfer:";

    /**
     * 锁等待时间（秒）
     */
    long LOCK_WAIT_TIME = 5L;

    /**
     * 锁自动释放时间（秒）
     */
    long LOCK_LEASE_TIME = 10L;
}
