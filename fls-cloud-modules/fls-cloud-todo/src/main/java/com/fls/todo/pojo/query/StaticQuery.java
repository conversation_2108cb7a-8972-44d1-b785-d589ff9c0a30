package com.fls.todo.pojo.query;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * StaticQuery
 *
 * <AUTHOR>
 * @since 2025-07-21
 */
@Data
public class StaticQuery {
    /**
     * 统计类型
     */
    @NotBlank(message = "统计类型不能为空")
    private String type;

    @NotBlank(message = "用户id不能为空")
    private String userId;

    @JsonIgnore
    private boolean isAssignee = true;
}
