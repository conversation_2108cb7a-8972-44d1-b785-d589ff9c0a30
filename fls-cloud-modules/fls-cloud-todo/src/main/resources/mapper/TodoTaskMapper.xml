<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fls.todo.mapper.TodoTaskMapper">
    <select id="selectTodoTaskPage" resultType="com.fls.todo.api.model.TodoTaskVo">
        SELECT tbt.*,tbb.name as bizunitName,tbu.name as initiatorName
        FROM t_base_todo_task tbt left join t_base_bizunit tbb on tbt.id_bizunit = tbb.id_bizunit
        left join t_base_user tbu on tbt.initiator = tbu.id_user
        WHERE tbt.delete_flag = '0' AND (tbt.task_type = '0' or tbt.task_type = '1')

        <if test="query.idResource != null and query.idResource != ''">
            AND tbt.id_resource = #{query.idResource}
        </if>

        <if test="query.sourceProjectName != null and query.sourceProjectName != ''">
            AND tbt.source_project_name = #{query.sourceProjectName}
        </if>

        <if test="query.status != null">
            <choose>
                <when test="query.status == 1">
                    AND (tbt.task_status = '1' or tbt.task_status = '6')
                </when>
                <otherwise>
                    AND tbt.task_status = #{query.status}
                </otherwise>
            </choose>
        </if>

        <if test="query.idUser != null and query.idUser != ''">
            AND tbt.id_assignee = #{query.idUser}
        </if>

        <if test="query.keyword != null and query.keyword != ''">
            AND (tbt.record_name LIKE CONCAT('%', #{query.keyword}, '%') or tbt.task_code LIKE CONCAT('%',
            #{query.keyword}, '%'))
        </if>

        <if test="query.recordCode != null and query.recordCode != ''">
            AND tbt.source_record_code LIKE CONCAT('%', #{query.recordCode}, '%')
        </if>

        <if test="query.idBizunit != null and query.idBizunit != ''">
            AND tbt.id_bizunit = #{query.idBizunit}
        </if>

        <if test="query.beginDate != null and query.beginDate != ''">
            AND tbt.create_time &gt;= CONCAT(#{query.beginDate}, ' 00:00:00')
        </if>

        <if test="query.endDate != null and query.endDate != ''">
            AND tbt.create_time &lt;= CONCAT(#{query.endDate}, ' 23:59:59')
        </if>
        ORDER BY tbt.create_time DESC
    </select>
    <select id="selectApproveTaskPage" resultType="com.fls.todo.pojo.vo.ApproveTaskVo">
        SELECT tbt.record_name as taskName,
        tbt.id_resource as idResource,
        tbt.resource_name as resName,
        tbt.trans_code as transCode,
        tbt.trans_name as transName,
        tbt.resource_name as processName,
        tbt.task_status as taskStatus,
        tbt.handle_status as handleStatus,
        tbt.id_source_record as billId,
        tbt.source_record_code as billCode,
        tbt.id_bizunit as idBizunit,
        tbt.initiator as initiator,
        tbt.start_time as startTime,
        tbt.end_time as endTime,
        tbb.name as bizunitName,
        tbu.name as initiatorName,
        tbt.create_time as createTime,
        tbt.visit_path as visitPath,
        tbt.visit_param as visitParam,
        tbt.applet_visit_path as appletVisitPath,
        tbt.applet_visit_param as appletVisitParam
        FROM t_base_todo_task tbt left join t_base_bizunit tbb on tbt.id_bizunit = tbb.id_bizunit
        left join t_base_user tbu on tbt.initiator = tbu.id_user
        WHERE tbt.delete_flag = '0' AND tbt.task_type = '2'
        <if test="query.billCode != null and query.billCode != ''">
            AND tbt.source_record_code LIKE CONCAT('%', #{query.billCode}, '%')
        </if>

        <if test="query.idUser != null and query.idUser != ''">
            AND tbt.id_assignee = #{query.idUser}
        </if>

        <if test="query.isActive != null">
            <choose>
                <when test="query.isActive == 1">
                    AND tbt.task_status = 0
                </when>
                <when test="query.isActive == 0">
                    AND tbt.task_status = 3
                </when>
            </choose>
        </if>

        <if test="query.idResource != null and query.idResource != ''">
            AND tbt.id_resource = #{query.idResource}
        </if>
        ORDER BY tbt.create_time DESC
    </select>
</mapper>
