<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="60 seconds" debug="false">
    <!-- 日志存放路径 -->
    <property name="log.path" value="logs/${project.artifactId}" />
    <!-- 日志输出格式 -->
    <property name="console.log.pattern" value="%red(%d{yyyy-MM-dd HH:mm:ss}) %green([%thread]) %highlight(%-5level) %boldMagenta(%logger{36}) - %msg%n"/>
    <property name="log.pattern" value="%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"/>

    <!-- 控制台输出 -->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${console.log.pattern}</pattern>
            <charset>utf-8</charset>
        </encoder>
    </appender>

    <!-- 控制台输出 -->
    <appender name="file_console" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/console.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- 日志文件名格式 -->
            <fileNamePattern>${log.path}/console.%d{yyyy-MM-dd}.log</fileNamePattern>
            <!-- 日志最大 90天 -->
            <maxHistory>90</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>${log.pattern}</pattern>
            <charset>utf-8</charset>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <!-- 过滤的级别 -->
            <level>DEBUG</level>
        </filter>
    </appender>

    <!-- 系统日志输出 -->
    <!--    <appender name="file_info" class="ch.qos.logback.core.rolling.RollingFileAppender">-->
    <!--        <file>${log.path}/info.log</file>-->
    <!--        &lt;!&ndash; 循环政策：基于时间创建日志文件 &ndash;&gt;-->
    <!--        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">-->
    <!--            &lt;!&ndash; 日志文件名格式 &ndash;&gt;-->
    <!--            <fileNamePattern>${log.path}/info.%d{yyyy-MM-dd}.log</fileNamePattern>-->
    <!--            &lt;!&ndash; 日志最大的历史 60天 &ndash;&gt;-->
    <!--            <maxHistory>60</maxHistory>-->
    <!--        </rollingPolicy>-->
    <!--        <encoder>-->
    <!--            <pattern>${log.pattern}</pattern>-->
    <!--        </encoder>-->
    <!--        <filter class="ch.qos.logback.classic.filter.LevelFilter">-->
    <!--            &lt;!&ndash; 过滤的级别 &ndash;&gt;-->
    <!--            <level>INFO</level>-->
    <!--            &lt;!&ndash; 匹配时的操作：接收（记录） &ndash;&gt;-->
    <!--            <onMatch>ACCEPT</onMatch>-->
    <!--            &lt;!&ndash; 不匹配时的操作：拒绝（不记录） &ndash;&gt;-->
    <!--            <onMismatch>DENY</onMismatch>-->
    <!--        </filter>-->
    <!--    </appender>-->

    <appender name="file_error" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/error.log</file>
        <!-- 循环政策：基于时间创建日志文件 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- 日志文件名格式 -->
            <fileNamePattern>${log.path}/error.%d{yyyy-MM-dd}.log</fileNamePattern>
            <!-- 日志最大的历史 90天 -->
            <maxHistory>90</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>${log.pattern}</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <!-- 过滤的级别 -->
            <level>ERROR</level>
            <!-- 匹配时的操作：接收（记录） -->
            <onMatch>ACCEPT</onMatch>
            <!-- 不匹配时的操作：拒绝（不记录） -->
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!-- info异步输出 -->
    <!--    <appender name="async_info" class="ch.qos.logback.classic.AsyncAppender">-->
    <!--        &lt;!&ndash; 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 &ndash;&gt;-->
    <!--        <discardingThreshold>0</discardingThreshold>-->
    <!--        &lt;!&ndash; 更改默认的队列的深度,该值会影响性能.默认值为256 &ndash;&gt;-->
    <!--        <queueSize>512</queueSize>-->
    <!--        &lt;!&ndash; 添加附加的appender,最多只能添加一个 &ndash;&gt;-->
    <!--        <appender-ref ref="file_info"/>-->
    <!--    </appender>-->

    <!-- error异步输出 -->
    <!--    <appender name="async_error" class="ch.qos.logback.classic.AsyncAppender">-->
    <!--        &lt;!&ndash; 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 &ndash;&gt;-->
    <!--        <discardingThreshold>0</discardingThreshold>-->
    <!--        &lt;!&ndash; 更改默认的队列的深度,该值会影响性能.默认值为256 &ndash;&gt;-->
    <!--        <queueSize>512</queueSize>-->
    <!--        &lt;!&ndash; 添加附加的appender,最多只能添加一个 &ndash;&gt;-->
    <!--        <appender-ref ref="file_error"/>-->
    <!--    </appender>-->

    <include resource="logback-logstash.xml" />
    <logger name="org.apache.ibatis" level="DEBUG" />
    <logger name="org.mybatis" level="DEBUG" />

    <!--系统操作日志-->
    <root level="info">
        <appender-ref ref="console" />
        <appender-ref ref="file_error"/>
        <appender-ref ref="file_console" />
        <!--        <appender-ref ref="async_error"/>-->
    </root>
</configuration>
