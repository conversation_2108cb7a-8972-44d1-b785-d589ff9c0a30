package com.fls.stock.pojo.query;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 基础权限查询参数
 *
 * <AUTHOR>
 * @since 2024-12-10
 */
@Data
public class BaseAuthQuery {
    @Schema(description = "用户id")
    private String userId;

    @Schema(description = "菜单路由")
    private String href;

    @JsonIgnore
    private List<String> authBizunitIds;
}
