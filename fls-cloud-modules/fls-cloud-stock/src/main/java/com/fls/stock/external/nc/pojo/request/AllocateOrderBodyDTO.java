package com.fls.stock.external.nc.pojo.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description:
 * @Author: caibenwei
 * @DATE: 2024/12/18 15:35
 */
@Data
public class AllocateOrderBodyDTO {

    @Schema(description = "创建人，用户PK")
    private String billmaker;

    @Schema(description = "调入库存组织，库存组织PK")
    private String cinstockorgvid;

    @Schema(description = "调拨类型，交易类型PK")
    private String ctrantypeid;

    @Schema(description = "调出库存组织 ，库存组织PK")
    private String pk_org;

    @Schema(description = "用途，自定义档案PK，可传空值")
    private String vdef5;

    @Schema(description = "是否直运，自定义档案PK（是否），可传空值")
    private String vdef7;

    private String vdef12;
    private String vdef18;
    private String vdef19;

    private String vnote;
    private String cbiztypeid;

}
