package com.fls.stock.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/7/31 9:12
 */
@Data
public class MaterialReturnSignVO {

    @Schema(description = "单据类型 TRANSFER:转库单，ALLOCATE:调拨单")
    private String billType;

    @Schema(description = "转库单或调拨单pk")
    private String pkNcBill;

    @Schema(description = "转库单或调拨单code")
    private String ncBillCode;

    public MaterialReturnSignVO withResult(ReceiveVO vo) {
        this.billType = vo.getBillType();
        this.pkNcBill = vo.getPkNcBill();
        this.ncBillCode = vo.getNcBillCode();
        return this;
    }
}
