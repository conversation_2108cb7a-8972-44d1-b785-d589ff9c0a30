package com.fls.stock.pojo.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 班组库存明细vo
 *
 * <AUTHOR>
 * @since 2024-12-11
 */
@Data
public class TransactionLodDetailVO {
    //库存明细id
    @ExcelIgnore
    private String idTransactionDetail;

    //库存记录id
    @ExcelIgnore
    private String idStock;

    //库存流水记录id
    @ExcelIgnore
    private String idTransactionLog;

    //仓库id
    @ExcelIgnore
    private String idWarehouse;

    //仓库pk
    @ExcelIgnore
    private String pkWarehouse;

    //仓库名称
    @ExcelProperty(value = "仓库名称")
    private String warehouseName;

    //货位id
    @ExcelIgnore
    private String idWhpos;

    //货位pk
    @ExcelIgnore
    private String pkWhpos;

    //货位名称
    @ExcelProperty(value = "货位名称")
    private String whposName;

    //批次编号pk
    @ExcelIgnore
    private String pkBatchCode;

    //批次编号
    @ExcelProperty(value = "批次编号")
    private String batchCode;

    //物料id
    @ExcelIgnore
    private String idMaterial;

    //物料pk
    @ExcelIgnore
    private String pkMaterial;

    //物料名称
    @ExcelProperty(value = "物料名称")
    private String materialName;

    //物料编码
    @ExcelProperty(value = "物料编码")
    private String materialCode;

    //物料参数
    @ExcelProperty(value = "物料参数")
    private String materialParam;

    //组织名称
    @ExcelProperty(value = "组织名称")
    private String orgName;

    //组织id
    @ExcelIgnore
    private String idOrg;

    //经营主体id
    @ExcelIgnore
    private String idBizunit;

    //经营主体名称
    @ExcelProperty(value = "经营主体名称")
    private String bizunitName;

    //班组id
    @ExcelIgnore
    private String idWorkteam;

    //班组名称
    @ExcelProperty(value = "班组名称")
    private String workteamName;

    //存货名称
    @ExcelProperty(value = "存货名称")
    private String inventoryName;

    //单位
    @ExcelProperty(value = "单位")
    private String unit;

    //NC计量单位主键
    @ExcelIgnore
    private String pkMeasdoc;

    //数量
    @ExcelProperty(value = "数量")
    private BigDecimal count;

    //流水号
    @ExcelProperty(value = "流水号")
    private String transactionCode;

    //1-入库, 2-出库
    @ExcelIgnore
    private Integer logType;

    //日志类型描述
    @ExcelProperty(value = "日志类型描述")
    private String logTypeDesc;

    //业务来源:1-物料领取, 2-物料归还, 3-组间调用
    @ExcelIgnore
    private Integer sourceType;

    //实际入库、出库时间
    @ExcelProperty(value = "实际入库、出库时间")
    private LocalDateTime stockTime;

    //来源描述
    @ExcelProperty(value = "来源描述")
    private String sourceDesc;

    //来源单据编号
    @ExcelProperty(value = "来源单据编号")
    private String sourceBillCode;

    //来源单据id
    @ExcelIgnore
    private String idSourceBill;


    //来源单据资源id
    @ExcelIgnore
    private String idSourceRes;

    //来源单据资源名称
    @ExcelProperty(value = "来源单据资源名称")
    private String sourceResName;

    //源头来源单据id
    @ExcelIgnore
    private String idFirstSourceBill;

    //源头来源单据编号
    @ExcelIgnore
    private String firstSourceBillCode;

    //初始来源单据资源名称
    @ExcelIgnore
    private String firstResName;

    //创建人名称
    @ExcelIgnore
    private String creator;

    //创建人名称
    @ExcelProperty(value = "创建人名称")
    private String createName;

    //创建时间
    @ExcelProperty(value = "创建时间")
    private LocalDateTime createTime;

    /**
     * 资源访问菜单路径
     */
    @ExcelIgnore
    private String visitPath;

    /**
     * 小程序资源访问菜单路径
     */
    @ExcelIgnore
    private String appletVisitPath;

    //来源库存操作单号
    private String sourceStockCode;
}
