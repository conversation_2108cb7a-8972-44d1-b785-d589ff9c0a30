package com.fls.stock.external.nc.adapter;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.fls.stock.config.NcParamConfig;
import com.fls.stock.enums.BillTypeEnum;
import com.fls.stock.external.nc.handle.order.NcOrderFactory;
import com.fls.stock.external.nc.handle.order.NcOrderHandle;
import com.fls.stock.external.nc.pojo.dto.PrepareMaterialResultDTO;
import com.fls.stock.pojo.dto.PrepareMaterialContextDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @Description:
 * @Author: caibenwei
 * @DATE: 2024/12/20 11:40
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class PrepareMaterialLockAdapter {

    private final NcOrderFactory ncOrderFactory;

    private final NcParamConfig ncParamConfig;


    public PrepareMaterialResultDTO onCreateOrder(Boolean hasSameOrg, PrepareMaterialContextDTO ctx) {
        JSONObject param = JSONUtil.createObj();
        param.set("vdef12",ctx.getBillCode());
        param.set("vnotebody",ctx.getAssetCode());
        if (hasSameOrg) {
            ctx.setBillTypeEnum(BillTypeEnum.TRANSFER);
            param.set("vdef15", "1");
            param.set("vtrantypecode",ncParamConfig.getLockTransferVtrantypecode()); // 转库单交易类型编码:维修转库（实发）
            ctx.setOrderParamMaps(param);
        } else {
            ctx.setBillTypeEnum(BillTypeEnum.ALLOCATE);
            param.set("cbiztypeid", ncParamConfig.getLockALlocateCbiztypeid()); //业务类型:调拨订单（基地维修）
            param.set("ctaxcodeid", ncParamConfig.getLockALlocateCtaxcodeid()); //税码
            param.set("ctrantypeid", ncParamConfig.getLockALlocateCtrantypeid()); //交易类型：调拨订单（基地维修），根据5X-Cxx-010查询
            ctx.setOrderParamMaps(param);
        }
        NcOrderHandle handle = ncOrderFactory.getHandle(ctx.getBillTypeEnum().name());
        PrepareMaterialResultDTO operate = handle.createOperate(ctx);
        return handle.supplementOtherInAndOut(operate);
    }

}
