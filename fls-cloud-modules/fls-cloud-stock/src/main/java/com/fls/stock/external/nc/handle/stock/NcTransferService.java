package com.fls.stock.external.nc.handle.stock;

import cn.hutool.json.JSONObject;
import com.fls.stock.entity.BaseWarehouse;
import com.fls.stock.external.nc.pojo.request.NcTemplateDTO;
import com.fls.stock.pojo.query.StockQuery;

/**
 * <AUTHOR>
 * @create 2024/12/11 17:38
 **/
public interface NcTransferService {

    JSONObject stockChange(BaseWarehouse warehouse, StockQuery query);

    JSONObject locationAdjustment(NcTemplateDTO dto);
}
