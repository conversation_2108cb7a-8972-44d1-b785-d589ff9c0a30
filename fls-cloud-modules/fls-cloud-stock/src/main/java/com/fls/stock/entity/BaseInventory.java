package com.fls.stock.entity;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 物料表(BaseInventory)表实体类
 *
 * <AUTHOR>
 * @since 2024-12-18 15:12:27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_base_inventory")
public class BaseInventory implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     **/
    @TableId(value = "id_inventory", type = IdType.ASSIGN_UUID)
    private String idInventory;

    /**
     * 编码
     **/
    @TableField("code")
    private String code;

    /**
     * 名称
     **/
    @TableField("name")
    private String name;

    /**
     * 英文名称
     **/
    @TableField("ename")
    private String ename;

    /**
     * 主计量单位
     **/
    @TableField("id_measdoc")
    private String idMeasdoc;

    /**
     * 物料分类
     **/
    @TableField("id_invclass")
    private String idInvclass;

    /**
     * OEM码
     **/
    @TableField("spec")
    private String spec;

    /**
     * 机型
     **/
    @TableField("model")
    private String model;

    /**
     * 标签机型
     **/
    @TableField("model_label")
    private String modelLabel;

    /**
     * 基本参数
     **/
    @TableField("base_param")
    private String baseParam;

    /**
     * 技术参数
     **/
    @TableField("tech_param")
    private String techParam;

    /**
     * 技术推荐
     **/
    @TableField("tech_memo")
    private String techMemo;

    /**
     * 采购推荐
     **/
    @TableField("purchase_memo")
    private String purchaseMemo;

    /**
     * 供应商编码
     **/
    @TableField("supplier_code")
    private String supplierCode;

    /**
     * 供应商存货编码
     **/
    @TableField("supplier_invcode")
    private String supplierInvcode;

    /**
     * 供货单位所在地
     **/
    @TableField("place_of_orgin")
    private String placeOfOrgin;

    /**
     * 存货属性 01=配件，02=叉车，03=二手叉车，04=电动车，05=其他成型机械，06=二手成型设备，07=手拉车，08=属具，09=电池组，10=轮胎，11=货叉，12=仓储设备 参见inv_type
     **/
    @TableField("inv_type")
    private String invType;

    /**
     * 配件属性 01=原厂件，02=拆车件，03=副厂件，04=代用件，05=进口件，06=台湾件，07=开发件，09=OEM配套件 参见parts_type
     **/
    @TableField("parts_type")
    private String partsType;

    /**
     * 铺货属性 01=铺货，02=非铺货 参见distribution_type
     **/
    @TableField("distribution_type")
    private String distributionType;

    /**
     * 是否内销 0=否，1=是 参见yesorno
     **/
    @TableField("sale_flag")
    private String saleFlag;

    /**
     * 是否外销 0=否，1=是 参见yesorno
     **/
    @TableField("exsale_flag")
    private String exsaleFlag;

    /**
     * 无税成本（最新成本）
     **/
    @TableField("cost")
    private BigDecimal cost;

    /**
     * 含税成本
     **/
    @TableField("taxcost")
    private BigDecimal taxcost;

    /**
     * 其他费用
     **/
    @TableField("outlay")
    private BigDecimal outlay;

    /**
     * 无税调拨价
     **/
    @TableField("tranprice")
    private BigDecimal tranprice;

    /**
     * 含税调拨价
     **/
    @TableField("taxtranprice")
    private BigDecimal taxtranprice;

    /**
     * 销售指导价
     **/
    @TableField("guideprice")
    private BigDecimal guideprice;

    /**
     * 外贸系数
     **/
    @TableField("exrate")
    private BigDecimal exrate;

    /**
     * 外贸网上价
     **/
    @TableField("exsaleprice")
    private BigDecimal exsaleprice;

    /**
     * 叉车车型
     **/
    @TableField("id_forkliftmodels")
    private String idForkliftmodels;

    /**
     * 品牌
     **/
    @TableField("id_brand")
    private String idBrand;

    /**
     * 吨位
     **/
    @TableField("id_tonnage")
    private String idTonnage;

    /**
     * 新旧 J=二手车，X=新车 参见neworold
     **/
    @TableField("new_degree")
    private String newDegree;

    /**
     * 整车车号
     **/
    @TableField("car_num")
    private String carNum;

    /**
     * 资产编号
     **/
    @TableField("asset_code")
    private String assetCode;

    /**
     * 整车配置说明
     **/
    @TableField("car_sets")
    private String carSets;

    /**
     * 对应U8编码
     **/
    @TableField("u8_code")
    private String u8Code;

    /**
     * 采购员
     **/
    @TableField("id_purman")
    private String idPurman;

    /**
     * 技术员
     **/
    @TableField("id_tecman")
    private String idTecman;

    /**
     * 仓管员
     **/
    @TableField("id_stman")
    private String idStman;

    /**
     * NC物料PK值
     **/
    @TableField("pk_material")
    private String pkMaterial;

    /**
     * 启用状态：1=未启用，2=已启用，3=已停用，默认1，参见basestatus
     **/
    @TableField("status")
    private String status;

    /**
     * 创建时间
     **/
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 创建人
     **/
    @TableField("creator")
    private String creator;

    /**
     * 失效时间
     **/
    @TableField("disable_time")
    private LocalDateTime disableTime;

    /**
     * 时间戳
     **/
    @TableField("ts")
    private LocalDateTime ts;

    /**
     * 是否删除：0=否，1=是，默认0，参见yesorno
     **/
    @TableField("delete_flag")
    @TableLogic
    private String deleteFlag;

}

