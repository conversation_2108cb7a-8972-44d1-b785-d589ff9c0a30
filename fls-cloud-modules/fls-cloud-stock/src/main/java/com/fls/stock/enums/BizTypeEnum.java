package com.fls.stock.enums;

import lombok.Getter;

import java.util.Objects;
import java.util.Optional;

/**
 * @Description:
 * @Author: caibenwei
 * @DATE: 2024/12/20 14:12
 */
@Getter
public enum BizTypeEnum {

    BASE("4K-Cxx-002", "5X-Cxx-010", "基地维修"),

    // 此字段为预留字段，未使用
    LEASE("", "", "租赁维修"),

    // 此字段为预留字段，未使用
    BUSINESS("", "", "商业维修");


    /**
     * 调拨单 code
     */
    private final String allocateCode;

    /**
     * 转库单 code
     */
    private final String transferCode;

    /**
     * 描述
     */
    private final String desc;

    BizTypeEnum(String allocateCode, String transferCode, String desc) {
        this.allocateCode = allocateCode;
        this.transferCode = transferCode;
        this.desc = desc;
    }

    public static Optional<BizTypeEnum> match(String code) {
        if (Objects.nonNull(code)) {
            for (BizTypeEnum enums : BizTypeEnum.values()) {
                if (enums.name().equalsIgnoreCase(code)) {
                    return Optional.ofNullable(enums);
                }
            }
        }
        return Optional.empty();
    }

}
