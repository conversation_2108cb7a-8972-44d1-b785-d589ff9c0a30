package com.fls.stock.service.invoke;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fls.stock.entity.BaseWhpos;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 货位表(BaseWhpos)表服务接口
 *
 * <AUTHOR>
 * @since 2024-12-11 17:23:16
 */
public interface BaseWhposService extends IService<BaseWhpos> {

    Map<String, BaseWhpos> getWhposEntityMaps(Set<String> idWhpos);

    List<String> getWarehouseByWhposIds(Set<String> whposIds);
}
