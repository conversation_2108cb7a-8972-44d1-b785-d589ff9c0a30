package com.fls.stock.service.invoke;


import com.fls.common.mybatis.core.page.PageResult;
import com.fls.stock.external.nc.pojo.dto.PrepareMaterialResultDTO;
import com.fls.stock.external.nc.pojo.request.NcTemplateDTO;
import com.fls.stock.pojo.dto.MaterialReturnDTO;
import com.fls.stock.pojo.dto.MaterialReturnSignDTO;
import com.fls.stock.pojo.dto.PrepareMaterialCreateDTO;
import com.fls.stock.pojo.dto.PrepareMaterialOutDTO;
import com.fls.stock.pojo.dto.PrepareMaterialReceiveDTO;
import com.fls.stock.pojo.dto.TransferDTO;
import com.fls.stock.pojo.query.LockLogQuery;
import com.fls.stock.pojo.query.StockQuery;
import com.fls.stock.pojo.query.TransactionLogQuery;
import com.fls.stock.pojo.vo.BarcodeVO;
import com.fls.stock.pojo.vo.LockLogVO;
import com.fls.stock.pojo.vo.MaterialReturnSignVO;
import com.fls.stock.pojo.vo.MaterialReturnVO;
import com.fls.stock.pojo.vo.ReceiveVO;
import com.fls.stock.pojo.vo.TransactionLogVO;
import com.fls.stock.pojo.vo.TransferVO;

/**
 * 班组库存服务
 *
 * <AUTHOR>
 * @since 2024-12-10
 */
public interface StockInvokeService {

    PageResult<TransactionLogVO> getTransactionLog(TransactionLogQuery query);

    PageResult<LockLogVO> getLockLog(LockLogQuery query);

    BarcodeVO getBarcode(String code);

    Boolean stockOut(StockQuery query);

    Boolean stockIn(StockQuery query);

    Boolean inventoryChange(StockQuery query);

    PrepareMaterialResultDTO prepareMaterialLock(PrepareMaterialCreateDTO dto);

    String prepareMaterialOut(PrepareMaterialOutDTO dto);

    ReceiveVO prepareMaterialReceive(PrepareMaterialReceiveDTO dto);

    ReceiveVO receive(PrepareMaterialCreateDTO dto);

    MaterialReturnVO materialReturn(MaterialReturnDTO dto);

    Object locationAdjustment(NcTemplateDTO dto);

    TransferVO transfer(TransferDTO dto);

    MaterialReturnSignVO materialReturnSign(MaterialReturnSignDTO dto);
}
