package com.fls.stock.entity;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 工作班组成员表(BaseWorkteamMember)表实体类
 *
 * <AUTHOR>
 * @since 2024-12-18 10:07:15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_base_workteam_member")
public class BaseWorkteamMember implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     **/
    @TableId(value = "id_workteam_member", type = IdType.ASSIGN_UUID)
    private String idWorkteamMember;

    /**
     * 工作班组
     **/
    @TableField("id_workteam")
    private String idWorkteam;

    /**
     * 班组成员
     **/
    @TableField("id_member")
    private String idMember;

    /**
     * 班组成员对应虚拟地点
     **/
    @TableField("id_address")
    private String idAddress;

    /**
     * 班组成员对应货位
     **/
    @TableField("id_whpos")
    private String idWhpos;

    /**
     * 班组成员默认归还仓库
     **/
    @TableField("id_whback")
    private String idWhback;

    /**
     * NC档案主键
     **/
    @TableField("nc_pk")
    private String ncPk;

    /**
     * 启用状态：1=未启用，2=已启用，3=已停用，默认1，参见basestatus
     **/
    @TableField("status")
    private String status;

    /**
     * 创建时间
     **/
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 创建人
     **/
    @TableField("creator")
    private String creator;

    /**
     * 修改时间
     **/
    @TableField("modify_time")
    private LocalDateTime modifyTime;

    /**
     * 修改人
     **/
    @TableField("modifier")
    private String modifier;

    /**
     * 作废时间
     **/
    @TableField("invalid_time")
    private LocalDateTime invalidTime;

    /**
     * 作废人
     **/
    @TableField("invalider")
    private String invalider;

    /**
     * 时间戳
     **/
    @TableField("ts")
    private LocalDateTime ts;

    /**
     * 是否删除：0=否，1=是，默认0，参见yesorno
     **/
    @TableField("delete_flag")
    @TableLogic
    private String deleteFlag;

}

