package com.fls.stock.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 班组库存流水明细表(WorkteamTransactionDetail)表实体类
 *
 * <AUTHOR>
 * @since 2024-12-11 09:08:06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_workteam_transaction_detail")
public class WorkteamTransactionDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    //库存流水明细主键id
    @TableId(value = "id_transaction_detail", type = IdType.ASSIGN_UUID)
    private String idTransactionDetail;

    //库存记录id
    private String idStock;

    //库存流水记录id
    private String idTransactionLog;

    //仓库id
    private String idWarehouse;

    //仓库pk
    private String pkWarehouse;

    //仓库名称
    private String warehouseName;

    //货位id
    private String idWhpos;

    //货位pk
    private String pkWhpos;

    //货位名称
    private String whposName;

    //批次编号pk
    private String pkBatchCode;

    //批次编号
    private String batchCode;

    //物料id
    private String idMaterial;

    //物料pk
    private String pkMaterial;

    //物料名称
    private String materialName;

    //物料编码
    private String materialCode;

    //物料参数
    private String materialParam;

    //组织名称
    private String orgName;

    //组织id
    private String idOrg;

    //经营主体id
    private String idBizunit;

    //经营主体名称
    private String bizunitName;

    //班组id
    private String idWorkteam;

    //班组名称
    private String workteamName;

    //存货名称
    private String inventoryName;

    //单位
    private String unit;

    //NC计量单位主键
    private String pkMeasdoc;

    //数量
    private BigDecimal count;

    //创建者
    private String creator;

    //创建时间
    private LocalDateTime createTime;

    //时间戳
    private LocalDateTime ts;

    //删除标记: 0-正常, 1-删除
    private String deleteFlag;
}

