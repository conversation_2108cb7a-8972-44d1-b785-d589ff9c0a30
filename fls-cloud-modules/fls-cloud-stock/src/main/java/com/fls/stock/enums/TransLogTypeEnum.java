package com.fls.stock.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 库存流水类型
 *
 * <AUTHOR>
 * @since 2024-12-24
 */
@Getter
@AllArgsConstructor
public enum TransLogTypeEnum {
    /**
     * 物料归还
     */
    INBOUND(1, "库存入库"),
    /**
     * 组间调用
     */
    OUTBOUND(2, "库存出库");

    private final int type;

    private final String desc;

    public static String getDescByType(int type) {
        for (TransLogTypeEnum value : values()) {
            if (value.type == type) {
                return value.desc;
            }
        }
        return "";
    }

}

