package com.fls.stock.pojo.vo;

import com.fls.stock.external.nc.pojo.dto.PrepareMaterialResultDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/7/31 9:12
 */
@Data
public class MaterialReturnVO {

    @Schema(description = "单据类型 TRANSFER:转库单，ALLOCATE:调拨单")
    private String billType;

    @Schema(description = "转库单或调拨单pk")
    private String pkNcBill;

    @Schema(description = "转库单或调拨单code")
    private String ncBillCode;

    public MaterialReturnVO withResult(PrepareMaterialResultDTO adapterResult) {
        this.billType = adapterResult.getBillType();
        this.pkNcBill = adapterResult.getPkNcBill();
        this.ncBillCode = adapterResult.getNcBillCode();
        return this;
    }
}
