package com.fls.stock.external.wms.pojo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @create 2024/12/11 17:01
 **/
@Data
public class WmsStockQueryResponseListDTO {

    private String whcode;

    private String invcode;

//    货位
    private String poscode;

    private String batchcode;

    private BigDecimal qty;

    private BigDecimal qtyLock;

    private String invstd;

    private String oem;


    /**
     * 获取可用库存数量
     *
     * @return
     */
    public BigDecimal obtainAvaileNum() {
        return this.qty.subtract(this.qtyLock);
    }

}
