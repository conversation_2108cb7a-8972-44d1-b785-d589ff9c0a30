package com.fls.stock.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 工作班组库存表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_workteam_stock")
public class WorkteamStock implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 库存主键
     */
    @TableId(value = "id_stock", type = IdType.ASSIGN_UUID)
    private String idStock;

    /**
     * 所属仓库id
     */
    private String idWarehouse;

    /**
     * 所属仓库pk
     */
    private String pkWarehouse;

    /**
     * 仓库名称
     */
    @TableField(exist = false)
    private String warehouseName;

    /**
     * 货位id
     */
    private String idWhpos;

    /**
     * 货位pk
     */
    private String pkWhpos;

    /**
     * 货位名称
     */
    @TableField(exist = false)
    private String whposName;

    /**
     * 批次号
     */
    private String batchCode;

    /**
     * 批次pk值
     */
    private String pkBatchCode;

    /**
     * 工作组id
     */
    private String idWorkteam;

    /**
     * 班组名称
     */
    @TableField(exist = false)
    private String workteamName;

    /**
     * 组织id
     */
    private String idOrg;

    /**
     * 组织名称
     */
    @TableField(exist = false)
    private String orgName;

    /**
     * 经营主体id
     */
    private String idBizunit;

    /**
     * 经营主体名称
     */
    @TableField(exist = false)
    private String bizunitName;

    /**
     * 物料id
     */
    private String idMaterial;

    /**
     * 物料名称
     */
    private String materialName;

    /**
     * 存货名称
     */
    private String inventoryName;

    /**
     * 物料编码
     */
    private String materialCode;

    //物料参数
    private String materialParam;

    /**
     * 物料pk值
     */
    private String pkMaterial;

    /**
     * 可用库存量
     */
    private BigDecimal availableNum;

    /**
     * 锁定库存量
     */
    private BigDecimal locksNum;

    /**
     * 现存量
     */
    private BigDecimal extantNum;

    /**
     * 单位
     */
    private String unit;

    /**
     * NC计量单位主键
     */
    private String pkMeasdoc;

    /**
     * 物料最新领取时间
     */
    private LocalDateTime receiveTime;

    /**
     * 物料最新使用时间
     */
    private LocalDateTime useTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更信任
     */
    private String updater;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 时间戳
     */
    private LocalDateTime ts;

    /**
     * 0-正常,1-删除
     */
    private String deleteFlag;

    /**
     * 变动数量，用来记录流水使用
     */
    @TableField(exist = false)
    private BigDecimal changes;
}
