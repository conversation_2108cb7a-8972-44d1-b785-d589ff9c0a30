package com.fls.stock.entity;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 组织表(BaseOrg)表实体类
 *
 * <AUTHOR>
 * @since 2024-12-18 14:27:08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_base_org")
public class BaseOrg implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 组织主键
     **/
    @TableId(value = "id_org", type = IdType.ASSIGN_UUID)
    private String idOrg;

    /**
     * 组织编码
     **/
    @TableField("code")
    private String code;

    /**
     * 内部编码
     **/
    @TableField("innercode")
    private String innercode;

    /**
     * 组织名称
     **/
    @TableField("name")
    private String name;

    /**
     * 组织简称
     **/
    @TableField("shortname")
    private String shortname;

    /**
     * NC组织pk值
     **/
    @TableField("pk_org")
    private String pkOrg;

    /**
     * 是否采购 0=否，1=是 参见yesorno
     **/
    @TableField("purchase_flag")
    private String purchaseFlag;

    /**
     * 是否销售 0=否，1=是 参见yesorno
     **/
    @TableField("sales_flag")
    private String salesFlag;

    /**
     * 是否物流 0=否，1=是 参见yesorno
     **/
    @TableField("traffic_flag")
    private String trafficFlag;

    /**
     * 是否财务 0=否，1=是 参见yesorno
     **/
    @TableField("finance_flag")
    private String financeFlag;

    /**
     * 是否库存 0=否，1=是 参见yesorno
     **/
    @TableField("stock_flag")
    private String stockFlag;

    /**
     * 是否人力资源 0=否，1=是 参见yesorno
     **/
    @TableField("hr_flag")
    private String hrFlag;

    /**
     * 是否行政 0=否，1=是 参见yesorno
     **/
    @TableField("admin_flag")
    private String adminFlag;

    /**
     * 是否独立法人 0=否，1=是 参见yesorno
     **/
    @TableField("company_flag")
    private String companyFlag;

    /**
     * 组织类型  0=总公司， 1=分公司，2=子公司，3=孙公司，4=营业部，9=虚拟公司 参见org_type
     **/
    @TableField("org_type")
    private String orgType;

    /**
     * 父级组织
     **/
    @TableField("id_parentorg")
    private String idParentorg;

    /**
     * 负责人
     **/
    @TableField("org_manager")
    private String orgManager;

    /**
     * 分管领导
     **/
    @TableField("org_leader")
    private String orgLeader;

    /**
     * 经纬度
     **/
    @TableField("lat_long_alt")
    private String latLongAlt;

    /**
     * 所属集团
     **/
    @TableField("id_group")
    private String idGroup;

    /**
     * 启用状态：1=未启用，2=已启用，3=已停用，默认1，参见basestatus
     **/
    @TableField("status")
    private String status;

    /**
     * 创建时间
     **/
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 创建人
     **/
    @TableField("creator")
    private String creator;

    /**
     * 失效时间
     **/
    @TableField("disable_time")
    private LocalDateTime disableTime;

    /**
     * 时间戳
     **/
    @TableField("ts")
    private LocalDateTime ts;

    /**
     * 是否删除：0=否，1=是，默认0，参见yesorno
     **/
    @TableField("delete_flag")
    @TableLogic
    private String deleteFlag;

    /**
     * 开票组织
     **/
    @TableField("id_org_invoice")
    private String idOrgInvoice;

    /**
     * 注销标识
     **/
    @TableField("cancel_flag")
    private String cancelFlag;

    /**
     * iHR组织id
     **/
    @TableField("id_ihr")
    private String idIhr;

    /**
     * 排序
     **/
    @TableField("sort")
    private Integer sort;

    /**
     * 付款银行账户融资请款用
     **/
    @TableField("my_payment_account")
    private String myPaymentAccount;

}

