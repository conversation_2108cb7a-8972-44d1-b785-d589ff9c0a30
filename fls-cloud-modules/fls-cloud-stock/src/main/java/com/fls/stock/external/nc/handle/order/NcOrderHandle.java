package com.fls.stock.external.nc.handle.order;

import cn.hutool.json.JSONObject;
import com.fls.stock.external.nc.pojo.dto.MaterialOutDTO;
import com.fls.stock.external.nc.pojo.dto.PrepareMaterialResultDTO;
import com.fls.stock.pojo.dto.OtherInDTO;
import com.fls.stock.pojo.dto.PrepareMaterialContextDTO;
import com.fls.stock.pojo.dto.PrepareMaterialOutDTO;
import com.fls.stock.pojo.dto.PrepareMaterialReceiveDTO;
import com.fls.stock.pojo.vo.ReceiveVO;

/**
 * @Description: nc备料单处理器
 * @Author: caibenwei
 * @DATE: 2024/12/19 14:29
 */
public interface NcOrderHandle {

    /**
     * 创建备料单
     * @param ctx
     * @return
     */
    PrepareMaterialResultDTO createOperate(PrepareMaterialContextDTO ctx);

    /**
     * 出库操作
     * @param dto
     * @return
     */
    Object outOperate(MaterialOutDTO dto);

    /**
     * 领料操作
     *
     * @param dto
     * @return
     */
    JSONObject receiveOperate(PrepareMaterialReceiveDTO dto);


    /**
     * 校验订单是否存在
     * @param pkNcBill
     * @return
     */
    Boolean validatedOrderExists(String pkNcBill);

    /**
     * 查询wms是否成功出库
     *
     * @param dto
     * @return
     */
    Boolean hasSuccessWmsOutOperate(PrepareMaterialOutDTO dto);

    /**
     * 补充其他出、入信息
     * @param operate
     * @return
     */
    PrepareMaterialResultDTO supplementOtherInAndOut(PrepareMaterialResultDTO operate);

    /**
     * 补充其他入信息pk
     * @param pkNcBill
     * @return
     */
    String supplementOtherIn(String pkNcBill);

    /**
     * 补充其他入信息信息
     * @param pkNcBill
     * @return
     */
    OtherInDTO supplementOtherInInfo(String pkNcBill);

    /**
     * 通过普通入库单或调拨入库单pk获取数据
     * @param pkIcIn
     * @return
     */
    ReceiveVO supplementReceiveByIcIn(String pkIcIn);
}
