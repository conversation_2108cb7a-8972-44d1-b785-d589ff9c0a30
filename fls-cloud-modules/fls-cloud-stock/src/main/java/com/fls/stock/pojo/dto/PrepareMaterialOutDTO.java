package com.fls.stock.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Description:
 * @Author: caibenwei
 * @DATE: 2024/12/18 17:25
 */
@Data
public class PrepareMaterialOutDTO extends BaseApiDTO{

    @Schema(description = "单据类型，转库/调拨")
    @NotBlank(message = "单据类型不能为空")
    private String billType;

    @Schema(description = "转库单或调拨单pk")
    @NotBlank(message = "转库单或调拨单pk不能为空")
    private String pkNcBill;

    @Schema(description = "普通仓库单pk")
    private String pkIcOut;

    @Schema(description = "出库仓库")
    @NotBlank(message = "出库仓库不能为空")
    private String idWarehouse;

}
