package com.fls.stock.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 班组库存入库单表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_workteam_inbound_bill")
public class WorkteamInboundBill extends BaseBillEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 入库单主键id
     */
    @TableId(value = "id_inbound_bill", type = IdType.ASSIGN_UUID)
    private String idInboundBill;

    /**
     * 入库数量
     */
    private BigDecimal inboundCount;
}
