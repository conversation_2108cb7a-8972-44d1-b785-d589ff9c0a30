package com.fls.stock.pojo.dto;

import com.fls.stock.pojo.model.BaseSourceBill;
import com.fls.stock.pojo.model.StockOperationRecord;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 库存释放DTO
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class StockLockReleaseDTO extends BaseSourceBill {
    /**
     * 锁定释放单据明细
     */
    @NotEmpty(message = "物料列表不能为空")
    List<StockOperationRecord> records;

    /**
     * 忽略库存校验
     */
    private Boolean ignoreStockCheck;
}
