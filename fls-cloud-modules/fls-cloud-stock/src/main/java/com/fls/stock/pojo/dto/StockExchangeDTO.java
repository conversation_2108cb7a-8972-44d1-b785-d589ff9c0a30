package com.fls.stock.pojo.dto;

import com.fls.stock.pojo.model.BaseSourceBill;
import com.fls.stock.pojo.model.MaterialOperationRecord;
import com.fls.stock.pojo.model.StockOperationRecord;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 班组库存领料归还DTO，补充：单独定义一个DTO在这里，考虑后面登记和归还的请求对象不一样
 *
 * <AUTHOR>
 * @since 2024-12-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class StockExchangeDTO extends BaseSourceBill {
    /**
     * 班组id
     */
    private String idWorkteam;
    /**
     * 退还用料列表
     */
    private List<StockOperationRecord> returns;

    /**
     * 领取用料列表
     */
    private List<MaterialOperationRecord> receives;


}
