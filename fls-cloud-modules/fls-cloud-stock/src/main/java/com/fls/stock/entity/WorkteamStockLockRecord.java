package com.fls.stock.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 班组库存锁定记录表(WorkteamStockLockRecord)表实体类
 *
 * <AUTHOR>
 * @since 2024-12-11 09:08:53
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_workteam_stock_lock_record")
public class WorkteamStockLockRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    //库存锁定记录主键id
    @TableId(value = "id_lock_record", type = IdType.ASSIGN_UUID)
    private String idLockRecord;

    //库存记录id
    private String idStock;

    //经营主体id
    private String idBizunit;

    //经营主体名称
    private String bizunitName;

    //班组id
    private String idWorkteam;

    //班组名称
    private String workteamName;

    //仓库id
    private String idWarehouse;

    //仓库名称
    private String warehouseName;

    //货位id
    private String idWhpos;

    //货位名称
    private String whposName;

    //批次号
    private String batchCode;

    //批次pk值
    private String pkBatchCode;

    //物料id
    private String idMaterial;

    //物料pk
    private String pkMaterial;

    //物料编码
    private String materialCode;

    //存货名称
    private String inventoryName;

    //物料名称
    private String materialName;

    //物料参数
    private String materialParam;

    //数量
    private BigDecimal lockNum;

    //NC计量单位主键
    private String pkMeasdoc;

    //单位
    private String unit;

    //操作类型: 1-锁定, 2-释放
    private String operateType;

    //项目名称
    private String projectCode;

    //库存锁定来源单据编号
    private String sourceBillCode;

    //库存锁定来源单据id
    private String idSourceBill;

    //来源单据资源id
    private String idSourceRes;

    //来源资源名称
    private String sourceResName;

    //源头来源单据id
    private String idFirstSourceBill;

    //源头来源单据编号
    private String firstSourceBillCode;

    //源头单据资源id
    private String idFirstRes;

    //源头资源名称
    private String firstResName;

    //操作时间
    private LocalDateTime operateTime;

    //创建者
    private String creator;

    //创建人名称
    private String createName;

    //创建时间
    private LocalDateTime createTime;

    //更新人
    private String updater;

    //更新人名称
    private  String updateName;

    //更新时间
    private LocalDateTime updateTime;

    //时间戳
    private LocalDateTime ts;

    //删除标记: 0-正常, 1-删除
    private String deleteFlag;

}

