package com.fls.stock.pojo.dto;

import com.fls.stock.entity.BaseInventory;
import com.fls.stock.entity.BaseWhpos;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;

/**
 * @Description:
 * @Author: caibenwei
 * @DATE: 2024/12/19 23:34
 */
@Data
public class MaterialDTO {

    @Schema(description = "物料id")
    @NotBlank(message = "物料id不能为空")
    private String idInventory;

    @Schema(description = "物料数量")
    @NotBlank(message = "物料数据不能为空")
    private BigDecimal number;

    @Schema(hidden = true, description = "物料对象实体")
    private BaseInventory inventory;

    @Schema(description = "转入货位id")
    private String inIdWhpos;

    @Schema(description = "转出货位id")
    private String outIdWhpos;

    @Schema(hidden = true, description = "转入货位")
    private BaseWhpos inWhpos;

    @Schema(hidden = true, description = "转出货位")
    private BaseWhpos outWhpos;
}
