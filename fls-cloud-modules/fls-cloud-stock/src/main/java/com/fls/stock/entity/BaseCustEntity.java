package com.fls.stock.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 客户基本档案表
 *
 * <AUTHOR>
 * @since 2024-11-06
 */

@Data
@TableName("t_cust_baseinfo")
public class BaseCustEntity {
    /**
     * 主键
     */
    @TableId(value = "id_customer", type = IdType.ASSIGN_UUID)
    private String idCustomer;

    /**
     * 编码
     */
    private String code;

    /**
     * 名称
     */
    private String name;

    /**
     * 简称
     */
    private String shortname;

    /**
     * 所属组织
     */
    private String idOrg;

    /**
     * 客户分类
     */
    private String idCustclass;

    /**
     * 上级客户
     */
    private String idParentcust;

    /**
     * 纳税人登记号
     */
    private String taxpayerid;

    /**
     * 法人
     */
    private String legalbody;

    /**
     * 公司地址
     */
    private String address;

    /**
     * 电话
     */
    private String tel;

    /**
     * 行业
     */
    private String idIndustry;

    /**
     * 发掘渠道 01=地推式寻访，02=公共媒体信息，03=政府信息，04=经济组织信息，05=会议活动，06=竞争对手信息，07=专业信息组织，08=社会关系信息，09=老客户信息 参见cust_channel
     */
    private String channel;

    /**
     * 是否供应商 0=否，1=是 参见yesorno
     */
    private String supplierFlag;

    /**
     * 对应供应商
     */
    private String idSupplier;

    /**
     * 客户属性 0=外部单位，1=内部单位 参见cust_prop
     */
    private String custProp;

    /**
     * 对应组织
     */
    private String idFinanceorg;

    /**
     * 客户付款方式 01=月结，03=代收货款，04=款到发货，05=预收订金，06=三月结，07=免费，08=双月结，10=四月结，11=半年结，99=综合 参见cust_paytype
     */
    private String custPaytype;

    /**
     * 客户类型 01=终端，02=同行，03=个体户 参见cust_type
     */
    private String custType;

    /**
     * 客户等级 1=A类客户，2=B类客户，3=C类客户 参见cust_level
     */
    private String custLevel;

    /**
     * 管理水平评级 1=很好，2=好，3=一般，4=差，5=很差 参见management_level
     */
    private String managementLevel;

    /**
     * 管理水平描述
     */
    private String management;

    /**
     * 工况评级 1=很好，2=好，3=一般，4=差，5=很差 参见workcond_level
     */
    private String workcondLevel;

    /**
     * 工况描述
     */
    private String workCondition;

    /**
     * 环境评级 1=很好，2=好，3=一般，4=差，5=很差 参见environment_level
     */
    private String environmentLevel;

    /**
     * 环境描述
     */
    private String environment;

    /**
     * NC客户基本信息主键
     */
    private String pkCustomer;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 失效时间
     */
    private LocalDateTime disableTime;

    /**
     * 时间戳
     */
    private LocalDateTime ts;


    /**
     * 启用状态：1=未启用，2=已启用，3=已停用，默认1，参见basestatus
     */
    private String status;

    /**
     * 是否删除：0=否，1=是，默认0，参见yesorno
     */
    private String deleteFlag;

}
