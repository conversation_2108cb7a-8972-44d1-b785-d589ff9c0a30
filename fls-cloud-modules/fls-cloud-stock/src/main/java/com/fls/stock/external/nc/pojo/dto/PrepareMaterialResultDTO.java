package com.fls.stock.external.nc.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description:
 * @Author: caibenwei
 * @DATE: 2024/12/18 17:14
 */
@Data
public class PrepareMaterialResultDTO {

    @Schema(description = "单据类型 TRANSFER:转库单，ALLOCATE:调拨单")
    private String billType;

    @Schema(description = "转库单或调拨单pk")
    private String pkNcBill;

    @Schema(description = "转库单或调拨单code")
    private String ncBillCode;

    @Schema(description = "其他出pk")
    private String pkOtherOut;

    @Schema(description = "其他入pk")
    private String pkOtherIn;

    @Schema(description = "入库仓库id")
    private String idWarehourseIn;

    @Schema(description = "入库货位id")
    private String idWhposIn;

}
