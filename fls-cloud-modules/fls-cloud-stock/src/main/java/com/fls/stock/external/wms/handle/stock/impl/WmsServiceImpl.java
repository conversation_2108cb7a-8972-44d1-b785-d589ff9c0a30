package com.fls.stock.external.wms.handle.stock.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.fls.common.core.constant.CommonConstants;
import com.fls.common.core.exception.ServiceException;
import com.fls.stock.config.WmsConfig;
import com.fls.stock.entity.BaseInventory;
import com.fls.stock.entity.BaseWarehouse;
import com.fls.stock.entity.BaseWhpos;
import com.fls.stock.external.wms.handle.client.WmsClient;
import com.fls.stock.external.wms.handle.stock.WmsService;
import com.fls.stock.external.wms.pojo.WmsStockQueryDTO;
import com.fls.stock.external.wms.pojo.WmsStockQueryResponseDTO;
import com.fls.stock.external.wms.pojo.WmsStockQueryResponseListDTO;
import com.fls.stock.pojo.dto.MaterialDTO;
import com.fls.stock.pojo.dto.MaterialDataDTO;
import com.fls.stock.pojo.dto.PrepareMaterialContextDTO;
import com.fls.stock.pojo.dto.WmsStockChangeDTO;
import com.fls.stock.pojo.dto.WmsStockChangeListDTO;
import com.fls.stock.pojo.query.MaterialNumReq;
import com.fls.stock.pojo.query.StockQuery;
import com.fls.stock.service.invoke.BaseWhposService;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2024/12/11 16:41
 **/
@Service
@Slf4j
@RequiredArgsConstructor
public class WmsServiceImpl implements WmsService {

    private final WmsConfig wmsConfig;

    private final WmsClient wmsClient;

    private final BaseWhposService baseWhposService;

    @Override
    public Object stockChange(BaseWarehouse warehouse, StockQuery query) {
//        查询库位编码
        BaseWhpos whpos = baseWhposService.lambdaQuery()
            .eq(BaseWhpos::getDeleteFlag, CommonConstants.DELETE_FLAG_NOT_DELETED)
            .eq(StrUtil.isNotBlank(query.getIdWhpos()), BaseWhpos::getIdWhpos, query.getIdWhpos())
            .eq(BaseWhpos::getPkRack, query.getPkWhpos())
            .last("limit 1")
            .one();
//        查询仓库货位下物料的获取批次号
        WmsStockQueryDTO queryDTO = new WmsStockQueryDTO();
        queryDTO.setFunNum(wmsConfig.getStockQueryFun());
        queryDTO.setOrgCode(warehouse.getWmsAlias());
        queryDTO.setWhcode(warehouse.getCode());
        queryDTO.setPoscode(whpos.getCode());
        WmsStockQueryResponseDTO wmsStockQueryResponseDTO = wmsClient.sendQuery(queryDTO);
        Map<String, String> invCodeMap = Maps.newHashMap();
        if (wmsStockQueryResponseDTO == null && CollUtil.isNotEmpty(wmsStockQueryResponseDTO.getList())) {
            List<WmsStockQueryResponseListDTO> list = wmsStockQueryResponseDTO.getList();
            invCodeMap = list.stream().collect(Collectors.toMap(WmsStockQueryResponseListDTO::getInvcode, WmsStockQueryResponseListDTO::getBatchcode));
        }
//        封装参数
        WmsStockChangeDTO dto = new WmsStockChangeDTO();
        dto.setFunNum(wmsConfig.getStockChangeListFun());
        dto.setOrgCode(warehouse.getWmsAlias());
        dto.setWhcode(warehouse.getCode());
        List<WmsStockChangeListDTO> list = Lists.newArrayList();
        for (MaterialNumReq item : query.getMaterialNumReq()) {
            WmsStockChangeListDTO changeDTO = new WmsStockChangeListDTO();
            changeDTO.setPoscode(whpos.getCode());
            changeDTO.setInvcode(item.getMaterialCode());
            changeDTO.setBatchcode(invCodeMap.get(item.getMaterialCode()));
            changeDTO.setQtyChange(item.getQuantity().negate());
            list.add(changeDTO);
        }
        dto.setList(list);
        JSONObject json = wmsClient.send(dto);
        if (json.getBool("IsSucc", false)) {
            return dto;
        }
        log.error("调用wms接口失败，失败原因为：{}", json);
        throw new RuntimeException(json.getStr("ResultMessage"));
    }


    /**
     * 逆向扣减
     *
     * @param query
     * @param whposCodeMaps
     * @param dto
     */
    private void reverseOperation(StockQuery query, Map<String, String> whposCodeMaps, WmsStockChangeDTO dto) {
        ArrayList<WmsStockChangeListDTO> list = Lists.newArrayList();
        for (MaterialNumReq item : query.getMaterialNumReq()) {
            WmsStockChangeListDTO changeDTO = new WmsStockChangeListDTO();
            changeDTO.setPoscode(whposCodeMaps.get(item.getIdStockInWhpos()));
            changeDTO.setInvcode(item.getMaterialCode());
            changeDTO.setBatchcode(item.getBatchCode());
            changeDTO.setQtyChange(item.getQuantity());
            list.add(changeDTO);
        }
        dto.setList(list);
    }

    /**
     * 正向扣减
     *
     * @param query
     * @param whposCodeMaps
     * @param dto
     */
    private static void forwardOperation(StockQuery query, Map<String, String> whposCodeMaps, WmsStockChangeDTO dto) {
        List<WmsStockChangeListDTO> list = Lists.newArrayList();
        for (MaterialNumReq item : query.getMaterialNumReq()) {
            WmsStockChangeListDTO changeDTO = new WmsStockChangeListDTO();
            changeDTO.setPoscode(whposCodeMaps.get(query.getIdWhpos()));
            changeDTO.setInvcode(item.getMaterialCode());
            changeDTO.setBatchcode(item.getBatchCode());
            changeDTO.setQtyChange(item.getQuantity().negate());
            list.add(changeDTO);
        }
        dto.setList(list);
    }

    private static void forwardOperationRollBack(StockQuery query, Map<String, String> whposCodeMaps, WmsStockChangeDTO dto) {
        ArrayList<WmsStockChangeListDTO> list = Lists.newArrayList();
        for (MaterialNumReq item : query.getMaterialNumReq()) {
            WmsStockChangeListDTO changeDTO = new WmsStockChangeListDTO();
            changeDTO.setPoscode(whposCodeMaps.get(query.getIdWhpos()));
            changeDTO.setInvcode(item.getMaterialCode());
            changeDTO.setBatchcode(item.getBatchCode());
            changeDTO.setQtyChange(item.getQuantity());
            list.add(changeDTO);
        }
        dto.setList(list);
    }


    @Override
    public WmsStockChangeDTO deductStock(PrepareMaterialContextDTO ctx) {
        return deductStock(ctx.getWarehouseOut(), ctx.getDataList());
    }

    @Override
    public WmsStockChangeDTO increaseStock(PrepareMaterialContextDTO ctx) {
        BaseWarehouse warehouse = ctx.getWarehouseIn();
        List<? extends MaterialDTO> dataList = ctx.getDataList();

        List<WmsStockChangeListDTO> changeList = Lists.newArrayList();
        for (MaterialDTO dto : dataList) {
            String batchCode = queryBatchCode(warehouse, dto);

            WmsStockChangeListDTO wmsStockChangeListDTO = new WmsStockChangeListDTO();
            wmsStockChangeListDTO.setQtyChange(dto.getNumber());
            wmsStockChangeListDTO.setInvcode(dto.getInventory().getCode());
            wmsStockChangeListDTO.setBatchcode(batchCode);
            wmsStockChangeListDTO.setPoscode(dto.getInWhpos().getCode());

            changeList.add(wmsStockChangeListDTO);
        }


        WmsStockChangeDTO dto = new WmsStockChangeDTO();
        dto.setFunNum(wmsConfig.getStockChangeListFun());
        dto.setOrgCode(warehouse.getWmsAlias());
        dto.setWhcode(warehouse.getCode());
        dto.setList(changeList);

        JSONObject json = wmsClient.send(dto);
        if (json.getBool("IsSucc", false)) {
            return dto;
        }
        log.error("调用wms接口失败，失败原因为：{}", json);
        throw new RuntimeException(json.getStr("ResultMessage"));

    }

    @NotNull
    private String queryBatchCode(BaseWarehouse warehouse, MaterialDTO dto) {
        WmsStockQueryDTO queryDTO = new WmsStockQueryDTO();
        queryDTO.setFunNum(wmsConfig.getStockQueryFun());
        queryDTO.setOrgCode(warehouse.getWmsAlias());
        queryDTO.setWhcode(dto.getInWhpos().getCode());
        queryDTO.setInvcode(dto.getInventory().getCode());

        WmsStockQueryResponseDTO wmsStockQueryResponseDTO = wmsClient.sendQuery(queryDTO);

        List<WmsStockQueryResponseListDTO> list = wmsStockQueryResponseDTO.getList();
        WmsStockQueryResponseListDTO first = CollUtil.getFirst(list);
        String batchCode = Optional.ofNullable(first)
            .map(WmsStockQueryResponseListDTO::getBatchcode)
            .orElse("000000");
        return batchCode;
    }

    private WmsStockChangeDTO deductStock(BaseWarehouse warehouse, List<? extends MaterialDTO> dataList) {
        synchronized (this) {
//        1、查询wms出库仓库货位信息
            List<String> invCodes = dataList.stream().map(MaterialDTO::getInventory).map(BaseInventory::getCode).collect(Collectors.toList());
            List<WmsStockQueryResponseListDTO> resultDTO = getWmsOutStockInfo(warehouse, invCodes);
//        2、判断总量
//        仓库中物料数据
            Map<String, List<WmsStockQueryResponseListDTO>> invInfoMaps = resultDTO.stream()
                .collect(Collectors.groupingBy(WmsStockQueryResponseListDTO::getInvcode));
//        统计每种物料总量
            Map<String, BigDecimal> invTotalMaps = aggregateInv(invInfoMaps);
            List<WmsStockChangeListDTO> changeList = Lists.newArrayList();
            for (Object o : dataList) {
                MaterialDataDTO dto = BeanUtil.toBean(o, MaterialDataDTO.class);
                BaseInventory baseInventory = dto.getInventory();
                BigDecimal invTotalNum = getInvTotalNum(invTotalMaps, baseInventory);
//            3、判断总量是否足够
                if (invTotalNum.compareTo(dto.getNumber()) < 0) {
                    throw new ServiceException(dto.getIdInventory() + "物料数量不足，无法进行扣减！");
                }
//            4、获取目标货位的物料数量
                List<WmsStockQueryResponseListDTO> invMappingWhops = invInfoMaps.get(baseInventory.getCode());
                String targetCode = Optional.ofNullable(dto.getOutWhpos())
                    .map(BaseWhpos::getCode)
                    .orElse(StrUtil.EMPTY);

                BigDecimal needNum = dto.getNumber();
                if (StrUtil.isNotEmpty(targetCode)) {
//                    扣减目标货位的物料数量
                    for (WmsStockQueryResponseListDTO invWhopsInfo : invMappingWhops) {

                        if (invWhopsInfo.getPoscode().equalsIgnoreCase(targetCode)) {
                            WmsStockChangeListDTO wmsStockChangeListDTO = new WmsStockChangeListDTO();
                            wmsStockChangeListDTO.setInvcode(baseInventory.getCode());
                            wmsStockChangeListDTO.setBatchcode(invWhopsInfo.getBatchcode());
                            wmsStockChangeListDTO.setPoscode(invWhopsInfo.getPoscode());

                            if (invWhopsInfo.obtainAvaileNum().compareTo(needNum) >= 0) {
//                                当前可用数量大于等于需要数量直接扣减
                                wmsStockChangeListDTO.setQtyChange(needNum.negate());
                                needNum = BigDecimal.ZERO;
                                changeList.add(wmsStockChangeListDTO);
                                break;
                            } else {
                                needNum = needNum.subtract(invWhopsInfo.obtainAvaileNum());
                                wmsStockChangeListDTO.setQtyChange(invWhopsInfo.obtainAvaileNum().negate());
                                changeList.add(wmsStockChangeListDTO);
                            }
                        }
                    }

                }

//              扣减非目标货位
                if (needNum.compareTo(BigDecimal.ZERO) > 0) {
                    for (WmsStockQueryResponseListDTO invWhopsInfo : invMappingWhops) {
                        if (!invWhopsInfo.getPoscode().equalsIgnoreCase(targetCode)) {
                            WmsStockChangeListDTO wmsStockChangeListDTO = new WmsStockChangeListDTO();
                            wmsStockChangeListDTO.setInvcode(baseInventory.getCode());
                            wmsStockChangeListDTO.setBatchcode(invWhopsInfo.getBatchcode());
                            wmsStockChangeListDTO.setPoscode(invWhopsInfo.getPoscode());
                            if (invWhopsInfo.obtainAvaileNum().compareTo(needNum) >= 0) {
//                                当前可用数量大于等于需要数量直接扣减
                                wmsStockChangeListDTO.setQtyChange(needNum.negate());
                                changeList.add(wmsStockChangeListDTO);
                                break;
                            } else {
                                needNum = needNum.subtract(invWhopsInfo.obtainAvaileNum());
                                wmsStockChangeListDTO.setQtyChange(invWhopsInfo.obtainAvaileNum().negate());
                                changeList.add(wmsStockChangeListDTO);
                            }
                        }
                    }
                }
            }

            WmsStockChangeDTO dto = new WmsStockChangeDTO();
            dto.setFunNum(wmsConfig.getStockChangeListFun());
            dto.setOrgCode(warehouse.getWmsAlias());
            dto.setWhcode(warehouse.getCode());
            dto.setList(changeList);

            JSONObject json = wmsClient.send(dto);
            if (json.getBool("IsSucc", false)) {
                return dto;
            }
            log.error("调用wms接口失败，失败原因为：{}", json);
            throw new RuntimeException(json.getStr("ResultMessage"));
        }
    }


    private BigDecimal getInvTotalNum(Map<String, BigDecimal> invTotalMaps, BaseInventory baseInventory) {
        return Optional.ofNullable(invTotalMaps.get(baseInventory.getCode()))
            .orElseThrow(() -> new ServiceException("仓库不存在该物料"));
    }

    private BaseInventory getBaseInventory(Map<String, BaseInventory> inventoryPk, String idInventory) {
        return Optional.ofNullable(inventoryPk.get(idInventory))
            .orElseThrow(() -> new ServiceException("无效的物料id"));
    }


    /**
     * 聚合物料数据
     *
     * @param invInfoMaps
     * @return
     */
    private Map<String, BigDecimal> aggregateInv(Map<String, List<WmsStockQueryResponseListDTO>> invInfoMaps) {
        Map<String, BigDecimal> invTotalMaps = Maps.newHashMap();
        for (String key : invInfoMaps.keySet()) {
            List<WmsStockQueryResponseListDTO> l = invInfoMaps.get(key);
            BigDecimal total = l.stream()
                .map(WmsStockQueryResponseListDTO::obtainAvaileNum)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            invTotalMaps.put(key, total);
        }
        return invTotalMaps;
    }

    private List<WmsStockQueryResponseListDTO> getWmsOutStockInfo(BaseWarehouse warehouse, List<String> invCodes) {
        List<WmsStockQueryResponseListDTO> result = Lists.newArrayList();
        WmsStockQueryDTO queryDTO = new WmsStockQueryDTO();
        queryDTO.setFunNum(wmsConfig.getStockQueryFun());
        queryDTO.setOrgCode(warehouse.getWmsAlias());
        queryDTO.setWhcode(warehouse.getCode());
//        根据传入的物料编码，查询wms的库存信息
        for (String invCode : invCodes) {
            queryDTO.setInvcode(invCode);
            WmsStockQueryResponseDTO wmsStockQueryResponseDTO = wmsClient.sendQuery(queryDTO);
            if (Objects.nonNull(wmsStockQueryResponseDTO) && CollUtil.isNotEmpty(wmsStockQueryResponseDTO.getList())) {
                result.addAll(wmsStockQueryResponseDTO.getList());
            }
        }
        return result;
    }


    @Override
    public Boolean rollBackStock(WmsStockChangeDTO rollBackData) {
        List<WmsStockChangeListDTO> list = rollBackData.getList();
        for (WmsStockChangeListDTO dto : list) {
            dto.setQtyChange(dto.getQtyChange().negate());
        }
        JSONObject json = wmsClient.send(rollBackData);
        if (json.getBool("IsSucc", false)) {
            return Boolean.TRUE;
        }
        log.error("调用wms接口失败，失败原因为：{}", json);
        throw new RuntimeException(json.getStr("ResultMessage"));
    }
}
