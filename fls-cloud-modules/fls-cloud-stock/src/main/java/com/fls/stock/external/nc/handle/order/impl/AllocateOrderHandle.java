package com.fls.stock.external.nc.handle.order.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fls.common.core.constant.CommonConstants;
import com.fls.common.core.exception.ServiceException;
import com.fls.stock.config.NcConfig;
import com.fls.stock.constant.StockConst;
import com.fls.stock.entity.BaseInventory;
import com.fls.stock.entity.BaseWhpos;
import com.fls.stock.entity.BdStordoc;
import com.fls.stock.entity.IcTransinB;
import com.fls.stock.entity.IcTransinH;
import com.fls.stock.entity.IcTransoutB;
import com.fls.stock.entity.IcTransoutH;
import com.fls.stock.entity.ToBill;
import com.fls.stock.entity.ToBillB;
import com.fls.stock.enums.BillTypeEnum;
import com.fls.stock.external.nc.handle.client.NcClient;
import com.fls.stock.external.nc.handle.order.NcOrderHandle;
import com.fls.stock.external.nc.pojo.dto.MaterialOutDTO;
import com.fls.stock.external.nc.pojo.dto.PrepareMaterialResultDTO;
import com.fls.stock.external.nc.pojo.request.AllocateInSignBodyDTO;
import com.fls.stock.external.nc.pojo.request.AllocateOrderBodyDTO;
import com.fls.stock.external.nc.pojo.request.AllocateOrderDetailDTO;
import com.fls.stock.external.nc.pojo.request.AllocateOutBodyDTO;
import com.fls.stock.external.nc.pojo.request.AllocateOutDetailDTO;
import com.fls.stock.external.nc.pojo.request.NcTemplateDTO;
import com.fls.stock.mapper.BdStordocMapper;
import com.fls.stock.mapper.IcTransinBMapper;
import com.fls.stock.mapper.IcTransinHMapper;
import com.fls.stock.mapper.IcTransoutBMapper;
import com.fls.stock.mapper.IcTransoutHMapper;
import com.fls.stock.mapper.NcMappper;
import com.fls.stock.mapper.ToBillBMapper;
import com.fls.stock.mapper.ToBillMapper;
import com.fls.stock.pojo.dto.MaterialDTO;
import com.fls.stock.pojo.dto.MaterialDataDTO;
import com.fls.stock.pojo.dto.OtherInDTO;
import com.fls.stock.pojo.dto.PrepareMaterialContextDTO;
import com.fls.stock.pojo.dto.PrepareMaterialOutDTO;
import com.fls.stock.pojo.dto.PrepareMaterialReceiveDTO;
import com.fls.stock.pojo.vo.ReceiveVO;
import com.fls.stock.service.invoke.BaseUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Description: 调拨单
 * @Author: caibenwei
 * @DATE: 2024/12/19 14:33
 */
@Slf4j
@RequiredArgsConstructor
@Service("ALLOCATE")
public class AllocateOrderHandle implements NcOrderHandle {

    private final NcConfig ncConfig;

    private final IcTransoutHMapper icTransoutHMapper;

    private final ToBillMapper toBillMapper;

    private final IcTransoutBMapper icTransoutBMapper;

    private final IcTransinBMapper icTransinBMapper;

    private final IcTransinHMapper icTransinHMapper;

    private final ToBillBMapper toBillBMapper;

    private final NcClient ncClient;

    private final NcMappper ncMappper;

    private final BdStordocMapper bdStordocMapper;

    private final BaseUserService userService;

    @Override
    public PrepareMaterialResultDTO createOperate(PrepareMaterialContextDTO ctx) {
//        获取用户
        String billmaker = userService.getUserNcPk(ctx.getUserCode());
//        获取税率
        BigDecimal taxRate = ncMappper.acceptTaxRate();
//        获取主含税单价
        Map<String, Object> priceMaps = getNorigtaxnetprice(ctx);
        PrepareMaterialResultDTO result = new PrepareMaterialResultDTO();
        AllocateOrderBodyDTO body = new AllocateOrderBodyDTO();
        body.setBillmaker(billmaker);
        body.setCinstockorgvid(ctx.getOrgIn().getPkOrg());
        body.setCtrantypeid(ctx.getOrderParamMaps().getStr("ctrantypeid"));
        body.setPk_org(ctx.getOrgOut().getPkOrg());
        body.setVdef7(StrUtil.EMPTY); //是否直运
        body.setVdef5(StrUtil.EMPTY); //用途
        body.setVdef18(ctx.getSourcePk()); //接口来源对象明细主键
        body.setCbiztypeid(ctx.getOrderParamMaps().getStr("cbiztypeid"));
        String ctaxcodeid = ctx.getOrderParamMaps().getStr("ctaxcodeid");
        List<AllocateOrderDetailDTO> detailList = Lists.newArrayList();
        for (Object o : ctx.getDataList()) {
            MaterialDataDTO dto = BeanUtil.toBean(o, MaterialDataDTO.class);
            AllocateOrderDetailDTO detailDTO = new AllocateOrderDetailDTO();
            detailDTO.setCinstordocid(ctx.getWarehouseIn().getPkStordoc());
            String cinventoryid = dto.getInventory().getPkMaterial();
            detailDTO.setCinventoryid(cinventoryid);
//          获取调拨入库货位
            String cinspaceid = Optional.ofNullable(dto.getInWhpos())
                .map(BaseWhpos::getPkRack)
                .orElse(null);
            detailDTO.setCinspaceid(cinspaceid); // 入库货位
            String coutspaceid = Optional.ofNullable(dto.getOutWhpos())
                .map(BaseWhpos::getPkRack)
                .orElse(null);
            detailDTO.setCoutspaceid(coutspaceid); // 出库货位
            detailDTO.setCoutstordocid(ctx.getWarehouseOut().getPkStordoc());
            detailDTO.setCtaxcodeid(ctaxcodeid);
            detailDTO.setDplanarrivedate(DateUtil.today());
            detailDTO.setDplanoutdate(DateUtil.today());
            detailDTO.setNnum(dto.getNumber());
            JSONObject jsonObject = JSONUtil.parseObj(priceMaps.get(cinventoryid));
            detailDTO.setNorigtaxnetprice(jsonObject.getBigDecimal("VDEF26"));
            detailDTO.setVbdef19(ctx.getOrderParamMaps().getStr("vnotebody"));
            detailDTO.setNtaxrate(taxRate);
            detailDTO.setVbdef20(StrUtil.EMPTY);
            detailList.add(detailDTO);
        }
        JSONObject response = ncClient.send(ncConfig.getAllocateOrder(), new NcTemplateDTO(body, detailList));
        if (response != null && response.getInt("code") == HttpStatus.OK.value()) {
            result.setBillType(BillTypeEnum.ALLOCATE.name());
            result.setPkNcBill(Convert.toStr(response.getByPath("data.billid")));
            result.setNcBillCode(Convert.toStr(response.getByPath("data.billno")));
            return result;
        }
        throw new ServiceException("NC转库单异常" + response);
    }


    private String getCinspaceid(BaseWhpos whpos) {
        String cinspaceid = null;
//        调拨入库是校验调入货位，如果某仓库启用了货位管理，传递货位，不启用不传
        if (Objects.nonNull(whpos)) {
            BdStordoc bdStordoc = bdStordocMapper.selectById(whpos.getPkRack());
            String csflay = Optional.ofNullable(bdStordoc).map(BdStordoc::getCsflag).orElse(StockConst.FLAG_FALSE);

            if (StrUtil.equals("0", csflay)) {
                cinspaceid = whpos.getPkRack();
            }
        }

        return cinspaceid;
    }

    private Map<String, Object> getNorigtaxnetprice(PrepareMaterialContextDTO ctx) {
        Set<String> pkMaterial = ctx.getDataList().stream()
            .map(MaterialDTO::getInventory)
            .map(BaseInventory::getPkMaterial)
            .collect(Collectors.toSet());
        Map<String, Object> priceMaps = ncMappper.acceptNorigtaxnetprice(pkMaterial);
        return priceMaps;
    }


    @Override
    public Boolean validatedOrderExists(String pkNcBill) {
//        校验调拨单是存在
        ToBill toBill = toBillMapper.selectById(pkNcBill);
        Assert.notNull(toBill, () -> new ServiceException("无效的调拨单pk"));
        Assert.isTrue(CommonConstants.DELETE_FLAG_NOT_DELETED.equals(toBill.getDr()), () -> new ServiceException("调拨单已被删除！"));
//        这里的调拨单是不会生成下游的调拨入和调拨出
        IcTransoutB icTransoutB = icTransoutBMapper.selectOne(new LambdaQueryWrapper<IcTransoutB>()
            .eq(IcTransoutB::getDr, CommonConstants.DELETE_FLAG_NOT_DELETED)
            .eq(IcTransoutB::getCsourcebillhid, pkNcBill));
        Assert.isNull(icTransoutB, () -> new ServiceException("订单类型错误，已生成调拨出订单！"));
        return Boolean.TRUE;
    }

    @Override
    public Boolean hasSuccessWmsOutOperate(PrepareMaterialOutDTO dto) {
        IcTransoutB transoutB = icTransoutBMapper.selectOne(new LambdaQueryWrapper<IcTransoutB>()
            .eq(IcTransoutB::getDr, CommonConstants.DELETE_FLAG_NOT_DELETED)
            .eq(IcTransoutB::getCsourcebillhid, dto.getPkNcBill())
            .last("AND ROWNUM = 1"));
        Assert.notNull(transoutB, () -> new ServiceException("仓库启用wms，需要在wms操作出库！"));
        IcTransoutH icTransoutH = icTransoutHMapper.selectById(transoutB.getCgeneralhid());
        Assert.notNull(icTransoutH, () -> new ServiceException("仓库启用wms，需要在wms操作出库！！"));
//        单据状态为签字
        Assert.isTrue(icTransoutH.getFbillflag() == 3, () -> new ServiceException("wms操作未签字！！!"));
        return Boolean.TRUE;
    }


    @Override
    public Object outOperate(MaterialOutDTO dto) {
        AllocateOutBodyDTO body = new AllocateOutBodyDTO();
        BeanUtils.copyProperties(dto, body);
        body.setCgeneralhid(dto.getCgeneralhid());
        List<ToBillB> toBillBs = toBillBMapper.selectList(new LambdaQueryWrapper<ToBillB>()
            .eq(ToBillB::getCbillid, dto.getCgeneralhid())
            .eq(ToBillB::getDr, CommonConstants.DELETE_FLAG_NOT_DELETED));
        Assert.notNull(toBillBs, () -> new ServiceException("无效的调拨订单！！"));
        List<AllocateOutDetailDTO> list = Lists.newArrayList();
        for (ToBillB toBillB : toBillBs) {
            AllocateOutDetailDTO detailDTO = new AllocateOutDetailDTO();
            detailDTO.setNnum(toBillB.getNnum());
            detailDTO.setCgeneralbid(toBillB.getCbillBid());
            list.add(detailDTO);
        }
        JSONObject response = ncClient.send(ncConfig.getAllocateOut(), new NcTemplateDTO(body, list));
        if (response != null && response.getInt("code") == HttpStatus.OK.value()) {
            return response;
        }
        throw new ServiceException("NC转库单异常" + response);
    }


    @Override
    public JSONObject receiveOperate(PrepareMaterialReceiveDTO dto) {
//        获取用户
        String billmaker = userService.getUserNcPk(dto.getUserCode());
//        调用调拨入库签字
        AllocateInSignBodyDTO body = new AllocateInSignBodyDTO();
        body.setApprover(billmaker);
        body.setCgeneralhid(dto.getPkIcIn());
        JSONObject response = ncClient.send(ncConfig.getAllocateInSign(), new NcTemplateDTO(body, null));
        if (response != null && response.getInt("code") == HttpStatus.OK.value()) {
            return response;
        }
        throw new ServiceException("NC调拨入库签字异常" + response);
    }

    @Override
    public PrepareMaterialResultDTO supplementOtherInAndOut(PrepareMaterialResultDTO operate) {
        IcTransoutB icTransoutB = icTransoutBMapper.selectOne(new LambdaQueryWrapper<IcTransoutB>()
            .eq(IcTransoutB::getDr, CommonConstants.DELETE_FLAG_NOT_DELETED)
            .eq(IcTransoutB::getCsourcebillhid, operate.getPkNcBill())
            .last("AND ROWNUM = 1"));
        IcTransinB icTransinB = icTransinBMapper.selectOne(new LambdaQueryWrapper<IcTransinB>()
            .eq(IcTransinB::getDr, CommonConstants.DELETE_FLAG_NOT_DELETED)
            .eq(IcTransinB::getCsourcebillhid, operate.getPkNcBill())
            .last("AND ROWNUM = 1"));

        String pkOtherOut = Optional.ofNullable(icTransoutB).map(IcTransoutB::getCgeneralhid).orElse("");
        operate.setPkOtherOut(pkOtherOut);
        String pkOtherIn = Optional.ofNullable(icTransinB).map(IcTransinB::getCgeneralhid).orElse("");
        operate.setPkOtherIn(pkOtherIn);
        return operate;
    }

    @Override
    public String supplementOtherIn(String pkNcBill) {
        String result = StrUtil.EMPTY;
        IcTransoutB icTransoutB = icTransoutBMapper.selectOne(new LambdaQueryWrapper<IcTransoutB>()
            .eq(IcTransoutB::getDr, CommonConstants.DELETE_FLAG_NOT_DELETED)
            .eq(IcTransoutB::getCsourcebillhid, pkNcBill)
            .last("AND ROWNUM = 1"));
        Optional<String> outpk = Optional.ofNullable(icTransoutB)
            .map(IcTransoutB::getCgeneralhid);
        if (outpk.isPresent()) {
            IcTransinB icTransinB = icTransinBMapper.selectOne(new LambdaQueryWrapper<IcTransinB>()
                .eq(IcTransinB::getDr, CommonConstants.DELETE_FLAG_NOT_DELETED)
                .eq(IcTransinB::getCsourcebillhid, outpk.get())
                .last("AND ROWNUM = 1"));
            result = Optional.ofNullable(icTransinB)
                .map(IcTransinB::getCgeneralhid)
                .orElse("");
        }
        return result;
    }

    @Override
    public OtherInDTO supplementOtherInInfo(String pkNcBill) {
        OtherInDTO result = new OtherInDTO();
        IcTransoutB icTransoutB = icTransoutBMapper.selectOne(new LambdaQueryWrapper<IcTransoutB>()
            .eq(IcTransoutB::getDr, CommonConstants.DELETE_FLAG_NOT_DELETED)
            .eq(IcTransoutB::getCsourcebillhid, pkNcBill)
            .last("AND ROWNUM = 1"));
        Optional<String> outpk = Optional.ofNullable(icTransoutB)
            .map(IcTransoutB::getCgeneralhid);
        if (outpk.isPresent()) {
            IcTransinB icTransinB = icTransinBMapper.selectOne(new LambdaQueryWrapper<IcTransinB>()
                .eq(IcTransinB::getDr, CommonConstants.DELETE_FLAG_NOT_DELETED)
                .eq(IcTransinB::getCsourcebillhid, outpk.get())
                .last("AND ROWNUM = 1"));
            String pkOtherIn = Optional.ofNullable(icTransinB)
                .map(IcTransinB::getCgeneralhid)
                .orElse("");
            result.setPkIcIn(pkOtherIn);
            if (StrUtil.isNotBlank(pkOtherIn)) {
                IcTransinH icTransinH = icTransinHMapper.selectById(pkOtherIn);
                String vbillcode = Optional.ofNullable(icTransinH)
                    .map(IcTransinH::getVbillcode)
                    .orElse("");
                result.setIcCodeIn(vbillcode);
            }
        }


        return result;
    }


    @Override
    public ReceiveVO supplementReceiveByIcIn(String pkIcIn) {
        ReceiveVO result = new ReceiveVO();
        result.setBillType(BillTypeEnum.ALLOCATE.name());
        result.setPkIcIn(pkIcIn);

        IcTransinH icTransinH = icTransinHMapper.selectById(pkIcIn);
        result.setIcCodeIn(icTransinH.getVbillcode());
        String codeNcBill = Optional.ofNullable(icTransinH)
            .map(IcTransinH::getVbillcode)
            .orElse("");
        result.setNcBillCode(codeNcBill);

        IcTransinB icTransinB = icTransinBMapper.selectOne(new LambdaQueryWrapper<IcTransinB>()
            .eq(IcTransinB::getDr, CommonConstants.DELETE_FLAG_NOT_DELETED)
            .eq(IcTransinB::getCgeneralhid, pkIcIn)
            .last("AND ROWNUM = 1"));

        if (icTransinB != null && icTransinB.getCfirstbillhid() != null) {
            result.setPkNcBill(icTransinB.getCfirstbillhid());
            ToBill toBill = toBillMapper.selectById(icTransinB.getCfirstbillhid());
            result.setNcBillCode(toBill.getVbillcode());
        }
        return result;
    }
}
