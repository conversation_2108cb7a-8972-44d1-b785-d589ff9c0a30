package com.fls.stock.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * @Description:
 * @Author: caibenwei
 * @DATE: 2024/12/23 17:39
 */
@Data
@Component
@ConfigurationProperties(prefix = "nc.param")
public class NcParamConfig {

//    备料单锁定
    private String lockTransferVtrantypecode;
    private String lockALlocateCbiztypeid;
    private String lockALlocateCtaxcodeid;
    private String lockALlocateCtrantypeid;

//    归还物料
    private String returnTransferVtrantypecode;
    private String returnALlocateCbiztypeid;
    private String returnALlocateCtaxcodeid;
    private String returnALlocateCtrantypeid;

//    自助领料
    private String receiveTransferVtrantypecode;
    private String receiveALlocateCtaxcodeid;
    private String receiveALlocateCtrantypeid;


}
