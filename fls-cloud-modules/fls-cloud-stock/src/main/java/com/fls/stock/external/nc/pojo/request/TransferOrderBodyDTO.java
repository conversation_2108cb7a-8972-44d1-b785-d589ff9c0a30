package com.fls.stock.external.nc.pojo.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description:
 * @Author: caibenwei
 * @DATE: 2024/12/18 11:03
 */
@Data
public class TransferOrderBodyDTO {

    @Schema(description = "入库仓库，仓库PK")
    private String cotherwhid;

    @Schema(description = "创建人，用户PK")
    private String creator;

    @Schema(description = "创建时间 ，格式要求： yyyy-mm-dd hh24:mi:ss")
    private String creationtime;

    @Schema(description = "出库仓库，仓库PK")
    private String cwarehouseid;

    @Schema(description = "单据日期，格式要求： yyyy-mm-dd")
    private String dbilldate;

    @Schema(description = "库存组织最新版本，库存组织PK")
    private String pk_org;

    @Schema(description = "用途，自定义档案PK，可传空值")
    private String vdef5;

    @Schema(description = "来源系统标识")
    private String vdef7;

    @Schema(description = "接口来源对象主键")
    private String vdef8;

    private String vdef12;

    private String vnote;

    @Schema(description = "控制标识，控制下游库存其它出入库单据是否完成签字（1、不签字；2、自动签字）")
    private String vdef15;

    private String vdef17;

    @Schema(description = "接口生成消息，可传空值")
    private String vdef18;

    @Schema(description = "转库单交易类型编码")
    private String vtrantypecode;
}
