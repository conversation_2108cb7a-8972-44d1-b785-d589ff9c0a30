package com.fls.stock.external.wms.handle.stock;

import com.fls.stock.entity.BaseWarehouse;
import com.fls.stock.pojo.dto.PrepareMaterialContextDTO;
import com.fls.stock.pojo.dto.WmsStockChangeDTO;
import com.fls.stock.pojo.query.StockQuery;

/**
 * <AUTHOR>
 * @create 2024/12/11 16:41
 **/
public interface WmsService {
    Object stockChange(BaseWarehouse warehouse, StockQuery query);

    WmsStockChangeDTO deductStock(PrepareMaterialContextDTO ctx);

    WmsStockChangeDTO increaseStock(PrepareMaterialContextDTO ctx);

    Boolean rollBackStock(WmsStockChangeDTO rollBackData);
}
