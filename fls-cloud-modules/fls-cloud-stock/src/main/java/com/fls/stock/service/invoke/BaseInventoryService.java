package com.fls.stock.service.invoke;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fls.stock.entity.BaseInventory;
import com.fls.stock.pojo.dto.MaterialDTO;

import java.util.List;
import java.util.Map;

/**
 * 物料表(BaseInventory)表服务接口
 *
 * <AUTHOR>
 * @since 2024-12-18 15:12:27
 */
public interface BaseInventoryService extends IService<BaseInventory> {

    Map<String, BaseInventory> getInventoryEntityMaps(List<? extends MaterialDTO> dataList);
}

