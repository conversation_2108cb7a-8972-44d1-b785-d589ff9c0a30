package com.fls.stock.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * @Description:
 * @Author: caibenwei
 * @DATE: 2024/12/19 23:30
 */
@Data
public class MaterialReturnSignDTO extends BaseApiDTO{

    @Schema(description = "工作班组id")
    @NotBlank(message = "工作班组id不能为空！")
    private String idWorkTeam;

    @Schema(description = "维修工id")
    @NotBlank(message = "维修工id不能为空！")
    private String idPerson;

    @Schema(description = "归还目标仓库")
    @NotBlank(message = "归还目标仓库不能为空！")
    private String idWarehourse;

    @Schema(description = "退料单据号")
    private String billCode;

    @Schema(description = "接口来源对象主键")
    @NotBlank(message = "接口来源对象主键不能为空！")
    private String idSource;

    @Schema(description = "物料明细")
    @NotNull(message = "物料明细数据不能为空")
    @Size(min = 1, message = "物料明细数据不能为空")
    private List<MaterialReturnSignDataDTO> dataList;
}
