package com.fls.stock.pojo.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

/**
 * 班组库存查询参数
 *
 * <AUTHOR>
 * @since 2024-12-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BasePageQuery extends BaseAuthQuery{
    @Schema(description = "页码")
    @Min(value = 1, message = "页码小于1")
    private long pageNo = 1;

    @Schema(description = "页大小")
    @Min(value = 1, message = "分页大小不能小于1")
    @Max(value = 5000, message = "分页大小不能超过5000")
    private long pageSize = 20;
}
