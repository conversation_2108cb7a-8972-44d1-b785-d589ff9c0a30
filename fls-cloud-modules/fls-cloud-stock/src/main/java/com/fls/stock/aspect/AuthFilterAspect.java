package com.fls.stock.aspect;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fls.common.mybatis.core.page.PageResult;
import com.fls.master.api.RemoteMasterDataService;
import com.fls.stock.annotation.AuthFilter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * 权限过滤切面
 *
 * <AUTHOR>
 * @since 2025-08-07
 */
@Aspect
@Component
@Slf4j
@RequiredArgsConstructor
public class AuthFilterAspect {

    @DubboReference
    private RemoteMasterDataService masterDataService;

    @Around("@annotation(authFilter)")
    public Object doAuthFilter(ProceedingJoinPoint joinPoint, AuthFilter authFilter) throws Throwable {
        Object[] args = joinPoint.getArgs();
        Object queryParam = args[0];
        if (queryParam == null) {
            return joinPoint.proceed();
        }
        // 获取用户ID和href
        String authId = (String) ReflectUtil.getFieldValue(queryParam, authFilter.userIdField());
        String href = (String) ReflectUtil.getFieldValue(queryParam, authFilter.hrefField());
        if (StrUtil.isNotBlank(authId) && StrUtil.isNotBlank(href)) {
            List<String> authBizunitIds = masterDataService.getAuthBizunitIds(authId, href);
            if (CollUtil.isEmpty(authBizunitIds)) {
                // 返回空结果
                return createEmptyPageResult(joinPoint);
            }
            // 设置权限经营主体ID列表
            ReflectUtil.setFieldValue(queryParam, authFilter.authBizunitIdsField(), authBizunitIds);
        }
        return joinPoint.proceed();
    }

    private Object createEmptyPageResult(ProceedingJoinPoint joinPoint) {
        // 根据返回类型创建空结果
        Class<?> returnType = ((org.aspectj.lang.reflect.MethodSignature) joinPoint.getSignature()).getReturnType();
        if (PageResult.class.isAssignableFrom(returnType)) {
            return new PageResult<>(new Page<>(), Collections.emptyList());
        }
        return null;
    }
}
