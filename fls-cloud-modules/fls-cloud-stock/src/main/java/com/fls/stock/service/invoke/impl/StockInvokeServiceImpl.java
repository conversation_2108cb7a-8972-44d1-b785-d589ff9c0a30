package com.fls.stock.service.invoke.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fls.common.core.constant.CommonConstants;
import com.fls.common.core.exception.ServiceException;
import com.fls.common.mybatis.core.page.PageResult;
import com.fls.stock.constant.StockConst;
import com.fls.stock.entity.BaseCustEntity;
import com.fls.stock.entity.BaseInventory;
import com.fls.stock.entity.BaseOrg;
import com.fls.stock.entity.BasePerson;
import com.fls.stock.entity.BaseUser;
import com.fls.stock.entity.BaseWarehouse;
import com.fls.stock.entity.BaseWhpos;
import com.fls.stock.entity.BaseWorkteam;
import com.fls.stock.entity.BaseWorkteamMember;
import com.fls.stock.entity.WorkteamTransactionDetail;
import com.fls.stock.entity.WorkteamTransactionLog;
import com.fls.stock.external.nc.adapter.MaterialReturnAdapter;
import com.fls.stock.external.nc.adapter.PrepareMaterialLockAdapter;
import com.fls.stock.external.nc.adapter.PrepareMaterialOutAdapter;
import com.fls.stock.external.nc.adapter.ReceiveAdapter;
import com.fls.stock.external.nc.adapter.TransferAdapter;
import com.fls.stock.external.nc.handle.order.NcOrderFactory;
import com.fls.stock.external.nc.handle.order.NcOrderHandle;
import com.fls.stock.external.nc.handle.stock.NcTransferService;
import com.fls.stock.external.nc.pojo.dto.PrepareMaterialResultDTO;
import com.fls.stock.external.nc.pojo.request.NcTemplateDTO;
import com.fls.stock.external.wms.handle.stock.WmsService;
import com.fls.stock.pojo.builder.PrepareMaterialContextBuilder;
import com.fls.stock.pojo.dto.MaterialDataDTO;
import com.fls.stock.pojo.dto.MaterialReturnDTO;
import com.fls.stock.pojo.dto.MaterialReturnSignDTO;
import com.fls.stock.pojo.dto.MaterialReturnSignDataDTO;
import com.fls.stock.pojo.dto.PrepareMaterialContextDTO;
import com.fls.stock.pojo.dto.PrepareMaterialCreateDTO;
import com.fls.stock.pojo.dto.PrepareMaterialOutDTO;
import com.fls.stock.pojo.dto.PrepareMaterialReceiveDTO;
import com.fls.stock.pojo.dto.StockContextDTO;
import com.fls.stock.pojo.dto.TransferDTO;
import com.fls.stock.pojo.dto.TransferMaterialDTO;
import com.fls.stock.pojo.dto.WmsStockChangeDTO;
import com.fls.stock.pojo.query.LockLogQuery;
import com.fls.stock.pojo.query.StockQuery;
import com.fls.stock.pojo.query.TransactionLogQuery;
import com.fls.stock.pojo.vo.BarcodeVO;
import com.fls.stock.pojo.vo.LockLogVO;
import com.fls.stock.pojo.vo.MaterialReturnSignVO;
import com.fls.stock.pojo.vo.MaterialReturnVO;
import com.fls.stock.pojo.vo.ReceiveVO;
import com.fls.stock.pojo.vo.TransactionLogVO;
import com.fls.stock.pojo.vo.TransferVO;
import com.fls.stock.service.invoke.BaseCustService;
import com.fls.stock.service.invoke.BaseInventoryService;
import com.fls.stock.service.invoke.BaseOrgService;
import com.fls.stock.service.invoke.BasePersonService;
import com.fls.stock.service.invoke.BaseUserService;
import com.fls.stock.service.invoke.BaseWarehouseService;
import com.fls.stock.service.invoke.BaseWhposService;
import com.fls.stock.service.invoke.LabelBatchService;
import com.fls.stock.service.invoke.StockInvokeService;
import com.fls.stock.service.invoke.StockValidatedService;
import com.fls.stock.service.woekteam.BaseWorkteamMemberService;
import com.fls.stock.service.woekteam.BaseWorkteamService;
import com.fls.stock.service.woekteam.IWorkteamStockLockRecordService;
import com.fls.stock.service.woekteam.IWorkteamTransactionLogService;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2024/12/10 21:13
 **/
@Service
@Slf4j
@RequiredArgsConstructor
public class StockInvokeServiceImpl implements StockInvokeService {

    private final IWorkteamTransactionLogService transactionLog;

    private final IWorkteamStockLockRecordService stockLockRecord;

    private final LabelBatchService labelBatchService;

    private final BaseWarehouseService warehouseService;

    private final BasePersonService personService;

    private final BaseOrgService orgService;

    private final BaseInventoryService inventoryService;

    private final BaseWhposService whposService;

    private final PrepareMaterialLockAdapter createOrderAdapter;

    private final MaterialReturnAdapter returnAdapter;

    private final ReceiveAdapter receiveAdapter;

    private final PrepareMaterialOutAdapter materialOutAdapter;

    private final TransferAdapter transferAdapter;

    private final WmsService wmsService;

    private final NcTransferService ncTransferService;

    private final StockValidatedService validatedStock;

    private final NcOrderFactory ncOrderFactory;

    private final BaseUserService userService;

    private final BaseCustService custService;

    private final BaseWorkteamMemberService workteamMemberService;

    private final BaseWorkteamService workteamService;


    @Override
    public PageResult<TransactionLogVO> getTransactionLog(TransactionLogQuery query) {
        MPJLambdaWrapper<WorkteamTransactionLog> wrapper = new MPJLambdaWrapper<>();
        wrapper.selectAll(WorkteamTransactionDetail.class)
            .selectAs(WorkteamTransactionDetail::getIdTransactionDetail, TransactionLogVO::getId)
//            .selectAs(WorkteamTransactionDetail::getIdTransactionDetail, TransactionLogVO::getIdTransaction)
            .selectAs(WorkteamTransactionLog::getTransactionCode, TransactionLogVO::getTransactionCode)
            .selectAs(WorkteamTransactionLog::getLogType, TransactionLogVO::getType)
            .selectAs(WorkteamTransactionLog::getSourceType, TransactionLogVO::getSourceType)
            .selectAs(WorkteamTransactionLog::getSourceBillCode, TransactionLogVO::getSourceBillCode)
            .selectAs(WorkteamTransactionLog::getIdSourceBill, TransactionLogVO::getIdSourceBill)
            .selectAs(WorkteamTransactionLog::getFirstSourceBillCode, TransactionLogVO::getFirstSourceBillCode)
            .selectAs(WorkteamTransactionLog::getIdFirstSourceBill, TransactionLogVO::getIdFirstSourceBill)
            .leftJoin(WorkteamTransactionDetail.class, WorkteamTransactionDetail::getIdTransactionDetail, WorkteamTransactionLog::getIdTransactionLog)
            .eq(WorkteamTransactionLog::getDeleteFlag, CommonConstants.DELETE_FLAG_NOT_DELETED)
            .eq(WorkteamTransactionDetail::getDeleteFlag, CommonConstants.DELETE_FLAG_NOT_DELETED)
            .ge(Objects.nonNull(query.getBeginDate()), "DATE(t.create_time)", query.getBeginDate())
            .le(Objects.nonNull(query.getEndDate()), "DATE(t.create_time)", query.getEndDate())
            .eq(StrUtil.isNotBlank(query.getCode()), WorkteamTransactionLog::getTransactionCode, query.getCode())
            .eq(StrUtil.isNotBlank(query.getMaterialName()), WorkteamTransactionDetail::getMaterialName, query.getMaterialName())
            .and(StrUtil.isNotBlank(query.getName()),
                wp -> wp.like(WorkteamTransactionDetail::getMaterialName, query.getName())
                    .or()
                    .like(WorkteamTransactionDetail::getOrgName, query.getName())
                    .or()
                    .like(WorkteamTransactionDetail::getBizunitName, query.getName())
            )
            .eq(Objects.nonNull(query.getType()), WorkteamTransactionLog::getLogType, query.getType());
        Page<TransactionLogVO> page = transactionLog.selectJoinListPage(new Page<>(query.getPageNo(), query.getPageSize()), TransactionLogVO.class, wrapper);
        return new PageResult(page, page.getRecords());
    }

    @Override
    public PageResult<LockLogVO> getLockLog(LockLogQuery query) {
//        Page<LockLogVO> page = stockLockRecord.getLockLog(new Page<>(query.getPageNo(), query.getPageSize()), query);
//        return new PageResult(page, page.getRecords());
        return null;
    }


    @Override
    public BarcodeVO getBarcode(String code) {
        if (!StrUtil.contains(code, "-")) {
            code = labelBatchService.selectBarcodeByCode(code);
        }
        return new BarcodeVO(code);
    }

    @Override
    public Boolean stockOut(StockQuery query) {
        StockContextDTO context = validatedStock.stockOut(query);
//        查询仓库信息
        BaseWarehouse warehouse = getBaseWarehouse(query);
//        先出货在入货
        if (StrUtil.equals(warehouse.getWmsFlag(), StockConst.FLAG_TRUE)) {
            wmsService.stockChange(warehouse, query);
        }
//        调用nc服务
        ncTransferService.stockChange(warehouse, query);
        return Boolean.TRUE;
    }


    @Override
    public Boolean stockIn(StockQuery query) {
        StockContextDTO context = validatedStock.stockIn(query);
//        查询仓库信息
        BaseWarehouse warehouse = getBaseWarehouse(query);
//        先出货在入货
        if (StrUtil.equals(warehouse.getWmsFlag(), StockConst.FLAG_TRUE)) {
            wmsService.stockChange(warehouse, query);
        }
//        调用nc服务
        ncTransferService.stockChange(warehouse, query);

        return Boolean.TRUE;
    }

    @Override
    public Boolean inventoryChange(StockQuery query) {
//        查询仓库信息
        BaseWarehouse warehouse = getBaseWarehouse(query);
//        先出货在入货
        if (StrUtil.equals(warehouse.getWmsFlag(), StockConst.FLAG_TRUE)) {
            wmsService.stockChange(warehouse, query);
        }
//        调用nc服务
        ncTransferService.stockChange(warehouse, query);

        return Boolean.TRUE;
    }


    private BaseWarehouse getBaseWarehouse(StockQuery query) {
        // 根据入参的仓库id和仓库pk查询
        BaseWarehouse warehouse = warehouseService.lambdaQuery()
            .eq(BaseWarehouse::getDeleteFlag, CommonConstants.DELETE_FLAG_NOT_DELETED)
            .eq(StrUtil.isNotBlank(query.getIdWarehouse()), BaseWarehouse::getIdWarehouse, query.getIdWarehouse())
            .eq(BaseWarehouse::getPkStordoc, query.getPkWarehouse())
            .last("limit 1")
            .one();
        Assert.notNull(warehouse, () -> new ServiceException("无效的仓库id"));
        return warehouse;
    }


    @Override
    public PrepareMaterialResultDTO prepareMaterialLock(PrepareMaterialCreateDTO dto) {
//        前置校验
        Pair<BaseWorkteam, List<BaseWorkteamMember>> workteamInfo = validatedStock.preMaterial(dto.getIdWorkTeam(), dto.getIdPerson(), dto.getIdWarehouseOut(), dto.getDataList());
//        获取出库仓库信息
        Pair<BaseWarehouse, BaseOrg> outStockInfo = queryStockInfoByWhposId(dto.getIdWarehouseOut(), dto.getDataList().get(0).getIdWhpos());
//        获取入库仓库信息
        Triple<BaseWarehouse, BaseWhpos, BaseOrg> inStockInfo = queryStockInfoByWorkTeamAndWhpos(workteamInfo, dto.getIdPerson());
//        封装请求上下文
        PrepareMaterialContextDTO ctx = buildContext(dto, workteamInfo, outStockInfo, inStockInfo);
//        判断组织是否相同
        Boolean hasSameOrg = hasSameOrg(workteamInfo, outStockInfo.getLeft());
//        生成订单
        PrepareMaterialResultDTO result = createOrderAdapter.onCreateOrder(hasSameOrg, ctx);
        result.setIdWarehourseIn(ctx.getWarehouseIn().getIdWarehouse());
        result.setIdWhposIn(ctx.getDataList().get(0).getInIdWhpos());
        return result;
    }


    private PrepareMaterialContextDTO buildContext(PrepareMaterialCreateDTO dto, Pair<BaseWorkteam, List<BaseWorkteamMember>> workteamInfo, Pair<BaseWarehouse, BaseOrg> outStockInfo, Triple<BaseWarehouse, BaseWhpos, BaseOrg> inStockInfo) {
        Map<String, BaseInventory> inventoryPk = inventoryService.getInventoryEntityMaps(dto.getDataList());
        Set<String> whposId = dto.getDataList().stream().map(MaterialDataDTO::getIdWhpos).filter(StrUtil::isNotBlank).collect(Collectors.toSet());
        Map<String, BaseWhpos> outWhposPk = whposService.getWhposEntityMaps(whposId);

        return PrepareMaterialContextBuilder.builder()
            .withMaterialCreate(dto)
            .withInventoryPk(inventoryPk, inStockInfo.getMiddle(), outWhposPk)
            .withWorkTeamInfo(workteamInfo.getLeft())
            .withOutStockInfo(outStockInfo)
            .withInStockInfo(inStockInfo)
            .build();
    }

    private PrepareMaterialContextDTO buildContext(MaterialReturnDTO dto, Pair<BaseWorkteam, List<BaseWorkteamMember>> workteamInfo, Triple<BaseWarehouse, BaseWhpos, BaseOrg> outStockInfo, Triple<BaseWarehouse, BaseWhpos, BaseOrg> inStockInfo) {
        Map<String, BaseInventory> inventoryPk = inventoryService.getInventoryEntityMaps(dto.getDataList());

        return PrepareMaterialContextBuilder.builder()
            .withMaterialReturn(dto)
            .withInventoryPk(inventoryPk, outStockInfo.getMiddle(), inStockInfo.getMiddle())
            .withWorkTeamInfo(workteamInfo.getLeft())
            .withOutStockInfo(Pair.of(outStockInfo.getLeft(), outStockInfo.getRight()))
            .withInStockInfo(inStockInfo)
            .build();
    }


    private PrepareMaterialContextDTO buildContext(MaterialReturnSignDTO dto, Pair<BaseWorkteam, List<BaseWorkteamMember>> workteamInfo, Triple<BaseWarehouse, BaseWhpos, BaseOrg> outStockInfo, Pair<BaseWarehouse, BaseOrg> inStockInfo) {
        Map<String, BaseInventory> inventoryPk = inventoryService.getInventoryEntityMaps(dto.getDataList());
        Set<String> inWhposId = dto.getDataList().stream().map(MaterialReturnSignDataDTO::getIdWhpos).filter(StrUtil::isNotBlank).collect(Collectors.toSet());
        Assert.isTrue(CollUtil.isNotEmpty(inWhposId), () -> new ServiceException("货位id不能为空"));
        Map<String, BaseWhpos> inWhposEntityMaps = whposService.getWhposEntityMaps(inWhposId);

        return PrepareMaterialContextBuilder.builder()
            .withMaterialReturnSign(dto)
            .withInventoryAndWhpos(inventoryPk, outStockInfo.getMiddle(), inWhposEntityMaps)
            .withWorkTeamInfo(workteamInfo.getLeft())
            .withOutStockInfo(Pair.of(outStockInfo.getLeft(), outStockInfo.getRight()))
            .withInStockInfo(inStockInfo)
            .build();
    }

    private PrepareMaterialContextDTO buildContext(TransferDTO dto, BaseWarehouse inWarehouse, BaseWarehouse outWarehouse, BaseOrg orgIn) {
        Map<String, BaseInventory> inventoryPk = inventoryService.getInventoryEntityMaps(dto.getDataList());
        Set<String> inWhposId = dto.getDataList().stream().map(TransferMaterialDTO::getInIdWhpos).filter(StrUtil::isNotBlank).collect(Collectors.toSet());
        Map<String, BaseWhpos> inWhposEntityMaps = whposService.getWhposEntityMaps(inWhposId);
        Set<String> outWhposId = dto.getDataList().stream().map(TransferMaterialDTO::getOutIdWhpos).filter(StrUtil::isNotBlank).collect(Collectors.toSet());
        Map<String, BaseWhpos> outWhposEntityMaps = whposService.getWhposEntityMaps(outWhposId);

        return PrepareMaterialContextBuilder.builder()
            .withTransfer(dto, inWarehouse, outWarehouse, orgIn)
            .withInventoryPk(inventoryPk)
            .withInWhposPk(inWhposEntityMaps)
            .withOutWhposPk(outWhposEntityMaps)
            .build();
    }

    private Boolean hasSameOrg(Pair<BaseWorkteam, List<BaseWorkteamMember>> workteamInfo, BaseWarehouse warehouseOut) {
//        如果传入的人员id不为空.判断人员的班组信息的组织是否和出库仓库组织相同，否则判断工作班组组织是否和出库仓库组织相同
        if (CollUtil.isNotEmpty(workteamInfo.getRight())) {
            List<String> persionWorkTeams = workteamInfo.getRight()
                .stream()
                .map(BaseWorkteamMember::getIdWorkteam)
                .collect(Collectors.toList());
            List<String> idOrgs = workteamService.lambdaQuery()
                .select(BaseWorkteam::getIdOrg)
                .eq(BaseWorkteam::getDeleteFlag, CommonConstants.DELETE_FLAG_NOT_DELETED)
                .in(BaseWorkteam::getIdWorkteam, persionWorkTeams)
                .list()
                .stream()
                .map(BaseWorkteam::getIdOrg)
                .collect(Collectors.toList());
            return CollUtil.contains(idOrgs, warehouseOut.getIdOrg());
        }
        return workteamInfo.getLeft().getIdOrg().equals(warehouseOut.getIdOrg());
    }

    private Boolean hasSameOrg(String idPerson, BaseWarehouse warehouseOut) {
        Assert.isTrue(StrUtil.isNotEmpty(idPerson), () -> new ServiceException("维修工不能为空！"));
        BasePerson person = personService.getById(idPerson);
        return person.getIdOrg().equals(warehouseOut.getIdOrg());
    }

    private Triple<BaseWarehouse, BaseWhpos, BaseOrg> queryStockInfoByWorkTeamAndWhpos(Pair<BaseWorkteam, List<BaseWorkteamMember>> workteamInfo, String idPerson) {
        BaseWorkteam workteam = workteamInfo.getLeft();
        BaseWarehouse warehouseIn = warehouseService.getById(workteam.getIdWarehouse());
        BaseOrg orgIn = orgService.getById(warehouseIn.getIdOrg());

        String idWhpos = workteam.getIdWhpos();
//        获取入库货位，如果传入的班组成员有货位，就使用班组成员货位，没有就使用班组货位
        if (StrUtil.isNotEmpty(idPerson)) {
            BaseWorkteamMember workteamMember = workteamMemberService.getOne(new LambdaQueryWrapper<BaseWorkteamMember>()
                .eq(BaseWorkteamMember::getDeleteFlag, CommonConstants.DELETE_FLAG_NOT_DELETED)
                .eq(BaseWorkteamMember::getStatus, "2")
                .eq(BaseWorkteamMember::getIdMember, idPerson)
                .eq(BaseWorkteamMember::getIdWorkteam, workteam.getIdWorkteam())
                .last("limit 1"));
            if (Objects.nonNull(workteamMember) && StrUtil.isNotEmpty(workteamMember.getIdWhpos())) {
                idWhpos = workteamMember.getIdWhpos();
            }
        }

        BaseWhpos whposIn = null;
        if (StockConst.FLAG_TRUE.equals(warehouseIn.getRackFlag()) && StrUtil.isNotEmpty(idWhpos)) {
            whposIn = whposService.getById(idWhpos);
        }
        return Triple.of(warehouseIn, whposIn, orgIn);
    }

    private Triple<BaseWarehouse, BaseWhpos, BaseOrg> queryStockInfoByWorkTeam(BaseWorkteam workteam) {
        BaseWarehouse warehouseIn = warehouseService.getById(workteam.getIdWarehouse());
        BaseWhpos whposIn = whposService.getById(workteam.getIdWhpos());
        BaseOrg orgIn = orgService.getById(warehouseIn.getIdOrg());
        return Triple.of(warehouseIn, whposIn, orgIn);
    }

    private Pair<BaseWarehouse, BaseOrg> queryStockInfoByWhposId(String idWarehouseOut, String whposId) {
        BaseWarehouse warehouseOut;
        if (StrUtil.isNotBlank(idWarehouseOut)) {
            warehouseOut = warehouseService.getById(idWarehouseOut);
        } else {
            BaseWhpos whpos = whposService.getById(whposId);
            Assert.isTrue(whpos != null && whpos.getIdWarehouse() != null, "货位对应仓库不存在");
            warehouseOut = warehouseService.getById(whpos.getIdWarehouse());
        }

        BaseOrg orgOut = orgService.getById(warehouseOut.getIdOrg());
        return Pair.of(warehouseOut, orgOut);
    }


    private Triple<BaseWarehouse, BaseWhpos, BaseOrg> queryStockInfoByWhposCode22(String whposCode) {
        BaseWhpos whposOut = whposService.lambdaQuery()
            .eq(BaseWhpos::getDeleteFlag, CommonConstants.DELETE_FLAG_NOT_DELETED)
            .eq(BaseWhpos::getCode, whposCode)
            .last("limit 1")
            .one();

        // TODO: 2024/12/24 截取前五位查询仓库
        String warehouseCode = whposCode.substring(0, 5);
        BaseWarehouse warehouseOut = warehouseService.lambdaQuery()
            .eq(BaseWarehouse::getCode, warehouseCode)
            .eq(BaseWarehouse::getDeleteFlag, CommonConstants.DELETE_FLAG_NOT_DELETED)
            .last("limit 1")
            .one();

        BaseOrg orgOut = orgService.getById(warehouseOut.getIdOrg());
        return Triple.of(warehouseOut, whposOut, orgOut);
    }


    private Triple<BaseWarehouse, BaseWhpos, BaseOrg> queryStockInfoByIdWarehouse(String idWarehouse) {
        BaseWarehouse warehouseOut = warehouseService.getById(idWarehouse);
        BaseOrg orgOut = orgService.getById(warehouseOut.getIdOrg());
        BaseWhpos whposOut = null;
//        未启用货位管理不查询
        if (StockConst.FLAG_TRUE.equals(warehouseOut.getRackFlag())) {
//            先查询地板货位，货位编码后5位是99999
            whposOut = whposService.lambdaQuery()
                .eq(BaseWhpos::getDeleteFlag, CommonConstants.DELETE_FLAG_NOT_DELETED)
                .eq(BaseWhpos::getIdWarehouse, idWarehouse)
                .likeLeft(BaseWhpos::getCode, "99999")
                .last("limit 1")
                .one();
            if (Objects.isNull(whposOut)) {
//                不存在则获取第一个货位
                whposOut = whposService.lambdaQuery()
                    .eq(BaseWhpos::getDeleteFlag, CommonConstants.DELETE_FLAG_NOT_DELETED)
                    .eq(BaseWhpos::getIdWarehouse, idWarehouse)
                    .last("limit 1")
                    .one();
            }
        }

        return Triple.of(warehouseOut, whposOut, orgOut);
    }

    private Pair<BaseWarehouse, BaseOrg> queryWarehouseInfoByIdWarehouse(String idWarehouse) {
        BaseWarehouse warehouseOut = warehouseService.getById(idWarehouse);
        BaseOrg orgOut = orgService.getById(warehouseOut.getIdOrg());
        return Pair.of(warehouseOut, orgOut);
    }


    @Override
    public String prepareMaterialOut(PrepareMaterialOutDTO dto) {
//        根据单据类型获取处理器
        NcOrderHandle handle = ncOrderFactory.getHandle(dto.getBillType().toUpperCase());
//        判断是否生成下游的转库单或调拨单
        handle.validatedOrderExists(dto.getPkNcBill());
//        获取仓库
        BaseWarehouse warehouse = warehouseService.queryBaseWarehouse(dto.getIdWarehouse());
//        判断是否启用wms
        if (StrUtil.equals(warehouse.getWmsFlag(), StockConst.FLAG_TRUE)) {
//            查询nc的出库单是否签字状态
            handle.hasSuccessWmsOutOperate(dto);
        } else {
//            不是启用wms
            materialOutAdapter.materialOut(dto, warehouse);
        }
        return handle.supplementOtherIn(dto.getPkNcBill());
    }


    @Override
    public ReceiveVO prepareMaterialReceive(PrepareMaterialReceiveDTO dto) {
        NcOrderHandle handle = ncOrderFactory.getHandle(dto.getBillType().toUpperCase());
        handle.receiveOperate(dto);
        return handle.supplementReceiveByIcIn(dto.getPkIcIn());
    }


    @Override
    public ReceiveVO receive(PrepareMaterialCreateDTO dto) {
//        前置转换
        preConvert(dto);
//        前置校验
        Pair<BaseWorkteam, List<BaseWorkteamMember>> workteamInfo = validatedStock.preMaterial(dto.getIdWorkTeam(), dto.getIdPerson(), dto.getIdWarehouseOut(), dto.getDataList());
//        获取出库仓库信息
        Pair<BaseWarehouse, BaseOrg> outStockInfo = queryStockInfoByWhposId(dto.getIdWarehouseOut(), dto.getDataList().get(0).getIdWhpos());
//        获取入库仓库信息
        Triple<BaseWarehouse, BaseWhpos, BaseOrg> inStockInfo = queryStockInfoByWorkTeamAndWhpos(workteamInfo, dto.getIdPerson());
//        封装请求上下文
        PrepareMaterialContextDTO ctx = buildContext(dto, workteamInfo, outStockInfo, inStockInfo);
//        判断是否扣减wms
        WmsStockChangeDTO params = null;
        if (StockConst.FLAG_TRUE.equals(outStockInfo.getLeft().getWmsFlag())) {
//            扣减库存
            params = wmsService.deductStock(ctx);
        }
//        判断组织是否相同
        Boolean hasSameOrg = hasSameOrg(workteamInfo, outStockInfo.getLeft());
//        生成订单
        try {
            return receiveAdapter.onReceive(hasSameOrg, ctx);
        } catch (Exception e) {
            // 回滚wms
            if (Objects.nonNull(params)) {
                wmsService.rollBackStock(params);
            }
            throw new ServiceException(e.getMessage());
        }
    }

    private void preConvert(PrepareMaterialCreateDTO dto) {
//        兼容货位id和货位编码
//        如果入参货位编码为空，查询货位编码并设置货位id
        Set<String> whposCodes = dto.getDataList().stream().map(MaterialDataDTO::getWhposCode)
            .filter(StrUtil::isNotBlank)
            .collect(Collectors.toSet());
        if (CollUtil.isNotEmpty(whposCodes)) {
            Map<String, BaseWhpos> whposMap = whposService.list(new LambdaQueryWrapper<BaseWhpos>()
                    .eq(BaseWhpos::getDeleteFlag, CommonConstants.DELETE_FLAG_NOT_DELETED)
                    .eq(BaseWhpos::getStatus, "2")
                    .in(BaseWhpos::getCode, whposCodes))
                .stream()
                .collect(Collectors.toMap(BaseWhpos::getCode, i -> i, (k1, k2) -> k1));

            dto.getDataList().forEach(i -> {
                if (StrUtil.isEmpty(i.getIdWhpos()) && StrUtil.isNotEmpty(i.getWhposCode())) {
                    String idWhpos = Optional.ofNullable(whposMap.get(i.getWhposCode()))
                        .map(BaseWhpos::getIdWhpos).orElseThrow(() -> new ServiceException("货位code不存在"));
                    i.setIdWhpos(idWhpos);
                }
            });
        }
    }


    @Override
    public MaterialReturnVO materialReturn(MaterialReturnDTO dto) {
//        前置校验
        Pair<BaseWorkteam, List<BaseWorkteamMember>> workteamInfo = validatedStock.preMaterial(dto.getIdWorkTeam(), dto.getIdPerson(), null, null);
//        获取入库仓库信息
        Triple<BaseWarehouse, BaseWhpos, BaseOrg> inStockInfo = queryStockInfoByIdWarehouse(dto.getIdWarehourse());
//        获取出库仓库信息
        Triple<BaseWarehouse, BaseWhpos, BaseOrg> outStockInfo = queryStockInfoByWorkTeamAndWhpos(workteamInfo, dto.getIdPerson());
//        判断组织是否相同
        Boolean hasSameOrg = hasSameOrg(workteamInfo, inStockInfo.getLeft());
//        构建上下文参数
        PrepareMaterialContextDTO ctx = buildContext(dto, workteamInfo, outStockInfo, inStockInfo);
//        生成订单
        PrepareMaterialResultDTO adapterResult = returnAdapter.onMaterialReturn(hasSameOrg, ctx);
//        签字或出库
        returnAdapter.onMaterialReturnOut(ctx, adapterResult);
        return new MaterialReturnVO()
            .withResult(adapterResult);
    }


    @Override
    public Object locationAdjustment(NcTemplateDTO dto) {
        JSONObject body = JSONUtil.parseObj(dto.getBody());
//        翻译body
        String billmaker = body.getStr("billmaker");
        Assert.isTrue(StrUtil.isNotEmpty(billmaker), () -> new ServiceException("单据创建人不能为空！"));
        BaseUser user = userService.getById(billmaker);
        Assert.isTrue(Objects.nonNull(user), () -> new ServiceException("用户不存在！"));
        body.set("billmaker", user.getPkUser());

        String cwarehouseid = body.getStr("cwarehouseid");
        Assert.isTrue(StrUtil.isNotEmpty(cwarehouseid), () -> new ServiceException("仓库不能为空！"));
        BaseWarehouse warehouse = warehouseService.getById(cwarehouseid);
        Assert.isTrue(Objects.nonNull(warehouse), () -> new ServiceException("仓库不存在！"));
        body.set("cwarehouseid", warehouse.getPkStordoc());

        String cwhsmanagerid = body.getStr("cwhsmanagerid");
        if (StrUtil.isNotEmpty(cwhsmanagerid)) {
            BasePerson person = personService.getById(cwhsmanagerid);
            Assert.isTrue(Objects.nonNull(person), () -> new ServiceException("库管员不存在！"));
            body.set("cwhsmanagerid", person.getPkPsndoc());
        }

        String pk_org = body.getStr("pk_org");
        Assert.isTrue(StrUtil.isNotEmpty(pk_org), () -> new ServiceException("组织不能为空！"));
        BaseOrg org = orgService.getById(pk_org);
        Assert.isTrue(Objects.nonNull(org), () -> new ServiceException("组织不存在！"));
        body.set("pk_org", org.getPkOrg());

//        翻译Detail
        JSONArray jsonDetails = JSONUtil.parseArray(dto.getDetail());
        List<String> idCustomers = Lists.newArrayList();
        List<String> idWhposs = Lists.newArrayList();
        List<String> idInv = Lists.newArrayList();
        for (Object o : jsonDetails) {
            JSONObject detail = JSONUtil.parseObj(o);
//            翻译客户
            String casscustid = detail.getStr("casscustid");
            if (StrUtil.isNotEmpty(casscustid)) {
                idCustomers.add(casscustid);
            }
//            转出货位
            String clocationid = detail.getStr("clocationid");
            if (StrUtil.isNotEmpty(clocationid)) {
                idWhposs.add(clocationid);
            }
//            转入货位
            String clocationinid = detail.getStr("clocationinid");
            Assert.isTrue(StrUtil.isNotEmpty(clocationinid), () -> new ServiceException("转入货位不能为空！"));
            idWhposs.add(clocationinid);
//            物料id
            String cmaterialvid = detail.getStr("cmaterialvid");
            Assert.isTrue(StrUtil.isNotEmpty(cmaterialvid), () -> new ServiceException("物料不能为空！"));
            idInv.add(cmaterialvid);
        }

        Map<String, BaseCustEntity> baseCustEntityMaps = Maps.newHashMap();
        if (CollUtil.isNotEmpty(idCustomers)) {
            baseCustEntityMaps = custService.listByIds(idCustomers)
                .stream()
                .collect(Collectors.toMap(BaseCustEntity::getIdCustomer, Function.identity(), (l, r) -> l));
        }

        Map<String, BaseWhpos> baseWhpoMaps = whposService.listByIds(idWhposs)
            .stream()
            .collect(Collectors.toMap(BaseWhpos::getIdWhpos, Function.identity(), (l, r) -> l));

        Map<String, BaseInventory> baseInvMaps = inventoryService.listByIds(idInv)
            .stream()
            .collect(Collectors.toMap(BaseInventory::getIdInventory, Function.identity(), (l, r) -> l));

        JSONArray array = new JSONArray();
        for (Object o : jsonDetails) {
            JSONObject detail = JSONUtil.parseObj(o);
            String casscustid = detail.getStr("casscustid");
            if (StrUtil.isNotEmpty(casscustid)) {
                BaseCustEntity baseCustEntity = Optional.ofNullable(baseCustEntityMaps.get(casscustid))
                    .orElseThrow(() -> new ServiceException("客户不存在！"));
                detail.set("casscustid", baseCustEntity.getPkCustomer());
            }

            String clocationid = detail.getStr("clocationid");
            if (StrUtil.isNotEmpty(clocationid)) {
                BaseWhpos baseWhposOut = Optional.ofNullable(baseWhpoMaps.get(clocationid))
                    .orElseThrow(() -> new ServiceException("出库仓库货位不存在！"));
                detail.set("clocationid", baseWhposOut.getPkRack());
            }

            String clocationinid = detail.getStr("clocationinid");
            BaseWhpos baseWhposIn = Optional.ofNullable(baseWhpoMaps.get(clocationinid))
                .orElseThrow(() -> new ServiceException("入库仓库货位不存在！"));
            detail.set("clocationinid", baseWhposIn.getPkRack());

            String cmaterialvid = detail.getStr("cmaterialvid");
            BaseInventory baseInventory = Optional.ofNullable(baseInvMaps.get(cmaterialvid))
                .orElseThrow(() -> new ServiceException("物料不存在！"));
            detail.set("cmaterialvid", baseInventory.getPkMaterial());
            array.add(detail);
        }

        JSONObject result = ncTransferService.locationAdjustment(new NcTemplateDTO(body, array));
        return result.getJSONObject("data");
    }


    @Override
    public TransferVO transfer(TransferDTO dto) {
        TransferVO result = new TransferVO();
//        兼容传参
        String inIdWarehouse = StrUtil.isNotEmpty(dto.getInIdWarehouse()) ? dto.getInIdWarehouse() : dto.getInIdWarehourse();
        BaseWarehouse inWarehouse = warehouseService.getById(inIdWarehouse);
        Assert.isTrue(Objects.nonNull(inWarehouse), () -> new ServiceException("入库仓库不存在！"));
        if (StockConst.FLAG_TRUE.equals(inWarehouse.getRackFlag())) {
            long count = dto.getDataList().stream().filter(item -> StrUtil.isBlank(item.getInIdWhpos())).count();
            Assert.isFalse(count > 0, () -> new ServiceException("入库仓库已启用货位，入库仓库货位不能为空！"));
        }
//        兼容传参
        String outIdWarehouse = StrUtil.isNotEmpty(dto.getOutIdWarehouse()) ? dto.getOutIdWarehouse() : dto.getOutIdWarehourse();
        BaseWarehouse outWarehouse = warehouseService.getById(outIdWarehouse);
        Assert.isTrue(Objects.nonNull(outWarehouse), () -> new ServiceException("出库仓库不存在！"));
        if (StockConst.FLAG_TRUE.equals(outWarehouse.getRackFlag())) {
            long count = dto.getDataList().stream().filter(item -> StrUtil.isBlank(item.getOutIdWhpos())).count();
            Assert.isFalse(count > 0, () -> new ServiceException("出库仓库已启用货位，出库仓库货位不能为空！"));
        }
        BaseOrg orgIn = orgService.getById(inWarehouse.getIdOrg());

        PrepareMaterialContextDTO ctx = buildContext(dto, inWarehouse, outWarehouse, orgIn);
        PrepareMaterialResultDTO transferResult = transferAdapter.onTransfer(ctx);
        BeanUtils.copyProperties(transferResult, result);
        return result;
    }


    @Override
    public MaterialReturnSignVO materialReturnSign(MaterialReturnSignDTO dto) {
//        前置校验
        Pair<BaseWorkteam, List<BaseWorkteamMember>> workteamInfo = validatedStock.preMaterial(dto.getIdWorkTeam(), dto.getIdPerson(), null, null);
//        获取入库仓库信息
        Pair<BaseWarehouse, BaseOrg> inStockInfo = queryWarehouseInfoByIdWarehouse(dto.getIdWarehourse());
//        获取出库仓库信息
        Triple<BaseWarehouse, BaseWhpos, BaseOrg> outStockInfo = queryStockInfoByWorkTeamAndWhpos(workteamInfo, dto.getIdPerson());
//        构建上下文参数
        PrepareMaterialContextDTO ctx = buildContext(dto, workteamInfo, outStockInfo, inStockInfo);
//        判断是否扣减wms
        WmsStockChangeDTO params = null;
        if (StockConst.FLAG_TRUE.equals(inStockInfo.getLeft().getWmsFlag())) {
//            扣减库存
            params = wmsService.increaseStock(ctx);
        }
//        判断组织是否相同
        Boolean hasSameOrg = hasSameOrg(workteamInfo, inStockInfo.getLeft());
//        生成订单
        try {
            ReceiveVO vo = receiveAdapter.onReceive(hasSameOrg, ctx);
            return new MaterialReturnSignVO()
                .withResult(vo);
        } catch (Exception e) {
            log.error("请求nc生成单据异常：", e);
            // 回滚wms
            if (Objects.nonNull(params)) {
                wmsService.rollBackStock(params);
            }
            throw new ServiceException(e.getMessage());
        }
    }
}
