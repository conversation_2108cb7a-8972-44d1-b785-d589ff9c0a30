package com.fls.stock.entity;

import com.fls.stock.enums.BoundTypeEnum;
import com.fls.stock.pojo.model.BaseSourceBill;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * <p>
 * 基础单据字段抽取
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-11
 */
@Data
public class BaseBillEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 单据编号
     */
    private String billCode;

    /**
     * 单据日期
     */
    private LocalDate billDate;

    /**
     * 来源操作类型
     */
    private Integer sourceBillType;

    /**
     * 来源单据编号
     */
    private String sourceBillCode;

    /**
     * 来源单据id
     */
    private String idSourceBill;

    /**
     * 来源单据资源id
     */
    private String idSourceRes;

    /**
     * 来源描述
     */
    private String sourceDesc;

    /**
     * 源头来源单据id
     */
    private String idFirstSourceBill;

    /**
     * 源头来源单据编号
     */
    private String firstSourceBillCode;

    /**
     * 源头单据资源id
     */
    private String idFirstRes;

    /**
     * 存储组织id(单据归属)-组织数据权限
     */
    private String idOrg;

    /**
     * 经营主体id(单据归属)--档案数据权限
     */
    private String idBizunit;

    /**
     * 资源id(单据归属)--档案数据权限
     */
    private String idResource;

    /**
     * 资源交易类型(单据归属)--档案数据权限
     */
    private String idResTrantype;

    /**
     * 1-启用审批流，0-未启用审批流
     */
    private String needprocFlag;

    /**
     * 单据审批状态 (0-待审批,1-审批通过,2-审批驳回)
     */
    private String status;

    /**
     * 作废标识:1-作废,0-有效
     */
    private String invalidFlag;

    /**
     * 审批时间
     */
    private LocalDateTime auditTime;

    /**
     * 审批人
     */
    private String auditor;

    /**
     * 作废时间
     */
    private LocalDateTime invalidTime;

    /**
     * 作废人
     */
    private String invalider;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    private String updater;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 时间戳
     */
    private LocalDateTime ts;

    /**
     * 0-正常,1-删除
     */
    private String deleteFlag;

    public void setBillProperties(BaseSourceBill baseSourceBill) {
        this.sourceBillType = baseSourceBill.getSourceBillType();
        this.sourceDesc = BoundTypeEnum.getEnumDesc(baseSourceBill.getSourceBillType());
        this.idSourceRes = baseSourceBill.getIdSourceRes();
        this.idFirstSourceBill = baseSourceBill.getIdFirstSourceBill();
        this.firstSourceBillCode = baseSourceBill.getFirstSourceCode();
        this.idFirstRes = baseSourceBill.getIdFirstRes();
        this.idSourceBill = baseSourceBill.getIdSourceBill();
        this.sourceBillCode = baseSourceBill.getSourceBillCode();
        this.creator = baseSourceBill.getOperator();
        this.updater = baseSourceBill.getOperator();
    }
}
