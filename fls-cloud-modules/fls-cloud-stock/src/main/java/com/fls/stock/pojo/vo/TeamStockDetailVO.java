package com.fls.stock.pojo.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 班组库存明细vo
 *
 * <AUTHOR>
 * @since 2024-12-11
 */
@Data
public class TeamStockDetailVO {
    /**
     * 可用数量
     */
    private double availableNum;
    /**
     * 批次号
     */
    private String batchCode;
    /**
     * 服务主体名称
     */
    private String bizName;
    /**
     * 班组长名称
     */
    private String headName;
    /**
     * 服务主体id
     */
    private String idBizunit;
    /**
     * 班组组长人员id
     */
    private String idHeadman;
    /**
     * 物料id
     */
    private String idMaterial;
    /**
     * 库存记录id
     */
    private String idStock;
    /**
     * 所属仓库id
     */
    private String idWarehouse;
    /**
     * 货位id
     */
    private String idWhpos;
    /**
     * 工作班组id
     */
    private String idWorkteam;
    /**
     * 存货名称
     */
    private String inventoryName;
    /**
     * 锁定数量
     */
    private double lockNum;
    /**
     * 物料编码
     */
    private String materialCode;
    /**
     * 物料名称
     */
    private String materialName;
    /**
     * 物料参数
     */
    private String materialParam;
    /**
     * 领取时间
     */
    private String pickupTime;
    /**
     * 批次pk值
     */
    private String pkBatchCode;
    /**
     * 物料pk
     */
    private String pkMaterial;
    /**
     * 单位pk
     */
    private String pkMeasdoc;
    /**
     * 所属仓库pk
     */
    private String pkWarehouse;
    /**
     * 货位pk
     */
    private String pkWhpos;
    /**
     * 现存数量
     */
    private double quantityNum;
    /**
     * 单位
     */
    private String unit;
    /**
     * 所属仓库名称
     */
    private String warehouseName;

    /**
     * 所属仓库编号
     */
    private String warehouseCode;
    /**
     * 货位名称
     */
    private String whposName;
    /**
     * 货位编号
     */
    private String whposCode;
    /**
     * 工作班组名称
     */
    private String workteamName;

    /**
     * 最新使用时间
     */
    private LocalDateTime useTime;
}
