package com.fls.stock.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Map;
import java.util.Set;

/**
 * @Description: 查询nc的其他操作
 * @Author: caibenwei
 * @DATE: 2024/12/21 13:47
 */
@DS("ncdb")
public interface NcMappper {

    /**
     * 查询税率
     * @return
     */
    BigDecimal acceptTaxRate();

    @MapKey("PK_MATERIAL")
    Map<String, Object> acceptNorigtaxnetprice(@Param("pkMaterial") Set<String> pkMaterial);
}
