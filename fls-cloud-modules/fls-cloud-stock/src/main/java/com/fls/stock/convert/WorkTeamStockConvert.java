package com.fls.stock.convert;

import com.fls.stock.entity.WorkteamStock;
import com.fls.stock.entity.WorkteamStockLockRecord;
import com.fls.stock.entity.WorkteamTransactionDetail;
import com.fls.stock.entity.WorkteamTransactionLog;
import com.fls.stock.pojo.dto.GroupTransferDTO;
import com.fls.stock.pojo.dto.StockInboundDTO;
import com.fls.stock.pojo.dto.StockOutboundDTO;
import com.fls.stock.pojo.model.BaseSourceBill;
import com.fls.stock.pojo.model.MaterialOperationRecord;
import com.fls.stock.pojo.vo.TeamStockDetailVO;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

import java.math.BigDecimal;
import java.util.List;

/**
 * 库存Mapstruct转换
 *
 * <AUTHOR>
 * @since 2024-12-10
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, uses = {})
public interface WorkTeamStockConvert {

    @Mapping(source = "locksNum", target = "lockNum")
    @Mapping(source = "extantNum", target = "quantityNum")
    @Mapping(source = "receiveTime", target = "pickupTime")
    TeamStockDetailVO stockToDetail(WorkteamStock stock);

    List<TeamStockDetailVO> toVoList(List<WorkteamStock> list);

    @Mapping(target = "idStock", ignore = true)
    WorkteamStock materialToStock(MaterialOperationRecord record);

    @AfterMapping
    default void afterStockMapping(MaterialOperationRecord record, @MappingTarget WorkteamStock stock){
        stock.setAvailableNum(BigDecimal.ZERO);
        stock.setLocksNum(BigDecimal.ZERO);
        stock.setExtantNum(BigDecimal.ZERO);
    }

    @Mapping(source = "idWorkteamOut", target = "idWorkteam")
    @Mapping(source = "operator", target = "operator")
    StockOutboundDTO transferToOutboundDTO(GroupTransferDTO trans);

    @Mapping(source = "idWorkteamIn", target = "idWorkteam")
    StockInboundDTO transferToInboundDTO(GroupTransferDTO trans);

    @Mapping(source = "firstSourceCode", target = "firstSourceBillCode")
    @Mapping(source = "operator", target = "creator")
    @Mapping(source = "sourceBillType", target = "sourceType")
    WorkteamTransactionLog billToTransLog(BaseSourceBill bill);

    @Mapping(source = "changes", target = "count")
    WorkteamTransactionDetail stockToTransDetail(WorkteamStock stock);

    MaterialOperationRecord stockToMaterial(WorkteamStock stock);

    @Mapping(source = "changes", target = "lockNum")
    WorkteamStockLockRecord stockToLock(WorkteamStock stock);
}
