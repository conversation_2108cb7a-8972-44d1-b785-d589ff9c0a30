package com.fls.stock.service.invoke.impl;

import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fls.common.core.exception.ServiceException;
import com.fls.stock.entity.BaseInventory;
import com.fls.stock.mapper.BaseInventoryMapper;
import com.fls.stock.pojo.dto.MaterialDTO;
import com.fls.stock.service.invoke.BaseInventoryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 物料表(BaseInventory)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-18 15:12:27
 */
@Service
@Slf4j
public class BaseInventoryServiceImpl extends ServiceImpl<BaseInventoryMapper, BaseInventory> implements BaseInventoryService {

    @Override
    public Map<String, BaseInventory> getInventoryEntityMaps(List<? extends MaterialDTO> dataList) {
        Set<String> invIds = dataList.stream().map(MaterialDTO::getIdInventory).collect(Collectors.toSet());
        List<BaseInventory> list = this.lambdaQuery()
            .in(BaseInventory::getIdInventory, invIds)
            .list();
        Assert.isTrue(list.size() == invIds.size(),() -> new ServiceException("数据集中包含无效的物料id！"));
        return list.stream()
            .collect(Collectors.toMap(BaseInventory::getIdInventory, i -> i, (k1, k2) -> k1));
    }
}

