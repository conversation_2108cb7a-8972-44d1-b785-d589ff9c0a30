package com.fls.stock.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * @Description:PrepareMaterialDTO
 * @Author: caibenwei
 * @DATE: 2024/12/18 9:22
 */
@Data
public class PrepareMaterialCreateDTO extends BaseApiDTO{

    @Schema(description = "工作班组id")
    @NotBlank(message = "工作班组id不能为空！")
    private String idWorkTeam;

    @Schema(description = "维修工id")
    private String idPerson;

    @Schema(description = "租赁资产编码")
    private String assetCode;

    @Schema(description = "领料单据号")
    private String billCode;

    @Schema(description = "接口来源对象主键")
    @NotBlank(message = "接口来源对象主键不能为空！")
    private String idSource;

    @Schema(description = "出库仓库id")
    private String idWarehouseOut;

    @Schema(description = "物料明细")
    @NotNull(message = "物料明细数据不能为空")
    @Size(min = 1, message = "物料明细数据不能为空")
    private List<MaterialDataDTO> dataList;

}
