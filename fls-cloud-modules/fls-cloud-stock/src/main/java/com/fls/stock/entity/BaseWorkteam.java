package com.fls.stock.entity;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 工作班组表(BaseWorkteam)表实体类
 *
 * <AUTHOR>
 * @since 2024-12-18 10:24:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_base_workteam")
public class BaseWorkteam implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     **/
    @TableId(value = "id_workteam", type = IdType.ASSIGN_UUID)
    private String idWorkteam;

    /**
     * 班组编码
     **/
    @TableField("code")
    private String code;

    /**
     * 班组名称
     **/
    @TableField("name")
    private String name;

    /**
     * 所属组织
     **/
    @TableField("id_org")
    private String idOrg;

    /**
     * 所属主体
     **/
    @TableField("id_bizunit")
    private String idBizunit;

    /**
     * 所属部门
     **/
    @TableField("id_department")
    private String idDepartment;

    /**
     * 班组对应虚拟地点
     **/
    @TableField("id_address")
    private String idAddress;

    /**
     * 班组对应仓库
     **/
    @TableField("id_warehouse")
    private String idWarehouse;

    /**
     * 班组对应货位
     **/
    @TableField("id_whpos")
    private String idWhpos;

    /**
     * 班组默认归还仓库
     **/
    @TableField("id_whback")
    private String idWhback;

    /**
     * 组长
     **/
    @TableField("id_headman")
    private String idHeadman;

    /**
     * 班组成员
     **/
    @TableField("members")
    private String members;

    /**
     * 租赁服务标识：0=否，1=是，默认0，参见yesorno
     **/
    @TableField("rental_service_flag")
    private String rentalServiceFlag;

    /**
     * 商业维修标识：0=否，1=是，默认0，参见yesorno
     **/
    @TableField("commercial_maintenance_flag")
    private String commercialMaintenanceFlag;

    /**
     * 是否外协：0=否，1=是，默认0，参见yesorno
     **/
    @TableField("ocunit_flag")
    private String ocunitFlag;

    /**
     * 外协单位
     **/
    @TableField("id_ocunit")
    private String idOcunit;

    /**
     * NC主键
     **/
    @TableField("nc_pk")
    private String ncPk;

    /**
     * 备注
     **/
    @TableField("memo")
    private String memo;

    /**
     * 创建时间
     **/
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 创建人
     **/
    @TableField("creator")
    private String creator;

    /**
     * 修改时间
     **/
    @TableField("modify_time")
    private LocalDateTime modifyTime;

    /**
     * 修改人
     **/
    @TableField("modifier")
    private String modifier;

    /**
     * 作废时间
     **/
    @TableField("invalid_time")
    private LocalDateTime invalidTime;

    /**
     * 作废人
     **/
    @TableField("invalider")
    private String invalider;

    /**
     * 启用状态：1=未启用，2=已启用，3=已停用，默认1，参见basestatus
     **/
    @TableField("status")
    private String status;

    /**
     * 时间戳
     **/
    @TableField("ts")
    private LocalDateTime ts;

    /**
     * 是否删除：0=否，1=是，默认0，参见yesorno
     **/
    @TableField("delete_flag")
    @TableLogic
    private String deleteFlag;

}

