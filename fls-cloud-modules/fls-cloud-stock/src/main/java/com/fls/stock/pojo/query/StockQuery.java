package com.fls.stock.pojo.query;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2024/12/11 16:22
 **/
@Data
public class StockQuery {

    private String pkUser;

    private String pkOrg;
    private String idOrg;
    @NotBlank(message = "出库仓库id不能为空")
    private String idWarehouse;
    private String pkWarehouse;
    private String houseName;
    private String pkWhpos;
    private String idWhpos;
    private String posName;
    private String remarks;

    private List<MaterialNumReq> materialNumReq;

}
