package com.fls.stock.pojo.query;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class TransLogDetailQuery extends BasePageQuery {
    /**
     * 开始日期：yyyy-MM-dd，格式：yyyy-MM-dd
     */
    private String beginDate;
    /**
     * 流水号/来源单据编号
     */
    private String code;
    /**
     * 结束日期：yyyy-MM-dd，格式：yyyy-MM-dd
     */
    private String endDate;
    /**
     * 物料名称
     */
    private String materialName;
    /**
     * 物料id
     */
    private String idMaterial;
    /**
     * 组织名称/经营主体名称/班组名称
     */
    private String name;
    /**
     * 出入库类型，1-入库,2-出库
     */
    private Integer type;

    /**
     * 库存操作单号
     */
    private List<String> codes;
}
