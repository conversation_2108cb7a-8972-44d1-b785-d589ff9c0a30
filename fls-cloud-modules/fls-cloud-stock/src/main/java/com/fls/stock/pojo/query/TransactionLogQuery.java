package com.fls.stock.pojo.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDate;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2024/12/10 21:12
 **/
@Data
public class TransactionLogQuery extends BasePageQuery {

    @Schema(description = "开始日期：yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate beginDate;

    @Schema(description = "结束日期：yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDate;

    @Schema(description = "流水号/来源单据编号")
    private String code;

    @Schema(description = "物料名称")
    private String materialName;

    @Schema(description = "组织名称/经营主体名称/班组名称")
    private String name;

    @Schema(description = "出入库类型，1-入库,2-出库")
    private Integer type;

}
