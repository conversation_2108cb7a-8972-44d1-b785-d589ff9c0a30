package com.fls.stock.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 权限过滤注解
 *
 * <AUTHOR>
 * @since 2025-08-07
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface AuthFilter {
    /**
     * 用户ID字段名
     */
    String userIdField() default "userId";

    /**
     * href字段名
     */
    String hrefField() default "href";

    /**
     * 权限经营主体ID列表字段名
     */
    String authBizunitIdsField() default "authBizunitIds";
}
