package com.fls.stock.external.nc.handle.client;

import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.fls.stock.external.nc.pojo.request.NcTemplateDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @Description: 远程调用客户端
 * @Author: caibenwei
 * @DATE: 2024/12/23 14:10
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class NcClient {


    public JSONObject send(String url, NcTemplateDTO dto) {
        String requestBody = JSONUtil.toJsonStr(dto);
        log.info("请求nc接口:{},参数:{}", url, requestBody);
        String response = HttpUtil.post(url, requestBody);
        log.info("nc接口返回:{}", response);
        return JSONUtil.parseObj(response);
    }

}
