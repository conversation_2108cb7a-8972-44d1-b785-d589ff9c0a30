package com.fls.stock.pojo.query;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 班组库存明细查询对象
 *
 * <AUTHOR>
 * @since 2024-12-10
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class TeamStockDetailQuery extends BasePageQuery {

    /**
     * 班组id
     */
    private List<String> idsWorkteam;

    /**
     * 搜索关键词，物料编码，物料名称
     */
    private String keyword;

    /**
     * 用户id
     */
    private String idUser;

    /**
     * 经营主体id
     */
    private String idBizunit;

    /**
     * 可用标识
     */
    private boolean available = false;
}
