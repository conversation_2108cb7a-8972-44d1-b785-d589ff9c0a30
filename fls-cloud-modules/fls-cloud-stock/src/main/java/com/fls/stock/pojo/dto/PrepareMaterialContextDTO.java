package com.fls.stock.pojo.dto;

import cn.hutool.json.JSONObject;
import com.fls.stock.entity.BaseOrg;
import com.fls.stock.entity.BaseWarehouse;
import com.fls.stock.entity.BaseWhpos;
import com.fls.stock.entity.BaseWorkteam;
import com.fls.stock.enums.BillTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Description:
 * @Author: caibenwei
 * @DATE: 2024/12/18 11:14
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class PrepareMaterialContextDTO {

    private BillTypeEnum billTypeEnum;

    @Schema(description = "来源系统")
    private String srcSysName;

    @Schema(description = "用户编码")
    private String userCode;

    @Schema(description = "租赁资产编码")
    private String assetCode;

    @Schema(description = "领料单据号")
    private String billCode;

    @Schema(description = "接口来源对象主键")
    private String sourcePk;

    @Schema(description = "物料模型")
    private List<? extends MaterialDTO> dataList;

    @Schema(description = "工作组")
    private BaseWorkteam workteam;

    @Schema(description = "出库仓库")
    private BaseWarehouse warehouseOut;

    @Schema(description = "出库组织")
    private BaseOrg orgOut;

    @Schema(description = "入库仓库")
    private BaseWarehouse warehouseIn;

    @Schema(description = "入库组织")
    private BaseOrg orgIn;

    @Schema(description = "订单其他参数")
    private JSONObject orderParamMaps;

}
