package com.fls.stock.service.woekteam;

import com.fls.stock.entity.WorkteamStock;
import com.fls.stock.entity.WorkteamTransactionLog;
import com.fls.stock.enums.TransLogTypeEnum;
import com.fls.stock.pojo.model.BaseSourceBill;
import com.github.yulichang.base.MPJBaseService;
import java.util.List;

/**
 * 班组库存流水表(WorkteamTransactionLog)表服务接口
 *
 * <AUTHOR>
 * @since 2024-12-11 09:13:44
 */
public interface IWorkteamTransactionLogService extends MPJBaseService<WorkteamTransactionLog> {
    /**
     * 生成库存流水记录
     * @param bill 物料单据
     * @param records 操作班组库存记录
     * @return 班组流水编号
     */
    void generateTransactionLog(BaseSourceBill bill, List<WorkteamStock> records, TransLogTypeEnum logType);
}

