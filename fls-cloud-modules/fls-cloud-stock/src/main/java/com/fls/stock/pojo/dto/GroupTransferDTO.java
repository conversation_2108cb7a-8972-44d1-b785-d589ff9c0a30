package com.fls.stock.pojo.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

@Data
@EqualsAndHashCode(callSuper = true)
public class GroupTransferDTO extends StockLockReleaseDTO {
    /**
     * 调入班组成员id
     */
    private String idMemberIn;

    /**
     * 待调出班组成员id
     */
    private String idMemberOut;

    /**
     * 调入维修班组id
     */
    @NotBlank(message = "调入班组id不能为空")
    private String idWorkteamIn;

    /**
     * 待调出维修班组id
     */
    @NotBlank(message = "调出班组id不能为空")
    private String idWorkteamOut;
}
