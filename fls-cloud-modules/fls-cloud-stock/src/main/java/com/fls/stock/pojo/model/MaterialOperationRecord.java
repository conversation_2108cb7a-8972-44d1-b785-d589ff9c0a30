package com.fls.stock.pojo.model;

import java.math.BigDecimal;
import lombok.Data;

@Data
public class MaterialOperationRecord {
    /**
     * 库存id
     */
    private String idStock;
    /**
     * 批次号
     */
    private String batchCode;
    /**
     * 物料id
     */
    private String idMaterial;
    /**
     * 存货名称
     */
    private String inventoryName;
    /**
     * 物料编码
     */
    private String materialCode;
    /**
     * 物料名称
     */
    private String materialName;
    /**
     * 物料参数
     */
    private String materialParam;
    /**
     * 批次pk值
     */
    private String pkBatchCode;
    /**
     * 物料pk
     */
    private String pkMaterial;
    /**
     * 单位pk
     */
    private String pkMeasdoc;
    /**
     * 数量（必须大于0.001）
     */
    private BigDecimal quantity;
    /**
     * 单位
     */
    private String unit;

    public String getUniqueKey(){
        return materialCode + "_" + batchCode;
    }
}
