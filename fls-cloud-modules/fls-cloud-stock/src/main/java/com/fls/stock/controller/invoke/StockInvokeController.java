package com.fls.stock.controller.invoke;

import cn.hutool.core.lang.Dict;
import com.fls.common.core.domain.ResponseData;
import com.fls.common.mybatis.core.page.PageResult;
import com.fls.stock.external.nc.pojo.dto.PrepareMaterialResultDTO;
import com.fls.stock.external.nc.pojo.request.NcTemplateDTO;
import com.fls.stock.pojo.dto.MaterialReturnSignDTO;
import com.fls.stock.pojo.vo.MaterialReturnSignVO;
import com.fls.stock.pojo.vo.MaterialReturnVO;
import com.fls.stock.pojo.vo.TransferVO;
import com.fls.stock.pojo.dto.MaterialReturnDTO;
import com.fls.stock.pojo.dto.PrepareMaterialCreateDTO;
import com.fls.stock.pojo.dto.PrepareMaterialOutDTO;
import com.fls.stock.pojo.dto.PrepareMaterialReceiveDTO;
import com.fls.stock.pojo.dto.TransferDTO;
import com.fls.stock.pojo.query.BarcodeQuery;
import com.fls.stock.pojo.query.LockLogQuery;
import com.fls.stock.pojo.query.StockQuery;
import com.fls.stock.pojo.query.TransactionLogQuery;
import com.fls.stock.pojo.vo.BarcodeVO;
import com.fls.stock.pojo.vo.LockLogVO;
import com.fls.stock.pojo.vo.ReceiveVO;
import com.fls.stock.pojo.vo.TransactionLogVO;
import com.fls.stock.service.invoke.StockInvokeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 库存调用中心
 *
 * <AUTHOR>
 * @since 2024-12-10
 */
@Tag(name = "库存调用中心")
@Slf4j
@RestController
@RequestMapping("/stock/invoke")
@RequiredArgsConstructor
public class StockInvokeController {

    private final StockInvokeService stockInvokeService;

    @Deprecated
    @Operation(summary = "库存流水记录查询")
    @PostMapping("/transaction/log")
    public ResponseData<PageResult<TransactionLogVO>> getTransactionLog(@RequestBody TransactionLogQuery query) {
        return ResponseData.ok(stockInvokeService.getTransactionLog(query));
    }

    @Deprecated
    @Operation(summary = "库存锁定记录查询")
    @PostMapping("/lock/log")
    public ResponseData<PageResult<LockLogVO>> getLockLog(@RequestBody LockLogQuery query) {
        return ResponseData.ok(stockInvokeService.getLockLog(query));
    }

    @Deprecated
    @Operation(summary = "物料扫码查询")
    @PostMapping("/label/code")
    public ResponseData<BarcodeVO> getBarcode(@Validated @RequestBody BarcodeQuery query) {
        return ResponseData.ok(stockInvokeService.getBarcode(query.getCode()));
    }

    @Operation(summary = "库存出库")
    @Deprecated
    @PostMapping("/stock/out")
    public ResponseData<Boolean> stockOut(@Validated @RequestBody StockQuery query) {
        Boolean r = stockInvokeService.stockOut(query);
        return ResponseData.ok(r);
    }

    @Operation(summary = "库存入库")
    @Deprecated
    @PostMapping("/stock/in")
    public ResponseData<Boolean> stockIn(@Validated @RequestBody StockQuery query) {
        Boolean r = stockInvokeService.stockIn(query);
        return ResponseData.ok(r);
    }

    @Operation(summary = "库存调整")
    @Deprecated
    @PostMapping("/stock/pos/adjust")
    public ResponseData<Boolean> inventoryChange(@Validated @RequestBody StockQuery query) {
        Boolean r = stockInvokeService.inventoryChange(query);
        return ResponseData.ok(r);
    }


    @Operation(summary = "备料单锁库")
    @PostMapping("/prepareMaterial/lock")
    public ResponseData<PrepareMaterialResultDTO> prepareMaterialLock(@Validated @RequestBody PrepareMaterialCreateDTO dto) {
        return ResponseData.ok(stockInvokeService.prepareMaterialLock(dto));
    }


    @Operation(summary = "备料单出库")
    @PostMapping("/prepareMaterial/out")
    public ResponseData<?> prepareMaterialOut(@Validated @RequestBody PrepareMaterialOutDTO dto) {
        String pkIcIn = stockInvokeService.prepareMaterialOut(dto);
        return ResponseData.ok(Dict.create().set("pkIcIn", pkIcIn));
    }

    @Operation(summary = "备料单领料")
    @PostMapping("/prepareMaterial/receive")
    public ResponseData<ReceiveVO> prepareMaterialReceive(@Validated @RequestBody PrepareMaterialReceiveDTO dto) {
        return ResponseData.ok(stockInvokeService.prepareMaterialReceive(dto));
    }


    @Operation(summary = "自助领料")
    @PostMapping("/receive")
    public ResponseData<ReceiveVO> receive(@Validated @RequestBody PrepareMaterialCreateDTO dto) {
        return ResponseData.ok(stockInvokeService.receive(dto));
    }


    @Operation(summary = "归还物料")
    @PostMapping("/materialReturn")
    public ResponseData<MaterialReturnVO> materialReturn(@Validated @RequestBody MaterialReturnDTO dto) {
        return ResponseData.ok(stockInvokeService.materialReturn(dto));
    }

    @Operation(summary = "归还物料-签字")
    @PostMapping("/materialReturnSign")
    public ResponseData<MaterialReturnSignVO> materialReturnSign(@Validated @RequestBody MaterialReturnSignDTO dto) {
        return ResponseData.ok(stockInvokeService.materialReturnSign(dto));
    }

    @Operation(summary = "货位调整")
    @PostMapping("/locationAdjustment")
    public ResponseData<?> locationAdjustment(@RequestBody NcTemplateDTO<Object, Object> dto) {
        return ResponseData.ok(stockInvokeService.locationAdjustment(dto));
    }

    @Operation(summary = "转库")
    @PostMapping("/transfer")
    public ResponseData<TransferVO> transfer(@Validated @RequestBody TransferDTO dto) {
        return ResponseData.ok(stockInvokeService.transfer(dto));
    }

}
