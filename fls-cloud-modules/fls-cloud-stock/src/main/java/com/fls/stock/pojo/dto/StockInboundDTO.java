package com.fls.stock.pojo.dto;

import com.fls.stock.pojo.model.BaseSourceBill;
import com.fls.stock.pojo.model.MaterialOperationRecord;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class StockInboundDTO extends BaseSourceBill {
    /**
     * 入库维修班组id
     */
    @NotBlank(message = "入库维修班组id不能为空")
    private String idWorkteam;
    /**
     * 物料列表
     */
    @NotEmpty(message = "物料列表不能为空")
    private List<MaterialOperationRecord> records;
}
