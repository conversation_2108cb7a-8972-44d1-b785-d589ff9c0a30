package com.fls.stock.enums;

import lombok.Getter;

import java.util.Objects;
import java.util.Optional;

/**
 * @Description:
 * @Author: caibenwei
 * @DATE: 2024/12/18 17:46
 */
@Getter
public enum BillTypeEnum {


    TRANSFER("转库单"),

    ALLOCATE("调拨单");

    /**
     * 描述
     */
    private final String desc;


    BillTypeEnum(String desc) {
        this.desc = desc;
    }


    public static Optional<BillTypeEnum> match(String str) {
        if (Objects.nonNull(str)) {
            for (BillTypeEnum enums : BillTypeEnum.values()) {
                if (enums.name().equalsIgnoreCase(str)) {
                    return Optional.ofNullable(enums);
                }
            }
        }
        return Optional.empty();
    }


}
