package com.fls.stock.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @create 2024/12/11 17:39
 **/
@Component
@Data
@ConfigurationProperties(prefix = "nc")
public class NcConfig {

    private String baseUrl;

//    货位调整
    private String locationAdjustment;

//    创建转库单
    private String transferOrder;

//    创建调拨单
    private String allocateOrder;

//    转库出库签字
    private String transferOutSign;

//    调拨出库
    private String allocateOut;

//    转库入库签字
    private String transferInSign;

//    调拨入库签字
    private String allocateInSign;

}
