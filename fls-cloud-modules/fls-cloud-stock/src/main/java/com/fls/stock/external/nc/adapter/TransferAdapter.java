package com.fls.stock.external.nc.adapter;

import cn.hutool.core.lang.Assert;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fls.common.core.constant.CommonConstants;
import com.fls.common.core.exception.ServiceException;
import com.fls.stock.config.NcParamConfig;
import com.fls.stock.entity.IcGeneraloutB;
import com.fls.stock.enums.BillTypeEnum;
import com.fls.stock.external.nc.handle.order.NcOrderFactory;
import com.fls.stock.external.nc.handle.order.NcOrderHandle;
import com.fls.stock.external.nc.pojo.dto.MaterialOutDTO;
import com.fls.stock.external.nc.pojo.dto.PrepareMaterialResultDTO;
import com.fls.stock.mapper.IcGeneraloutBMapper;
import com.fls.stock.pojo.dto.PrepareMaterialContextDTO;
import com.fls.stock.service.invoke.BaseUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/7/4 17:10
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TransferAdapter {

    private final NcOrderFactory ncOrderFactory;

    private final IcGeneraloutBMapper icGeneraloutBMapper;

    private final NcParamConfig ncParamConfig;

    private final BaseUserService userService;

    public PrepareMaterialResultDTO onTransfer(PrepareMaterialContextDTO ctx) {
        BillTypeEnum orderType = BillTypeEnum.TRANSFER;
        JSONObject param = JSONUtil.createObj();
        param.set("vdef15", "2");  // 自动签字
        param.set("vtrantypecode", ncParamConfig.getLockTransferVtrantypecode()); // 转库单交易类型编码:维修转库（实发）
        ctx.setOrderParamMaps(param);

        NcOrderHandle ncOrderHandle = ncOrderFactory.getHandle(orderType.name());
        PrepareMaterialResultDTO resultDTO = ncOrderHandle.createOperate(ctx);
        return resultDTO;

    }

    public Object onMaterialReturnOut(PrepareMaterialContextDTO ctx, PrepareMaterialResultDTO resultDTO) {
        String userNcPk = userService.getUserNcPk(ctx.getUserCode());
        MaterialOutDTO.MaterialOutDTOBuilder builder = MaterialOutDTO.builder()
            .approver(userNcPk);
        // 根据转库单查询出库单pk，作为入参
        IcGeneraloutB icGeneraloutB = icGeneraloutBMapper.selectOne(new LambdaQueryWrapper<IcGeneraloutB>()
            .eq(IcGeneraloutB::getDr, CommonConstants.DELETE_FLAG_NOT_DELETED)
            .eq(IcGeneraloutB::getCsourcebillhid, resultDTO.getPkNcBill())
            .last("AND ROWNUM = 1"));

        Assert.notNull(icGeneraloutB, () -> new ServiceException("nc普通出库单不存在！"));
        builder.cgeneralhid(icGeneraloutB.getCgeneralhid());

        NcOrderHandle handle = ncOrderFactory.getHandle(resultDTO.getBillType());
        return handle.outOperate(builder.build());
    }

}
