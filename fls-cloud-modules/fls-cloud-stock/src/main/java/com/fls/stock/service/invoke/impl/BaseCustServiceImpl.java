package com.fls.stock.service.invoke.impl;

import com.fls.stock.entity.BaseCustEntity;
import com.fls.stock.mapper.BaseCustMapper;
import com.fls.stock.service.invoke.BaseCustService;
import com.github.yulichang.base.MPJBaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 客户基本档案-service
 *
 * <AUTHOR>
 * @since 2024-11-06
 */
@Service
@Slf4j
public class BaseCustServiceImpl extends MPJBaseServiceImpl<BaseCustMapper, BaseCustEntity> implements BaseCustService {
}
