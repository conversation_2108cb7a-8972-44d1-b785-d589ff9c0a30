package com.fls.stock.service.invoke;


import com.fls.stock.entity.BaseWorkteam;
import com.fls.stock.entity.BaseWorkteamMember;
import com.fls.stock.pojo.dto.MaterialDataDTO;
import com.fls.stock.pojo.dto.StockContextDTO;
import com.fls.stock.pojo.query.StockQuery;
import org.apache.commons.lang3.tuple.Pair;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2024/12/12 10:25
 **/
public interface StockValidatedService {
    StockContextDTO stockOut(StockQuery query);

    StockContextDTO stockIn(StockQuery query);

    StockContextDTO inventoryChange(StockQuery query);

    Pair<BaseWorkteam, List<BaseWorkteamMember>> preMaterial(String idWorkTeam, String idPerson, String idWarehourseOut, List<MaterialDataDTO> dataList);
}
