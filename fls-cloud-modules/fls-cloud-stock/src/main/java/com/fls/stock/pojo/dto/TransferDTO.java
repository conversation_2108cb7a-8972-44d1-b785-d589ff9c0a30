package com.fls.stock.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/4 16:56
 */
@Data
public class TransferDTO extends BaseApiDTO {


    @Schema(description = "接口来源对象主键")
    @NotBlank(message = "接口来源对象主键不能为空！")
    private String idSource;

//    不使用这个传参
    @Deprecated
    @Schema(description = "转出仓库", hidden = true)
    private String outIdWarehourse;

//    不使用这个传参
    @Deprecated
    @Schema(description = "转出仓库", hidden = true)
    private String inIdWarehourse;


    @Schema(description = "转出仓库")
    private String outIdWarehouse;

    @Schema(description = "转出仓库")
    private String inIdWarehouse;


    @Schema(description = "物料明细")
    @NotNull(message = "物料明细数据不能为空")
    @Size(min = 1, message = "物料明细数据不能为空")
    private List<TransferMaterialDTO> dataList;
}
