package com.fls.stock.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 仓库表
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_base_warehouse")
public class BaseWarehouse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId("id_warehouse")
    private String idWarehouse;

    /**
     * 编码
     */
    @TableField("code")
    private String code;

    /**
     * 名称
     */
    @TableField("name")
    private String name;

    /**
     * 所属组织
     */
    @TableField("id_org")
    private String idOrg;

    /**
     * NC仓库主键
     */
    @TableField("pk_stordoc")
    private String pkStordoc;

    /**
     * 是否启用货位，参见yesorno
     */
    @TableField("rack_flag")
    private String rackFlag;

    /**
     * 系统类型，01=NC，02=LABS
     */
    @TableField("system_type")
    private String systemType;

    /**
     * 是否外贸仓，参见yesorno
     */
    @TableField("foreign_flag")
    private String foreignFlag;

    /**
     * 是否参与ROP，参见yesorno
     */
    @TableField("rop_flag")
    private String ropFlag;

    /**
     * 是否启用WMS，参见yesorno
     */
    @TableField("wms_flag")
    private String wmsFlag;

    /**
     * wms别名，gz=广州基地，tj=天津基地，hf=合肥基地
     */
    @TableField("wms_alias")
    private String wmsAlias;

    /**
     * wms数据库名
     */
    @TableField("wms_db_name")
    private String wmsDbName;

    /**
     * ROP主体ID
     */
    @TableField("id_rop_unit")
    private String idRopUnit;

    /**
     * 启用状态：1=未启用，2=已启用，3=已停用，默认1，参见basestatus
     */
    @TableField("status")
    private String status;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 失效时间
     */
    @TableField("disable_time")
    private Date disableTime;

    /**
     * 时间戳
     */
    @TableField("ts")
    private Date ts;

    /**
     * 是否删除：0=否，1=是，默认0，参见yesorno
     */
    @TableField("delete_flag")
    @TableLogic
    private String deleteFlag;
}
