package com.fls.stock.utils;

import com.fls.stock.entity.BaseBillEntity;
import com.fls.stock.entity.WorkteamInboundBill;
import com.fls.stock.entity.WorkteamOutboundBill;
import com.fls.stock.pojo.model.BaseSourceBill;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.function.BiConsumer;

public class BillGenerator {

    private static <T extends BaseBillEntity> T generateBill(T bill, BaseSourceBill dto, BigDecimal count, BiConsumer<T, BigDecimal> setCount) {
        bill.setBillProperties(dto);
        bill.setBillDate(LocalDate.now());
        setCount.accept(bill, count);
        return bill;
    }

    public static WorkteamOutboundBill generateOutboundBill(BaseSourceBill outbound, BigDecimal count) {
        return generateBill(new WorkteamOutboundBill(), outbound, count, WorkteamOutboundBill::setOutboundCount);
    }

    public static WorkteamInboundBill generateInboundBill(BaseSourceBill inbound, BigDecimal count) {
        return generateBill(new WorkteamInboundBill(), inbound, count, WorkteamInboundBill::setInboundCount);
    }
}