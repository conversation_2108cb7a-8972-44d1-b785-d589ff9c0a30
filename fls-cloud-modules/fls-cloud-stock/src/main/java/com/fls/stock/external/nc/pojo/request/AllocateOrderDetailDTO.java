package com.fls.stock.external.nc.pojo.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description:
 * @Author: caibenwei
 * @DATE: 2024/12/18 15:35
 */
@Data
public class AllocateOrderDetailDTO {

    @Schema(description = "调入仓库，仓库PK")
    private String cinstordocid;

    @Schema(description = "物料，PK值")
    private String cinventoryid;

    @Schema(description = "调出仓库，库PK")
    private String coutstordocid;

    @Schema(description = "税码，增值税税码税率PK，默认为CN01，一般纳税货品增值税")
    private String ctaxcodeid;

    @Schema(description = "计划到货日期，格式要求：yyyy-mm-dd")
    private String dplanarrivedate;

    @Schema(description = "计划发货日期，格式要求：yyyy-mm-dd")
    private String dplanoutdate;

    @Schema(description = "主数量，两位小数")
    private BigDecimal nnum;

    @Schema(description = "主含税单价，四位小数")
    private BigDecimal norigtaxnetprice;

    @Schema(description = "税率")
    private BigDecimal ntaxrate;

    private String vbdef19;

    @Schema(description = "接口来源对象明细主键，可传空值")
    private String vbdef20;

    @Schema(description = "调入货位，货位PK")
    private String cinspaceid;

    @Schema(description = "调出货位，货位PK")
    private String coutspaceid;

}
