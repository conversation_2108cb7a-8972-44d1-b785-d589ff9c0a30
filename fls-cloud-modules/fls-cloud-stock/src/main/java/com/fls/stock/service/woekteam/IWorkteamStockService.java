package com.fls.stock.service.woekteam;

import cn.hutool.core.lang.Dict;
import com.baomidou.mybatisplus.extension.service.IService;
import com.fls.common.mybatis.core.page.PageResult;
import com.fls.stock.entity.WorkteamStock;
import com.fls.stock.pojo.dto.GroupTransferDTO;
import com.fls.stock.pojo.dto.StockExchangeDTO;
import com.fls.stock.pojo.dto.StockInboundDTO;
import com.fls.stock.pojo.dto.StockLockReleaseDTO;
import com.fls.stock.pojo.dto.StockOutboundDTO;
import com.fls.stock.pojo.query.TeamStockDetailQuery;
import com.fls.stock.pojo.vo.TeamStockDetailVO;

/**
 * 工作班组库存表(WorkteamStock)表服务接口
 *
 * <AUTHOR>
 * @since 2024-12-11 10:36:04
 */
public interface IWorkteamStockService extends IService<WorkteamStock> {
    /**
     * 查询班组库存详情
     * @param teamStockDetailQuery 班组库存详情查询条件
     * @return 班组库存详情分页结果
     */
    PageResult<TeamStockDetailVO> getWorkteamStockPage(TeamStockDetailQuery teamStockDetailQuery);

    /**
     * 班组库存组间调用
     * @param groupTransfer 组间调用参数
     */
    void groupTransfer(GroupTransferDTO groupTransfer);

    /**
     * 班组库存出库
     * @param outbound 出库参数
     * @return 班组库存出库单号
     */
    String stockOutbound(StockOutboundDTO outbound);

    /**
     * 班组库存入库
     * @param inboundDTO 入库参数
     * @return 班组库存入库单号
     */
    String stockInbound(StockInboundDTO inboundDTO);

    /**
     * 班组库存锁定
     * @param lock 班组库存锁定参数
     */
    void stockLock(StockLockReleaseDTO lock);

    /**
     * 班组库存释放
     * @param lock 班组库存释放参数
     */
    void stockRelease(StockLockReleaseDTO lock);

    /**
     * 班组成员用料登记
     * @param apply 用料登记参数
     */
    void stockApply(StockLockReleaseDTO apply);

    Dict stockExchange(StockExchangeDTO exchange);
}

