package com.fls.stock.external.nc.handle.order;

import com.fls.common.core.exception.ServiceException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Optional;

/**
 * @Description: 单据工厂，用于初始化单据
 * @Author: caibenwei
 * @DATE: 2024/12/19 14:34
 */
@Component
@RequiredArgsConstructor
public class NcOrderFactory {

    private final Map<String, NcOrderHandle> handle;

    public NcOrderHandle getHandle(String type) {
        return Optional.ofNullable(handle.get(type))
            .orElseThrow(() -> new ServiceException("无效的订单类型！！"));
    }

}
