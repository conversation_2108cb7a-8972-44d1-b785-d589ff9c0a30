package com.fls.stock.service.invoke.impl;

import cn.hutool.core.util.ObjectUtil;
import com.fls.stock.entity.BaseWarehouse;
import com.fls.stock.entity.LabelBatch;
import com.fls.stock.mapper.LabelBatchMapper;
import com.fls.stock.service.invoke.BaseWarehouseService;
import com.fls.stock.service.invoke.LabelBatchService;
import com.github.yulichang.base.MPJBaseServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;


@Service
@RequiredArgsConstructor
public class LabelBatchServiceImpl extends MPJBaseServiceImpl<LabelBatchMapper, LabelBatch> implements LabelBatchService {
    private final BaseWarehouseService warehouseService;

    @Override
    public String selectBarcodeByCode(String code) {
        List<String> wmsDatabase = getWmsDatabase();
        String byCode = null;
        for (String dbName : wmsDatabase) {
            byCode = this.baseMapper.selectBarcodeByCode(code, dbName);
            if (ObjectUtil.isNotEmpty(byCode)) {
                return byCode;
            }
        }
        if (ObjectUtil.isEmpty(byCode)) {
            byCode = code.toUpperCase();
        }
        return byCode;
    }

    private List<String> getWmsDatabase() {
        return warehouseService.lambdaQuery()
            .select(BaseWarehouse::getWmsDbName)
            .isNotNull(BaseWarehouse::getWmsDbName)
            .ne(BaseWarehouse::getWmsDbName, "")
            .list()
            .stream()
            .map(BaseWarehouse::getWmsDbName)
            .distinct()
            .collect(Collectors.toList());
    }

}
