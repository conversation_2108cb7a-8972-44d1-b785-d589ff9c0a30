package com.fls.stock.service.invoke.impl;

import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fls.common.core.constant.CommonConstants;
import com.fls.common.core.exception.ServiceException;
import com.fls.stock.mapper.BaseUserMapper;
import com.fls.stock.entity.BaseUser;
import com.fls.stock.service.invoke.BaseUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 用户表(BaseUser)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-27 14:44:47
 */
@Service
@Slf4j
public class BaseUserServiceImpl extends ServiceImpl<BaseUserMapper, BaseUser> implements BaseUserService {


    @Override
    public String getUserNcPk(String userCode) {
        BaseUser baseUser = this.lambdaQuery()
            .eq(BaseUser::getDeleteFlag, CommonConstants.DELETE_FLAG_NOT_DELETED)
            .eq(BaseUser::getCode, userCode)
            .one();
        Assert.notNull(baseUser, () -> new ServiceException("用户不存在！"));
        Assert.notNull(baseUser.getPkUser(), () -> new ServiceException("用户NCpk值为空！"));
        return baseUser.getPkUser();
    }
}
