package com.fls.stock.service.invoke.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fls.common.core.constant.CommonConstants;
import com.fls.common.core.exception.ServiceException;
import com.fls.stock.constant.StockConst;
import com.fls.stock.entity.BaseWarehouse;
import com.fls.stock.entity.BaseWhpos;
import com.fls.stock.entity.BaseWorkteam;
import com.fls.stock.entity.BaseWorkteamMember;
import com.fls.stock.pojo.dto.MaterialDataDTO;
import com.fls.stock.pojo.dto.StockContextDTO;
import com.fls.stock.pojo.query.StockQuery;
import com.fls.stock.service.invoke.BaseWarehouseService;
import com.fls.stock.service.invoke.BaseWhposService;
import com.fls.stock.service.invoke.StockValidatedService;
import com.fls.stock.service.woekteam.BaseWorkteamMemberService;
import com.fls.stock.service.woekteam.BaseWorkteamService;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2024/12/12 10:26
 **/
@Service
@Slf4j
@RequiredArgsConstructor
public class StockValidatedServiceImpl implements StockValidatedService {

    private final BaseWorkteamService workteamService;

    private final BaseWorkteamMemberService workteamMemberService;

    private final BaseWhposService whposService;

    private final BaseWarehouseService warehouseService;


    @Override
    public StockContextDTO stockOut(StockQuery query) {
        StockContextDTO dto = init(query);
        return dto;
    }


    @Override
    public StockContextDTO stockIn(StockQuery query) {
        StockContextDTO dto = init(query);
        return dto;
    }

    @Override
    public StockContextDTO inventoryChange(StockQuery query) {
        StockContextDTO dto = init(query);
        return dto;
    }


    private static StockContextDTO init(StockQuery query) {
        StockContextDTO dto = new StockContextDTO();
        dto.setQuery(query);
        return dto;
    }


    @Override
    public Pair<BaseWorkteam, List<BaseWorkteamMember>> preMaterial(String idWorkTeam, String idPerson, String idWarehourseOut, List<MaterialDataDTO> dataList) {
//        工作班组或维修人员是否存在
        Pair<BaseWorkteam, List<BaseWorkteamMember>> workteamInfo = hasWorkteamAndMember(idWorkTeam, idPerson);
//        是否存在货位
        hasWhpos(workteamInfo);
//        data仓库是否唯一
        hasOnlyWarehouse(dataList, idWarehourseOut);
        return workteamInfo;
    }

    private void hasOnlyWarehouse(List<MaterialDataDTO> dataList, String idWarehourseOut) {
        if (CollUtil.isNotEmpty(dataList)) {
            Set<String> whposIds = dataList.stream()
                .map(MaterialDataDTO::getIdWhpos)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toSet());
            if (StrUtil.isNotBlank(idWarehourseOut)) {
                BaseWarehouse warehouse = warehouseService.getById(idWarehourseOut);
                Assert.isTrue(Objects.nonNull(warehouse), () -> new ServiceException("仓库不存在！"));
//                如果启用货位，货位编码必传，并且货位编码和出库仓库必须一致
                if (StockConst.FLAG_TRUE.equals(warehouse.getRackFlag())) {
                    Assert.isTrue(CollUtil.isNotEmpty(whposIds), () -> new ServiceException("出库仓库已启用货位，仓库货位不能为空！"));
                    List<String> warehouseIds = whposService.getWarehouseByWhposIds(whposIds);
                    Assert.isFalse(warehouseIds.size() == 0, () -> new ServiceException("无效的货位编码!！"));
                    Assert.isFalse(warehouseIds.size() > 1, () -> new ServiceException("不支持跨库同时领料！"));
                    Assert.isTrue(warehouseIds.get(0).equals(idWarehourseOut), () -> new ServiceException("出库出库与货位仓库不一致！"));
                } else {
                    if (CollUtil.isNotEmpty(whposIds)) {
//                        清空货位
                        dataList.forEach(item -> item.setIdWhpos(StrUtil.EMPTY));
                    }
                }
            } else {
//                出库货位不能为空
                Assert.isTrue(CollUtil.isNotEmpty(whposIds), () -> new ServiceException("出库货位编码不能为空"));
//                出库仓库必须唯一
                QueryWrapper<BaseWhpos> queryWrapper = new QueryWrapper<>();
                queryWrapper.select("distinct id_warehouse");
                queryWrapper.in("id_whpos", whposIds);
                long count = whposService.count(queryWrapper);
                Assert.isFalse(count == 0, () -> new ServiceException("无效的货位编码！"));
                Assert.isFalse(count > 1L, () -> new ServiceException("不支持跨库同时领料！"));
            }
        }
    }

    private void hasWhpos(Pair<BaseWorkteam, List<BaseWorkteamMember>> workteamInfo) {
        String idWorkTeamWhpos = workteamInfo.getLeft().getIdWhpos();
        String memberWhpos = workteamInfo.getRight().stream()
            .filter(item -> StrUtil.equals(idWorkTeamWhpos, item.getIdWorkteam()))
            .map(BaseWorkteamMember::getIdWhpos)
            .findFirst()
            .orElse(StrUtil.EMPTY);
        Assert.isFalse(StrUtil.isAllBlank(idWorkTeamWhpos, memberWhpos), () -> new ServiceException("工作班组货位和维修人员货位不能同时为空！"));
    }

    private Pair<BaseWorkteam, List<BaseWorkteamMember>> hasWorkteamAndMember(String idWorkTeam, String idPerson) {
        BaseWorkteam workteam = workteamService.lambdaQuery()
            .eq(BaseWorkteam::getDeleteFlag, CommonConstants.DELETE_FLAG_NOT_DELETED)
            .eq(BaseWorkteam::getIdWorkteam, idWorkTeam)
            .last("limit 1")
            .one();
        Assert.isTrue(Objects.nonNull(workteam), () -> new ServiceException("工作班组不存在！"));

        List<BaseWorkteamMember> workTeamMembers = Lists.newArrayList();
        if (StrUtil.isNotBlank(idPerson)) {
            workTeamMembers = workteamMemberService.lambdaQuery()
                .eq(BaseWorkteamMember::getDeleteFlag, CommonConstants.DELETE_FLAG_NOT_DELETED)
                .eq(BaseWorkteamMember::getIdMember, idPerson)
                .list();
            Assert.isTrue(CollUtil.isNotEmpty(workTeamMembers), () -> new ServiceException("维修人员不存在工作班组中！"));
        }
        return Pair.of(workteam, workTeamMembers);
    }
}
