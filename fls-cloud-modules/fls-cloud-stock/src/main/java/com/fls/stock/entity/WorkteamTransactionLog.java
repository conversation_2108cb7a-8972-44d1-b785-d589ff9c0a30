package com.fls.stock.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 班组库存流水表(WorkteamTransactionLog)表实体类
 *
 * <AUTHOR>
 * @since 2024-12-11 09:13:44
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_workteam_transaction_log")
public class WorkteamTransactionLog implements Serializable {

    private static final long serialVersionUID = 1L;

    //主键id
    @TableId(value = "id_transaction_log", type = IdType.ASSIGN_UUID)
    private String idTransactionLog;

    //流水号
    private String transactionCode;

    //1-入库, 2-出库
    private Integer logType;

    //业务来源:1-物料领取, 2-物料归还, 3-组间调用
    private Integer sourceType;

    //实际入库、出库时间
    private LocalDateTime stockTime;

    //来源描述
    private String sourceDesc;

    //来源库存操作编号
    private String sourceStockCode;

    //来源单据编号
    private String sourceBillCode;

    //来源单据id
    private String idSourceBill;

    //来源单据资源id
    private String idSourceRes;

    //来源单据资源名称
    private String sourceResName;

    //源头来源单据id
    private String idFirstSourceBill;

    //源头来源单据编号
    private String firstSourceBillCode;

    //源头单据资源id
    private String idFirstRes;

    //初始来源单据资源名称
    private String firstResName;

    //创建者
    private String creator;

    //创建时间
    private LocalDateTime createTime;

    //创建人名称
    private String createName;

    //时间戳
    private LocalDateTime ts;

    //删除标记: 0-正常, 1-删除
    private String deleteFlag;
}

