package com.fls.stock.service.invoke.impl;

import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fls.common.core.exception.ServiceException;
import com.fls.stock.entity.BaseWarehouse;
import com.fls.stock.mapper.BaseWarehouseMapper;
import com.fls.stock.service.invoke.BaseWarehouseService;
import org.springframework.stereotype.Service;


/**
 * 仓库表 服务实现类
 */
@Service
public class BaseWarehouseServiceImpl extends ServiceImpl<BaseWarehouseMapper, BaseWarehouse> implements BaseWarehouseService {

    @Override
    public BaseWarehouse queryBaseWarehouse(String idWarehouse) {
        BaseWarehouse warehouse = this.getById(idWarehouse);
        Assert.notNull(warehouse, () -> new ServiceException("无效的仓库id"));
        return warehouse;

    }
}
