package com.fls.stock.enums;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 库存操作类型
 *
 * <AUTHOR>
 * @since 2024-12-24
 */
@Getter
@AllArgsConstructor
public enum BoundTypeEnum {
    /**
     * 物料退换
     */
    RECEIVE(1, "物料领取"),
    /**
     * 物料归还
     */
    RETURN(2, "物料归还"),
    /**
     * 组间调用
     */
    TRANSFER(3, "组间调用"),
    /**
     * 物料退换
     */
    EXCHANGE(4, "物料退换");

    private final int type;

    private final String desc;

    public static String getEnumDesc(Integer code) {
        if (code == null) {
            return null;
        }
        for (BoundTypeEnum approveCodeEnum : BoundTypeEnum.values()) {
            if (approveCodeEnum.getType() == code) {
                return approveCodeEnum.getDesc();
            }
        }
        return StrUtil.EMPTY;
    }

}

