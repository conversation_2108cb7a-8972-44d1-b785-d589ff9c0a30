package com.fls.stock.external.nc.handle.stock.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.fls.common.core.exception.ServiceException;
import com.fls.stock.config.NcConfig;
import com.fls.stock.entity.BaseWarehouse;
import com.fls.stock.external.nc.handle.client.NcClient;
import com.fls.stock.external.nc.handle.stock.NcTransferService;
import com.fls.stock.external.nc.pojo.request.NcStockChangeBodyDTO;
import com.fls.stock.external.nc.pojo.request.NcStockChangeDetailDTO;
import com.fls.stock.external.nc.pojo.request.NcTemplateDTO;
import com.fls.stock.pojo.query.MaterialNumReq;
import com.fls.stock.pojo.query.StockQuery;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @create 2024/12/11 17:39
 **/
@Service
@Slf4j
@RequiredArgsConstructor
public class NcTransferServiceImpl implements NcTransferService {

    private final NcConfig ncConfig;

    private final NcClient ncClient;


    @Override
    public JSONObject stockChange(BaseWarehouse warehouse, StockQuery query) {
        NcStockChangeBodyDTO body = new NcStockChangeBodyDTO();
        body.setBillmaker(query.getPkUser());
        body.setCwarehouseid(warehouse.getPkStordoc());
        body.setDbilldate(DateUtil.today());
        body.setPk_org(query.getPkOrg());
        AtomicInteger rowNo = new AtomicInteger(0);
        ArrayList<NcStockChangeDetailDTO> list = Lists.newArrayList();
        for (MaterialNumReq item : query.getMaterialNumReq()) {
            NcStockChangeDetailDTO detail = new NcStockChangeDetailDTO();
            detail.setClocationid(query.getPkWhpos());
            detail.setClocationinid(item.getPkStockInWhpos());
            detail.setCmaterialvid(item.getPkMaterial());
            detail.setCrowno(rowNo.addAndGet(10));
            detail.setNnum(item.getQuantity());
            list.add(detail);
        }
        JSONObject result = ncClient.send(ncConfig.getLocationAdjustment(), new NcTemplateDTO(body, list));
        if (result != null && JSONUtil.parseObj(result).getInt("code") == HttpStatus.OK.value()) {
            return result;
        }
        throw new ServiceException("NC货位调整异常");
    }


    @Override
    public JSONObject locationAdjustment(NcTemplateDTO dto) {
        JSONObject result = ncClient.send(ncConfig.getLocationAdjustment(), dto);
        if (result != null && JSONUtil.parseObj(result).getInt("code") == HttpStatus.OK.value()) {
            return result;
        }
        throw new ServiceException("远程调用NC货位调整异常:" + result.getStr("msg"));
    }
}
