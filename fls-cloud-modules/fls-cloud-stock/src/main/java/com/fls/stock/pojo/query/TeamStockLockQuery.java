package com.fls.stock.pojo.query;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 班组库存明细查询对象
 *
 * <AUTHOR>
 * @since 2024-12-10
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class TeamStockLockQuery extends BasePageQuery {
    /**
     * 开始日期：yyyy-MM-dd，格式：yyyy-MM-dd
     */
    private String beginDate;
    /**
     * 流水号/来源单据编号
     */
    private String billCode;
    /**
     * 结束日期：yyyy-MM-dd，格式：yyyy-MM-dd
     */
    private String endDate;
    /**
     * 物料名称
     */
    private String materialName;
    /**
     * 物料id
     */
    private String idMaterial;
    /**
     * 组织名称/经营主体名称/班组名称
     */
    private String name;
    /**
     * 锁定类型,1-库存锁定，2-库存释放
     */
    private String lockType;
    /**
     * 经营主体
     */
    private String idBizunit;

    /**
     * 班组id
     */
    private String idWorkteam;
}
