package com.fls.stock.pojo.builder;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.fls.stock.entity.BaseInventory;
import com.fls.stock.entity.BaseOrg;
import com.fls.stock.entity.BaseWarehouse;
import com.fls.stock.entity.BaseWhpos;
import com.fls.stock.entity.BaseWorkteam;
import com.fls.stock.pojo.dto.MaterialDataDTO;
import com.fls.stock.pojo.dto.MaterialReturnDTO;
import com.fls.stock.pojo.dto.MaterialReturnSignDTO;
import com.fls.stock.pojo.dto.MaterialReturnSignDataDTO;
import com.fls.stock.pojo.dto.PrepareMaterialContextDTO;
import com.fls.stock.pojo.dto.PrepareMaterialCreateDTO;
import com.fls.stock.pojo.dto.TransferDTO;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Description: 构建 PrepareMaterialContextDTO 的构建器模式实现。
 * @Author: caibenwei
 * @DATE: 2024/12/23 16:23
 */
public class PrepareMaterialContextBuilder {

    private final PrepareMaterialContextDTO ctx;

    public PrepareMaterialContextBuilder() {
        this.ctx = new PrepareMaterialContextDTO();
    }

    public static PrepareMaterialContextBuilder builder() {
        return new PrepareMaterialContextBuilder();
    }

    public PrepareMaterialContextDTO build() {
        return ctx;
    }

    /**
     * 设置材料创建信息。
     *
     * @param dto 材料创建 DTO
     * @return 当前构建器实例
     */
    public PrepareMaterialContextBuilder withMaterialCreate(PrepareMaterialCreateDTO dto) {
        ctx.setSrcSysName(dto.getSrcSysName());
        ctx.setUserCode(dto.getUserCode());
        ctx.setAssetCode(dto.getAssetCode());
        ctx.setBillCode(dto.getBillCode());
        ctx.setSourcePk(dto.getIdSource());
        ctx.setDataList(dto.getDataList());
        return this;
    }

    /**
     * 设置材料退货信息。
     *
     * @param dto 材料退货 DTO
     * @return 当前构建器实例
     */
    public PrepareMaterialContextBuilder withMaterialReturn(MaterialReturnDTO dto) {
        ctx.setSrcSysName(dto.getSrcSysName());
        ctx.setBillCode(dto.getBillCode());
        ctx.setSourcePk(dto.getIdSource());
        ctx.setDataList(dto.getDataList());
        ctx.setUserCode(dto.getUserCode());
        return this;
    }

    public PrepareMaterialContextBuilder withMaterialReturnSign(MaterialReturnSignDTO dto) {
        ctx.setSrcSysName(dto.getSrcSysName());
        ctx.setBillCode(dto.getBillCode());
        ctx.setSourcePk(dto.getIdSource());
        ctx.setDataList(dto.getDataList());
        ctx.setUserCode(dto.getUserCode());
        return this;
    }

    /**
     * 设置工作团队信息。
     *
     * @param workteamInfo 工作团队信息
     * @return 当前构建器实例
     */
    public PrepareMaterialContextBuilder withWorkTeamInfo(BaseWorkteam workteamInfo) {
        ctx.setWorkteam(workteamInfo);
        return this;
    }

    /**
     * 设置出库信息。
     *
     * @param outStockInfo 入库信息三元组 (仓库, 仓位, 组织)
     * @return 当前构建器实例
     */
    public PrepareMaterialContextBuilder withOutStockInfo(Pair<BaseWarehouse, BaseOrg> outStockInfo) {
        ctx.setWarehouseOut(outStockInfo.getLeft());
        ctx.setOrgOut(outStockInfo.getRight());
        return this;
    }


    /**
     * 设置入库信息。
     *
     * @param inStockInfo 入库信息三元组 (仓库, 仓位, 组织)
     * @return 当前构建器实例
     */
    public PrepareMaterialContextBuilder withInStockInfo(Triple<BaseWarehouse, BaseWhpos, BaseOrg> inStockInfo) {
        ctx.setWarehouseIn(inStockInfo.getLeft());
        ctx.setOrgIn(inStockInfo.getRight());
        return this;
    }

    public PrepareMaterialContextBuilder withInStockInfo(Pair<BaseWarehouse, BaseOrg> inStockInfo) {
        ctx.setWarehouseIn(inStockInfo.getLeft());
        ctx.setOrgIn(inStockInfo.getRight());
        return this;
    }

    public PrepareMaterialContextBuilder withInventoryPk(Map<String, BaseInventory> inventoryPk) {
        ctx.getDataList().forEach(item -> item.setInventory(inventoryPk.get(item.getIdInventory())));
        return this;
    }

    public PrepareMaterialContextBuilder withInventoryAndWhpos(Map<String, BaseInventory> inventoryPk, BaseWhpos outWhpos, Map<String, BaseWhpos> inWhposPk) {
        List<MaterialReturnSignDataDTO> newDataList = ctx.getDataList().stream()
            .map(o -> {
                MaterialReturnSignDataDTO dto = BeanUtil.toBean(o, MaterialReturnSignDataDTO.class);
                dto.setInventory(inventoryPk.get(dto.getIdInventory()));
                dto.setIdInventory(dto.getIdInventory());
                dto.setInIdWhpos(dto.getIdWhpos());
                if (StrUtil.isNotEmpty(dto.getIdWhpos())) {
                    dto.setInWhpos(inWhposPk.get(dto.getIdWhpos()));
                }
                dto.setOutWhpos(outWhpos);
                String outIdWhpos = Optional.ofNullable(outWhpos)
                    .map(BaseWhpos::getIdWhpos)
                    .orElse(null);
                dto.setOutIdWhpos(outIdWhpos);

                return dto;
            }).collect(Collectors.toList());
        ctx.setDataList(newDataList);
        return this;
    }

    public PrepareMaterialContextBuilder withInventoryPk(Map<String, BaseInventory> inventoryPk, BaseWhpos inWhpos, Map<String, BaseWhpos> outWhposPk) {
        List<MaterialDataDTO> newDataList = ctx.getDataList().stream()
            .map(o -> {
                MaterialDataDTO dto = BeanUtil.toBean(o, MaterialDataDTO.class);
                dto.setInventory(inventoryPk.get(dto.getIdInventory()));
                dto.setIdInventory(dto.getIdInventory());
                dto.setInWhpos(inWhpos);
                String inIdWhpos = Optional.ofNullable(inWhpos)
                    .map(BaseWhpos::getIdWhpos)
                    .orElse(null);
                dto.setInIdWhpos(inIdWhpos);
                dto.setOutIdWhpos(dto.getIdWhpos());
                if (StrUtil.isNotEmpty(dto.getIdWhpos())) {
                    dto.setOutWhpos(outWhposPk.get(dto.getIdWhpos()));
                }
                return dto;
            }).collect(Collectors.toList());
        ctx.setDataList(newDataList);
        return this;
    }

    public PrepareMaterialContextBuilder withInventoryPk(Map<String, BaseInventory> inventoryPk, BaseWhpos outWhpos, BaseWhpos inWhpos) {
        ctx.getDataList().forEach(item -> {
            item.setInventory(inventoryPk.get(item.getIdInventory()));
            item.setOutWhpos(outWhpos);
            item.setInWhpos(inWhpos);
        });
        return this;
    }

    public PrepareMaterialContextBuilder withInWhposPk(Map<String, BaseWhpos> inWhposPk) {
        if (MapUtil.isNotEmpty(inWhposPk)) {
            ctx.getDataList().forEach(item -> item.setInWhpos(inWhposPk.get(item.getInIdWhpos())));
        }
        return this;
    }

    public PrepareMaterialContextBuilder withOutWhposPk(Map<String, BaseWhpos> outWhposPk) {
        if (MapUtil.isNotEmpty(outWhposPk)) {
            ctx.getDataList().forEach(item -> item.setOutWhpos(outWhposPk.get(item.getOutIdWhpos())));
        }
        return this;
    }

    public PrepareMaterialContextBuilder withTransfer(TransferDTO dto, BaseWarehouse inWarehouse, BaseWarehouse outWarehouse, BaseOrg orgIn) {
        ctx.setSrcSysName(dto.getSrcSysName());
        ctx.setUserCode(dto.getUserCode());
        ctx.setSourcePk(dto.getIdSource());
        ctx.setDataList(dto.getDataList());
        ctx.setWarehouseIn(inWarehouse);
        ctx.setWarehouseOut(outWarehouse);
        ctx.setOrgIn(orgIn);
        return this;
    }
}
