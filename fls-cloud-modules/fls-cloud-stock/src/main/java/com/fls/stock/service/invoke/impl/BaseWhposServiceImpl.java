package com.fls.stock.service.invoke.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fls.common.core.exception.ServiceException;
import com.fls.stock.entity.BaseWhpos;
import com.fls.stock.mapper.BaseWhposMapper;
import com.fls.stock.service.invoke.BaseWhposService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 货位表(BaseWhpos)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-11 17:23:16
 */
@Service
@Slf4j
public class BaseWhposServiceImpl extends ServiceImpl<BaseWhposMapper, BaseWhpos> implements BaseWhposService {


    @Override
    public Map<String, BaseWhpos> getWhposEntityMaps(Set<String> whposIds) {
        if (CollUtil.isNotEmpty(whposIds)) {
            List<BaseWhpos> list = this.lambdaQuery()
                .in(BaseWhpos::getIdWhpos, whposIds)
                .list();
            Assert.isTrue(list.size() == whposIds.size(),() -> new ServiceException("数据集中包含无效的货位id！"));
            return list.stream()
                .collect(Collectors.toMap(BaseWhpos::getIdWhpos, i -> i, (k1, k2) -> k1));
        }
        return MapUtil.empty();
    }

    @Override
    public List<String> getWarehouseByWhposIds(Set<String> whposIds) {
        return this.getBaseMapper().getWarehouseByWhposIds(whposIds);
    }
}
