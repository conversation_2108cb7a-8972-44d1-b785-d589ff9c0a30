package com.fls.stock.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Description:
 * @Author: caibenwei
 * @DATE: 2024/12/19 9:48
 */
@Data
public class PrepareMaterialReceiveDTO extends BaseApiDTO{

    @Schema(description = "单据类型，转库/调拨")
    @NotBlank(message = "单据类型不能为空")
    private String billType;

    @Schema(description = "普通入库单或调拨入pk")
    @NotBlank(message = "普通入库单或调拨入pk不能为空")
    private String pkIcIn;

}
