package com.fls.stock.external.nc.handle.order.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fls.common.core.constant.CommonConstants;
import com.fls.common.core.exception.ServiceException;
import com.fls.stock.config.NcConfig;
import com.fls.stock.entity.BaseWhpos;
import com.fls.stock.entity.IcGeneralinB;
import com.fls.stock.entity.IcGeneralinH;
import com.fls.stock.entity.IcGeneraloutB;
import com.fls.stock.entity.IcGeneraloutH;
import com.fls.stock.entity.IcWhstransH;
import com.fls.stock.enums.BillTypeEnum;
import com.fls.stock.external.nc.handle.client.NcClient;
import com.fls.stock.external.nc.handle.order.NcOrderHandle;
import com.fls.stock.external.nc.pojo.dto.MaterialOutDTO;
import com.fls.stock.external.nc.pojo.dto.PrepareMaterialResultDTO;
import com.fls.stock.external.nc.pojo.request.NcTemplateDTO;
import com.fls.stock.external.nc.pojo.request.TransferInSignBodyDTO;
import com.fls.stock.external.nc.pojo.request.TransferOrderBodyDTO;
import com.fls.stock.external.nc.pojo.request.TransferOrderDetailDTO;
import com.fls.stock.external.nc.pojo.request.TransferOutSignBodyDTO;
import com.fls.stock.mapper.IcGeneralinBMapper;
import com.fls.stock.mapper.IcGeneralinHMapper;
import com.fls.stock.mapper.IcGeneraloutBMapper;
import com.fls.stock.mapper.IcGeneraloutHMapper;
import com.fls.stock.mapper.IcWhstransHMapper;
import com.fls.stock.pojo.dto.MaterialDataDTO;
import com.fls.stock.pojo.dto.OtherInDTO;
import com.fls.stock.pojo.dto.PrepareMaterialContextDTO;
import com.fls.stock.pojo.dto.PrepareMaterialOutDTO;
import com.fls.stock.pojo.dto.PrepareMaterialReceiveDTO;
import com.fls.stock.pojo.vo.ReceiveVO;
import com.fls.stock.service.invoke.BaseUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * @Description: 转库单
 * @Author: caibenwei
 * @DATE: 2024/12/19 14:31
 */
@Slf4j
@RequiredArgsConstructor
@Service("TRANSFER")
public class TransferOrderHandle implements NcOrderHandle {

    private final NcConfig ncConfig;

    private final IcWhstransHMapper icWhstransHMapper;

    private final IcGeneraloutHMapper icGeneraloutHMapper;

    private final IcGeneralinHMapper icGeneralinHMapper;

    private final IcGeneraloutBMapper icGeneraloutBMapper;

    private final IcGeneralinBMapper icGeneralinBMapper;

    private final NcClient ncClient;

    private final BaseUserService userService;


    @Override
    public PrepareMaterialResultDTO createOperate(PrepareMaterialContextDTO ctx) {
        //获取用户nc主键
        String userNcPk = userService.getUserNcPk(ctx.getUserCode());
        PrepareMaterialResultDTO result = new PrepareMaterialResultDTO();
        TransferOrderBodyDTO body = new TransferOrderBodyDTO();
        body.setCotherwhid(ctx.getWarehouseIn().getPkStordoc());
        body.setCreator(userNcPk);
        body.setCreationtime(DateUtil.now());
        body.setCwarehouseid(ctx.getWarehouseOut().getPkStordoc());
        body.setDbilldate(DateUtil.today());
        body.setPk_org(ctx.getOrgIn().getPkOrg());
        body.setVdef7(ctx.getSrcSysName());
        body.setVdef8(ctx.getSourcePk());

        body.setVdef12(ctx.getOrderParamMaps().getStr("vdef12"));
        body.setVdef15(ctx.getOrderParamMaps().getStr("vdef15"));
        body.setVdef17(ctx.getSrcSysName());
        body.setVdef18(StrUtil.EMPTY);
        body.setVdef5(StrUtil.EMPTY);

        body.setVtrantypecode(ctx.getOrderParamMaps().getStr("vtrantypecode"));

        String vnotebody = ctx.getOrderParamMaps().getStr("vnotebody");
        List<TransferOrderDetailDTO> detailList = Lists.newArrayList();
        for (Object o : ctx.getDataList()) {
            MaterialDataDTO dto = BeanUtil.toBean(o, MaterialDataDTO.class);
            TransferOrderDetailDTO detailDTO = new TransferOrderDetailDTO();
            detailDTO.setCmaterialvid(dto.getInventory().getPkMaterial());
            detailDTO.setNnum(dto.getNumber());
            String outWhposPkRack = Optional.ofNullable(dto.getOutWhpos())
                .map(BaseWhpos::getPkRack)
                .orElse(StrUtil.EMPTY);
            detailDTO.setClocationid(outWhposPkRack);
            String inWhposPkRack = Optional.ofNullable(dto.getInWhpos())
                .map(BaseWhpos::getPkRack)
                .orElse(StrUtil.EMPTY);
            detailDTO.setVbdef5(inWhposPkRack);

            detailDTO.setVnotebody(vnotebody);
            detailDTO.setVbatchcode(dto.getBatchCode());
            detailDTO.setVbdef20(StrUtil.EMPTY);
            detailList.add(detailDTO);
        }
        JSONObject response = ncClient.send(ncConfig.getTransferOrder(), new NcTemplateDTO(body, detailList));
        if (response != null && response.getInt("code") == HttpStatus.OK.value()) {
            result.setBillType(BillTypeEnum.TRANSFER.name());
            result.setPkNcBill(Convert.toStr(response.getByPath("data.billid")));
            result.setNcBillCode(Convert.toStr(response.getByPath("data.billno")));
            return result;
        }
        throw new ServiceException("NC转库单异常" + response);
    }


    @Override
    public Boolean validatedOrderExists(String pkNcBill) {
        IcWhstransH whstrans = icWhstransHMapper.selectById(pkNcBill);
        Assert.notNull(whstrans, () -> new ServiceException("无效的转库单pk"));
        Assert.isTrue(CommonConstants.DELETE_FLAG_NOT_DELETED.equals(whstrans.getDr()), () -> new ServiceException("转库单已被删除！"));
        return Boolean.TRUE;
    }

    @Override
    public Boolean hasSuccessWmsOutOperate(PrepareMaterialOutDTO dto) {
        IcGeneraloutH icGeneraloutH = icGeneraloutHMapper.selectOne(new LambdaQueryWrapper<IcGeneraloutH>()
            .eq(IcGeneraloutH::getCgeneralhid, dto.getPkIcOut())
            .eq(IcGeneraloutH::getDr, CommonConstants.DELETE_FLAG_IS_DELETED)
            .last("AND ROWNUM = 1"));
        Assert.notNull(icGeneraloutH, () -> new ServiceException("wms未操作出库！"));
        return Boolean.TRUE;
    }


    @Override
    public Object outOperate(MaterialOutDTO dto) {
        TransferOutSignBodyDTO body = new TransferOutSignBodyDTO();
        BeanUtils.copyProperties(dto, body);
        JSONObject response = ncClient.send(ncConfig.getTransferOutSign(), new NcTemplateDTO(body, null));
        if (response != null && response.getInt("code") == HttpStatus.OK.value()) {
            return response;
        }
        throw new ServiceException("NC转库出库签字异常" + response);
    }

    @Override
    public JSONObject receiveOperate(PrepareMaterialReceiveDTO dto) {
        //获取用户nc主键
        String userNcPk = userService.getUserNcPk(dto.getUserCode());
//        调用其他入签字
        TransferInSignBodyDTO body = new TransferInSignBodyDTO();
        body.setApprover(userNcPk);
        body.setCgeneralhid(dto.getPkIcIn());
        JSONObject response = ncClient.send(ncConfig.getTransferInSign(), new NcTemplateDTO(body, null));
        if (response != null && response.getInt("code") == HttpStatus.OK.value()) {
            return response;
        }
        throw new ServiceException("NC其他入签字异常" + response);
    }

    @Override
    public PrepareMaterialResultDTO supplementOtherInAndOut(PrepareMaterialResultDTO operate) {
        IcGeneraloutB icGeneraloutB = icGeneraloutBMapper.selectOne(new LambdaQueryWrapper<IcGeneraloutB>()
            .eq(IcGeneraloutB::getDr, CommonConstants.DELETE_FLAG_NOT_DELETED)
            .eq(IcGeneraloutB::getCsourcebillhid, operate.getPkNcBill())
            .last("AND ROWNUM = 1"));

        IcGeneralinB icGeneralinB = icGeneralinBMapper.selectOne(new LambdaQueryWrapper<IcGeneralinB>()
            .eq(IcGeneralinB::getDr, CommonConstants.DELETE_FLAG_NOT_DELETED)
            .eq(IcGeneralinB::getCsourcebillhid, operate.getPkNcBill())
            .last("AND ROWNUM = 1"));

        String pkOtherOut = Optional.ofNullable(icGeneraloutB).map(IcGeneraloutB::getCgeneralhid).orElse("");
        operate.setPkOtherOut(pkOtherOut);
        String pkOtherIn = Optional.ofNullable(icGeneralinB).map(IcGeneralinB::getCgeneralhid).orElse("");
        operate.setPkOtherIn(pkOtherIn);
        return operate;
    }

    @Override
    public String supplementOtherIn(String pkNcBill) {
        IcGeneralinB icGeneralinB = icGeneralinBMapper.selectOne(new LambdaQueryWrapper<IcGeneralinB>()
            .eq(IcGeneralinB::getDr, CommonConstants.DELETE_FLAG_NOT_DELETED)
            .eq(IcGeneralinB::getCsourcebillhid, pkNcBill)
            .last("AND ROWNUM = 1"));
        return Optional.ofNullable(icGeneralinB)
            .map(IcGeneralinB::getCgeneralhid)
            .orElse("");
    }

    @Override
    public OtherInDTO supplementOtherInInfo(String pkNcBill) {
        OtherInDTO result = new OtherInDTO();

        IcGeneralinB icGeneralinB = icGeneralinBMapper.selectOne(new LambdaQueryWrapper<IcGeneralinB>()
            .eq(IcGeneralinB::getDr, CommonConstants.DELETE_FLAG_NOT_DELETED)
            .eq(IcGeneralinB::getCsourcebillhid, pkNcBill)
            .last("AND ROWNUM = 1"));

        String pkOtherIn = Optional.ofNullable(icGeneralinB)
            .map(IcGeneralinB::getCgeneralhid)
            .orElse("");
        result.setPkIcIn(pkOtherIn);

        if (StrUtil.isNotBlank(pkOtherIn)) {
            IcGeneralinH icGeneralinH = icGeneralinHMapper.selectById(pkOtherIn);
            String vbillcode = Optional.ofNullable(icGeneralinH)
                .map(IcGeneralinH::getVbillcode)
                .orElse("");
            result.setIcCodeIn(vbillcode);
        }

        return result;
    }

    @Override
    public ReceiveVO supplementReceiveByIcIn(String pkIcIn) {
        ReceiveVO result = new ReceiveVO();
        result.setBillType(BillTypeEnum.TRANSFER.name());

        IcGeneralinB oldGeneralinB = icGeneralinBMapper.selectOne(new LambdaQueryWrapper<IcGeneralinB>()
            .eq(IcGeneralinB::getCgeneralhid, pkIcIn)
            .last("AND ROWNUM = 1"));

        IcGeneralinB newIcGeneralinB = icGeneralinBMapper.selectOne(new LambdaQueryWrapper<IcGeneralinB>()
            .eq(IcGeneralinB::getDr, CommonConstants.DELETE_FLAG_NOT_DELETED)
            .eq(IcGeneralinB::getCsourcebillhid, oldGeneralinB.getCsourcebillhid())
            .last("AND ROWNUM = 1"));

        IcGeneralinH newIcGeneralinH = icGeneralinHMapper.selectById(newIcGeneralinB.getCgeneralhid());
        result.setPkIcIn(newIcGeneralinH.getCgeneralhid());
        result.setIcCodeIn(newIcGeneralinH.getVbillcode());

        if (oldGeneralinB != null && oldGeneralinB.getCsourcebillhid() != null) {
            result.setPkNcBill(oldGeneralinB.getCsourcebillhid());
            IcWhstransH icWhstransH = icWhstransHMapper.selectById(oldGeneralinB.getCsourcebillhid());
            String codeNcBill = Optional.ofNullable(icWhstransH)
                .map(IcWhstransH::getVbillcode)
                .orElse("");
            result.setNcBillCode(codeNcBill);
        }
        return result;
    }
}
