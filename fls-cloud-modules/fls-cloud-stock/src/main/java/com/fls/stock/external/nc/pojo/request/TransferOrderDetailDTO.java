package com.fls.stock.external.nc.pojo.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description:
 * @Author: caibenwei
 * @DATE: 2024/12/18 11:03
 */
@Data
public class TransferOrderDetailDTO {

    @Schema(description = "货位，PK值，转出仓库未启用货位管理时可传空值")
    private String clocationid;

    @Schema(description = "物料PK")
    private String cmaterialvid;

    @Schema(description = "应转主数量")
    private BigDecimal nnum;

    @Schema(description = "转入仓库货位，货位PK，转入仓库未启用货位管理时可传空值")
    private String vbdef5;

    @Schema(description = "接口来源对象明细主键，可传空值")
    private String vbdef20;

    @Schema(description = "行备注")
    private String vnotebody;

    private String vbatchcode;
}
