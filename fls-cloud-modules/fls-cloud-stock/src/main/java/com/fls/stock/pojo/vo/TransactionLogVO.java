package com.fls.stock.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @create 2024/12/10 21:13
 **/
@Data
public class TransactionLogVO {

    @Schema(description = "主键")
    private String id;

    @Schema(description = "库存流水主键id")
    private String idTransaction;

    @Schema(description = "库存流水号")
    private String transactionCode;

    @Schema(description = "仓库id")
    private String idWarehouse;

    @Schema(description = "仓库pk")
    private String pkWarehouse;

    @Schema(description = "仓库pk")
    private String idWhpos;

    @Schema(description = "仓库pk")
    private String pkWhpos;

    @Schema(description = "仓库pk")
    private String pkBatchCode;

    @Schema(description = "批次号")
    private String batchCode;

    @Schema(description = "物料名称")
    private String materialName;

    @Schema(description = "物料编码")
    private String materialCode;

    @Schema(description = "物料参数")
    private String materialParam;

    @Schema(description = "数量")
    private BigDecimal count;

    @Schema(description = "组织id")
    private String idOrg;

    @Schema(description = "组织名称")
    private String orgName;

    @Schema(description = "经营主体id")
    private String idBizunit;

    @Schema(description = "主体名称")
    private String bizunitName;

    @Schema(description = "班组id")
    private String idWorkteam;

    @Schema(description = "工作班组名称")
    private String workteamName;

    @Schema(description = "存货名称")
    private String inventoryName;

    @Schema(description = "实际入库、出库时间")
    private Date stockTime;

    @Schema(description = "1-入库,2-出库")
    private Integer type;

    @Schema(description = "业务来源:1:物料领取2：物料归还3：组间调用")
    private Integer sourceType;

    @Schema(description = "来源单据编号")
    private String sourceBillCode;

    @Schema(description = "来源单据id")
    private String IdSourceBill;

    @Schema(description = "初始来源单据编号")
    private String firstSourceBillCode;

    @Schema(description = "初始来源单据id")
    private String IdFirstSourceBill;

}
