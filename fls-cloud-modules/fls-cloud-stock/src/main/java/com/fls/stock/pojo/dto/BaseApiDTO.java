package com.fls.stock.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Description: api接口基础四天
 * @Author: caibenwei
 * @DATE: 2024/12/21 15:22
 */
@Data
public class BaseApiDTO {

    @Schema(description = "来源系统")
    @NotBlank(message = "来源系统不能为空")
    private String srcSysName;

    @Schema(description = "用户编号")
    @NotBlank(message = "用户编号不能为空！")
    private String userCode;
}
