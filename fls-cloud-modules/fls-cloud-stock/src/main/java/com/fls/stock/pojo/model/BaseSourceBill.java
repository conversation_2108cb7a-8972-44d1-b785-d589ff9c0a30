package com.fls.stock.pojo.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class BaseSourceBill {
    /**
     * 初始来源单据编码
     */
    private String firstSourceCode;

    /**
     * 初始来源单据id
     */
    private String idFirstSourceBill;

    /**
     * 初始来源单据资源id
     */
    private String idFirstRes;

    /**
     * 来源单据id
     */
    @NotBlank(message = "来源单据id不能为空")
    private String idSourceBill;

    // 来源单据资源名称
    @JsonIgnore
    private String sourceResName;

    /**
     * 来源单据编码
     */
    @NotBlank(message = "来源单据编码不能为空")
    private String sourceBillCode;

    /**
     * 来源单据资源id
     */
    @NotBlank(message = "来源单据资源id不能为空")
    private String idSourceRes;

    //初始来源单据资源名称
    @JsonIgnore
    private String firstResName;

    @JsonIgnore
    private Integer sourceBillType;

    /**
     * 操作人id
     */
    @NotBlank(message = "操作人id不能为空")
    private String operator;

    /**
     * 库存单号
     */
    @JsonIgnore
    private String stockCode;
}
