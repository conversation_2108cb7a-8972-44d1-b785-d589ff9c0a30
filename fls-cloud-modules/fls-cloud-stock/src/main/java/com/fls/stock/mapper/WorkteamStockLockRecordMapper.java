package com.fls.stock.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fls.stock.entity.WorkteamStockLockRecord;
import com.fls.stock.pojo.query.LockLogQuery;
import com.fls.stock.pojo.vo.LockLogVO;
import com.github.yulichang.base.MPJBaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * 班组库存锁定记录表(WorkteamStockLockRecord)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-12-11 09:08:53
 */
public interface WorkteamStockLockRecordMapper extends MPJBaseMapper<WorkteamStockLockRecord> {

    Page<LockLogVO> getLockLog(@Param("page") Page page, @Param("query") LockLogQuery query);
}

