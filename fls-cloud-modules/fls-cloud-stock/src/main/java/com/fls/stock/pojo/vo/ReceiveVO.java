package com.fls.stock.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/7/5 17:24
 */
@Data
public class ReceiveVO {

    @Schema(description = "单据类型 TRANSFER:转库单，ALLOCATE:调拨单")
    private String billType;

    @Schema(description = "转库单PK或调拨单PK")
    private String pkNcBill;

    @Schema(description = "转库单号或调拨单号")
    private String ncBillCode;

    @Schema(description = "其他入库单PK或调拨入库单PK")
    private String pkIcIn;

    @Schema(description = "其他入库单号或调拨入库单号")
    private String icCodeIn;
}
