package com.fls.stock.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 用户表(BaseUser)表实体类
 *
 * <AUTHOR>
 * @since 2024-12-27 14:44:47
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_base_user")
public class BaseUser implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     **/
    @TableId(value = "id_user", type = IdType.ASSIGN_UUID)
    private String idUser;

    /**
     * 用户名
     **/
    @TableField("code")
    private String code;

    /**
     * 昵称
     **/
    @TableField("name")
    private String name;

    /**
     * 密码
     **/
    @TableField("password")
    private String password;

    /**
     * 身份类型：0=员工，1=客户，2=供应商，3=审计，4=外部系统，5=开发者，6=合作伙伴，默认0，参见identity_type
     **/
    @TableField("identity_type")
    private String identityType;

    /**
     * 身份id
     **/
    @TableField("id_identity")
    private String idIdentity;

    /**
     * NC用户pk值
     **/
    @TableField("pk_user")
    private String pkUser;

    /**
     * NC身份pk值
     **/
    @TableField("pk_base_doc")
    private String pkBaseDoc;

    /**
     * 启用状态：1=未启用，2=已启用，3=已停用，默认1，参见basestatus
     **/
    @TableField("status")
    private String status;

    /**
     * 创建时间
     **/
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 创建人
     **/
    @TableField("creator")
    private String creator;

    /**
     * 失效时间
     **/
    @TableField("disable_time")
    private LocalDateTime disableTime;

    /**
     * 时间戳
     **/
    @TableField("ts")
    private LocalDateTime ts;

    /**
     * 是否删除：0=否，1=是，默认0，参见yesorno
     **/
    @TableField("delete_flag")
    @TableLogic
    private String deleteFlag;

    /**
     * 盐值
     **/
    @TableField("salt")
    private String salt;

    /**
     * 个人虚拟地址，将个人视为存放地址
     **/
    @TableField("id_address")
    private String idAddress;

    /**
     * 检保人员标识，0=否，1=是，默认0
     **/
    @TableField("maintenance_flag")
    private String maintenanceFlag;

    /**
     * 内部收货地址id
     **/
    @TableField("id_inneraddress")
    private String idInneraddress;

    /**
     * 手机端登录时返回的token
     **/
    @TableField("miop_login_token")
    private String miopLoginToken;

    /**
     * 手机端token最新更新时间
     **/
    @TableField("miop_login_token_ts")
    private LocalDateTime miopLoginTokenTs;

    /**
     * 签名附件id
     **/
    @TableField("id_link")
    private String idLink;

    /**
     * 签名附件文件id
     **/
    @TableField("id_autograph")
    private String idAutograph;

    /**
     * 微信openId
     **/
    @TableField("open_id_wx")
    private String openIdWx;

    /**
     * 微信小程序openId
     **/
    @TableField("open_id_wxxcx")
    private String openIdWxxcx;

    /**
     * 外部联系人
     **/
    @TableField("id_linkman_outer")
    private String idLinkmanOuter;

}

