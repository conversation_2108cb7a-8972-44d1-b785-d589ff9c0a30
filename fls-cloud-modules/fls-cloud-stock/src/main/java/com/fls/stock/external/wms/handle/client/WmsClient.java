package com.fls.stock.external.wms.handle.client;

import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.fls.common.core.exception.ServiceException;
import com.fls.stock.config.WmsConfig;
import com.fls.stock.external.wms.pojo.WmsStockQueryDTO;
import com.fls.stock.external.wms.pojo.WmsStockQueryResponseDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @Description:
 * @Author: caibenwei
 * @DATE: 2024/12/23 14:35
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class WmsClient {

    private final WmsConfig wmsConfig;

    public WmsStockQueryResponseDTO sendQuery(WmsStockQueryDTO dto) {
        JSONObject send = send(dto);
        return JSONUtil.toBean(send, WmsStockQueryResponseDTO.class);
    }


    public JSONObject send(Object dto) {
        String path = wmsConfig.getPath();
        String body = JSONUtil.toJsonStr(dto);
        log.info("请求wms接口:{},参数:{}", path, body);
        String result = null;
        try {
            result = HttpUtil.post(path, body);
            log.info("wms返回:{}", result);
        } catch (Exception e) {
            throw new ServiceException("调用wms接口失败: " + e.getMessage());
        }
        return JSONUtil.parseObj(result);
    }


}
