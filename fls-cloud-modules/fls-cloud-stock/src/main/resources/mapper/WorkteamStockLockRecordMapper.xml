<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fls.stock.mapper.WorkteamStockLockRecordMapper">

    <select id="getLockLog" resultType="com.fls.stock.pojo.vo.LockLogVO">
        select
            t.id_lock_record as id,
            t.id_stock as idStock,
            t.id_warehouse as idWarehouse,
            t.pk_warehouse as pkWarehouse,
            t.id_whpos as idWhpos,
            t.pk_whpos as pkWhpos,
            t.pk_batch_code as pkBatchCode,
            t.batch_code as batchCode,
            t.id_material as idMaterial,
            t.material_code as materialCode,
            t.material_param as materialParam,
            t.material_name as materialName,
            t.lock_num as lockNum,
            t.unit as unit,
            t.lock_status as status,
            t.lock_time as lockTime,
            t.unlock_time as unlock_time,
            t.create_time as createTime,
            t.source_bill_code as sourceBillCode,
            t.id_source_bill as IdSourceBill,
            t.id_first_source_bill as firstSourceBillCode,
            t.first_source_bill_code as IdFirstSourceBill
        from t_workteam_stock_lock_record t
        left join t_workteam_stock t1 on t.id_stock = t1.id_stock
        <if test="query.name != null and query.name != ''">
            left join t_base_workteam t2 on t1.id_workteam = t2.id_workteam
            left join t_base_org t3 on t1.id_org = t3.id_org
            left join t_base_bizunit t4 on t1.id_bizunit = t4.id_bizunit
        </if>
        where t.delete_flag = '0' and t1.delete_flag = '0'
        <if test="query.beginDate != null and query.beginDate != ''">
            AND DATE(t.create_time) >= #{query.beginDate}
        </if>
        <if test="query.endDate != null and query.endDate != ''">
            AND DATE(t.create_time) &lt;= #{query.endDate}
        </if>
        <if test="query.billCode != null and query.billCode != ''">
            AND t.source_bill_code like concat('%',#{query.billCode},'%')
        </if>
        <if test="query.materialName != null and query.materialName != ''">
            AND t.material_name like concat('%',#{query.materialName},'%')
        </if>
        <if test="query.name != null and query.name != ''">
            AND (t2.name like concat('%',#{query.name},'%') or t3.name like concat('%',#{query.name},'%') or t4.name like concat('%',#{query.name},'%') )
        </if>
    </select>
</mapper>

