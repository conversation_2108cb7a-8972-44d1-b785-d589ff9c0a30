<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fls.stock.mapper.NcMappper">

    <select id="acceptTaxRate" resultType="java.math.BigDecimal">
        SELECT taxrate
        FROM (
                 SELECT b.taxrate
                 FROM bd_taxcode a
                 JOIN bd_taxrate b ON a.pk_taxcode = b.pk_taxcode
                 WHERE a.dr = 0 AND b.dr = 0 AND a.code = 'CN01'
                 ORDER BY b.begindate DESC
             )
        WHERE ROWNUM = 1
    </select>



    <select id="acceptNorigtaxnetprice" resultType="java.util.Map">
        select trim(PK_MATERIAL) as PK_MATERIAL ,trim(VDEF26) as VDEF26
        from BD_MATERIAL
        where dr = '0'
        <if test="pkMaterial != null and pkMaterial.size > 0">
            and pk_material in
            <foreach collection="pkMaterial" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </select>


</mapper>
