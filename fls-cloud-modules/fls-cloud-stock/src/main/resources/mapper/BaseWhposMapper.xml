<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fls.stock.mapper.BaseWhposMapper">

    <select id="getWarehouseByWhposIds" resultType="java.lang.String">
        SELECT distinct id_warehouse
        from t_base_whpos
        where delete_flag = '0' and status = '2'
        <if test="whposIds != null and whposIds.size() > 0">
            and id_whpos IN
            <foreach item="item" collection="whposIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>
</mapper>
