<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fls.task.mapper.ApiTaskInfoMapper">

    <resultMap id="ApiTaskInfoVoMap" type="com.fls.task.pojo.vo.ApiTaskInfoVo">
        <id column="id_api_task" property="idApiTask"/>
        <result column="id_api_def" property="idApiDef"/>
        <result column="api_name" property="apiName"/>
        <result column="task_status" property="taskStatus"/>
        <result column="notify_status" property="notifyStatus"/>
        <result column="req_url" property="reqUrl"/>
        <result column="req_method" property="reqMethod"/>
        <result column="req_body" property="reqBody"/>
        <result column="res_code" property="resCode"/>
        <result column="res_text" property="resText"/>
        <result column="task_cost" property="taskCost"/>
        <result column="source_project_code" property="sourceProjectCode"/>
        <result column="id_source_bill" property="idSourceBill"/>
        <result column="source_bill_code" property="sourceBillCode"/>
        <result column="id_source_res" property="idSourceRes"/>
        <result column="source_bill_name" property="sourceBillName"/>
        <result column="create_time" property="createTime"/>
        <result column="creator" property="creator"/>
        <result column="create_name" property="createName"/>
        <result column="asset_code" property="assetCode"/>
    </resultMap>

    <select id="selectApiTaskPage" resultMap="ApiTaskInfoVoMap">
        SELECT
        t.id_api_task,
        t.id_api_def,
        d.api_name,
        t.task_status,
        t.notify_status,
        t.req_url,
        t.req_method,
        t.req_body,
        t.res_code,
        t.res_text,
        t.res_msg,
        t.task_cost,
        t.asset_code,
        t.source_project_code,
        t.id_source_bill,
        t.source_bill_code,
        t.id_source_res,
        t.source_bill_name,
        t.create_time,
        t.creator,
        t.create_name as create_name
        FROM t_api_task_info t
        LEFT JOIN t_api_def_info d ON t.id_api_def = d.id_api_def
        WHERE t.delete_flag = '0'
        <if test="query.apiName != null and query.apiName != ''">
            AND d.api_name LIKE CONCAT('%', #{query.apiName}, '%')
        </if>
        <if test="query.taskStatus != null and query.taskStatus != ''">
            AND t.task_status = #{query.taskStatus}
        </if>
        <if test="query.idSource != null and query.idSource != ''">
            AND t.id_source_bill = #{query.idSource}
        </if>
        <if test="query.sourceCode != null and query.sourceCode != ''">
            AND t.source_bill_code = #{query.sourceCode}
        </if>
        <if test="query.assetCode != null and query.assetCode != ''">
            AND t.asset_code = #{query.assetCode}
        </if>
        <if test="query.beginDate != null and query.beginDate != ''">
            AND t.create_time >= #{query.beginDate}
        </if>
        <if test="query.endDate != null and query.endDate != ''">
            AND t.create_time <![CDATA[<=]]> #{query.endDate}
        </if>
        <if test="query.idSourceRes != null and query.idSourceRes != ''">
            AND t.id_source_res = #{query.idSourceRes}
        </if>
        ORDER BY t.create_time DESC
    </select>

</mapper>
