<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fls.task.mapper.TaskTriggerLogMapper">

    <select id="scheduleTaskQuery" resultType="com.fls.task.entity.TaskScheduleInfo">
        SELECT tti.id_task_schedule,
               tti.id_api_task,
               tti.schedule_type,
               tti.schedule_conf,
               tti.schedule_status,
               tti.executor_timeout,
               tti.trigger_last_time,
               tti.trigger_next_time,
               tti.remark,
               tti.create_time,
               tti.creator,
               tti.update_time,
               tti.updater,
               tti.delete_flag,
               tti.ts
        FROM t_task_schedule_info AS tti
        WHERE tti.schedule_status = 1
          and tti.trigger_next_time <![CDATA[ <= ]]> FROM_UNIXTIME(#{time} / 1000)
        ORDER BY id_task_schedule ASC
        LIMIT #{count}
    </select>
</mapper>

