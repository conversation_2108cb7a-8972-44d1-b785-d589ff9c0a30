package com.fls.task.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fls.task.entity.TaskScheduleInfo;
import com.fls.task.entity.TaskTriggerLog;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 任务调度日志表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
public interface TaskTriggerLogMapper extends BaseMapper<TaskTriggerLog> {

    /**
     * 调度任务查询
     *
     * @param time  时间戳
     * @param count 查询数量
     * @return 调度任务信息列表
     */
    List<TaskScheduleInfo> scheduleTaskQuery(@Param("time") long time, @Param("count") int count);
}
