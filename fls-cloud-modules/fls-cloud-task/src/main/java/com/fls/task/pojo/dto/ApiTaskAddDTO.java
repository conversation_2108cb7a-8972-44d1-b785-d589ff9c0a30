package com.fls.task.pojo.dto;

import cn.hutool.json.JSONObject;
import lombok.Data;

import java.io.Serializable;

/**
 * 接口任务添加
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
@Data
public class ApiTaskAddDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 接口编码
     */
    private String apiCode;

    /**
     * 来源单据id
     */
    private String idSource;

    /**
     * 来源单据号
     */
    private String codeSource;

    /**
     * 来源单据资源id
     */
    private String idSourceRes;

    /**
     * 请求参数
     */
    private JSONObject requestData;

    /**
     * 资产编号
     */
    private String assetCode;

    /**
     * 处理器名称
     */
    private String taskHandler;

    /**
     * 项目编码
     */
    private String projectCode;

    /**
     * 项目地址
     */
    private String projectCodeUrl;
}
