package com.fls.task.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 接口定义表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_api_def_info")
public class ApiDefInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id_api_def", type = IdType.ASSIGN_UUID)
    private String idApiDef;

    /**
     * 接口类型（服务总线|原生请求|链式任务）
     */
    private String apiType;

    /**
     * 接口编码（用于识别不同的操作）
     */
    private String apiCode;

    /**
     * 接口名称
     */
    private String apiName;

    /**
     * 目标地址
     */
    private String host;

    /**
     * 接口路径
     */
    private String apiPath;

    /**
     * 请求方法
     */
    private String apiMethod;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    private String updater;

    /**
     * 删除标识
     */
    private String deleteFlag;

    /**
     * 时间戳
     */
    private LocalDateTime ts;
}
