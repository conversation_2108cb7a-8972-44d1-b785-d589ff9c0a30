package com.fls.task.pojo.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 任务调度信息请求
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
@Data
public class TaskSchedInfoDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 调度任务id
     */
    private String idSched;

    /**
     * 接口任务id
     */
    @NotBlank(message = "接口任务id不能为空")
    private String idApiTask;

    /**
     * 调度类型
     */
    @NotBlank(message = "调度类型不能为空")
    private String scheduleType;

    /**
     * 调度配置，值含义取决于调度类型
     */
    private String scheduleConf;

    /**
     * 调度过期策略
     */
    @NotBlank(message = "调度过期策略不能为空")
    private String misfireStrategy;

    /**
     * 任务执行超时时间，单位秒
     */
    private Integer executorTimeout;
}
