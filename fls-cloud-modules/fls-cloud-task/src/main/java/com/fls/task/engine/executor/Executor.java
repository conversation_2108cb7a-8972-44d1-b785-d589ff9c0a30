package com.fls.task.engine.executor;

import com.fls.common.core.domain.ResponseData;
import com.fls.task.engine.model.TriggerParam;

public interface Executor {

    /**
     * 心跳检测方法
     *
     * @return 通用响应
     */
    default ResponseData<Void> beat() {
        return ResponseData.ok();
    }

    /**
     * 执行触发器
     *
     * @param triggerParam 触发参数
     * @return 通用响应
     */
   ResponseData<Void> run(TriggerParam triggerParam);

    /**
     * 关闭执行器任务
     *
     * @param killParam 触发参数
     * @return 通用响应
     */
    ResponseData<Void> kill(TriggerParam killParam);

}