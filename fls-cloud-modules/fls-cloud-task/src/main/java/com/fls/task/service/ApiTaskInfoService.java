package com.fls.task.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fls.common.core.domain.ResponseData;
import com.fls.common.mybatis.core.page.PageResult;
import com.fls.task.entity.ApiTaskInfo;
import com.fls.task.pojo.dto.ApiTaskAddDTO;
import com.fls.task.pojo.dto.ApiTaskQueryDTO;
import com.fls.task.pojo.vo.ApiTaskInfoVo;

/**
 * <p>
 * 接口任务请求信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
public interface ApiTaskInfoService extends IService<ApiTaskInfo> {

    /**
     * 分页查询接口任务信息
     *
     * @param query 查询条件
     * @return 分页结果
     */
    PageResult<ApiTaskInfoVo> selectApiTaskPage(ApiTaskQueryDTO query);

    /**
     * 添加接口任务
     *
     * @param apiTaskAddDTO 接口添加信息
     * @return 操作结果
     */
    boolean addApiTask(ApiTaskAddDTO apiTaskAddDTO);

    /**
     * 任务重试接口
     *
     * @param idApiTask 接口任务id
     * @return 操作结果
     */
    ResponseData<Void> retry(String idApiTask);
}
