package com.fls.task.pojo.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * ApiTaskInfoVo
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
@Data
public class ApiTaskInfoVo {

    /**
     * 接口任务主键
     */
    private String idApiTask;

    /**
     * 所属接口定义id
     */
    private String idApiDef;

    /**
     * 接口名称
     */
    private String apiName;

    /**
     * 任务状态（待调度|进行中|执行成功|执行失败）
     */
    private String taskStatus;

    /**
     * 任务回调处理状态（成功|失败）
     */
    private String notifyStatus;

    /**
     * 资产编号
     */
    private String assetCode;

    /**
     * 请求url
     */
    private String reqUrl;

    /**
     * 请求方法
     */
    private String reqMethod;

    /**
     * 请求内容
     */
    private String reqBody;

    /**
     * 响应编码
     */
    private String resCode;

    /**
     * 响应内容
     */
    private String resText;

    /**
     * 响应描述
     */
    private String resMsg;

    /**
     * 任务耗时（单位：秒）
     */
    private Integer taskCost;

    /**
     * 来源项目编码
     */
    private String sourceProjectCode;

    /**
     * 来源单据主键
     */
    private String idSourceBill;

    /**
     * 来源单据编号
     */
    private String sourceBillCode;

    /**
     * 来源单据资源主键
     */
    private String idSourceRes;

    /**
     * 来源单据名称
     */
    private String sourceBillName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建人名称
     */
    private String createName;
}
