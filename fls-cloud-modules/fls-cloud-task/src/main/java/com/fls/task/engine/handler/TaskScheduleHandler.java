package com.fls.task.engine.handler;

import com.fls.task.config.FlsTaskConfig;
import com.fls.task.engine.holder.TimeRingManager;
import com.fls.task.engine.thread.ScheduleThread;
import com.fls.task.engine.thread.TimeRingThread;
import com.fls.task.service.TaskScheduleInfoService;
import java.util.concurrent.TimeUnit;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

/**
 * 任务触发监听处理
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TaskScheduleHandler {

    private final TaskScheduleInfoService taskScheduleInfoService;

    private final FlsTaskConfig flsTaskConfig;

    private final RedissonClient redissonClient;

    private final TaskTriggerHandler taskTriggerHandler;

    private final TimeRingManager timeRingManager;

    /**
     * 调度线程
     */
    private ScheduleThread scheduleThread;

    /**
     * 时间轮线程
     */
    private TimeRingThread ringThread;

    public void start() {
        // 计划任务线程
        scheduleThread = new ScheduleThread(taskScheduleInfoService, flsTaskConfig, redissonClient, taskTriggerHandler, timeRingManager);
        scheduleThread.setDaemon(true);
        scheduleThread.setName("fls-task,TaskScheduleHandler#scheduleThread");
        scheduleThread.start();

        // 时间轮线程
        ringThread = new TimeRingThread(timeRingManager, taskTriggerHandler);
        ringThread.setDaemon(true);
        ringThread.setName("fls-task, TaskScheduleHelper#ringThread");
        ringThread.start();
    }

    public void stop() {
        // 先停止调度线程
        stopScheduleThread();

        // 清除时间轮数据
        cleanRingData();

        // 最后终止时间轮线程
        stopRingThread();
        log.info(">>>>>>>>>>> fls-task, TaskScheduleHandler stop");
    }

    private void stopScheduleThread() {
        if (scheduleThread != null) {
            try {
                TimeUnit.SECONDS.sleep(1);  // wait
            }
            catch (InterruptedException e) {
                log.error(e.getMessage(), e);
            }
            scheduleThread.cleanup();
            if (scheduleThread.getState() != Thread.State.TERMINATED) {
                // interrupt and wait
                scheduleThread.interrupt();
                try {
                    scheduleThread.join();
                }
                catch (InterruptedException e) {
                    log.error(e.getMessage(), e);
                }
            }
        }
    }

    private void stopRingThread() {
        if (ringThread != null) {
            ringThread.stopThread();
            try {
                TimeUnit.SECONDS.sleep(1);
            }
            catch (InterruptedException e) {
                log.error(e.getMessage(), e);
            }
            if (ringThread.getState() != Thread.State.TERMINATED) {
                // interrupt and wait
                ringThread.interrupt();
                try {
                    ringThread.join();
                }
                catch (InterruptedException e) {
                    log.error(e.getMessage(), e);
                }
            }
        }
    }

    private void cleanRingData() {
        if (timeRingManager.hasRingData()) {
            try {
                TimeUnit.SECONDS.sleep(8);
            }
            catch (InterruptedException e) {
                log.error(e.getMessage(), e);
            }
        }
    }
}