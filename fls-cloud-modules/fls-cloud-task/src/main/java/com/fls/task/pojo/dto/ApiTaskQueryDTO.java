package com.fls.task.pojo.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 接口任务查询
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
@Data
public class ApiTaskQueryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 接口名称
     */
    private String apiName;

    /**
     * 任务状态
     */
    private String taskStatus;

    /**
     * 来源单据id
     */
    private String idSource;

    /**
     * 来源单据编号
     */
    private String sourceCode;

    /**
     * 来源单据资源id
     */
    private String idSourceRes;

    /**
     * 资产编号
     */
    private String assetCode;

    /**
     * 开始时间
     */
    private String beginDate;

    /**
     * 结束时间
     */
    private String endDate;

    /**
     * 页码
     */
    private Long pageNum = 1L;

    /**
     * 每页大小
     */
    private Long pageSize = 10L;
}
