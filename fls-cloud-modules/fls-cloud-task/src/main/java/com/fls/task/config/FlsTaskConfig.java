package com.fls.task.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * 任务配置
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Data
@RefreshScope
@Configuration
@ConfigurationProperties(prefix = "fls.task")
public class FlsTaskConfig {

    /**
     * 触发工作池（快）最大线程池数量
     */
    private int fastTriggerPoolMax;

    /**
     * 触发工作池（慢）最大线程池数量
     */
    private int slowTriggerPoolMax;

    /**
     * 最大操作日志保留时间
     */
    private int logRetentionDays;

    /**
     * 内部交互的请求Token
     */
    private String accessToken;
}
