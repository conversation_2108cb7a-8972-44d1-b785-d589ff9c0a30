package com.fls.task.enums;

import lombok.Getter;

/**
 * 调度过期策略
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@Getter
public enum MisfireStrategyEnum {
    /**
     * do nothing
     */
    DO_NOTHING("Do nothing"),

    /**
     * fire once now
     */
    FIRE_ONCE_NOW("Fire once now");

    private String title;

    MisfireStrategyEnum(String title) {
        this.title = title;
    }

    public String getTitle() {
        return title;
    }

    public static MisfireStrategyEnum match(String name, MisfireStrategyEnum defaultItem){
        for (MisfireStrategyEnum item: MisfireStrategyEnum.values()) {
            if (item.name().equals(name)) {
                return item;
            }
        }
        return defaultItem;
    }

}
