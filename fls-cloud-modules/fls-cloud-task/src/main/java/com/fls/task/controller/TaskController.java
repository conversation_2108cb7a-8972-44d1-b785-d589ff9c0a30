package com.fls.task.controller;

import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.StrUtil;
import com.fls.common.core.domain.ResponseData;
import com.fls.common.core.exception.ServiceException;
import com.fls.task.constant.CommonConstant;
import com.fls.task.constant.ErrorMsgConstant;
import com.fls.task.engine.handler.TaskTriggerHandler;
import com.fls.task.entity.TaskScheduleInfo;
import com.fls.task.enums.TriggerTypeEnum;
import com.fls.task.pojo.dto.TaskSchedInfoDTO;
import com.fls.task.service.TaskScheduleInfoService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 通用任务管理入口，接口任务的操作入口与通用操作拆分开，后续可以在此基础上扩展出其他类型任务
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
@RestController
@RequestMapping("/task")
@RequiredArgsConstructor
public class TaskController {

    private final TaskScheduleInfoService taskSchedService;

    private final TaskTriggerHandler taskTriggerHandler;

    @PostMapping("/add")
    public ResponseData<Void> add(@Valid @RequestBody TaskSchedInfoDTO info) {
        return taskSchedService.add(info) ? ResponseData.ok() : ResponseData.fail();
    }

    @PostMapping("/edit")
    public ResponseData<Void> edit(@Valid @RequestBody TaskSchedInfoDTO info) {
        return taskSchedService.edit(info) ? ResponseData.ok() : ResponseData.fail();
    }

    @PostMapping("/delete")
    public ResponseData<Void> delete(@RequestBody Dict dict) {
        String idSched = dict.getStr("idSched");
        if (StrUtil.isBlank(idSched)) {
            throw new IllegalArgumentException("调度任务id不能为空");
        }
        return taskSchedService.delete(idSched) ? ResponseData.ok() : ResponseData.fail();
    }

    @PostMapping("/stop")
    public ResponseData<Void> pause(@RequestBody Dict dict) {
        String idSched = dict.getStr("idSched");
        if (StrUtil.isBlank(idSched)) {
            throw new IllegalArgumentException("调度任务id不能为空");
        }
        return taskSchedService.stop(idSched) ? ResponseData.ok() : ResponseData.fail();
    }

    @PostMapping("/start")
    public ResponseData<Void> start(@RequestBody Dict dict) {
        String idSched = dict.getStr("idSched");
        if (StrUtil.isBlank(idSched)) {
            throw new IllegalArgumentException("调度任务id不能为空");
        }
        return taskSchedService.start(idSched) ? ResponseData.ok() : ResponseData.fail();
    }

    @PostMapping("/trigger")
    public ResponseData<Void> triggerJob(@RequestBody Dict dict) {
        String idSched = dict.getStr("idSched");
        if (StrUtil.isBlank(idSched)) {
            throw new IllegalArgumentException("调度任务id不能为空");
        }
        TaskScheduleInfo scheduleInfo = taskSchedService.getById(idSched);
        if (scheduleInfo == null) {
            throw new ServiceException(ErrorMsgConstant.SCHED_TASK_NOT_EXIST);
        }
        taskTriggerHandler.trigger(idSched, TriggerTypeEnum.MANUAL, CommonConstant.TRIGGER_RETRY_TIMES);
        return ResponseData.ok();
    }
}
