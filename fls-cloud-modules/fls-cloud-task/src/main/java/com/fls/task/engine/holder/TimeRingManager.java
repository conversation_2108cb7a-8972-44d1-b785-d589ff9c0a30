package com.fls.task.engine.holder;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 时间轮管理器
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Slf4j
@Component
public class TimeRingManager {
    private final Map<Integer, List<String>> ringData = new ConcurrentHashMap<>();
    
    public void pushTimeRing(int ringSecond, String scheduleId) {
        List<String> ringItemData = ringData.computeIfAbsent(ringSecond, k -> new ArrayList<>());
        ringItemData.add(scheduleId);
        log.debug(">>>>>>>>>>> fls-task, schedule push time-ring : {} = {}", ringSecond, Collections.singletonList(ringItemData));
    }
    
    public List<String> fetchSchedules(int second) {
        List<String> result = new ArrayList<>();
        List<String> tmpData = ringData.remove(second);
        if (tmpData != null) {
            result.addAll(tmpData);
        }
        return result;
    }
    
    public boolean hasRingData() {
        if (!ringData.isEmpty()) {
            for (List<String> tmpData : ringData.values()) {
                if (tmpData != null && !tmpData.isEmpty()) {
                    return true;
                }
            }
        }
        return false;
    }
}