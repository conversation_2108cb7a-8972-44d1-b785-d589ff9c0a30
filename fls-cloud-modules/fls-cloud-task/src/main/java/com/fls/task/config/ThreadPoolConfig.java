package com.fls.task.config;

import java.util.concurrent.Executor;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * 触发器核心线程池配置
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Slf4j
@EnableAsync
@Configuration
public class ThreadPoolConfig {
    private final FlsTaskConfig flsTaskConfig;

    @Autowired
    public ThreadPoolConfig(FlsTaskConfig flsTaskConfig) {
        this.flsTaskConfig = flsTaskConfig;
    }

    @Bean("fastTriggerPool")
    public Executor fastTriggerPool() {
        return new ThreadPoolExecutor(
                10,
                flsTaskConfig.getFastTriggerPoolMax(),
                60L,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(1000),
                r -> new Thread(r, "fls-task,TaskTriggerHandler-fastTriggerPool-" + r.hashCode()));
    }

    @Bean("slowTriggerPool")
    public Executor slowTriggerPool() {
        return new ThreadPoolExecutor(
                10,
                flsTaskConfig.getSlowTriggerPoolMax(),
                60L,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(2000),
                r -> new Thread(r, "fls-task,TaskTriggerHandler-slowTriggerPool-" + r.hashCode()));
    }
}
