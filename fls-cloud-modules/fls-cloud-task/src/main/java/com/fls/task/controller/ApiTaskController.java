package com.fls.task.controller;

import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.StrUtil;
import com.fls.common.core.domain.ResponseData;
import com.fls.common.mybatis.core.page.PageResult;
import com.fls.task.pojo.dto.ApiTaskAddDTO;
import com.fls.task.pojo.dto.ApiTaskQueryDTO;
import com.fls.task.pojo.vo.ApiTaskInfoVo;
import com.fls.task.service.ApiTaskInfoService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 接口任务管理
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
@RestController
@RequestMapping("/task/api")
@RequiredArgsConstructor
public class ApiTaskController {

    private final ApiTaskInfoService apiTaskInfoService;

    @PostMapping("/page")
    public ResponseData<PageResult<ApiTaskInfoVo>> page(@Valid @RequestBody ApiTaskQueryDTO query) {
        PageResult<ApiTaskInfoVo> result = apiTaskInfoService.selectApiTaskPage(query);
        return ResponseData.ok(result);
    }

    @PostMapping("/add")
    public ResponseData<Void> add(@Valid @RequestBody ApiTaskAddDTO addInfo) {
        return apiTaskInfoService.addApiTask(addInfo) ? ResponseData.ok() : ResponseData.fail();
    }

    @PostMapping("/retry")
    public ResponseData<Void> retry(@Valid @RequestBody Dict dict) {
        String idApiTask = dict.getStr("idApiTask");
        if (StrUtil.isBlank(idApiTask)) {
            throw new IllegalArgumentException("接口任务id不能为空");
        }
        return apiTaskInfoService.retry(idApiTask);
    }
}
