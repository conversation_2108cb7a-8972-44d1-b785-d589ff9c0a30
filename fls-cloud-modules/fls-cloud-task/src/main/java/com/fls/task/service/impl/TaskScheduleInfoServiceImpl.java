package com.fls.task.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fls.common.core.constant.CommonConstants;
import com.fls.common.core.exception.ServiceException;
import com.fls.common.satoken.utils.LoginHelper;
import com.fls.task.constant.ErrorMsgConstant;
import com.fls.task.cron.CronExpression;
import com.fls.task.engine.thread.ScheduleThread;
import com.fls.task.entity.TaskScheduleInfo;
import com.fls.task.enums.ScheduleTriggerStatusEnum;
import com.fls.task.enums.ScheduleTypeEnum;
import com.fls.task.mapper.TaskScheduleInfoMapper;
import com.fls.task.mapper.TaskTriggerLogMapper;
import com.fls.task.pojo.dto.TaskSchedInfoDTO;
import com.fls.task.service.TaskScheduleInfoService;
import com.fls.upms.api.model.LoginUser;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <p>
 * 任务调度信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TaskScheduleInfoServiceImpl extends ServiceImpl<TaskScheduleInfoMapper, TaskScheduleInfo> implements TaskScheduleInfoService {

    private final TaskTriggerLogMapper taskTriggerLogMapper;

    @Override
    public boolean add(TaskSchedInfoDTO info) {
        checkParam(info);
        TaskScheduleInfo schedInfo = new TaskScheduleInfo();
        BeanUtils.copyProperties(info, schedInfo);
        // 接入人员
        LoginUser loginUser = (LoginUser) (StpUtil.getTokenSession().get(LoginHelper.LOGIN_USER_KEY));
        schedInfo.setCreator(loginUser != null ? loginUser.getIdUser() : "");
        return this.save(schedInfo);
    }

    @Override
    public boolean edit(TaskSchedInfoDTO info) {
        checkParam(info);
        // 接入人员
        LoginUser loginUser = (LoginUser) (StpUtil.getTokenSession().get(LoginHelper.LOGIN_USER_KEY));
        TaskScheduleInfo existInfo = Optional.ofNullable(this.getById(info.getIdSched())).orElseThrow(() -> new ServiceException(ErrorMsgConstant.SCHED_TASK_NOT_EXIST));
        // next trigger time (5s后生效，避开预读周期)
        LocalDateTime nextTriggerTime = existInfo.getTriggerNextTime();
        TaskScheduleInfo handleJob = new TaskScheduleInfo();
        handleJob.setScheduleType(info.getScheduleType());
        handleJob.setScheduleConf(info.getScheduleConf());
        boolean scheduleDataNotChanged = info.getScheduleType().equals(existInfo.getScheduleType()) && info.getScheduleConf().equals(existInfo.getScheduleConf());
        if (Objects.equals(existInfo.getScheduleStatus(), ScheduleTriggerStatusEnum.ENABLE.getCode()) && !scheduleDataNotChanged) {
            try {
                Date nextValidTime = generateNextValidTime(handleJob, new Date(System.currentTimeMillis() + ScheduleThread.PRE_READ_MS));
                if (nextValidTime == null) {
                    throw new ServiceException(ErrorMsgConstant.SCHED_TASK_NOT_EXIST);
                }

                nextTriggerTime = nextValidTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                throw new ServiceException(ErrorMsgConstant.SCHED_TASK_NOT_EXIST);
            }
        }
        handleJob.setIdTaskSchedule(info.getIdSched());
        handleJob.setTriggerNextTime(nextTriggerTime);
        handleJob.setCreator(loginUser != null ? loginUser.getIdUser() : "");
        return this.updateById(handleJob);
    }

    @Override
    public boolean delete(String idSched) {
        LoginUser loginUser = (LoginUser) (StpUtil.getTokenSession().get(LoginHelper.LOGIN_USER_KEY));
        TaskScheduleInfo info = this.getById(idSched);
        if (info == null) {
            return false;
        }
        boolean updated = this.update(Wrappers.<TaskScheduleInfo>lambdaUpdate()
            .set(TaskScheduleInfo::getUpdater, loginUser != null ? loginUser.getIdUser() : "")
            .set(TaskScheduleInfo::getUpdateTime, new Date())
            .set(TaskScheduleInfo::getDeleteFlag, CommonConstants.DELETE_FLAG_IS_DELETED)
            .eq(TaskScheduleInfo::getIdTaskSchedule, idSched));
        return updated;
    }

    @Override
    public boolean stop(String idSched) {
        TaskScheduleInfo info = this.getById(idSched);
        info.setScheduleStatus(ScheduleTriggerStatusEnum.DISABLE.getCode());
        info.setTriggerLastTime(null);
        info.setTriggerNextTime(null);
        return this.updateById(info);
    }

    @Override
    public boolean start(String idSched) {
        TaskScheduleInfo info = this.getById(idSched);

        //NONE类型不适用
        ScheduleTypeEnum scheduleTypeEnum = ScheduleTypeEnum.match(info.getScheduleType(), ScheduleTypeEnum.NONE);
        if (ScheduleTypeEnum.NONE == scheduleTypeEnum) {
            throw new ServiceException("");
        }

        LocalDateTime nextTriggerTime;
        try {
            Date nextValidTime = generateNextValidTime(info, new Date(System.currentTimeMillis() + ScheduleThread.PRE_READ_MS));
            if (nextValidTime == null) {
                throw new ServiceException("");
            }
            nextTriggerTime = nextValidTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServiceException("");
        }
        //启用
        info.setScheduleStatus(ScheduleTriggerStatusEnum.ENABLE.getCode());
        info.setTriggerLastTime(null);
        info.setTriggerNextTime(nextTriggerTime);

        return this.updateById(info);
    }

    public void checkParam(TaskSchedInfoDTO info) {
        //获取调度类型
        ScheduleTypeEnum scheduleTypeEnum = Optional.ofNullable(ScheduleTypeEnum.match(info.getScheduleType(), null))
            .orElseThrow(() -> new ServiceException(ErrorMsgConstant.SCHED_TYPE_NOT_MATCH));

        //判断cron和速率模式的调度配置是否合法
        String conf = info.getScheduleConf();

        if (scheduleTypeEnum == ScheduleTypeEnum.CRON) {
            if (conf == null || !CronExpression.isValidExpression(conf)) {
                throw new ServiceException(ErrorMsgConstant.SCHED_CONF_ERROR);
            }
        } else if (scheduleTypeEnum == ScheduleTypeEnum.FIX_RATE) {
            try {
                if (conf == null || Integer.parseInt(conf) < 1) {
                    throw new ServiceException(ErrorMsgConstant.SCHED_CONF_ERROR);
                }
            } catch (NumberFormatException e) {
                throw new ServiceException(ErrorMsgConstant.SCHED_CONF_ERROR);
            }
        }
    }

    @Override
    public List<TaskScheduleInfo> preReadTask(long time, int count) {
        List<TaskScheduleInfo> taskScheduleInfos = taskTriggerLogMapper.scheduleTaskQuery(time, count);
        return taskScheduleInfos == null ? Collections.emptyList() : taskScheduleInfos;
    }

    @Override
    public void refreshNextValidTime(TaskScheduleInfo taskScheduleInfo, Date fromTime) throws Exception {
        Date nextValidTime = generateNextValidTime(taskScheduleInfo, fromTime);
        if (nextValidTime != null) {
            LocalDateTime nextTriggerTime = nextValidTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
            taskScheduleInfo.setTriggerLastTime(taskScheduleInfo.getTriggerNextTime());
            taskScheduleInfo.setTriggerNextTime(nextTriggerTime);
        } else {
            taskScheduleInfo.setScheduleStatus("0");
            taskScheduleInfo.setTriggerLastTime(null);
            taskScheduleInfo.setTriggerNextTime(null);
            log.warn(">>>>>>>>>>> fls-task, refreshNextValidTime fail for task: jobId={}, scheduleType={}, scheduleConf={}", taskScheduleInfo.getIdTaskSchedule(),
                taskScheduleInfo.getScheduleType(), taskScheduleInfo.getScheduleConf());
        }
    }

    public Date generateNextValidTime(TaskScheduleInfo taskScheduleInfo, Date fromTime) throws Exception {
        ScheduleTypeEnum scheduleTypeEnum = ScheduleTypeEnum.match(taskScheduleInfo.getScheduleType(), null);
        if (ScheduleTypeEnum.CRON == scheduleTypeEnum) {
            return new CronExpression(taskScheduleInfo.getScheduleConf()).getNextValidTimeAfter(fromTime);
        } else if (ScheduleTypeEnum.FIX_RATE == scheduleTypeEnum) {
            //新增逻辑，如果是一次性任务，则推迟至10s后触发
            return new Date(fromTime.getTime() + Integer.parseInt(taskScheduleInfo.getScheduleConf()) * 1000L);
        }
        return null;
    }
}
