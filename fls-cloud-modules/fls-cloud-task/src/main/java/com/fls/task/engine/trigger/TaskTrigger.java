package com.fls.task.engine.trigger;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.fls.common.core.domain.ResponseData;
import com.fls.task.constant.CommonConstant;
import com.fls.task.constant.ErrorMsgConstant;
import com.fls.task.engine.model.TriggerParam;
import com.fls.task.entity.ApiTaskInfo;
import com.fls.task.entity.TaskScheduleInfo;
import com.fls.task.entity.TaskTriggerLog;
import com.fls.task.enums.ExecutorBlockStrategyEnum;
import com.fls.task.enums.TriggerTypeEnum;
import com.fls.task.manager.ExecutorManager;
import com.fls.task.service.ApiTaskInfoService;
import com.fls.task.service.TaskLogService;
import com.fls.task.service.TaskScheduleInfoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * 任务触发器实现类
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TaskTrigger {

    private final TaskScheduleInfoService taskScheduleInfoService;

    private final TaskLogService taskLogService;

    private final ApiTaskInfoService apiTaskInfoService;

    private final ExecutorManager executorManager;

    /**
     * 任务触发调度
     *
     * @param taskId         任务id
     * @param triggerType    触发类型
     * @param failRetryCount 重试次数
     */
    public void trigger(String taskId, TriggerTypeEnum triggerType, int failRetryCount) {
        //获取任务调度信息
        TaskScheduleInfo scheduleInfo = taskScheduleInfoService.getById(taskId);
        if (scheduleInfo == null) {
            log.warn(">>>>>>>>>>>> trigger fail, taskId invalid，taskId={}", taskId);
            return;
        }

        processTrigger(scheduleInfo, triggerType);
    }

    private void processTrigger(TaskScheduleInfo scheduleInfo, TriggerTypeEnum triggerType) {
        //调度逻辑先使用Springboot默认的Web入口进行交互，增加AccessToken校验
        String idApiTask = scheduleInfo.getIdApiTask();
        ApiTaskInfo apiTaskInfo = apiTaskInfoService.getById(idApiTask);
        // 记录调度日志
        TaskTriggerLog taskTriggerLog = new TaskTriggerLog();
        taskTriggerLog.setIdTaskSchedule(scheduleInfo.getIdTaskSchedule());
        taskTriggerLog.setTriggerTime(LocalDateTime.now());
        taskTriggerLog.setScheduleType(triggerType.name());
        taskTriggerLog.setIdApiTask(idApiTask);
        taskTriggerLog.setCreator(CommonConstant.SYSTEM_CREATE_NAME);
        taskLogService.save(taskTriggerLog);
        log.debug(">>>>>>>>>>> fls-task trigger start, schedule task log id:{}", taskTriggerLog.getIdTriggerLog());

        ResponseData result;
        // 触发逻辑：如果任务信息完整，则通过执行器调度执行，等待回调补充剩下日志信息
        if (apiTaskInfo != null && StrUtil.isNotBlank(apiTaskInfo.getSourceProjectUrl())) {
            log.debug(">>>>>>>>>>> fls-task trigger api task, api task id:{}", idApiTask);
            TriggerParam triggerParam = assemTriggerParam(scheduleInfo, triggerType, idApiTask);
            triggerParam.setLogId(taskTriggerLog.getIdTriggerLog());
            taskTriggerLog.setTriggerParam(JSONUtil.toJsonStr(triggerParam));
            String address = apiTaskInfo.getSourceProjectUrl();
            result = executorManager.runExecutor(triggerParam, address);
        } else {
            //如果接口信息不正确，则直接存储失败信息
            result = ResponseData.fail(ErrorMsgConstant.API_TASK_NOT_EXIST);
        }

        taskTriggerLog.setResHttpCode(String.valueOf(result.getCode()));
        taskTriggerLog.setResCode(String.valueOf(result.getCode()));
        taskTriggerLog.setResMsg(result.getMsg());
        taskTriggerLog.setResText(JSONUtil.toJsonStr(result));
        taskLogService.updateById(taskTriggerLog);
    }

    @NotNull
    private static TriggerParam assemTriggerParam(TaskScheduleInfo scheduleInfo, TriggerTypeEnum triggerType, String idApiTask) {
        TriggerParam triggerParam = new TriggerParam();
        triggerParam.setSchedulerInfoId(scheduleInfo.getIdTaskSchedule());
        triggerParam.setIdApiTask(idApiTask);
        triggerParam.setBlockStrategy(ExecutorBlockStrategyEnum.DISCARD_LATER.getTitle());
        triggerParam.setTriggerType(triggerType.name());
        int exeTimeout = scheduleInfo.getExecutorTimeout() == null ? CommonConstant.DEFAULT_TRIGGER_TIME_OUT : scheduleInfo.getExecutorTimeout();
        triggerParam.setTimeout(exeTimeout);
        return triggerParam;
    }
}
