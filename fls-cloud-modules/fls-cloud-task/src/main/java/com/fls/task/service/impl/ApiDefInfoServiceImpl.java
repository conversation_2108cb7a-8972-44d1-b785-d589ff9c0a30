package com.fls.task.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fls.task.entity.ApiDefInfo;
import com.fls.task.mapper.ApiDefInfoMapper;
import com.fls.task.service.ApiDefInfoService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 接口定义表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Service
@RequiredArgsConstructor
public class ApiDefInfoServiceImpl extends ServiceImpl<ApiDefInfoMapper, ApiDefInfo> implements ApiDefInfoService {

    private final ApiDefInfoMapper apiDefInfoMapper;

    @Override
    public ApiDefInfo getByApiCode(String apiCode) {
        LambdaQueryWrapper<ApiDefInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ApiDefInfo::getApiCode, apiCode);
        return apiDefInfoMapper.selectOne(wrapper);
    }
}
