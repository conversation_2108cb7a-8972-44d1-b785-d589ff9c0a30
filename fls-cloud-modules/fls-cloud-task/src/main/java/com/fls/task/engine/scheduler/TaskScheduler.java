package com.fls.task.engine.scheduler;

import com.fls.task.engine.handler.TaskScheduleHandler;
import com.fls.task.engine.handler.TaskTriggerHandler;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

/**
 * 任务调度器
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Component
@RequiredArgsConstructor
public class TaskScheduler implements ApplicationRunner, DisposableBean {
    private static final Logger logger = LoggerFactory.getLogger(TaskScheduler.class);

    private final TaskTriggerHandler taskTriggerHandler;

    private final TaskScheduleHandler taskScheduleHandler;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        taskScheduleHandler.start();
        logger.info(">>>>>>>>> fls-task server start success. <<<<<<<<<");
    }

    @Override
    public void destroy() throws Exception {
        //先停止触发器的线程
        taskTriggerHandler.stop();
        //最后终止调度器的线程
        taskScheduleHandler.stop();
        logger.info(">>>>>>>>> fls-task server destroy success. <<<<<<<<<");
    }
}
