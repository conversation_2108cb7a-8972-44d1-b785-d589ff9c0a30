package com.fls.task.engine.handler;

import com.fls.task.engine.trigger.TaskTrigger;
import com.fls.task.enums.TriggerTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 任务触发线程池，摘抄自xxl-job框架中的逻辑
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Slf4j
@Component
public class TaskTriggerHandler {

    @Resource
    private TaskTrigger taskTrigger;

    @Resource
    private ThreadPoolExecutor fastTriggerPool;

    @Resource
    private ThreadPoolExecutor slowTriggerPool;

    private volatile long minTim = System.currentTimeMillis() / 60000;

    private final ConcurrentMap<String, AtomicInteger> jobTimeoutCountMap = new ConcurrentHashMap<>();

    /**
     * 执行调度
     *
     * @param taskId         任务id
     * @param triggerType    调度类型
     * @param failRetryCount 失败重试次数
     */
    public void trigger(String taskId, TriggerTypeEnum triggerType, int failRetryCount) {
        //选择线程池
        ThreadPoolExecutor triggerPool = fastTriggerPool;
        AtomicInteger jobTimeoutCount = jobTimeoutCountMap.get(taskId);
        //在1分钟内失败10次
        if (jobTimeoutCount != null && jobTimeoutCount.get() > 10) {
            triggerPool = slowTriggerPool;
        }
        triggerPool.execute(() -> {
            long start = System.currentTimeMillis();
            try {
                taskTrigger.trigger(taskId, triggerType, failRetryCount);
            }
            catch (Exception e) {
                log.error(e.getMessage(), e);
            }
            finally {
                long minTimNow = System.currentTimeMillis() / 60000;
                if (minTim != minTimNow) {
                    minTim = minTimNow;
                    jobTimeoutCountMap.clear();
                }
                long cost = System.currentTimeMillis() - start;
                if (cost > 500) {       // ob-timeout threshold 500ms
                    AtomicInteger timeoutCount = jobTimeoutCountMap.putIfAbsent(taskId, new AtomicInteger(1));
                    if (timeoutCount != null) {
                        timeoutCount.incrementAndGet();
                    }
                }
            }
        });
    }

    public void stop() {
        fastTriggerPool.shutdownNow();
        slowTriggerPool.shutdownNow();
        log.info(">>>>>>>>> fls-task trigger thread pool shutdown success.");
    }
}
