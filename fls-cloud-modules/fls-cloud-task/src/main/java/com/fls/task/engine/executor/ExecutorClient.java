package com.fls.task.engine.executor;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONUtil;
import com.fls.common.core.domain.ResponseData;
import com.fls.task.constant.CommonConstant;
import com.fls.task.constant.ExecutorConstant;
import com.fls.task.engine.model.TriggerParam;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;

@Data
@Slf4j
public class ExecutorClient implements Executor {

    /**
     * 执行器地址
     */
    private String addressUrl;

    /**
     * 执行器访问token
     */
    private String accessToken;

    /**
     * 执行器超时时间
     */
    private int timeout;

    public ExecutorClient() {
    }

    public ExecutorClient(String addressUrl, String accessToken, int timeout) {
        this.addressUrl = addressUrl;
        this.accessToken = accessToken;
        this.timeout = timeout;

        // valid
        if (!this.addressUrl.endsWith("/")) {
            this.addressUrl = this.addressUrl + "/";
        }
    }

    @Override
    public ResponseData run(TriggerParam triggerParam) {
        String url = addressUrl + ExecutorConstant.EXECUTOR_RUN_URI;
        String runReq = JSONUtil.toJsonStr(triggerParam);
        return remoteCall(url, runReq);
    }

    @Override
    public ResponseData kill(TriggerParam killParam) {
        String url = addressUrl + ExecutorConstant.EXECUTOR_KILL_URI;
        String runReq = JSONUtil.toJsonStr(killParam);
        return remoteCall(url, runReq);
    }

    @NotNull
    private static ResponseData remoteCall(String url, String runReq) {
        HttpRequest httpRequest = HttpRequest.post(url).body(JSONUtil.toJsonStr(runReq));
        httpRequest.timeout(CommonConstant.DEFAULT_TRIGGER_TIME_OUT);
        try {
            log.info("发起任务调度请求===>url:{}, reqBody:{}", httpRequest.getUrl(), runReq);
            HttpResponse response = httpRequest.execute();
            log.info("调度请求返回===>{}", response.body());
            if (!response.isOk()) {
                return ResponseData.fail("请求失败,请检查网络环境以及配置信息");
            }
            return JSONUtil.toBean(response.body(), ResponseData.class);
        } catch (Exception e) {
            log.error("调度请求请求失败，请求失败原因: {}", e.getMessage());
            return ResponseData.fail("请求失败,请检查网络环境以及配置信息");
        }
    }
}
