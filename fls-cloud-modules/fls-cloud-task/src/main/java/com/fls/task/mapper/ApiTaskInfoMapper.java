package com.fls.task.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fls.task.entity.ApiTaskInfo;
import com.fls.task.pojo.dto.ApiTaskQueryDTO;
import com.fls.task.pojo.vo.ApiTaskInfoVo;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 接口任务请求信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
public interface ApiTaskInfoMapper extends BaseMapper<ApiTaskInfo> {

        /**
         * 分页查询接口任务信息
         *
         * @param page  分页参数
         * @param query 查询条件
         * @return 分页结果
         */
        IPage<ApiTaskInfoVo> selectApiTaskPage(Page<ApiTaskInfoVo> page, @Param("query") ApiTaskQueryDTO query);
}
