package com.fls.task.engine.thread;

import com.fls.task.engine.handler.TaskTriggerHandler;
import com.fls.task.engine.holder.TimeRingManager;
import com.fls.task.enums.TriggerTypeEnum;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * TimeRingThread
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Slf4j
@RequiredArgsConstructor
public class TimeRingThread extends Thread {

    private final TimeRingManager timeRingManager;

    private final TaskTriggerHandler taskTriggerHandler;

    private volatile boolean ringThreadToStop = false;

    @Override
    public void run() {
        while (!ringThreadToStop) {
            try {
                TimeUnit.MILLISECONDS.sleep(1000 - System.currentTimeMillis() % 1000);
            }
            catch (InterruptedException e) {
                if (!ringThreadToStop) {
                    log.error(e.getMessage(), e);
                }
            }
            // 按秒分配
            try {
                List<String> ringItemData = new ArrayList<>();
                int nowSecond = Calendar.getInstance().get(Calendar.SECOND);   // 避免处理耗时太长，跨过刻度，向前校验一个刻度；
                for (int i = 0; i < 2; i++) {
                    List<String> tmpData = timeRingManager.fetchSchedules((nowSecond + 60 - i) % 60);
                    if (tmpData != null) {
                        ringItemData.addAll(tmpData);
                    }
                }
                // 时间轮调度
                log.debug(">>>>>>>>>>> fls-task, time-ring beat : {} = {}", nowSecond, Collections.singletonList(ringItemData));
                if (!ringItemData.isEmpty()) {
                    for (String taskScheduleId : ringItemData) {
                        taskTriggerHandler.trigger(taskScheduleId, TriggerTypeEnum.CRON, -1);
                    }
                    // 清理任务
                    ringItemData.clear();
                }
            }
            catch (Exception e) {
                if (!ringThreadToStop) {
                    log.error(">>>>>>>>>>> fls-task, TaskScheduleHandler#ringThread error", e);
                }
            }
        }
        log.info(">>>>>>>>>>> fls-task, TaskScheduleHandler#ringThread stop");
    }

    public void stopThread() {
        ringThreadToStop = true;
    }
}
