package com.fls.task.enums;

import lombok.Getter;

/**
 * 触发类型枚举定义
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Getter
public enum TriggerTypeEnum {
    /**
     * 手动
     */
    MANUAL("手动"),
    /**
     * 定时
     */
    CRON("定时"),
    /**
     * 固定速率
     */
    INTERVAL("固定速率"),
    /**
     * 补偿
     */
    MISFIRE("不起作用的补偿"),
    /**
     * 执行一次
     */
    ONCE("执行一次");

    TriggerTypeEnum(String title) {
        this.title = title;
    }

    private final String title;
}
