package com.fls.task.enums;

import lombok.Getter;

/**
 * 调度类型枚举
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Getter
public enum ScheduleTypeEnum {

    /**
     * 手动
     */
    NONE("NONE", "手动"),

    /**
     * schedule by cron
     */
    CRON("CRON", "CRON表达式"),

    /**
     * 执行一次
     */
    ONCE("ONCE", "执行一次"),

    /**
     * 固定速率
     */
    FIX_RATE("FIX_RATE", "固定速率");

    private final String code;
    private final String title;

    ScheduleTypeEnum(String code, String title) {
        this.code = code;
        this.title = title;
    }

    public static ScheduleTypeEnum match(String name, ScheduleTypeEnum defaultItem) {
        for (ScheduleTypeEnum item : ScheduleTypeEnum.values()) {
            if (item.name().equals(name)) {
                return item;
            }
        }
        return defaultItem;
    }

}
