package com.fls.task.engine.model;

import lombok.Data;

import java.io.Serializable;

/**
 * 触发器触发参数
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@Data
public class TriggerParam implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 调度信息id
     */
    private String schedulerInfoId;

    /**
     * 接口任务id
     */
    private String idApiTask;

    /**
     * 阻塞策略，为重复任务处理策略
     */
    private String blockStrategy;

    /**
     * 超时时间，单位：s
     */
    private Integer timeout;

    /**
     * 触发日志id
     */
    private String logId;

    /**
     * 触发类型
     */
    private String triggerType;
}
