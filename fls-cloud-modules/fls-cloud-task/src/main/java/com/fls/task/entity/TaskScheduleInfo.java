package com.fls.task.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 任务调度信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_task_schedule_info")
public class TaskScheduleInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id_task_schedule", type = IdType.ASSIGN_UUID)
    private String idTaskSchedule;

    /**
     * 请求任务id
     */
    private String idApiTask;

    /**
     * 调度类型（定时|固定速率|执行一次|手动）
     */
    private String scheduleType;

    /**
     * 调度配置
     */
    private String scheduleConf;

    /**
     * 调度状态（进行中|已终止）
     */
    private String scheduleStatus;

    /**
     * 执行器触发超时时间
     */
    private Integer executorTimeout;

    /**
     * 上次触发时间
     */
    private LocalDateTime triggerLastTime;

    /**
     * 下次触发时间
     */
    private LocalDateTime triggerNextTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    private String updater;

    /**
     * 删除标识
     */
    private String deleteFlag;

    /**
     * 时间戳
     */
    private LocalDateTime ts;
}
