package com.fls.task.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fls.task.entity.TaskScheduleInfo;
import com.fls.task.pojo.dto.TaskSchedInfoDTO;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 任务调度信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
public interface TaskScheduleInfoService extends IService<TaskScheduleInfo> {

    /**
     * 预读待调度任务信息列表
     *
     * @param time  时间戳（毫秒）
     * @param count 预读数量
     * @return 待调度任务列表
     */
    List<TaskScheduleInfo> preReadTask(long time, int count);

    /**
     * 刷新任务调度信息的下一次触发时间
     *
     * @param taskScheduleInfo 任务调度信息
     * @param fromTime         时间基准
     */
    void refreshNextValidTime(TaskScheduleInfo taskScheduleInfo, Date fromTime) throws Exception;

    /**
     * 添加任务调度信息
     *
     * @param info 调度信息
     * @return 是否成功
     */
    boolean add(@Valid TaskSchedInfoDTO info);

    /**
     * 更新任务调度
     *
     * @param info 调度信息
     * @return 是否成功
     */
    boolean edit(@Valid TaskSchedInfoDTO info);

    /**
     * 通过id删除调度任务信息
     *
     * @param idSched 调度信息id
     * @return 是否成功
     */
    boolean delete(String idSched);

    /**
     * 通过id停止调度任务
     *
     * @param idSched 调度id
     * @return 是否成功
     */
    boolean stop(String idSched);

    /**
     * 通过调度id触发调度任务
     *
     * @param idSched 调度id
     * @return 是否成功
     */
    boolean start(String idSched);
}
