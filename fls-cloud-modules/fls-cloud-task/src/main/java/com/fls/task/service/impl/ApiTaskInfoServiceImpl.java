package com.fls.task.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fls.common.core.domain.ResponseData;
import com.fls.common.core.exception.ServiceException;
import com.fls.common.mybatis.core.page.PageResult;
import com.fls.common.satoken.utils.LoginHelper;
import com.fls.master.api.RemoteResourceService;
import com.fls.master.api.model.ResourceInfo;
import com.fls.task.constant.CommonConstant;
import com.fls.task.constant.ErrorMsgConstant;
import com.fls.task.engine.model.TriggerParam;
import com.fls.task.entity.ApiDefInfo;
import com.fls.task.entity.ApiTaskInfo;
import com.fls.task.enums.ApiTaskStatusEnum;
import com.fls.task.enums.TriggerTypeEnum;
import com.fls.task.manager.ExecutorManager;
import com.fls.task.mapper.ApiTaskInfoMapper;
import com.fls.task.pojo.dto.ApiTaskAddDTO;
import com.fls.task.pojo.dto.ApiTaskQueryDTO;
import com.fls.task.pojo.vo.ApiTaskInfoVo;
import com.fls.task.service.ApiDefInfoService;
import com.fls.task.service.ApiTaskInfoService;
import com.fls.upms.api.model.LoginUser;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.time.LocalDateTime;

/**
 * <p>
 * 接口任务请求信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Service
@RequiredArgsConstructor
public class ApiTaskInfoServiceImpl extends ServiceImpl<ApiTaskInfoMapper, ApiTaskInfo> implements ApiTaskInfoService {

    private final ApiDefInfoService apiDefInfoService;

    @DubboReference
    private RemoteResourceService remoteResourceService;

    private final ExecutorManager executorManager;

    @Override
    public PageResult<ApiTaskInfoVo> selectApiTaskPage(ApiTaskQueryDTO query) {
        Page<ApiTaskInfoVo> page = new Page<>(query.getPageNum(), query.getPageSize());
        IPage<ApiTaskInfoVo> result = baseMapper.selectApiTaskPage(page, query);
        return new PageResult<>(page, result.getRecords());
    }

    @Override
    public boolean addApiTask(ApiTaskAddDTO apiTaskAddDTO) {
        // 1. 获取并验证接口定义
        ApiDefInfo apiDefInfo = getAndValidateApiDef(apiTaskAddDTO.getApiCode());

        // 2. 保存初始任务信息
        return saveInitialTaskInfo(apiTaskAddDTO, apiDefInfo);
    }

    /**
     * 获取并验证API定义信息
     */
    private ApiDefInfo getAndValidateApiDef(String apiCode) {
        ApiDefInfo apiDefInfo = apiDefInfoService.getByApiCode(apiCode);
        Assert.notNull(apiDefInfo, "未找到对应的接口定义信息，apiCode: " + apiCode);
        return apiDefInfo;
    }

    /**
     * 保存初始任务信息
     */
    private boolean saveInitialTaskInfo(ApiTaskAddDTO apiTaskAddDTO, ApiDefInfo apiDefInfo) {
        ApiTaskInfo taskInfo = new ApiTaskInfo();

        // 设置API定义相关信息
        taskInfo.setIdApiDef(apiDefInfo.getIdApiDef());
        taskInfo.setReqUrl(apiDefInfo.getHost() + apiDefInfo.getApiPath());
        taskInfo.setReqMethod(apiDefInfo.getApiMethod());
        taskInfo.setTaskHandler(apiTaskAddDTO.getTaskHandler());

        // 设置请求相关信息
        taskInfo.setReqBody(apiTaskAddDTO.getRequestData().toStringPretty());

        // 设置来源信息
        ResourceInfo resTableInfo = remoteResourceService.getResourceById(apiTaskAddDTO.getIdSourceRes());
        if (resTableInfo != null) {
            taskInfo.setSourceBillName(resTableInfo.getName());
        }
        taskInfo.setIdSourceBill(apiTaskAddDTO.getIdSource());
        taskInfo.setSourceBillCode(apiTaskAddDTO.getCodeSource());
        taskInfo.setIdSourceRes(apiTaskAddDTO.getIdSourceRes());
        taskInfo.setAssetCode(apiTaskAddDTO.getAssetCode());

        // 设置任务状态和创建时间
        taskInfo.setTaskStatus(ApiTaskStatusEnum.PREPARE.getCode());

        LoginUser loginUser = (LoginUser) (StpUtil.getTokenSession().get(LoginHelper.LOGIN_USER_KEY));
        taskInfo.setCreator(loginUser != null ? loginUser.getIdUser() : "");
        taskInfo.setCreateName(loginUser != null ? loginUser.getName() : "");
        taskInfo.setCreateTime(LocalDateTime.now());

        // 设置项目来源信息
        taskInfo.setSourceProjectCode(apiTaskAddDTO.getProjectCode());
        taskInfo.setSourceProjectUrl(apiTaskAddDTO.getProjectCodeUrl());

        return this.save(taskInfo);
    }


    @Override
    public ResponseData<Void> retry(String idApiTask) {
        ApiTaskInfo apiTaskInfo = getById(idApiTask);
        if (apiTaskInfo == null || StrUtil.isBlank(apiTaskInfo.getSourceProjectUrl())) {
            throw new ServiceException(ErrorMsgConstant.API_TASK_NOT_EXIST);
        }
        TriggerParam triggerParam = new TriggerParam();
        triggerParam.setIdApiTask(idApiTask);
        triggerParam.setTriggerType(TriggerTypeEnum.ONCE.name());
        int exeTimeout = CommonConstant.DEFAULT_TRIGGER_TIME_OUT;
        triggerParam.setTimeout(exeTimeout);
        triggerParam.setLogId(UUID.randomUUID().toString());
        String address = apiTaskInfo.getSourceProjectUrl();
        return executorManager.runExecutor(triggerParam, address);
    }
}
