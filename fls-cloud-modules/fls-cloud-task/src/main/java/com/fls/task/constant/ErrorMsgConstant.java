package com.fls.task.constant;

/**
 * 错误信息常量定义
 */
public interface ErrorMsgConstant {

    /**
     * 接口任务信息不存在
     */
    String API_TASK_NOT_EXIST = "接口任务信息不存在";

    /**
     * 执行器请求异常
     */
    String EXECUTOR_REQ_ERROR = "执行器请求异常";

    /**
     *  执行器地址不存在
     */
    String EXECUTOR_ADDRESS_NOT_EXIST = "执行器地址不存在";

    /**
     * 调度类型不匹配
     */
    String SCHED_TYPE_NOT_MATCH = "调度类型不匹配";

    /**
     * 调度配置异常
     */
    String SCHED_CONF_ERROR = "调度配置异常";

    /**
     * 调度任务信息不存在
     */
    String SCHED_TASK_NOT_EXIST = "调度任务信息不存在";
}
