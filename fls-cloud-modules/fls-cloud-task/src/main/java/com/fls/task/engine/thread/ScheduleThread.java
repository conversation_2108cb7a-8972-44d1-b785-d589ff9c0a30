package com.fls.task.engine.thread;

import cn.hutool.core.util.ObjectUtil;
import com.fls.task.config.FlsTaskConfig;
import com.fls.task.engine.handler.TaskTriggerHandler;
import com.fls.task.engine.holder.TimeRingManager;
import com.fls.task.entity.TaskScheduleInfo;
import com.fls.task.enums.ScheduleTriggerStatusEnum;
import com.fls.task.enums.TriggerTypeEnum;
import com.fls.task.service.TaskScheduleInfoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;

import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 调度线程实现
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Slf4j
@RequiredArgsConstructor
public class ScheduleThread extends Thread {

    private final TaskScheduleInfoService taskScheduleInfoService;

    private final FlsTaskConfig flsTaskConfig;

    private final RedissonClient redissonClient;

    private final TaskTriggerHandler taskTriggerHandler;

    private final TimeRingManager timeRingManager;

    private volatile boolean scheduleThreadToStop = false;

    /**
     * 预读时间
     */
    public static final long PRE_READ_MS = 5000;

    public static final String SCHEDULE_LOCK_KEY = "fls:task:schedule:lock";

    @Override
    public void run() {
        try {
            TimeUnit.MILLISECONDS.sleep(PRE_READ_MS - System.currentTimeMillis() % 1000);
        }
        catch (InterruptedException e) {
            if (!scheduleThreadToStop) {
                log.error(e.getMessage(), e);
            }
        }
        log.info(">>>>>>>>> init fls-task schedule handler start.");
        // 预读数量为两个触发线程池的20倍大小
        int preReadCount = (flsTaskConfig.getFastTriggerPoolMax() + flsTaskConfig.getSlowTriggerPoolMax()) * 20;
        while (!scheduleThreadToStop) {
            // 获取分布式锁
            RLock lock = redissonClient.getLock(SCHEDULE_LOCK_KEY);
            // 扫描任务信息
            long start = System.currentTimeMillis();
            boolean preReadSuc = true;
            try {
                if (lock.tryLock()) {
                    // 加上预读时间后读取数据
                    long nowTime = System.currentTimeMillis();
                    long preReadTime = nowTime + PRE_READ_MS;
                    List<TaskScheduleInfo> scheduleList = taskScheduleInfoService.preReadTask(preReadTime, preReadCount);
                    if (ObjectUtil.isNotEmpty(scheduleList)) {
                        log.info(">>>>>>>>> fls-task, task loop schedule found preReadTaskCount:{}", scheduleList.size());
                        for (TaskScheduleInfo scheduleInfo : scheduleList) {
                            long triggerNextTime = scheduleInfo.getTriggerNextTime().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
                            String idTaskSchedule = scheduleInfo.getIdTaskSchedule();
                            // 跳过了时间轮，一般出现这种情况，可能因为cpu的压力比较大，或者说中间可能出现了服务器终止的情况，导致任务的触发时间超过了预读的时间范围
                            if (nowTime > triggerNextTime + PRE_READ_MS) {
                                // 调度时间-已知将要调度时间 > 5
                                taskTriggerHandler.trigger(idTaskSchedule, TriggerTypeEnum.MISFIRE, -1);
                                log.warn(">>>>>>>>>>> fls-task, schedule misfire, taskScheduleId = {}", idTaskSchedule);
                                // 刷新下次调度时间
                                taskScheduleInfoService.refreshNextValidTime(scheduleInfo, new Date());
                            }
                            else if (nowTime > triggerNextTime) {
                                // 调度时间-已知将要调度时间 < 5
                                taskTriggerHandler.trigger(idTaskSchedule, TriggerTypeEnum.CRON, -1);
                                log.debug(">>>>>>>>>>> fls-task, schedule push trigger : taskScheduleId = {}", idTaskSchedule);
                                taskScheduleInfoService.refreshNextValidTime(scheduleInfo, new Date());
                                // 已知将要调度时间在5秒区间内
                                if (scheduleInfo.getScheduleStatus().equals(ScheduleTriggerStatusEnum.ENABLE.getCode()) && preReadTime > triggerNextTime) {
                                    // 1、时间轮秒数
                                    int ringSecond = (int)((triggerNextTime / 1000) % 60);

                                    // 2、放入时间轮
                                    timeRingManager.pushTimeRing(ringSecond, idTaskSchedule);

                                    // 3、fresh next
                                    taskScheduleInfoService.refreshNextValidTime(scheduleInfo, new Date(triggerNextTime));
                                }
                            }
                            else {
                                // 已知将要调度时间大于调度时间
                                int ringSecond = (int)((triggerNextTime / 1000) % 60);

                                timeRingManager.pushTimeRing(ringSecond, idTaskSchedule);

                                taskScheduleInfoService.refreshNextValidTime(scheduleInfo, new Date(triggerNextTime));
                            }
                        }
                        // 3、批量刷新调度信息
                        taskScheduleInfoService.updateBatchById(scheduleList);
                    }
                    else {
                        preReadSuc = false;
                    }
                }
            }
            catch (Exception e) {
                if (!scheduleThreadToStop) {
                    log.error(">>>>>>>>>>> fls-task, TaskScheduleHandler#scheduleThread error:", e);
                }
            }
            finally {
                if (null != lock && lock.isLocked()) {
                    try {
                        lock.unlock();
                    }
                    catch (Exception e) {
                        if (!scheduleThreadToStop) {
                            log.error(e.getMessage(), e);
                        }
                    }
                }
            }
            long cost = System.currentTimeMillis() - start;

            if (cost < 1000) {
                try {
                    TimeUnit.MILLISECONDS.sleep((preReadSuc ? 1000 : PRE_READ_MS) - System.currentTimeMillis() % 1000);
                }
                catch (InterruptedException e) {
                    if (!scheduleThreadToStop) {
                        log.error(e.getMessage(), e);
                    }
                }
            }
        }
        log.info(">>>>>>>>>>> fls-task, TaskScheduleHandler#scheduleThread stop");
    }

    public void cleanup() {
        scheduleThreadToStop = true;
    }
}
