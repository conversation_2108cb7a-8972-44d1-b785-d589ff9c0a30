package com.fls.task.manager;

import cn.hutool.core.util.StrUtil;
import com.fls.common.core.domain.ResponseData;
import com.fls.task.config.FlsTaskConfig;
import com.fls.task.constant.CommonConstant;
import com.fls.task.constant.ErrorMsgConstant;
import com.fls.task.engine.executor.Executor;
import com.fls.task.engine.executor.ExecutorClient;
import com.fls.task.engine.model.TriggerParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * ExecutorManager
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
@Slf4j
@Component
public class ExecutorManager {

    @Resource
    private FlsTaskConfig flsTaskConfig;

    private final ConcurrentMap<String, Executor> executorRepository = new ConcurrentHashMap<>();

    public ResponseData runExecutor(TriggerParam triggerParam, String address) {
        ResponseData result;
        try {
            Executor executor = getExecutor(address);
            if (executor == null) {
                return ResponseData.fail(ErrorMsgConstant.EXECUTOR_ADDRESS_NOT_EXIST);
            }
            result = executor.run(triggerParam);
        } catch (Exception e) {
            log.error(">>>>>>>>>>> fls-task trigger error, please check if the executor[{}] is running.", address, e);
            result = ResponseData.fail(ErrorMsgConstant.EXECUTOR_REQ_ERROR);
        }

        return result;
    }

    public Executor getExecutor(String address) throws Exception {
        if (StrUtil.isBlank(address)) {
            return null;
        }

        address = address.trim();
        Executor executor = executorRepository.get(address);
        if (executor != null) {
            return executor;
        }

        // set-cache
        executor = new ExecutorClient(address, flsTaskConfig.getAccessToken(), CommonConstant.DEFAULT_TRIGGER_TIME_OUT);

        executorRepository.put(address, executor);
        return executor;
    }
}
