package com.fls.task.enums;

import lombok.Getter;

/**
 * 执行器阻塞策略枚举
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@Getter
public enum ExecutorBlockStrategyEnum {
    /**
     * 串行
     */
    SERIAL_EXECUTION("Serial execution"),

    /**
     * 丢弃后调度
     */
    DISCARD_LATER("Discard Later"),

    /**
     * 覆盖之前
     */
    COVER_EARLY("Cover Early");

    private String title;

    ExecutorBlockStrategyEnum(String title) {
        this.title = title;
    }

    public static ExecutorBlockStrategyEnum match(String name, ExecutorBlockStrategyEnum defaultItem) {
        if (name != null) {
            for (ExecutorBlockStrategyEnum item:ExecutorBlockStrategyEnum.values()) {
                if (item.name().equals(name)) {
                    return item;
                }
            }
        }
        return defaultItem;
    }
}
