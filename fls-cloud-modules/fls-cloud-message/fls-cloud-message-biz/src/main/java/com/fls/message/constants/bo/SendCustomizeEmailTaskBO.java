package com.fls.message.constants.bo;

import com.fls.message.constants.dto.MessageWithFileParam;
import com.fls.message.pipeline.ProcessModel;
import lombok.Data;

import java.util.List;

/**
 * @Author: jiepeng.cen
 * @Description: 自定义邮件发送
 * @Date: create in 2023/2/8 9:12
 */
@Data
public class SendCustomizeEmailTaskBO implements ProcessModel {
    /**
     * 渠道账号id
     */
    private String accountId;

    /**
     * 消息模板Id
     */
    private String messageTemplateId;

    /**
     * 请求参数
     */
    private List<MessageWithFileParam> messageParamList;

    /**
     * 发送任务
     */
    private List<TaskInfoBO> taskInfo;
}
