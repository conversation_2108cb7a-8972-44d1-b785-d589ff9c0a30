package com.fls.message.service.impl;

import com.alibaba.fastjson.JSON;
import com.fls.message.entity.ChannelAccount;
import com.fls.message.mapper.ChannelAccountMapper;
import com.fls.message.service.ChannelAccountService;
import com.google.common.base.Throwables;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * @Author: jiepeng.cen
 * @Description: 渠道账号服务
 * @Date: create in 2023/1/2 16:44
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ChannelAccountServiceImpl implements ChannelAccountService {

    private final ChannelAccountMapper channelAccountMapper;

    public <T> T getAccountById(String sendAccountId, Class<T> clazz){
        try {
            ChannelAccount channelAccount = channelAccountMapper.selectById(sendAccountId);
            if (channelAccount != null){
                return JSON.parseObject(channelAccount.getAccountConfig(), clazz);
            }
        } catch (Exception e) {
            log.error("getAccount fail!", Throwables.getStackTraceAsString(e));
        }
        log.error("account not found!:{}", sendAccountId);
        return null;
    }
}
