package com.fls.message.action;

import com.fls.message.constants.bo.SendCustomizeEmailTaskBO;
import com.fls.message.constants.bo.TaskInfoBO;
import com.fls.message.exception.ProcessException;
import com.fls.message.exception.ProcessExceptionEnum;
import com.fls.message.listener.EmailCustomizeHandlerTask;
import com.fls.message.pipeline.EmailCustomizeProcess;
import com.fls.message.pipeline.ProcessContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * @Author: jiepeng.cen
 * @Description: 自定义邮件发送
 * @Date: create in 2023/2/8 9:09
 */
@Slf4j
@Component
public class SendCustomizeEmailAction implements EmailCustomizeProcess<SendCustomizeEmailTaskBO> {

    @Resource
    private ThreadPoolExecutor messageConsumerPool;

    @Autowired
    private ApplicationContext springContext;

    @Override
    public void customizeEmail(ProcessContext<SendCustomizeEmailTaskBO> processContext) {
        SendCustomizeEmailTaskBO processModel = processContext.getProcessModel();
        List<TaskInfoBO> taskInfo = processModel.getTaskInfo();
        for (TaskInfoBO taskInfoBO : taskInfo) {
            EmailCustomizeHandlerTask emailCustomizeHandlerTask = springContext.getBean(EmailCustomizeHandlerTask.class).setTaskInfo(taskInfoBO);
            try {
                messageConsumerPool.execute(emailCustomizeHandlerTask);
            } catch (Exception e) {
                // 线程池默认拒绝抛出异常，直接反应给使用方系统繁忙
                throw new ProcessException(ProcessExceptionEnum.SERVICE_BUSY.getCode(), ProcessExceptionEnum.SERVICE_BUSY.getMsg());
            }
        }
    }
}
