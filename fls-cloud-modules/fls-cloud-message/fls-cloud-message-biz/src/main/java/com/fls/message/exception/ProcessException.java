package com.fls.message.exception;

import lombok.Getter;
import lombok.Setter;

/**
 * @Author: jiepeng.cen
 * @Description:
 * @Date: create in 2022/12/29 16:22
 */
@Getter
@Setter
public class ProcessException extends RuntimeException{
    protected int code;

    protected String message;

    public ProcessException(int code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }

    public ProcessException(String message) {
        super(message);
        this.message = message;
    }
}
