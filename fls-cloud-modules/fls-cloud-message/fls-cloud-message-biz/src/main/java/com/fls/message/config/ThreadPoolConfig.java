package com.fls.message.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;

import java.util.concurrent.Executor;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * @Author: jiepeng.cen
 * @Description: 线程池配置
 * @Date: create in 2023/1/2 13:52
 */
@Slf4j
@EnableAsync
@Configuration
public class ThreadPoolConfig {

    @Autowired
    private FlsMessageConfig flsMessageConfig;

    @Autowired
    public ThreadPoolConfig(FlsMessageConfig flsMessageConfig) {
        this.flsMessageConfig = flsMessageConfig;
    }

    @Bean("messageConsumerPool")
    public Executor messageConsumerPool(){
        return new ThreadPoolExecutor(
            flsMessageConfig.getCorePoolSize(),
            flsMessageConfig.getMaxPoolSize(),
            60L,
            TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(flsMessageConfig.getBlockSize()),
            r -> new Thread(r, "fls-message,messageConsumer-" + r.hashCode()));
    }
}
