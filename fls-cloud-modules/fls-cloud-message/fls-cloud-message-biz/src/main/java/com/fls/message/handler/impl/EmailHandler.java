package com.fls.message.handler.impl;

import cn.hutool.extra.mail.MailAccount;
import cn.hutool.extra.mail.MailUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.fls.message.constants.bo.EmailContentModel;
import com.fls.message.constants.bo.TaskInfoBO;
import com.fls.message.constants.enums.ChannelType;
import com.fls.message.handler.BaseHandler;
import com.fls.message.handler.Handler;
import com.fls.message.service.ChannelAccountService;
import com.fls.message.utils.MultipartFileUtil;
import com.google.common.base.Throwables;
import com.sun.mail.util.MailSSLSocketFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.security.GeneralSecurityException;

/**
 * @Author: jiepeng.cen
 * @Description: 邮件发送处理
 * @Date: create in 2023/1/30 14:27
 */
@Slf4j
@Component
public class EmailHandler extends BaseHandler implements Handler {

    @Autowired
    private ChannelAccountService channelAccountService;

    public EmailHandler() {
        channelCode = ChannelType.EMAIL.getCode();
    }

    @Override
    public boolean handler(JSONObject taskObject) {
        TaskInfoBO taskInfo = JSON.parseObject(JSON.toJSONString(taskObject),new TypeReference<TaskInfoBO<EmailContentModel>>(){});

        EmailContentModel contentModel = (EmailContentModel)taskInfo.getContentModel();
        MailAccount account = getAccountConfig(taskInfo.getSendAccount());
        try {
            MailUtil.send(account, taskInfo.getReceiver(), contentModel.getTitle(),
                contentModel.getContent(), true, null);
        } catch (Exception e) {
            log.error("EmailHandler#handler fail!{},params:{}", Throwables.getStackTraceAsString(e), taskInfo);
            return false;
        }
        return true;
    }

    /**
     * 自定义邮件发送
     * @param taskInfo 任务
     * @return boolean
     */
    public boolean customizeHandler(TaskInfoBO taskInfo){
        EmailContentModel contentModel = (EmailContentModel)taskInfo.getContentModel();
        MailAccount account = getAccountConfig(taskInfo.getSendAccount());

        try {
            MailUtil.send(account, taskInfo.getReceiver(), contentModel.getTitle(),
                contentModel.getContent(), true, contentModel.getFiles());
        } catch (Exception e) {
            log.error("EmailHandler#customizeHandler fail!{}", Throwables.getStackTraceAsString(e), taskInfo);
            return false;
        } finally {
            if (contentModel.getFiles() != null && contentModel.getFiles().length > 0){
                for (File file : contentModel.getFiles()) {
                    //删除暂存文件
                    MultipartFileUtil.deleteFile(file);
                }
            }
        }
        return true;
    }

    private MailAccount getAccountConfig(String sendAccount){
        MailAccount account = channelAccountService.getAccountById(sendAccount, MailAccount.class);
        try {
            MailSSLSocketFactory sf = new MailSSLSocketFactory();
            sf.setTrustAllHosts(true);
            account = account.defaultIfEmpty();
            if (account.isSslEnable()){
                account.setSocketFactoryClass("javax.net.ssl.SSLSocketFactory");
                account.setSocketFactoryPort(account.getPort());
            }
            account.setTimeout(25000).setConnectionTimeout(25000);
        } catch (GeneralSecurityException e) {
            log.error("EmailHandler#getAccount fail!{}", Throwables.getStackTraceAsString(e));
        }
        return account;
    }
}
