package com.fls.message.handler;

import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.PostConstruct;

/**
 * @Author: jiepeng.cen
 * @Description: handler模板
 * @Date: create in 2023/1/2 15:50
 */
public abstract class Base<PERSON><PERSON>ler implements Handler{
    @Autowired
    private HandlerHolder handlerHolder;

    protected Integer channelCode;

    @PostConstruct
    private void init(){
        handlerHolder.putHandler(channelCode, this);
    }

    @Override
    public void doHandler(JSONObject taskInfo) {
        if (handler(taskInfo)){
            //todo 记录
        }
    }

    public abstract boolean handler(JSONObject taskInfo);
}
