package com.fls.message.config;

import org.springframework.amqp.core.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import static com.fls.message.constants.RabbitMqConstant.*;

/**
 * @Author: jiepeng.cen
 * @Description: rabbitmq配置
 * @Date: create in 2023/1/2 9:41
 */
@Configuration
public class RabbitMqConfig {

    @Autowired
    private FlsMessageConfig flsMessageConfig;

    //声明队列
    @Bean
    public Queue messageTransQueue(){
        return new Queue(MESSAGE_TRANS_QUEUE);
    }

    //声明交换机
    @Bean
    public Exchange messageTransExchange(){
        return ExchangeBuilder.topicExchange(MESSAGE_TRANS_EXCHANGE).durable(true).build();
    }

    @Bean
    public Binding messageTransBinding(){
        return BindingBuilder.bind(messageTransQueue()).to(messageTransExchange()).with(MESSAGE_TRANS_ROUTE).noargs();
    }
}
