package com.fls.message.pipeline;

import cn.hutool.core.collection.CollUtil;
import com.fls.message.constants.bo.SendCustomizeEmailTaskBO;
import com.fls.message.constants.dto.MessageWithFileParam;
import com.fls.message.exception.ProcessException;
import com.fls.message.exception.ProcessExceptionEnum;
import com.fls.message.utils.MultipartFileUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.List;

/**
 * @Author: jiepeng.cen
 * @Description:
 * @Date: create in 2023/2/8 10:58
 */
@Component
@RequiredArgsConstructor
public class EmailCustomizeProcessHandler {

    private final List<EmailCustomizeProcess> processList;

    /**
     * 执行责任链
     * @param context
     * @return
     */
    public void process(ProcessContext context){
        // 前置检查
        preCheck(context);

        try {
            for (EmailCustomizeProcess businessProcess : processList) {
                businessProcess.customizeEmail(context);
            }
        } catch (Exception e) {
            // 出现异常时也删除暂存文件
            SendCustomizeEmailTaskBO processModel = (SendCustomizeEmailTaskBO) context.getProcessModel();
            List<MessageWithFileParam> messageParamList = processModel.getMessageParamList();
            if (messageParamList != null && messageParamList.size() > 0){
                for (MessageWithFileParam messageWithFileParam : messageParamList) {
                    if (messageWithFileParam.getFiles() != null && messageWithFileParam.getFiles().size() > 0){
                        for (File file : messageWithFileParam.getFiles()) {
                            //删除暂存文件
                            MultipartFileUtil.deleteFile(file);
                        }
                    }
                }
            }
            throw e;
        }
    }

    private void preCheck(ProcessContext context){
        // 上下文
        if (context == null){
            throw new ProcessException(ProcessExceptionEnum.CONTEXT_IS_NULL.getCode(), ProcessExceptionEnum.CONTEXT_IS_NULL.getMsg());
        }

        // 执行模板列表
        if (CollUtil.isEmpty(processList)){
            throw new ProcessException(ProcessExceptionEnum.PROCESS_LIST_IS_NULL.getCode(), ProcessExceptionEnum.PROCESS_LIST_IS_NULL.getMsg());
        }
    }
}
