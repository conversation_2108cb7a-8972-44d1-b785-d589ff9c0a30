package com.fls.message.exception;

import com.fls.common.core.domain.ResponseData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * @Author: jiepeng.cen
 * @Description:
 * @Date: create in 2023/2/9 14:28
 */
@Slf4j
@RestControllerAdvice
public class ProcessExceptionHandler {

    /**
     * 消息处理异常
     */
    @ExceptionHandler(ProcessException.class)
    public ResponseData handleProcessException(ProcessException e) {
        return  ResponseData.fail(e.getCode(),e.getMessage());
    }

}
