package com.fls.message.constants.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * @Author: jiepeng.cen
 * @Description: 接口用发送参数
 * @Date: create in 2023/2/4 11:28
 */
@Data
public class SendParams implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 模板id
     */
    private String messageTemplateId;

    /**
     * 接收者
     */
    private String receiver;

    /**
     * 消息内容中可变部分
     */
    private Map<String, String> variables;
}
