package com.fls.message.action;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.fls.message.constants.bo.SendCustomizeEmailTaskBO;
import com.fls.message.constants.bo.SendTaskBO;
import com.fls.message.constants.dto.MessageParam;
import com.fls.message.constants.dto.MessageWithFileParam;
import com.fls.message.exception.ProcessException;
import com.fls.message.exception.ProcessExceptionEnum;
import com.fls.message.pipeline.BusinessProcess;
import com.fls.message.pipeline.EmailCustomizeProcess;
import com.fls.message.pipeline.ProcessContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author: jiepeng.cen
 * @Description: 前置参数校验
 * @Date: create in 2022/12/30 15:51
 */
@Order(1)
@Slf4j
@Component
public class PreParamCheckAction implements BusinessProcess<SendTaskBO>, EmailCustomizeProcess<SendCustomizeEmailTaskBO> {
    @Override
    public void process(ProcessContext<SendTaskBO> context) {
        SendTaskBO sendTaskBO = context.getProcessModel();

        String messageTemplateId = sendTaskBO.getMessageTemplateId();
        List<MessageParam> messageParamList = sendTaskBO.getMessageParamList();

        // 没有传入 消息模板Id 或者 messageParam
        if (messageTemplateId == null || CollUtil.isEmpty(messageParamList)){
            throw new ProcessException(ProcessExceptionEnum.CLIENT_BAD_PARAMETERS.getCode(), ProcessExceptionEnum.CLIENT_BAD_PARAMETERS.getMsg());
        }

        // 没有传入receiver的messageParam(不使用备用人群时)
        boolean present = messageParamList.stream().anyMatch(messageParam -> !messageParam.getTemplateCrowd() && StrUtil.isBlank(messageParam.getReceiver()));
        if (present){
            throw new ProcessException(ProcessExceptionEnum.CLIENT_BAD_PARAMETERS.getCode(), ProcessExceptionEnum.CLIENT_BAD_PARAMETERS.getMsg());
        }
    }

    @Override
    public void customizeEmail(ProcessContext<SendCustomizeEmailTaskBO> context) {
        SendCustomizeEmailTaskBO sendTaskBO = context.getProcessModel();

        List<MessageWithFileParam> messageParamList = sendTaskBO.getMessageParamList();

        // messageParam 没有传入
        if (CollUtil.isEmpty(messageParamList)){
            throw new ProcessException(ProcessExceptionEnum.CLIENT_BAD_PARAMETERS.getCode(), ProcessExceptionEnum.CLIENT_BAD_PARAMETERS.getMsg());
        }

        // 没有传入receiver的messageParam(不使用备用人群时)
        boolean present = messageParamList.stream().anyMatch(messageParam -> !messageParam.getTemplateCrowd() && StrUtil.isBlank(messageParam.getReceiver()));
        if (present){
            throw new ProcessException(ProcessExceptionEnum.CLIENT_BAD_PARAMETERS.getCode(), ProcessExceptionEnum.CLIENT_BAD_PARAMETERS.getMsg());
        }
    }
}
