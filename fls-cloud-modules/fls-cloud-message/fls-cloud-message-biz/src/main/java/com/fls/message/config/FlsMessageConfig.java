package com.fls.message.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * @Author: jiepeng.cen
 * @Description: 消息中心配置
 * @Date: create in 2023/1/2 13:53
 */
@Data
@RefreshScope
@Configuration
@ConfigurationProperties(prefix = "fls.message")
public class FlsMessageConfig {
    private int corePoolSize;

    private int maxPoolSize;

    private int blockSize;

    private int consumerSize;

    private int consumerMaxSize;
}
