package com.fls.message.listener;

import com.alibaba.fastjson.JSON;
import com.fls.message.constants.bo.TaskInfoBO;
import com.fls.message.constants.enums.ChannelType;
import com.fls.message.handler.impl.EmailHandler;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

/**
 * @Author: jiepeng.cen
 * @Description:
 * @Date: create in 2023/2/8 15:54
 */
@Data
@Accessors(chain = true)
@Slf4j
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class EmailCustomizeHandlerTask implements Runnable{

    @Autowired
    private EmailHandler emailHandler;

    private TaskInfoBO taskInfo;

    @Override
    public void run() {
        // 可以加其它业务
        // 这里暂时只发消息
        emailHandler.customizeHandler(taskInfo);
        log.debug(String.format("已处理[%s]渠道信息,消息参数：%s", ChannelType.EMAIL.getCode(), JSON.toJSONString(taskInfo)));
    }
}
