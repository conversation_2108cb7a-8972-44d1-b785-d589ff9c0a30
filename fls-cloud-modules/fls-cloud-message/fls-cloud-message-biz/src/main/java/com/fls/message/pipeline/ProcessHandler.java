package com.fls.message.pipeline;

import cn.hutool.core.collection.CollUtil;
import com.fls.message.exception.ProcessException;
import com.fls.message.exception.ProcessExceptionEnum;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author: jiepeng.cen
 * @Description:
 * @Date: create in 2022/12/29 15:29
 */
@Component
@RequiredArgsConstructor
public class ProcessHandler {

    private final List<BusinessProcess> processList;

    /**
     * 执行责任链
     * @param context
     * @return
     */
    public void process(ProcessContext context){
        // 前置检查
        preCheck(context);

        for (BusinessProcess businessProcess : processList) {
            businessProcess.process(context);
        }
    }

    private void preCheck(ProcessContext context){
        // 上下文
        if (context == null){
            throw new ProcessException(ProcessExceptionEnum.CONTEXT_IS_NULL.getCode(), ProcessExceptionEnum.CONTEXT_IS_NULL.getMsg());
        }

        // 执行模板列表
        if (CollUtil.isEmpty(processList)){
            throw new ProcessException(ProcessExceptionEnum.PROCESS_LIST_IS_NULL.getCode(), ProcessExceptionEnum.PROCESS_LIST_IS_NULL.getMsg());
        }
    }
}
