package com.fls.message.controller;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.fls.common.core.constant.CacheConstants;
import com.fls.common.core.domain.ResponseData;
import com.fls.common.core.utils.StringUtils;
import com.fls.common.log.annotation.Log;
import com.fls.common.log.enums.BusinessType;
import com.fls.common.log.enums.OperatorType;
import com.fls.common.redis.utils.RedisUtils;
import com.fls.message.constants.dto.MessageParam;
import com.fls.message.constants.dto.SendRequest;
import com.fls.message.service.SendService;
import com.fls.upms.api.RemoteUserService;
import com.fls.upms.api.model.LoginUser;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.time.Duration;
import java.util.HashMap;

/**
 * @Author: jiepeng.cen
 * @Description: 通用发送消息（发送大量消息）
 * @Date: create in 2023/2/2 16:37
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/common")
@Slf4j
public class CommonMessageController {

    private final SendService sendService;

    /**
     * 通过模板里的备用人群列表发送信息
     * @param sendRequest 发送参数
     * @return
     */
    @PostMapping("/send-by-backup-crowd")
    public ResponseData<Boolean> sendByBackupCrowd(@RequestBody SendRequest sendRequest){
        if (StrUtil.isBlank(sendRequest.getMessageTemplateId())){
            return ResponseData.fail("消息模板参数有误");
        }
        if (sendRequest.getMessageParam() != null && StrUtil.isNotBlank(sendRequest.getMessageParam().getReceiver())){
            return ResponseData.fail("该接口使用模板备用接收者,不支持传入");
        }

        return ResponseData.ok(sendService.send(sendRequest, true));
    }

    /**
     * 使用请求参数里的接收者列表发送信息
     * @param sendRequest 发送参数
     * @return ResponseData<Boolean>
     */
    @PostMapping("/send-by-request-crowd")
    public ResponseData<Boolean> sendByRequestCrowd(@RequestBody SendRequest sendRequest){
        if (StrUtil.isBlank(sendRequest.getMessageTemplateId())){
            return ResponseData.fail("消息模板参数有误");
        }
        if (sendRequest.getMessageParam() == null || StrUtil.isBlank(sendRequest.getMessageParam().getReceiver())){
            return ResponseData.fail("该接口使用参数传入接收者,不能为空");
        }

        return ResponseData.ok(sendService.send(sendRequest, false));
    }
}
