package com.fls.message.listener;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fls.message.constants.RabbitMqConstant;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * @Author: jiepeng.cen
 * @Description: 监听rabbitmq队列
 * @Date: create in 2023/1/2 10:55
 */
@Slf4j
@Component
public class RabbitMQListener {
    @Resource
    private ThreadPoolExecutor messageConsumerPool;

    @Autowired
    private ApplicationContext context;

    @RabbitListener(queues = RabbitMqConstant.MESSAGE_TRANS_QUEUE)
    public void messageConsumer(String msg, Message message, Channel channel){
        if (StrUtil.isBlank(msg)){
            return;
        }
        JSONArray taskInfoBOS = JSON.parseArray(msg);

        for (Object taskInfoBO : taskInfoBOS) {

            JSONObject taskObject = (JSONObject) taskInfoBO;

            MessageHandlerTask task = context.getBean(MessageHandlerTask.class).setTaskInfo(taskObject);
            try {
                messageConsumerPool.execute(task);
                // 手动确认消息
                try {
                    channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                } catch (IOException e) {
                    log.error("fail to confirm message:" + msg);
                }
            } catch (Exception e) {
                // 线程池满则等待10s
                try {
                    Thread.sleep(10000);
                } catch (InterruptedException ex) {
                    throw new RuntimeException(ex);
                }
            }
        }
    }
}
