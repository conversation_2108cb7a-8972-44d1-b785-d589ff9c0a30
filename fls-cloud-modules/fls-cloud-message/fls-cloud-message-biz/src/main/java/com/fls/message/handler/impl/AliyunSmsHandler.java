package com.fls.message.handler.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.aliyun.dysmsapi20170525.Client;
import com.aliyun.dysmsapi20170525.models.SendSmsRequest;
import com.aliyun.dysmsapi20170525.models.SendSmsResponse;
import com.aliyun.tea.TeaException;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.models.RuntimeOptions;
import com.fls.message.constants.bo.SmsContentModel;
import com.fls.message.constants.bo.TaskInfoBO;
import com.fls.message.constants.enums.ChannelType;
import com.fls.message.handler.BaseHandler;
import com.fls.message.handler.Handler;
import com.fls.message.handler.account.AliSmsAccount;
import com.fls.message.service.ChannelAccountService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Author: jiepeng.cen
 * @Description: 阿里云短信渠道
 * @Date: create in 2023/1/2 15:48
 */
@Slf4j
@Component
public class AliyunSmsHandler extends BaseHandler implements Handler {
    @Autowired
    private ChannelAccountService channelAccountService;
    private final String ALI_ENDPOINT = "dysmsapi.aliyuncs.com";

    public AliyunSmsHandler() {
        channelCode = ChannelType.ALI_SMS.getCode();
    }

    @Override
    public boolean handler(JSONObject taskObject) {
        try {
            //因为渠道内容不同，只能到各自渠道进行解析
            TaskInfoBO taskInfo = JSON.parseObject(JSON.toJSONString(taskObject),new TypeReference<TaskInfoBO<SmsContentModel>>(){});

            AliSmsAccount account = channelAccountService.getAccountById(taskInfo.getSendAccount(), AliSmsAccount.class);
            Client client = this.createClient(account.getAccessKeyId(), account.getAccessKeySecret());
            // 获取短信的内容模板
            SmsContentModel contentModel = (SmsContentModel)taskInfo.getContentModel();
            SendSmsRequest sendSmsRequest = new SendSmsRequest()
                .setPhoneNumbers(String.join(",", taskInfo.getReceiver()))
                .setSignName(contentModel.getSignName())
                .setTemplateCode(contentModel.getTemplateCode());

            if (taskInfo.getVariables() != null){
                sendSmsRequest.setTemplateParam(JSON.toJSONString(taskInfo.getVariables()));
            }
            RuntimeOptions runtime = new RuntimeOptions();

            // 复制代码运行请自行打印 API 的返回值
            SendSmsResponse sendSmsResponse = client.sendSmsWithOptions(sendSmsRequest, runtime);
        } catch (TeaException error) {
            // 如有需要，请打印 error
            log.error(com.aliyun.teautil.Common.assertAsString(error.message));
            return false;
        } catch (Exception _error) {
            TeaException error = new TeaException(_error.getMessage(), _error);
            // 如有需要，请打印 error
            log.error(com.aliyun.teautil.Common.assertAsString(error.message));
            return false;
        }
        return true;
    }

    private Client createClient(String accessKeyId, String accessKeySecret) throws Exception {
        Config config = new Config()
            // 必填，您的 AccessKey ID
            .setAccessKeyId(accessKeyId)
            // 必填，您的 AccessKey Secret
            .setAccessKeySecret(accessKeySecret);
        // 访问的域名
        config.endpoint = ALI_ENDPOINT;
        return new Client(config);
    }
}
