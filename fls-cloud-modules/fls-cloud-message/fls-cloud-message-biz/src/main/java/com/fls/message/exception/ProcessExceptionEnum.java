package com.fls.message.exception;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: jiepeng.cen
 * @Description:
 * @Date: create in 2022/12/29 16:27
 */
@Getter
@AllArgsConstructor
public enum ProcessExceptionEnum {

    CLIENT_BAD_PARAMETERS(10001, "客户端参数错误"),

    TEMPLATE_NOT_FOUND(10002, "找不到模板或模板已被删除"),

    TEMPLATE_CROWD_IS_BLANK(10002, "模板备用人群参数错误"),

    FILE_UPLOAD_ERROR(10003, "文件上传有误"),

    REQUEST_VARIABLES_ERROR(10004, "模板内容可替换变量解析错误"),

    CONTEXT_IS_NULL(20001, "流程上下文为空"),

    PROCESS_TEMPLATE_IS_NULL(20002, "流程模板配置为空"),

    PROCESS_LIST_IS_NULL(20003, "业务处理器配置为空"),

    SERVICE_ERROR(30001, "服务执行异常"),

    SERVICE_BUSY(30002, "服务当前执行繁忙,请稍后再试"),

    SEND_THIRD_PLAT_MESSAGE_ERROR(40001, "发送第三方平台信息异常")
    ;

    /**
     * 响应状态
     */
    private final int code;
    /**
     * 响应编码
     */
    private final String msg;
}
