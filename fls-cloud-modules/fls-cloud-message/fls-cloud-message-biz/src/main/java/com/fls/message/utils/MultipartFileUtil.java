package com.fls.message.utils;

import cn.hutool.core.util.StrUtil;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;

/**
 * @Author: jiepeng.cen
 * @Description: 文件上传后暂存，使用后删除
 * @Date: create in 2023/2/8 10:04
 */
public class MultipartFileUtil {
    private static final String TEMP_FILEPATH = "/email/temp";

    /**
     * 转存临时文件（使用后请删除：下面有deleteFile）
     *
     * @param multipartFile 待转存文件
     * @return 临时文件绝对路径
     */
    public static String storageTempFile(MultipartFile multipartFile) {
        // 文件无效，无需转存
        File toFile = null;
        if (multipartFile.getSize() <= 0) {
            return StrUtil.EMPTY;
        } else {
            // 文件原名称
            String originalFilename = multipartFile.getOriginalFilename();
            // 文件全路径（绝对路径+文件名称）
            toFile = new File(TEMP_FILEPATH + File.separator + originalFilename);

            String absolutePath = null;
            try {
                // 获取临时文件所需要转存的文件夹路劲，不存在则创建
                absolutePath = toFile.getCanonicalPath();
                String dirPath = absolutePath.substring(0, absolutePath.lastIndexOf(File.separator));
                File dir = new File(dirPath);
                if (!dir.exists()) {
                    dir.mkdirs();
                }

                // 流写入
                InputStream ins = multipartFile.getInputStream();
                inputStreamToFile(ins, toFile);
                ins.close();

            } catch (IOException e) {
                e.printStackTrace();
            }

            // 返回绝对路径
            return absolutePath;
        }
    }

    /**
     * 流写入文件
     *
     * @param inputStream 文件输入流
     * @param file        输出文件
     */
    public static void inputStreamToFile(InputStream inputStream, File file) {
        try {
            OutputStream os = new FileOutputStream(file);
            int bytesRead = 0;
            byte[] buffer = new byte[8192];
            while ((bytesRead = inputStream.read(buffer, 0, 8192)) != -1) {
                os.write(buffer, 0, bytesRead);
            }
            os.close();
            inputStream.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 删除本地文件
     *
     * @param file 要删除的文件
     */
    public static void deleteFile(File file) {
        if (file != null) {
            File del = new File(file.toURI());
            del.delete();
        }
    }
}
