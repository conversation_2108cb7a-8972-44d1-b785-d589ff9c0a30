package com.fls.message.action;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ReUtil;
import com.alibaba.fastjson.JSON;
import com.fls.message.constants.bo.SendCustomizeEmailTaskBO;
import com.fls.message.constants.bo.SendTaskBO;
import com.fls.message.constants.bo.TaskInfoBO;
import com.fls.message.constants.enums.IdType;
import com.fls.message.exception.ProcessException;
import com.fls.message.exception.ProcessExceptionEnum;
import com.fls.message.pipeline.BusinessProcess;
import com.fls.message.pipeline.EmailCustomizeProcess;
import com.fls.message.pipeline.ProcessContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Author: jiepeng.cen
 * @Description: 后置参数检查
 * @Date: create in 2022/12/29 16:56
 */
@Order(3)
@Slf4j
@Component
public class AfterParamCheckAction implements BusinessProcess<SendTaskBO>, EmailCustomizeProcess<SendCustomizeEmailTaskBO> {

    public static final String PHONE_REGEX_EXP = "^((13[0-9])|(14[5,7,9])|(15[0-3,5-9])|(166)|(17[0-9])|(18[0-9])|(19[1,8,9]))\\d{8}$";
    public static final String EMAIL_REGEX_EXP = "^[A-Za-z0-9-_\\u4e00-\\u9fa5]+@[a-zA-Z0-9_-]+(\\.[a-zA-Z0-9_-]+)+$";

    public static final HashMap<Integer, String> CHANNEL_REGEX_EXP = new HashMap<>();
    static {
        CHANNEL_REGEX_EXP.put(IdType.PHONE.getCode(), PHONE_REGEX_EXP);
        CHANNEL_REGEX_EXP.put(IdType.EMAIL.getCode(), EMAIL_REGEX_EXP);
    }

    @Override
    public void process(ProcessContext<SendTaskBO> context) {
        SendTaskBO sendTaskBO = context.getProcessModel();
        List<TaskInfoBO> taskInfo = sendTaskBO.getTaskInfo();

        // 过滤掉不合法的手机号、邮件
        filterIllegalReceiver(taskInfo);
    }

    /**
     * 如果指定类型是手机号，检测输入手机号是否合法
     * 如果指定类型是邮件，检测输入邮件是否合法
     * @param taskInfo 任务列表
     */
    private void filterIllegalReceiver(List<TaskInfoBO> taskInfo) {
        Integer idType = CollUtil.getFirst(taskInfo.iterator()).getIdType();
        filter(taskInfo, CHANNEL_REGEX_EXP.get(idType));
    }

    /**
     * 利用正则过滤掉不合法的接收者
     *
     * @param taskInfo 任务列表
     * @param regexExp 正则表达式
     */
    private void filter(List<TaskInfoBO> taskInfo, String regexExp) {
        for (TaskInfoBO task : taskInfo) {
            Set<String> receiver = task.getReceiver();
            Set<String> illegalPhone = receiver.stream()
                .filter(phone -> !ReUtil.isMatch(regexExp, phone))
                .collect(Collectors.toSet());

            if (CollUtil.isNotEmpty(illegalPhone)) {
                task.getReceiver().removeAll(illegalPhone);
                log.error("messageTemplateId:{} find illegal receiver!{}", task.getMessageTemplateId(), JSON.toJSONString(illegalPhone));
                throw new ProcessException(ProcessExceptionEnum.CLIENT_BAD_PARAMETERS.getCode(), ProcessExceptionEnum.CLIENT_BAD_PARAMETERS.getMsg());
            }
        }
    }

    @Override
    public void customizeEmail(ProcessContext<SendCustomizeEmailTaskBO> context) {
        SendCustomizeEmailTaskBO sendTaskBO = context.getProcessModel();
        List<TaskInfoBO> taskInfo = sendTaskBO.getTaskInfo();

        // 过滤掉不合法的手机号、邮件
        filterIllegalReceiver(taskInfo);
    }
}
