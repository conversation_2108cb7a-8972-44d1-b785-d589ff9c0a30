package com.fls.message.handler;

import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: jiepeng.cen
 * @Description: channel->handler的映射关系
 * @Date: create in 2023/1/2 14:45
 */
@Component
public class HandlerHolder {
    private Map<Integer, Handler> handlers = new HashMap(128);

    public void putHandler(Integer channelCode, Handler handler) {
        handlers.put(channelCode, handler);
    }

    public Handler route(Integer channelCode) {
        return handlers.get(channelCode);
    }
}
