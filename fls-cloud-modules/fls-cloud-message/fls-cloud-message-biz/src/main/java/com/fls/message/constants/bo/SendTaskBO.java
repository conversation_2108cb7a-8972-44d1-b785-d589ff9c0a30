package com.fls.message.constants.bo;

import com.fls.message.constants.dto.MessageParam;
import com.fls.message.pipeline.ProcessModel;
import lombok.Data;

import java.util.List;

/**
 * @Author: jiepeng.cen
 * @Description: 发送消息任务业务模型
 * @Date: create in 2022/12/29 14:21
 */
@Data
public class SendTaskBO  implements ProcessModel {
    /**
     * 消息模板Id
     */
    private String messageTemplateId;

    /**
     * 请求参数
     */
    private List<MessageParam> messageParamList;

    /**
     * 发送任务
     */
    private List<TaskInfoBO> taskInfo;
}
