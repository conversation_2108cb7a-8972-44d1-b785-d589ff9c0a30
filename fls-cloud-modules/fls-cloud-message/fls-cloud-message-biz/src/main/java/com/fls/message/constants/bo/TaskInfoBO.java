package com.fls.message.constants.bo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;
import java.util.Set;

/**
 * @Author: jiepeng.cen
 * @Description:
 * @Date: create in 2022/12/29 14:30
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TaskInfoBO<T extends ContentModel> {
    /**
     * 消息模板Id
     */
    private String messageTemplateId;

    /**
     * 业务Id(数据追踪使用)
     * 生成逻辑参考 TaskInfoUtils
     */
    private Long businessId;

    /**
     * 接收者
     */
    private Set<String> receiver;

    /**
     * 发送的Id类型
     */
    private Integer idType;

    /**
     * 发送渠道
     */
    private Integer sendChannel;

    /**
     * 消息类型
     */
    private Integer msgType;

    /**
     * 发送文案模型
     * message_template表存储的content是JSON(所有内容都会塞进去)
     * 不同的渠道要发送的内容不一样(比如发push会有img，而短信没有)
     * 所以会有ContentModel
     */
    private T contentModel;

    /**
     * 发送账号（邮件下可有多个发送账号、短信可有多个发送账号..）
     */
    private String sendAccount;

    /**
     * 消息内容中可变部分
     */
    private Map<String, String> variables;
}
