package com.fls.message.service;

import com.fls.message.constants.dto.SendRequest;
import org.springframework.web.multipart.MultipartFile;

/**
 * @Author: jiepeng.cen
 * @Description:
 * @Date: create in 2023/2/7 16:57
 */
public interface EmailCustomizeMessageService {
    /**
     * 发送邮件时使用模板内容
     * @param sendParams 发送参数
     * @param files 使用方传入的文件
     * @param templateCrowd 是否使用模板备用接收者
     * @return boolean
     */
    boolean sendByTemplate(SendRequest sendParams, MultipartFile[] files, boolean templateCrowd);

    /**
     * 发送邮件时由参数决定
     * @param templateId 模板id
     * @param accountId 账号id
     * @param title 主题
     * @param content 内容
     * @param receiver 消息接收者（多个时,分割）
     * @param files 文件
     * @param templateCrowd 是否使用模板备用接收者
     * @return boolean
     */
    boolean sendByCustomize(String templateId, String accountId, String title, String content, String receiver, MultipartFile[] files, boolean templateCrowd);
}
