package com.fls.message.listener;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fls.message.handler.HandlerHolder;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

/**
 * @Author: jiepeng.cen
 * @Description: 消息处理
 * @Date: create in 2023/1/2 14:39
 */
@Data
@Accessors(chain = true)
@Slf4j
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class MessageHandlerTask implements Runnable{

    @Autowired
    private HandlerHolder handlerHolder;

    private JSONObject taskInfo;

    @Override
    public void run() {
        // 可以加其它业务
        // 这里暂时只发消息
        Integer sendChannel = taskInfo.getInteger("sendChannel");
        handlerHolder.route(sendChannel).doHandler(taskInfo);
        log.debug(String.format("已处理[%s]渠道信息,消息参数：%s", sendChannel, JSON.toJSONString(taskInfo)));
    }
}
