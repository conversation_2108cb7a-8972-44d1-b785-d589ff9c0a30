package com.fls.message;

import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.metrics.buffering.BufferingApplicationStartup;

/**
 * @Author: jiepeng.cen
 * @Description: 消息中心业务模块启动类
 * @Date: create in 2023/1/3 9:17
 */
@EnableDubbo
@SpringBootApplication
public class FlsMessageApplication {
    public static void main(String[] args) {
        SpringApplication application = new SpringApplication(FlsMessageApplication.class);
        application.setApplicationStartup(new BufferingApplicationStartup(2048));
        application.run(args);
    }
}
