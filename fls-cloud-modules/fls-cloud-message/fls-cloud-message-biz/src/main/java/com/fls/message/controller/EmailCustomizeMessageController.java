package com.fls.message.controller;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.fls.common.core.domain.ResponseData;
import com.fls.message.constants.dto.SendRequest;
import com.fls.message.service.EmailCustomizeMessageService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * @Author: jiepeng.cen
 * @Description: 邮件自定义发送
 * @Date: create in 2023/2/7 16:50
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/email")
public class EmailCustomizeMessageController {

    private final EmailCustomizeMessageService emailCustomizeMessageService;

    /**
     * 使用模板里备用人群列表发送邮件
     * @param sendRequest 发送参数
     * @param files 文件参数
     * @return ResponseData<Boolean>
     */
    @PostMapping("/send-template-by-backup-crowd")
    public ResponseData<Boolean> sendByTemplateAndBackupCrowd(@RequestParam("sendRequest") String sendRequest, MultipartFile[] files){
        SendRequest sendParams;
        try {
            sendParams = JSON.parseObject(sendRequest, SendRequest.class);
        } catch (Exception e) {
            return ResponseData.fail("发送参数解析有误");
        }
        if (StrUtil.isBlank(sendParams.getMessageTemplateId())){
            return ResponseData.fail("消息模板参数有误");
        }
        if (sendParams.getMessageParam() != null && StrUtil.isNotBlank(sendParams.getMessageParam().getReceiver())){
            return ResponseData.fail("该接口使用模板备用接收者,不支持传入");
        }
        return ResponseData.ok(emailCustomizeMessageService.sendByTemplate(sendParams, files, true));
    }

    /**
     * 使用请求参数里的接收者列表发送邮件
     * @param sendRequest 发送参数
     * @param files 文件参数
     * @return ResponseData<Boolean>
     */
    @PostMapping("/send-template-by-request-crowd")
    public ResponseData<Boolean> sendByTemplateAndRequestCrowd(@RequestParam("sendRequest") String sendRequest, MultipartFile[] files){
        SendRequest sendParams;
        try {
            sendParams = JSON.parseObject(sendRequest, SendRequest.class);
        } catch (Exception e) {
            return ResponseData.fail("发送参数解析有误");
        }
        if (StrUtil.isBlank(sendParams.getMessageTemplateId())){
            return ResponseData.fail("消息模板参数有误");
        }
        if (sendParams.getMessageParam() == null || StrUtil.isBlank(sendParams.getMessageParam().getReceiver())){
            return ResponseData.fail("该接口使用参数传入接收者,不能为空");
        }
        return ResponseData.ok(emailCustomizeMessageService.sendByTemplate(sendParams, files, false));
    }

    /**
     * 使用模板里备用人群列表发送邮件
     * @param templateId 模板id
     * @param title 主题
     * @param content 内容
     * @param files 文件
     * @return ResponseData<Boolean>
     */
    @PostMapping("/send-customize-by-backup-crowd")
    public ResponseData<Boolean> sendByCustomizeAndBackupCrowd(@RequestParam("templateId") String templateId,
                                                               @RequestParam("title") String title,
                                                               @RequestParam("content") String content,
                                                               MultipartFile[] files){
        if (StrUtil.isBlank(templateId)){
            return ResponseData.fail("模板ID不能为空");
        }
        if (StrUtil.isBlank(title)){
            return ResponseData.fail("标题不能为空");
        }
        if (StrUtil.isBlank(content)){
            return ResponseData.fail("消息内容不能为空");
        }
        return ResponseData.ok(emailCustomizeMessageService.sendByCustomize(templateId, null, title, content, null, files, true));
    }

    /**
     * 使用请求参数里的接收者发送邮件
     * @param templateId 模板id
     * @param title 主题
     * @param content 内容
     * @param receiver 接收者
     * @param files 文件
     * @return ResponseData<Boolean>
     */
    @PostMapping("/send-customize-by-request-crowd")
    public ResponseData<Boolean> sendByCustomizeAndRequestCrowd(@RequestParam("templateId") String templateId,
                                                               @RequestParam("title") String title,
                                                               @RequestParam("content") String content,
                                                               @RequestParam("receiver") String receiver,
                                                               MultipartFile[] files){
        if (StrUtil.isBlank(templateId)){
            return ResponseData.fail("模板ID不能为空");
        }
        if (StrUtil.isBlank(title)){
            return ResponseData.fail("标题不能为空");
        }
        if (StrUtil.isBlank(content)){
            return ResponseData.fail("消息内容不能为空");
        }
        if (StrUtil.isBlank(receiver)){
            return ResponseData.fail("接收者不能为空");
        }
        return ResponseData.ok(emailCustomizeMessageService.sendByCustomize(templateId, null, title, content, receiver, files, false));
    }

    /**
     * 使用账号维度发送邮件
     * @param accountId 账号id
     * @param title 主题
     * @param content 内容
     * @param receiver 接收者
     * @param files 文件
     * @return ResponseData<Boolean>
     */
    @PostMapping("/send-customize-by-account")
    public ResponseData<Boolean> sendByCustomizeByAccount(@RequestParam("accountId") String accountId,
                                                                @RequestParam("title") String title,
                                                                @RequestParam("content") String content,
                                                                @RequestParam("receiver") String receiver,
                                                                MultipartFile[] files){
        if (StrUtil.isBlank(accountId)){
            return ResponseData.fail("账号ID不能为空");
        }
        if (StrUtil.isBlank(title)){
            return ResponseData.fail("标题不能为空");
        }
        if (StrUtil.isBlank(content)){
            return ResponseData.fail("消息内容不能为空");
        }
        if (StrUtil.isBlank(receiver)){
            return ResponseData.fail("接收者不能为空");
        }
        return ResponseData.ok(emailCustomizeMessageService.sendByCustomize(null, accountId, title, content, receiver, files, false));
    }
}
