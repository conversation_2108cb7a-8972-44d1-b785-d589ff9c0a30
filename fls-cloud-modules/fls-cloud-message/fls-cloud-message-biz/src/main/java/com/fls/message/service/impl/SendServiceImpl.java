package com.fls.message.service.impl;

import com.fls.message.constants.bo.SendTaskBO;
import com.fls.message.constants.dto.MessageParam;
import com.fls.message.constants.dto.SendRequest;
import com.fls.message.pipeline.ProcessContext;
import com.fls.message.pipeline.ProcessHandler;
import com.fls.message.pipeline.ProcessModel;
import com.fls.message.service.SendService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collections;

/**
 * @Author: jiepeng.cen
 * @Description: 发送消息服务类
 * @Date: create in 2022/12/29 14:17
 */
@Service
@RequiredArgsConstructor
public class SendServiceImpl implements SendService {
    private final ProcessHandler processHandler;

    @Override
    public boolean send(SendRequest sendRequest, boolean templateCrowd) {
        SendTaskBO sendTaskBO = new SendTaskBO();
        sendTaskBO.setMessageTemplateId(sendRequest.getMessageTemplateId());
        //使用无参模板以及备用人群这种情况下为空
        if (sendRequest.getMessageParam() == null){
            sendRequest.setMessageParam(new MessageParam());
        }
        //是否使用备用人群
        sendRequest.getMessageParam().setTemplateCrowd(templateCrowd);

        sendTaskBO.setMessageParamList(Collections.singletonList(sendRequest.getMessageParam()));

        ProcessContext<ProcessModel> context = new ProcessContext<>();
        context.setProcessModel(sendTaskBO);

        processHandler.process(context);
        return true;
    }
}
