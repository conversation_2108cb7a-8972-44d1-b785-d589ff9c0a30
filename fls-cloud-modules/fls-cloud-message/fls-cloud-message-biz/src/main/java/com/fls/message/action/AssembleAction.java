package com.fls.message.action;

import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fls.message.constants.bo.*;
import com.fls.message.constants.dto.MessageParam;
import com.fls.message.constants.dto.MessageWithFileParam;
import com.fls.message.constants.enums.ChannelType;
import com.fls.message.constants.enums.IdType;
import com.fls.message.constants.enums.MessageTypeEnum;
import com.fls.message.entity.MessageTemplate;
import com.fls.message.exception.ProcessException;
import com.fls.message.exception.ProcessExceptionEnum;
import com.fls.message.mapper.MessageTemplateMapper;
import com.fls.message.pipeline.BusinessProcess;
import com.fls.message.pipeline.EmailCustomizeProcess;
import com.fls.message.pipeline.ProcessContext;
import com.fls.message.utils.ContentHolderUtil;
import com.google.common.base.Throwables;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.io.File;
import java.lang.reflect.Field;
import java.util.*;

/**
 * @Author: jiepeng.cen
 * @Description: 拼装参数
 * @Date: create in 2022/12/30 11:10
 */
@Order(2)
@Slf4j
@Component
@RequiredArgsConstructor
public class AssembleAction implements BusinessProcess<SendTaskBO>, EmailCustomizeProcess<SendCustomizeEmailTaskBO> {

    private final MessageTemplateMapper messageTemplateMapper;

    @Override
    public void process(ProcessContext<SendTaskBO> context) {
        SendTaskBO sendTaskBO = context.getProcessModel();
        String messageTemplateId = sendTaskBO.getMessageTemplateId();

        try {
            MessageTemplate messageTemplate = messageTemplateMapper.selectById(messageTemplateId);
            if (messageTemplate == null) {
                throw new ProcessException(ProcessExceptionEnum.TEMPLATE_NOT_FOUND.getCode(), ProcessExceptionEnum.TEMPLATE_NOT_FOUND.getMsg());
            }
            List<TaskInfoBO> taskInfoBOS = assembleTaskInfo(sendTaskBO, messageTemplate);
            sendTaskBO.setTaskInfo(taskInfoBOS);
        } catch (ProcessException p){
            throw p;
        } catch (Exception e) {
            log.error("assemble task fail! templateId:{}, e:{}", messageTemplateId, Throwables.getStackTraceAsString(e));
            throw new ProcessException(ProcessExceptionEnum.SERVICE_ERROR.getCode(), ProcessExceptionEnum.SERVICE_ERROR.getMsg());
        }
    }

    private List<TaskInfoBO> assembleTaskInfo(SendTaskBO sendTaskBO, MessageTemplate messageTemplate){
        List<MessageParam> messageParamList = sendTaskBO.getMessageParamList();
        List<TaskInfoBO> taskInfoBOS = new ArrayList<>();

        for (MessageParam messageParam : messageParamList) {
            //使用模板人群时，传入模板设置的参数
            if (messageParam.getTemplateCrowd()){
                if (StrUtil.isBlank(messageTemplate.getCrowds())){
                    throw new ProcessException(ProcessExceptionEnum.TEMPLATE_CROWD_IS_BLANK.getCode(), ProcessExceptionEnum.TEMPLATE_CROWD_IS_BLANK.getMsg());
                }
                messageParam.setReceiver(messageTemplate.getCrowds());
            }
            TaskInfoBO taskInfoBO = new TaskInfoBO();
            taskInfoBO.setMessageTemplateId(messageTemplate.getId());
            taskInfoBO.setReceiver(new HashSet<>(Arrays.asList(messageParam.getReceiver().split(String.valueOf(StrUtil.C_COMMA)))));
            taskInfoBO.setIdType(messageTemplate.getIdType());
            taskInfoBO.setSendChannel(messageTemplate.getSendChannel());
            taskInfoBO.setMsgType(messageTemplate.getMsgType());
            taskInfoBO.setSendAccount(messageTemplate.getSendAccount());
            try {
                taskInfoBO.setContentModel(getContentModelValue(messageTemplate, messageParam.getVariables()));
            } catch (Exception e) {
                throw new ProcessException(ProcessExceptionEnum.REQUEST_VARIABLES_ERROR.getCode(), ProcessExceptionEnum.REQUEST_VARIABLES_ERROR.getMsg());
            }
            taskInfoBO.setVariables(messageParam.getVariables());
            taskInfoBOS.add(taskInfoBO);
        }
        return taskInfoBOS;
    }

    private ContentModel getContentModelValue(MessageTemplate messageTemplate, Map<String, String> variables){
        // 得到真正的类型
        Integer sendChannel = messageTemplate.getSendChannel();
        Class contentModelClass = ChannelType.getChannelModelClassByCode(sendChannel);

        // 得到内容模板和入参
        JSONObject jsonObject = JSON.parseObject(messageTemplate.getMsgContent());

        //通过反射组装出contentModel
        Field[] fields = ReflectUtil.getFields(contentModelClass);
        ContentModel contentModel = (ContentModel)ReflectUtil.newInstance(contentModelClass);
        for (Field field : fields) {
            String originValue = jsonObject.getString(field.getName());
            if (StrUtil.isNotBlank(originValue)){
                String resultValue = ContentHolderUtil.replacePlaceHolder(originValue, variables);
                Object resultObj = JSONUtil.isTypeJSON(resultValue) ? JSONUtil.toBean(resultValue, field.getType()) : resultValue;
                ReflectUtil.setFieldValue(contentModel, field, resultObj);
            }
        }
        return contentModel;
    }


    //-------------------------------------------邮件自定义封装---------------------------------------------------------
    @Override
    public void customizeEmail(ProcessContext<SendCustomizeEmailTaskBO> context) {
        SendCustomizeEmailTaskBO sendCustomizeEmailTaskBO = context.getProcessModel();
        List<TaskInfoBO> taskInfoBOS = assembleEmailCustomizeTaskInfo(sendCustomizeEmailTaskBO);
        sendCustomizeEmailTaskBO.setTaskInfo(taskInfoBOS);
    }

    private List<TaskInfoBO> assembleEmailCustomizeTaskInfo(SendCustomizeEmailTaskBO sendCustomizeEmailTaskBO){
        MessageTemplate messageTemplate = null;
        // 当入参时传入了模板id,则检验模板是否存在
        if (StrUtil.isNotBlank(sendCustomizeEmailTaskBO.getMessageTemplateId())) {
            messageTemplate = messageTemplateMapper.selectById(sendCustomizeEmailTaskBO.getMessageTemplateId());
            if (messageTemplate == null) {
                throw new ProcessException(ProcessExceptionEnum.TEMPLATE_NOT_FOUND.getCode(), ProcessExceptionEnum.TEMPLATE_NOT_FOUND.getMsg());
            }
        }

        List<MessageWithFileParam> messageParamList = sendCustomizeEmailTaskBO.getMessageParamList();

        List<TaskInfoBO> taskInfoBOS = new ArrayList<>();

        for (MessageWithFileParam messageParam : messageParamList) {
            // 如果模板为空,则消息参数中的自定义内容和主题不能为空
            if ((messageTemplate == null || StrUtil.isBlank(messageTemplate.getTitle()) || StrUtil.isBlank(messageTemplate.getMsgContent()))
                && (StrUtil.isBlank(messageParam.getTitle()) || StrUtil.isBlank(messageParam.getContent()))){
                throw new ProcessException(ProcessExceptionEnum.CLIENT_BAD_PARAMETERS.getCode(), ProcessExceptionEnum.CLIENT_BAD_PARAMETERS.getMsg());
            }
            //使用模板人群时，传入模板设置的参数
            if (messageTemplate != null && messageParam.getTemplateCrowd()){
                if (StrUtil.isBlank(messageTemplate.getCrowds())){
                    throw new ProcessException(ProcessExceptionEnum.TEMPLATE_CROWD_IS_BLANK.getCode(), ProcessExceptionEnum.TEMPLATE_CROWD_IS_BLANK.getMsg());
                }
                messageParam.setReceiver(messageTemplate.getCrowds());
            }
            TaskInfoBO taskInfoBO = new TaskInfoBO();
            taskInfoBO.setMessageTemplateId(messageTemplate != null ? messageTemplate.getId() : null);
            taskInfoBO.setReceiver(new HashSet<>(Arrays.asList(messageParam.getReceiver().split(String.valueOf(StrUtil.C_COMMA)))));
            // 填入邮件类型
            taskInfoBO.setIdType(IdType.EMAIL.getCode());
            taskInfoBO.setSendChannel(ChannelType.EMAIL.getCode());

            taskInfoBO.setMsgType(messageTemplate != null ? messageTemplate.getMsgType() : MessageTypeEnum.NOTICE.getCode());
            taskInfoBO.setSendAccount(messageTemplate != null ? messageTemplate.getSendAccount() : sendCustomizeEmailTaskBO.getAccountId());
            // 当存在模板时，需要对可替换内容把指填上
            EmailContentModel contentModel;
            if (StrUtil.isNotBlank(messageParam.getTitle()) && StrUtil.isNotBlank(messageParam.getContent())){
                // 封装
                EmailContentModel emailContentModel = new EmailContentModel();
                emailContentModel.setContent(messageParam.getContent());
                emailContentModel.setTitle(messageParam.getTitle());
                contentModel = emailContentModel;
            } else {
                try {
                    ContentModel contentModelValue = getContentModelValue(messageTemplate, messageParam.getVariables());
                    contentModel = (EmailContentModel) contentModelValue;
                } catch (Exception e) {
                    throw new ProcessException(ProcessExceptionEnum.REQUEST_VARIABLES_ERROR.getCode(), ProcessExceptionEnum.REQUEST_VARIABLES_ERROR.getMsg());
                }
            }

            if (messageParam.getFiles() != null && messageParam.getFiles().size() > 0){
                contentModel.setFiles(messageParam.getFiles().toArray(new File[0]));
            }

            taskInfoBO.setContentModel(contentModel);
            taskInfoBO.setVariables(messageParam.getVariables());
            taskInfoBOS.add(taskInfoBO);
        }
        return taskInfoBOS;
    }
}
