package com.fls.message.action;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.fls.message.constants.RabbitMqConstant;
import com.fls.message.constants.bo.SendTaskBO;
import com.fls.message.exception.ProcessException;
import com.fls.message.exception.ProcessExceptionEnum;
import com.fls.message.pipeline.BusinessProcess;
import com.fls.message.pipeline.ProcessContext;
import com.google.common.base.Throwables;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * @Author: jiepeng.cen
 * @Description: 将消息发送到MQ
 * @Date: create in 2023/1/2 10:08
 */
@Order(4)
@Slf4j
@Component
@RequiredArgsConstructor
public class SendMqAction implements BusinessProcess<SendTaskBO> {

    private final RabbitTemplate rabbitTemplate;

    @Override
    public void process(ProcessContext<SendTaskBO> context) {
        SendTaskBO sendTaskBO = context.getProcessModel();
        String message = JSON.toJSONString(sendTaskBO.getTaskInfo());
        try {
            rabbitTemplate.convertAndSend(RabbitMqConstant.MESSAGE_TRANS_EXCHANGE, RabbitMqConstant.MESSAGE_TRANS_ROUTE, message);
        } catch (Exception e) {
            log.error("send message fail! e:{},params:{}", Throwables.getStackTraceAsString(e)
                , JSON.toJSONString(CollUtil.getFirst(sendTaskBO.getTaskInfo().listIterator())));
            throw new ProcessException(ProcessExceptionEnum.SERVICE_ERROR.getCode(), ProcessExceptionEnum.SERVICE_ERROR.getMsg());
        }
    }
}
