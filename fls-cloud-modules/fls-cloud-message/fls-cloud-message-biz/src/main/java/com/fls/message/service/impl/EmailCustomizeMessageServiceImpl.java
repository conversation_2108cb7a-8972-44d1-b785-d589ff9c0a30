package com.fls.message.service.impl;

import cn.hutool.core.util.StrUtil;
import com.fls.message.constants.bo.SendCustomizeEmailTaskBO;
import com.fls.message.constants.dto.MessageWithFileParam;
import com.fls.message.constants.dto.SendRequest;
import com.fls.message.exception.ProcessException;
import com.fls.message.exception.ProcessExceptionEnum;
import com.fls.message.pipeline.EmailCustomizeProcessHandler;
import com.fls.message.pipeline.ProcessContext;
import com.fls.message.pipeline.ProcessModel;
import com.fls.message.service.EmailCustomizeMessageService;
import com.fls.message.utils.MultipartFileUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * @Author: jiepeng.cen
 * @Description:
 * @Date: create in 2023/2/8 8:54
 */
@Service
@RequiredArgsConstructor
public class EmailCustomizeMessageServiceImpl implements EmailCustomizeMessageService {

    private final EmailCustomizeProcessHandler processHandler;

    @Override
    public boolean sendByTemplate(SendRequest sendParams, MultipartFile[] files, boolean templateCrowd) {
        SendCustomizeEmailTaskBO sendCustomizeEmailTaskBO = new SendCustomizeEmailTaskBO();
        sendCustomizeEmailTaskBO.setMessageTemplateId(sendParams.getMessageTemplateId());
        MessageWithFileParam messageWithFileParam = new MessageWithFileParam();

        List<File> fileList = new ArrayList<>();
        // 暂存文件
        if (files != null && files.length > 0){
            for (MultipartFile file : files) {
                String tempFile = MultipartFileUtil.storageTempFile(file);
                if (tempFile == null){
                    throw new ProcessException(ProcessExceptionEnum.FILE_UPLOAD_ERROR.getCode(), ProcessExceptionEnum.FILE_UPLOAD_ERROR.getMsg());
                }
                fileList.add(new File(tempFile));
            }
        }

        // 封装参数
        messageWithFileParam.setTemplateCrowd(templateCrowd);
        messageWithFileParam.setReceiver(sendParams.getMessageParam() != null
            && StrUtil.isNotBlank(sendParams.getMessageParam().getReceiver()) ? sendParams.getMessageParam().getReceiver() : null);
        messageWithFileParam.setVariables(sendParams.getMessageParam() != null && sendParams.getMessageParam().getVariables() != null
            && !sendParams.getMessageParam().getVariables().isEmpty() ? sendParams.getMessageParam().getVariables() : null);

        messageWithFileParam.setFiles(fileList);

        sendCustomizeEmailTaskBO.setMessageParamList(Collections.singletonList(messageWithFileParam));

        ProcessContext<ProcessModel> context = new ProcessContext<>();
        context.setProcessModel(sendCustomizeEmailTaskBO);

        processHandler.process(context);
        return true;
    }

    @Override
    public boolean sendByCustomize(String templateId, String accountId, String title, String content, String receiver, MultipartFile[] files, boolean templateCrowd) {
        //封装参数
        SendCustomizeEmailTaskBO sendCustomizeEmailTaskBO = new SendCustomizeEmailTaskBO();
        sendCustomizeEmailTaskBO.setMessageTemplateId(templateId);
        sendCustomizeEmailTaskBO.setAccountId(accountId);

        List<File> fileList = new ArrayList<>();
        // 暂存文件
        if (files != null && files.length > 0){
            for (MultipartFile file : files) {
                String tempFile = MultipartFileUtil.storageTempFile(file);
                if (tempFile == null){
                    throw new ProcessException(ProcessExceptionEnum.FILE_UPLOAD_ERROR.getCode(), ProcessExceptionEnum.FILE_UPLOAD_ERROR.getMsg());
                }
                fileList.add(new File(tempFile));
            }
        }

        //封装消息参数
        MessageWithFileParam messageWithFileParam = new MessageWithFileParam();
        messageWithFileParam.setTemplateCrowd(templateCrowd);
        messageWithFileParam.setReceiver(receiver);
        messageWithFileParam.setTitle(title);
        messageWithFileParam.setContent(content);
        messageWithFileParam.setFiles(fileList);

        sendCustomizeEmailTaskBO.setMessageParamList(Collections.singletonList(messageWithFileParam));
        ProcessContext<ProcessModel> context = new ProcessContext<>();
        context.setProcessModel(sendCustomizeEmailTaskBO);

        processHandler.process(context);
        return true;
    }
}
