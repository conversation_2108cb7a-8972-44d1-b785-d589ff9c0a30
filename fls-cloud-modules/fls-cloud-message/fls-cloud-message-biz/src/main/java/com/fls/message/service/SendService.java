package com.fls.message.service;

import com.fls.message.constants.dto.SendRequest;

/**
 * @Author: jiepeng.cen
 * @Description: 发送信息服务层接口
 * @Date: create in 2022/12/29 14:04
 */
public interface SendService {

    /**
     * 单信息发送(使用备用人群列表)
     * @param sendRequest 发送请求参数
     * @param templateCrowd 是否使用模板备用接收者
     * @return boolean
     */
    boolean send(SendRequest sendRequest, boolean templateCrowd);
}
