package com.fls.message.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.fls.message.constants.dto.MessageTemplateDTO;
import com.fls.message.constants.query.MessageTemplateQuery;
import com.fls.message.entity.MessageTemplate;

/**
 * @Author: jiepeng.cen
 * @Description:
 * @Date: create in 2022/12/29 9:08
 */
public interface IMessageTemplateService extends IService<MessageTemplate> {
    /**
     * 新增消息模板
     * @param messageTemplateDTO 消息模板信息
     * @return boolean
     */
    boolean add(MessageTemplateDTO messageTemplateDTO);

    /**
     * 分页查询
     * @param page 分页参数
     * @param query 查询参数
     * @return Page<MessageTemplate>
     */
    Page<MessageTemplate> pageByQuery(Page<MessageTemplate> page, MessageTemplateQuery query);

    /**
     * 根据id集合删除
     * @param ids id集合
     * @return boolean
     */
    boolean delete(String ids);

    /**
     * 编辑消息模板
     * @param messageTemplateDTO 消息模板信息
     * @return boolean
     */
    boolean edit(MessageTemplateDTO messageTemplateDTO);
}
