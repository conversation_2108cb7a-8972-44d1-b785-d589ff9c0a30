package com.fls.message.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fls.common.core.constant.CommonConstants;
import com.fls.common.satoken.utils.LoginHelper;
import com.fls.message.constants.dto.ChannelAccountDTO;
import com.fls.message.constants.query.ChannelAccountQuery;
import com.fls.message.entity.ChannelAccount;
import com.fls.message.mapper.ChannelAccountMapper;
import com.fls.message.service.IChannelAccountService;
import com.fls.upms.api.model.LoginUser;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: jiepeng.cen
 * @Description: 渠道账号服务层实现类
 * @Date: create in 2022/12/24 17:34
 */
@Service
@RequiredArgsConstructor
public class ChannelAccountServiceImpl extends ServiceImpl<ChannelAccountMapper, ChannelAccount> implements IChannelAccountService {
    @Override
    public boolean add(ChannelAccountDTO account) {
        ChannelAccount channelAccount = new ChannelAccount();
        BeanUtils.copyProperties(account, channelAccount);
        channelAccount.setId(IdUtil.randomUUID());
        // 生成随机5位大写字符串
        channelAccount.setCode(RandomUtil.randomStringUpper(5));
        // 接入人员
        LoginUser loginUser = (LoginUser) (StpUtil.getTokenSession().get(LoginHelper.LOGIN_USER_KEY));
        channelAccount.setCreateBy(loginUser != null ? loginUser.getIdUser() : "");
        channelAccount.setCreateByName(loginUser != null ? loginUser.getName() : "");

        return this.save(channelAccount);
    }

    @Override
    public List<ChannelAccount> queryByChannel(Integer channelCode) {
        return this.list(Wrappers.<ChannelAccount>lambdaQuery().eq(ChannelAccount::getSendChannel, channelCode));
    }

    @Override
    public boolean delete(String ids) {
        // 接入人员
        LoginUser loginUser = (LoginUser) (StpUtil.getTokenSession().get(LoginHelper.LOGIN_USER_KEY));
        List<String> idList = Arrays.stream(ids.split(StrUtil.COMMA)).collect(Collectors.toList());
        return this.update(Wrappers.<ChannelAccount>lambdaUpdate()
            .set(ChannelAccount::getUpdateBy, loginUser != null ? loginUser.getIdUser() : "")
            .set(ChannelAccount::getUpdateByName, loginUser != null ? loginUser.getName() : "")
            .set(ChannelAccount::getDelFlag, CommonConstants.DELETE_FLAG_IS_DELETED)
            .in(ChannelAccount::getId, idList));
    }

    @Override
    public Page<ChannelAccount> pageByQuery(Page<ChannelAccount> page, ChannelAccountQuery query) {
        return this.page(page, Wrappers.<ChannelAccount>lambdaQuery()
            .like(StrUtil.isNotBlank(query.getTitle()), ChannelAccount::getTitle, query.getTitle())
            .eq(query.getSendChannel() != null, ChannelAccount::getSendChannel, query.getSendChannel()));
    }

    @Override
    public boolean edit(ChannelAccountDTO account) {
        // 接入人员
        LoginUser loginUser = (LoginUser) (StpUtil.getTokenSession().get(LoginHelper.LOGIN_USER_KEY));

        ChannelAccount channelAccount = new ChannelAccount();
        BeanUtils.copyProperties(account, channelAccount);

        channelAccount.setUpdateBy(loginUser != null ? loginUser.getIdUser() : "");
        channelAccount.setUpdateByName(loginUser != null ? loginUser.getName() : "");
        return this.updateById(channelAccount);
    }
}
