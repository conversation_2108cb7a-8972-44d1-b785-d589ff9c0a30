package com.fls.message.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.fls.message.constants.dto.ChannelAccountDTO;
import com.fls.message.constants.query.ChannelAccountQuery;
import com.fls.message.entity.ChannelAccount;

import java.util.List;

/**
 * @Author: jiepeng.cen
 * @Description: 渠道账号服务层接口
 * @Date: create in 2022/12/24 17:30
 */
public interface IChannelAccountService extends IService<ChannelAccount> {
    /**
     * 增加对应渠道账号
     * @param account 账号信息
     * @return boolean
     */
    boolean add(ChannelAccountDTO account);

    /**
     * 根据关联渠道获取账号列表
     * @param channelCode 渠道code
     * @return List<ChannelAccount>
     */
    List<ChannelAccount> queryByChannel(Integer channelCode);

    /**
     * 根据id集合串删除
     * @param ids id集合串
     * @return boolean
     */
    boolean delete(String ids);

    /**
     * 条件查询
     * @param page 分页查询
     * @param query 条件参数
     * @return Page<ChannelAccount>
     */
    Page<ChannelAccount> pageByQuery(Page<ChannelAccount> page, ChannelAccountQuery query);

    /**
     * 编辑渠道账号信息
     * @param account 账号信息
     * @return boolean
     */
    boolean edit(ChannelAccountDTO account);
}
