/*
package com.fls.message.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fls.common.core.constant.CommonConstants;
import com.fls.common.satoken.utils.LoginHelper;
import com.fls.message.constants.dto.ChannelConfigDTO;
import com.fls.message.constants.query.ChannelConfigQuery;
import com.fls.message.entity.ChannelConfig;
import com.fls.message.mapper.ChannelConfigMapper;
import com.fls.message.service.IChannelConfigService;
import com.fls.upms.api.model.LoginUser;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;

*/
/**
 * @Author: jiepeng.cen
 * @Description: 消息渠道服务层实现类
 * @Date: create in 2022/12/24 14:55
 *//*

@Service
@RequiredArgsConstructor
public class ChannelConfigServiceImpl extends ServiceImpl<ChannelConfigMapper, ChannelConfig> implements IChannelConfigService {
    @Override
    public boolean add(ChannelConfigDTO config) {
        ChannelConfig configToDB = new ChannelConfig();
        BeanUtils.copyProperties(config, configToDB);
        configToDB.setId(IdUtil.randomUUID());
        //todo 各渠道验证
        return this.save(configToDB);
    }

    @Override
    public boolean edit(ChannelConfigDTO config) {
        ChannelConfig configToDB = new ChannelConfig();
        BeanUtils.copyProperties(config, configToDB);
        return this.updateById(configToDB);
    }

    @Override
    public Page<ChannelConfig> pageByQuery(Page<ChannelConfig> page, ChannelConfigQuery query) {
        return this.page(page, Wrappers.<ChannelConfig>lambdaQuery()
            .eq(query.getChannelType() != null, ChannelConfig::getChannelType, query.getChannelType())
            .like(StrUtil.isNotBlank(query.getTitle()), ChannelConfig::getTitle, query.getTitle()));
    }

    @Override
    public boolean deleteByIds(List<String> idList) {
        // 接入人员
        LoginUser loginUser = (LoginUser) (StpUtil.getTokenSession().get(LoginHelper.LOGIN_USER_KEY));

        return this.update(Wrappers.<ChannelConfig>lambdaUpdate()
            .set(ChannelConfig::getUpdateBy, loginUser != null ? loginUser.getName() : "")
            .set(ChannelConfig::getDelFlag, CommonConstants.DELETE_FLAG_IS_DELETED)
            .in(ChannelConfig::getId, idList));
    }
}
*/
