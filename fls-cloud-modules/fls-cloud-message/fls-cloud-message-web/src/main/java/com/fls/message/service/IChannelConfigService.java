/*
package com.fls.message.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.fls.message.constants.dto.ChannelConfigDTO;
import com.fls.message.constants.query.ChannelConfigQuery;
import com.fls.message.entity.ChannelConfig;

import java.util.List;

*/
/**
 * @Author: jiepeng.cen
 * @Description: 渠道配置服务层接口
 * @Date: create in 2022/12/24 14:54
 *//*

public interface IChannelConfigService  extends IService<ChannelConfig> {
    */
/**
     * 添加渠道配置
     * @param config 配置信息
     * @return boolean
     *//*

    boolean add(ChannelConfigDTO config);

    */
/**
     * 修改渠道配置
     * @param config 配置信息
     * @return boolean
     *//*

    boolean edit(ChannelConfigDTO config);

    */
/**
     * 按条件分页查询
     * @param page 分页参数
     * @param query 查询参数
     * @return Page<ChannelConfig>
     *//*

    Page<ChannelConfig> pageByQuery(Page<ChannelConfig> page, ChannelConfigQuery query);

    */
/**
     * 删除
     * @param idList id集合
     * @return boolean
     *//*

    boolean deleteByIds(List<String> idList);
}
*/
