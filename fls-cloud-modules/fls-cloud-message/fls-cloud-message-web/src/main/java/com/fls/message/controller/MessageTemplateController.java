package com.fls.message.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fls.common.core.domain.ResponseData;
import com.fls.message.constants.dto.MessageTemplateDTO;
import com.fls.message.constants.query.MessageTemplateQuery;
import com.fls.message.entity.MessageTemplate;
import com.fls.message.service.IMessageTemplateService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * @Author: jiepeng.cen
 * @Description: 消息模板
 * @Date: create in 2022/12/28 16:42
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/template")
public class MessageTemplateController {
    private final IMessageTemplateService messageTemplateService;

    @PostMapping("/save")
    public ResponseData<Boolean> save(@Valid @RequestBody MessageTemplateDTO messageTemplateDTO){
        return messageTemplateService.add(messageTemplateDTO) ? ResponseData.ok() : ResponseData.fail();
    }

    @PutMapping("/edit")
    public ResponseData<Boolean> edit(@Valid @RequestBody MessageTemplateDTO messageTemplateDTO){
        if (StrUtil.isBlank(messageTemplateDTO.getId())){
            return  ResponseData.fail("主键不能为空");
        }
        return messageTemplateService.edit(messageTemplateDTO) ? ResponseData.ok() : ResponseData.fail();
    }

    @GetMapping("/page")
    public ResponseData<Page<MessageTemplate>> pageByQuery(Page<MessageTemplate> page, MessageTemplateQuery query){
        return ResponseData.ok(messageTemplateService.pageByQuery(page, query));
    }

    @GetMapping("/get")
    public ResponseData<MessageTemplate> queryById(String id){
        return ResponseData.ok(messageTemplateService.getById(id));
    }

    @DeleteMapping("/remove")
    public ResponseData remove(String ids){
        return messageTemplateService.delete(ids) ? ResponseData.ok() : ResponseData.fail();
    }
}
