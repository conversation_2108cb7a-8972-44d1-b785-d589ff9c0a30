package com.fls.message;

import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.metrics.buffering.BufferingApplicationStartup;

/**
 * @Author: jiepeng.cen
 * @Description: 消息中心web模块启动类
 * @Date: create in 2023/1/4 10:13
 */
@EnableDubbo
@SpringBootApplication
public class FlsMessageWebApplication {
    public static void main(String[] args) {
        SpringApplication application = new SpringApplication(FlsMessageWebApplication.class);
        application.setApplicationStartup(new BufferingApplicationStartup(2048));
        application.run(args);
    }
}
