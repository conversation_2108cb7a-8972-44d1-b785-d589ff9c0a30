/*
package com.fls.message.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fls.common.core.domain.ResponseData;
import com.fls.message.constants.dto.ChannelConfigDTO;
import com.fls.message.constants.query.ChannelConfigQuery;
import com.fls.message.entity.ChannelConfig;
import com.fls.message.service.IChannelConfigService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;


*/
/**
 * @Author: jiepeng.cen
 * @Description: 消息渠道配置
 * @Date: create in 2022/12/24 14:59
 *//*


@RestController
@RequiredArgsConstructor
@RequestMapping("/channel")
public class ChannelConfigController {

    private final IChannelConfigService channelConfigService;

    @GetMapping("/page")
    public ResponseData<Page<ChannelConfig>> pageList(Page<ChannelConfig> page, ChannelConfigQuery query){
        return ResponseData.ok(channelConfigService.pageByQuery(page, query));
    }

    @PostMapping("/save")
    public ResponseData save(@Valid @RequestBody ChannelConfigDTO config){
        return channelConfigService.add(config) ? ResponseData.ok() : ResponseData.fail();
    }

    @PutMapping("/update")
    public ResponseData update(@Valid @RequestBody ChannelConfigDTO config){
        return channelConfigService.edit(config) ? ResponseData.ok() : ResponseData.fail();
    }

    @DeleteMapping("/remove")
    public ResponseData remove(String ids){
        if (StrUtil.isNotBlank(ids)) {
            List<String> idList = Arrays.stream(ids.split(StrUtil.COMMA)).collect(Collectors.toList());
            return channelConfigService.deleteByIds(idList) ? ResponseData.ok() : ResponseData.fail();
        }
        return ResponseData.fail("请校验参数是否正确");
    }
}*/
