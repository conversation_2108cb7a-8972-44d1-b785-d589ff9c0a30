package com.fls.message.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fls.common.core.domain.ResponseData;
import com.fls.message.constants.dto.ChannelAccountDTO;
import com.fls.message.constants.query.ChannelAccountQuery;
import com.fls.message.entity.ChannelAccount;
import com.fls.message.service.IChannelAccountService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * @Author: jiepeng.cen
 * @Description: 账号配置
 * @Date: create in 2022/12/28 8:49
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/account")
public class ChannelAccountController {

    private final IChannelAccountService channelAccountService;

    @PostMapping("/save")
    public ResponseData save(@Valid @RequestBody ChannelAccountDTO account){
        return ResponseData.ok(channelAccountService.add(account));
    }

    @PutMapping("/edit")
    public ResponseData edit(@Valid @RequestBody ChannelAccountDTO account){
        if (StrUtil.isBlank(account.getId())){
            return ResponseData.fail("主键不能为空");
        }
        return ResponseData.ok(channelAccountService.edit(account));
    }

    @GetMapping("/queryByChannel")
    public ResponseData<List<ChannelAccount>> queryByChannel(Integer channelCode){
        return ResponseData.ok(channelAccountService.queryByChannel(channelCode));
    }

    @DeleteMapping("/delete")
    public ResponseData remove(String ids){
        if (StrUtil.isBlank(ids)){
            return ResponseData.fail("传入参数有误");
        }
        return channelAccountService.delete(ids) ? ResponseData.ok() : ResponseData.fail();
    }

    @GetMapping("/page")
    public ResponseData<Page<ChannelAccount>> pageByQuery(Page<ChannelAccount> page, ChannelAccountQuery query){
        return ResponseData.ok(channelAccountService.pageByQuery(page, query));
    }
}
