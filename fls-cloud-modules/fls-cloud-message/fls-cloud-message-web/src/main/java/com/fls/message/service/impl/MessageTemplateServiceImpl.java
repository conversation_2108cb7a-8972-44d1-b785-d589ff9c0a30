package com.fls.message.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fls.common.core.constant.CommonConstants;
import com.fls.common.satoken.utils.LoginHelper;
import com.fls.message.constants.dto.MessageTemplateDTO;
import com.fls.message.constants.query.MessageTemplateQuery;
import com.fls.message.entity.MessageTemplate;
import com.fls.message.mapper.MessageTemplateMapper;
import com.fls.message.service.IMessageTemplateService;
import com.fls.upms.api.model.LoginUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: jiepeng.cen
 * @Description:
 * @Date: create in 2022/12/29 9:09
 */
@Slf4j
@Service
public class MessageTemplateServiceImpl extends ServiceImpl<MessageTemplateMapper, MessageTemplate> implements IMessageTemplateService {
    @Override
    public boolean add(MessageTemplateDTO messageTemplateDTO) {
        MessageTemplate messageTemplate = new MessageTemplate();
        BeanUtils.copyProperties(messageTemplateDTO, messageTemplate);
        messageTemplate.setId(IdUtil.randomUUID());

        // 接入人员
        LoginUser loginUser = (LoginUser) (StpUtil.getTokenSession().get(LoginHelper.LOGIN_USER_KEY));
        messageTemplate.setCreateBy(loginUser != null ? loginUser.getIdUser() : "");
        messageTemplate.setCreateByName(loginUser != null ? loginUser.getName() : "");
        return this.save(messageTemplate);
    }

    @Override
    public Page<MessageTemplate> pageByQuery(Page<MessageTemplate> page, MessageTemplateQuery query) {
        return this.page(page, Wrappers.<MessageTemplate>lambdaQuery()
            .like(StrUtil.isNotBlank(query.getName()), MessageTemplate::getTitle, query.getName())
            .eq(query.getMsgType() != null, MessageTemplate::getMsgType, query.getMsgType())
            .eq(StrUtil.isNotBlank(query.getSendChannel()), MessageTemplate::getSendChannel, query.getSendChannel()));
    }

    @Override
    public boolean delete(String ids) {
        // 接入人员
        LoginUser loginUser = (LoginUser) (StpUtil.getTokenSession().get(LoginHelper.LOGIN_USER_KEY));

        List<String> idList = Arrays.stream(ids.split(StrUtil.COMMA)).collect(Collectors.toList());
        return this.update(Wrappers.<MessageTemplate>lambdaUpdate()
            .set(MessageTemplate::getUpdateBy, loginUser != null ? loginUser.getName() : "")
            .set(MessageTemplate::getUpdateByName, loginUser != null ? loginUser.getName() : "")
            .set(MessageTemplate::getDelFlag, CommonConstants.DELETE_FLAG_IS_DELETED)
            .in(MessageTemplate::getId, idList));
    }

    @Override
    public boolean edit(MessageTemplateDTO messageTemplateDTO) {
        // 接入人员
        LoginUser loginUser = (LoginUser) (StpUtil.getTokenSession().get(LoginHelper.LOGIN_USER_KEY));

        MessageTemplate messageTemplate = new MessageTemplate();
        BeanUtils.copyProperties(messageTemplateDTO, messageTemplate);

        messageTemplate.setUpdateBy(loginUser != null ? loginUser.getIdUser() : "");
        messageTemplate.setUpdateByName(loginUser != null ? loginUser.getName() : "");
        return this.updateById(messageTemplate);
    }
}
