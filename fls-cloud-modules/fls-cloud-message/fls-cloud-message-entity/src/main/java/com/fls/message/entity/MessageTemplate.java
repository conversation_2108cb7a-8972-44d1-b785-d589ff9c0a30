package com.fls.message.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Author: jiepeng.cen
 * @Description: 消息模板
 * @Date: create in 2022/12/28 14:09
 */
@Data
@TableName("t_message_template")
public class MessageTemplate implements Serializable {
    @TableId(type = IdType.INPUT)
    private String id;

    /**
     * 模板标题
     */
    private String title;

    /**
     * 备用发送人群
     */
    private String crowds;

    /**
     * 发送的Id类型
     */
    private Integer idType;

    /**
     * 发送渠道
     */
    private Integer sendChannel;

    /**
     * 消息类型
     */
    private Integer msgType;

    /**
     * 渠道模板信息
     */
    private String msgContent;

    /**
     * 发送账号（邮件下可有多个发送账号、短信可有多个发送账号..）
     */
    private String sendAccount;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建人姓名
     */
    private String createByName;

    /**
     * 最后一次修改人
     */
    private String updateBy;

    /**
     * 最后一次修改人姓名
     */
    private String updateByName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 删除标识（0：否 1：是）
     */
    @TableLogic
    private Integer delFlag;
}
