/*
package com.fls.message.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

*/
/**
 * 消息渠道配置
 *//*

@Data
@TableName("t_message_channel_config")
public class ChannelConfig {

    @TableId(type = IdType.INPUT)
    private String id;

    */
/**
     * 渠道标题
     *//*

    private String title;

    */
/**
     * 渠道类型
     *//*

    private Integer channelType;

    */
/**
     * 配置详情
     *//*

    private String configDetail;

    */
/**
     * 启用状态
     *//*

    private Integer status;

    */
/**
     * 创建人
     *//*

    private String createBy;

    */
/**
     * 最后一次修改人
     *//*

    private String updateBy;

    */
/**
     * 创建时间
     *//*

    private Date createTime;

    */
/**
     * 修改时间
     *//*

    private Date updateTime;

    */
/**
     * 删除标识（0：否 1：是）
     *//*

    @TableLogic
    private Integer delFlag;
}
*/
