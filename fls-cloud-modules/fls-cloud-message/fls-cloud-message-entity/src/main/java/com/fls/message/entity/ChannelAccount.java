package com.fls.message.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * @Author: jiepeng.cen
 * @Description: 渠道账号
 * @Date: create in 2022/12/24 17:05
 */
@Data
@TableName("t_message_channel_account")
public class ChannelAccount {
    @TableId(type = IdType.INPUT)
    private String id;

    /**
     * 标题
     */
    private String title;

    /**
     * 使用code，由程序生成
     */
    private String code;

    /**
     * 关联渠道
     */
    private Integer sendChannel;

    /**
     * 账号配置
     */
    private String accountConfig;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建人姓名
     */
    private String createByName;

    /**
     * 最后一次修改人
     */
    private String updateBy;

    /**
     * 最后一次修改人姓名
     */
    private String updateByName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 删除标识（0：否 1：是）
     */
    @TableLogic
    private Integer delFlag;
}
