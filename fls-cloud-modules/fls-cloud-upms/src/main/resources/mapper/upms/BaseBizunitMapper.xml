<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fls.upms.mapper.BaseBizunitMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.fls.upms.entity.BaseBizunit">
        <id column="id_bizunit" property="idBizunit" />
        <result column="name" property="name" />
        <result column="display_order" property="displayOrder" />
        <result column="innercode" property="innercode" />
        <result column="bizunit_type" property="bizunitType" />
        <result column="begin_date" property="beginDate" />
        <result column="end_date" property="endDate" />
        <result column="id_group" property="idGroup" />
        <result column="status" property="status" />
        <result column="create_time" property="createTime" />
        <result column="creator" property="creator" />
        <result column="disable_time" property="disableTime" />
        <result column="ts" property="ts" />
        <result column="delete_flag" property="deleteFlag" />
        <result column="id_accunit" property="idAccunit" />
        <result column="id_org" property="idOrg" />
        <result column="area_name" property="areaName" />
        <result column="id_manager" property="idManager" />
        <result column="id_manager_maintenance" property="idManagerMaintenance" />
        <result column="checking_city_dep" property="checkingCityDep" />
        <result column="appunitcode" property="appunitcode" />
        <result column="id_link_doc" property="idLinkDoc" />
        <result column="bill_code" property="billCode" />
        <result column="usecompostcode" property="usecompostcode" />
        <result column="usecomplace" property="usecomplace" />
        <result column="usecomtel" property="usecomtel" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id_bizunit, name, display_order, innercode, bizunit_type, begin_date, end_date, id_group, status, create_time, creator, disable_time, ts, delete_flag, id_accunit, id_org, area_name, id_manager, id_manager_maintenance, checking_city_dep, appunitcode, id_link_doc, bill_code, usecompostcode, usecomplace, usecomtel
    </sql>

</mapper>
