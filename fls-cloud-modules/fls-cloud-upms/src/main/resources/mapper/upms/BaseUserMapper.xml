<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fls.upms.mapper.BaseUserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.fls.upms.entity.BaseUser">
        <id column="id_user" property="idUser" />
        <result column="code" property="code" />
        <result column="name" property="name" />
        <result column="password" property="password" />
        <result column="identity_type" property="identityType" />
        <result column="id_identity" property="idIdentity" />
        <result column="pk_user" property="pkUser" />
        <result column="pk_base_doc" property="pkBaseDoc" />
        <result column="status" property="status" />
        <result column="create_time" property="createTime" />
        <result column="creator" property="creator" />
        <result column="disable_time" property="disableTime" />
        <result column="ts" property="ts" />
        <result column="delete_flag" property="deleteFlag" />
        <result column="salt" property="salt" />
        <result column="id_address" property="idAddress" />
        <result column="maintenance_flag" property="maintenanceFlag" />
        <result column="id_inneraddress" property="idInneraddress" />
        <result column="miop_login_token" property="miopLoginToken" />
        <result column="miop_login_token_ts" property="miopLoginTokenTs" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id_user, code, name, password, identity_type, id_identity, pk_user, pk_base_doc, status, create_time, creator, disable_time, ts, delete_flag, salt, id_address, maintenance_flag, id_inneraddress, miop_login_token, miop_login_token_ts
    </sql>
    <select id="getUserInfoByIdPerson" resultType="com.fls.upms.entity.BaseUser"
            resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/> FROM t_base_user WHERE id_identity=#{idPerson}
    </select>

</mapper>
