<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fls.upms.mapper.BaseRoleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.fls.upms.entity.BaseRole">
        <id column="id_role" property="idRole" />
        <result column="code" property="code" />
        <result column="name" property="name" />
        <result column="memo" property="memo" />
        <result column="role_type" property="roleType" />
        <result column="status" property="status" />
        <result column="create_time" property="createTime" />
        <result column="creator" property="creator" />
        <result column="disable_time" property="disableTime" />
        <result column="ts" property="ts" />
        <result column="delete_flag" property="deleteFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id_role, code, name, memo, role_type, status, create_time, creator, disable_time, ts, delete_flag
    </sql>

    <select id="getLoginRoles" resultType="com.fls.upms.entity.BaseRole">
        SELECT br.id_role idRole, br.code, br.name
        FROM t_base_role br
        LEFT JOIN t_base_userrole bur on bur.id_role = br.id_role
        where bur.id_user = #{userId} and bur.`status` = '2' and br.`status` = '2'
    </select>

</mapper>
