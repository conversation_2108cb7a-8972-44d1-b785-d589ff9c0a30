<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fls.upms.mapper.BaseRoledataMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.fls.upms.entity.BaseRoledata">
        <id column="id_roledata" property="idRoledata" />
        <result column="id_org" property="idOrg" />
        <result column="id_department" property="idDepartment" />
        <result column="id_postseries" property="idPostseries" />
        <result column="id_post" property="idPost" />
        <result column="id_role" property="idRole" />
        <result column="status" property="status" />
        <result column="create_time" property="createTime" />
        <result column="creator" property="creator" />
        <result column="disable_time" property="disableTime" />
        <result column="ts" property="ts" />
        <result column="delete_flag" property="deleteFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id_roledata, id_org, id_department, id_postseries, id_post, id_role, status, create_time, creator, disable_time, ts, delete_flag
    </sql>

</mapper>
