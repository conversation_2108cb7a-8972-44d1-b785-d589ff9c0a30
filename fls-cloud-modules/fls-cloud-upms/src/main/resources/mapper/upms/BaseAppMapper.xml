<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fls.upms.mapper.BaseAppMapper">

    <resultMap type="com.fls.upms.entity.BaseApp" id="baseAppMap">
        <result property="idBaseApp" column="id_base_app"/>
        <result property="code" column="code"/>
        <result property="name" column="name"/>
        <result property="appType" column="app_type"/>
        <result property="memo" column="memo"/>
        <result property="url" column="url"/>
        <result property="urlIndex" column="url_index"/>
        <result property="permission" column="permission"/>
        <result property="sort" column="sort"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="creator" column="creator"/>
        <result property="disableTime" column="disable_time"/>
        <result property="deleteFlag" column="delete_flag"/>
        <result property="ts" column="ts"/>
    </resultMap>

</mapper>
