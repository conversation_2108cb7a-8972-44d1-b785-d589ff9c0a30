<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fls.upms.mapper.BaseRoleDataBizUnitMapper">

    <select id="getUnitByIdOrgs" resultType="com.fls.upms.entity.dto.BizUnitResult">
        select id_bizunit as idUnit,id_org as idOrg
        from t_base_bizunit
        where status ='2' and delete_flag='0' and id_org in
        <foreach collection="orgIds" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
    </select>
</mapper>
