<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fls.upms.mapper.BaseOrgMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.fls.upms.entity.BaseOrg">
        <id column="id_org" property="idOrg" />
        <result column="code" property="code" />
        <result column="innercode" property="innercode" />
        <result column="name" property="name" />
        <result column="shortname" property="shortname" />
        <result column="pk_org" property="pkOrg" />
        <result column="purchase_flag" property="purchaseFlag" />
        <result column="sales_flag" property="salesFlag" />
        <result column="traffic_flag" property="trafficFlag" />
        <result column="finance_flag" property="financeFlag" />
        <result column="stock_flag" property="stockFlag" />
        <result column="hr_flag" property="hrFlag" />
        <result column="admin_flag" property="adminFlag" />
        <result column="company_flag" property="companyFlag" />
        <result column="org_type" property="orgType" />
        <result column="id_parentorg" property="idParentorg" />
        <result column="org_manager" property="orgManager" />
        <result column="org_leader" property="orgLeader" />
        <result column="lat_long_alt" property="latLongAlt" />
        <result column="id_group" property="idGroup" />
        <result column="status" property="status" />
        <result column="create_time" property="createTime" />
        <result column="creator" property="creator" />
        <result column="disable_time" property="disableTime" />
        <result column="ts" property="ts" />
        <result column="delete_flag" property="deleteFlag" />
        <result column="id_org_invoice" property="idOrgInvoice" />
        <result column="cancel_flag" property="cancelFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id_org, code, innercode, name, shortname, pk_org, purchase_flag, sales_flag, traffic_flag, finance_flag, stock_flag, hr_flag, admin_flag, company_flag, org_type, id_parentorg, org_manager, org_leader, lat_long_alt, id_group, status, create_time, creator, disable_time, ts, delete_flag, id_org_invoice, cancel_flag
    </sql>

</mapper>
