<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fls.upms.mapper.BasePersonMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.fls.upms.entity.BasePerson">
        <id column="id_person" property="idPerson" />
        <result column="code" property="code" />
        <result column="name" property="name" />
        <result column="usedname" property="usedname" />
        <result column="sex" property="sex" />
        <result column="nation" property="nation" />
        <result column="officephone" property="officephone" />
        <result column="mobile" property="mobile" />
        <result column="id_org" property="idOrg" />
        <result column="id_department" property="idDepartment" />
        <result column="id_post" property="idPost" />
        <result column="pk_psndoc" property="pkPsndoc" />
        <result column="status" property="status" />
        <result column="create_time" property="createTime" />
        <result column="creator" property="creator" />
        <result column="disable_time" property="disableTime" />
        <result column="ts" property="ts" />
        <result column="delete_flag" property="deleteFlag" />
        <result column="post_flag" property="postFlag" />
        <result column="id_job" property="idJob" />
        <result column="email" property="email" />
        <result column="dingdid" property="dingdid" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id_person, code, name, usedname, sex, nation, officephone, mobile, id_org, id_department, id_post, pk_psndoc, status, create_time, creator, disable_time, ts, delete_flag, post_flag, id_job, email, dingdid
    </sql>

    <select id="getPersonJobAndPostList" resultType="com.fls.upms.entity.vo.JobAndPostRecordVo">
        select id_hrpsnjob as idHrpsnjob,id_person as idPerson,id_org as idOrg,id_post as idPost,
            id_job as idJob,end_flag as endFlag
        from t_hr_psnjob
        where id_person=#{idPerson} and end_flag='0'
    </select>
    <select id="getUserInfoByPhoneNumber" resultType="com.fls.upms.entity.BasePerson"
            resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" /> FROM t_base_person where mobile=#{phoneNumber};
    </select>

</mapper>
