<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fls.upms.mapper.BaseBizunitshipMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.fls.upms.entity.BaseBizunitship">
        <id column="id_bizunitship" property="idBizunitship" />
        <result column="id_org" property="idOrg" />
        <result column="id_department" property="idDepartment" />
        <result column="id_bizunit" property="idBizunit" />
        <result column="status" property="status" />
        <result column="create_time" property="createTime" />
        <result column="creator" property="creator" />
        <result column="disable_time" property="disableTime" />
        <result column="ts" property="ts" />
        <result column="delete_flag" property="deleteFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id_bizunitship, id_org, id_department, id_bizunit, status, create_time, creator, disable_time, ts, delete_flag
    </sql>

</mapper>
