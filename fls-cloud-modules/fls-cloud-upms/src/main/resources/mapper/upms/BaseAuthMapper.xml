<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fls.upms.mapper.BaseAuthMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.fls.upms.entity.BaseAuth">
        <id column="id_auth" property="idAuth" />
        <result column="code" property="code" />
        <result column="name" property="name" />
        <result column="memo" property="memo" />
        <result column="auth_type" property="authType" />
        <result column="status" property="status" />
        <result column="create_time" property="createTime" />
        <result column="creator" property="creator" />
        <result column="disable_time" property="disableTime" />
        <result column="ts" property="ts" />
        <result column="delete_flag" property="deleteFlag" />
        <result column="href" property="href" />
        <result column="icon" property="icon" />
        <result column="id_parentauth" property="idParentauth" />
        <result column="project_name" property="projectName" />
        <result column="sort" property="sort" />
        <result column="menu_type" property="menuType" />
        <result column="component" property="component" />
        <result column="open_type" property="openType" />
        <result column="link" property="link" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id_auth, code, name, memo, auth_type, status, create_time, creator, disable_time, ts, delete_flag, href, icon, id_parentauth, project_name, sort, menu_type, component, open_type, link
    </sql>

</mapper>
