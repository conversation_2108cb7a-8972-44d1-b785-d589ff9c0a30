<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fls.upms.mapper.BaseRoleauthMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.fls.upms.entity.BaseRoleauth">
        <id column="id_roleauth" property="idRoleauth" />
        <result column="id_auth" property="idAuth" />
        <result column="id_role" property="idRole" />
        <result column="status" property="status" />
        <result column="create_time" property="createTime" />
        <result column="creator" property="creator" />
        <result column="disable_time" property="disableTime" />
        <result column="ts" property="ts" />
        <result column="delete_flag" property="deleteFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id_roleauth, id_auth, id_role, status, create_time, creator, disable_time, ts, delete_flag
    </sql>

    <select id="getMenuByRoleIdsAndApps" resultType="com.fls.upms.entity.BaseAuth">
        select
            ba.id_auth idAuth, ba.code, ba.name, ba.memo, ba.auth_type authType, ba.status,
            ba.create_time createTime, ba.creator, ba.disable_time disableTime, ba.ts,
            ba.delete_flag deleteFlag, ba.href, ba.icon, ba.id_parentauth idParentauth,
            ba.project_name projectName, ba.sort, ba.component, ba.menu_type menuType,br.id_role as idRole
        from t_base_roleauth br
        left join t_base_auth ba on  br.id_auth = ba.id_auth
        where br.status = '2' and ba.status = '2' and ba.menu_type != 'F'  and ba.delete_flag = '0'
        and br.id_role in
        <foreach collection="roleIds" separator="," close=")" open="(" item="item">
            #{item}
        </foreach>
        <if test="apps != null and apps.size > 0">
            and ba.project_name in
            <foreach collection="apps" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="href != null and href!=''">
            and ba.href = #{href}
        </if>
        order by ba.sort,ba.code
    </select>

    <select id="getAuthByRoleIdsAndUrls" resultType="com.fls.upms.entity.BaseAuth">
        select
        ba.id_auth idAuth, ba.code, ba.name, ba.memo, ba.auth_type authType, ba.status,
        ba.create_time createTime, ba.creator, ba.disable_time disableTime, ba.ts,
        ba.delete_flag deleteFlag, ba.href, ba.icon, ba.id_parentauth idParentauth,
        ba.project_name projectName, ba.sort, ba.component, ba.menu_type menuType
        from t_base_roleauth br
        left join t_base_auth ba on  br.id_auth = ba.id_auth
        where br.status = '2' and ba.status = '2' and ba.delete_flag = '0'
        and br.id_role in
        <foreach collection="roleIds" separator="," close=")" open="(" item="item">
            #{item}
        </foreach>
        <if test="urls != null and urls.size > 0">
            and ba.href in
            <foreach collection="urls" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getAuthByRoleIdsAndIdParent" resultType="com.fls.upms.entity.BaseAuth">
        select
        ba.id_auth idAuth, ba.code, ba.name, ba.memo, ba.auth_type authType, ba.status,
        ba.create_time createTime, ba.creator, ba.disable_time disableTime, ba.ts,
        ba.delete_flag deleteFlag, ba.href, ba.icon, ba.id_parentauth idParentauth,
        ba.project_name projectName, ba.sort, ba.component, ba.menu_type menuType
        from t_base_roleauth br
        left join t_base_auth ba on  br.id_auth = ba.id_auth
        where br.status = '2' and ba.status = '2' and ba.delete_flag = '0'
        and br.id_role in
        <foreach collection="roleIds" separator="," close=")" open="(" item="item">
            #{item}
        </foreach>
        <if test="idParentauth != null and idParentauth != ''">
            and ba.id_parentauth = #{idParentauth}
        </if>
    </select>

    <select id="getAuthByRoleIdsAndApps" resultType="com.fls.upms.entity.BaseAuth">
        select ba.id_auth as idAuth,ba.code,ba.name,ba.memo,ba.auth_type as authType,ba.status,
        ba.create_time as createTime,ba.creator,ba.disable_time as disableTime,ba.ts,
        ba.delete_flag as deleteFlag,ba.href,ba.icon,ba.id_parentauth as idParentauth,
        ba.project_name as projectName,ba.sort,br.id_role as idRole
        from t_base_roleauth br
        left join t_base_auth ba on  br.id_auth = ba.id_auth
        where br.status = '2' and ba.status = '2' and ba.delete_flag = '0'
        <if test="menuType!=null and menuType!=''">
            and ba.menu_type = #{menuType}
        </if>
        and br.id_role in
        <foreach collection="roleIds" separator="," close=")" open="(" item="item">
            #{item}
        </foreach>
        and ba.project_name in
        <foreach collection="apps" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        order by ba.sort,ba.code
    </select>

    <select id="getAppByRoleIds" resultType="String">
        select distinct ba.project_name
        from t_base_roleauth br
        inner join t_base_auth ba on  br.id_auth = ba.id_auth
        where br.status = '2' and ba.status = '2' and ba.delete_flag = '0'
        and br.id_role in
        <foreach collection="roleIds" separator="," close=")" open="(" item="item">
            #{item}
        </foreach>
    </select>

</mapper>
