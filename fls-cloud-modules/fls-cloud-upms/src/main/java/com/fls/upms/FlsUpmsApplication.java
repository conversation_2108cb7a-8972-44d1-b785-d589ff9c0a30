package com.fls.upms;

import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.metrics.buffering.BufferingApplicationStartup;

/**
 * 系统模块
 *
 * <AUTHOR>
 * @date 2022/05/14
 */
@EnableDubbo
@SpringBootApplication
public class FlsUpmsApplication {
    public static void main(String[] args) {
        SpringApplication application = new SpringApplication(FlsUpmsApplication.class);
        application.setApplicationStartup(new BufferingApplicationStartup(2048));
        application.run(args);
    }
}
