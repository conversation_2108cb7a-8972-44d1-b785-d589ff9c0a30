package com.fls.upms.service.impl;

import com.fls.upms.entity.BaseRole;
import com.fls.upms.mapper.BaseRoleMapper;
import com.fls.upms.service.IBaseRoleService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.base.MPJBaseServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 角色表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @date 2022/05/18
 */
@Service
public class BaseRoleServiceImpl extends MPJBaseServiceImpl<BaseRoleMapper, BaseRole> implements IBaseRoleService {

    @Override
    public List<BaseRole> getLoginRoles(String idUser) {
        return this.baseMapper.getLoginRoles(idUser);
    }
}
