package com.fls.upms.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNodeConfig;
import cn.hutool.core.lang.tree.TreeUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fls.common.core.constant.CommonConstants;
import com.fls.common.core.domain.ResponseData;
import com.fls.common.core.enums.FlagEnum;
import com.fls.common.core.exception.ServiceException;
import com.fls.common.satoken.utils.LoginHelper;
import com.fls.upms.constants.AppConstants;
import com.fls.upms.entity.BaseApp;
import com.fls.upms.entity.BaseAppUser;
import com.fls.upms.entity.BaseAuth;
import com.fls.upms.entity.BaseRoleauth;
import com.fls.upms.entity.vo.AppUserVo;
import com.fls.upms.entity.vo.LoginMenuNode;
import com.fls.upms.extension.IHRPlaceholderResolver;
import com.fls.upms.extension.ResolverStrategyFactory;
import com.fls.upms.param.BaseAppAddParam;
import com.fls.upms.param.BaseAppEditParam;
import com.fls.upms.param.BaseAppQueryParam;
import com.fls.upms.service.BaseAppUserService;
import com.fls.upms.service.IBaseAppService;
import com.fls.upms.service.IBaseAuthService;
import com.fls.upms.service.IBaseRoleauthService;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 系统应用控制器
 *
 * <AUTHOR>
 **/
@RequiredArgsConstructor
@RestController
@RefreshScope
@RequestMapping("app")
public class ApplicationController {
    private final IBaseAppService appService;
    private final IBaseRoleauthService roleauthService;
    private final IBaseAuthService authService;
    private final BaseAppUserService appUserService;
    private final IHRPlaceholderResolver ihrResolver;
    @Value("${user.app.size}")
    private int userAppSize;

    /**
     * 查询所有app应用
     */
    @GetMapping("/list")
    public ResponseData<Page<BaseApp>> getAppList(BaseAppQueryParam param) {
        Page<BaseApp> page = new Page<>(param.getPageNo(), param.getPageSize());
        MPJLambdaWrapper<BaseApp> wrapper = new MPJLambdaWrapper<>();
        wrapper.like(ObjectUtil.isNotEmpty(param.getName()), BaseApp::getName, param.getName())
            .eq(BaseApp::getDeleteFlag, CommonConstants.DELETE_FLAG_NOT_DELETED);
        return ResponseData.ok(appService.selectJoinListPage(page, BaseApp.class, wrapper));
    }

    /**
     * 根据权限应用
     */
    @GetMapping("/auth-list")
    public ResponseData<List<BaseApp>> getAuthAppList() {
        List<String> permissions = roleauthService.getAppByRoleIds(LoginHelper.getLoginUser().getRoleIds());
        MPJLambdaWrapper<BaseApp> wrapper = new MPJLambdaWrapper<>();
        wrapper.in(BaseApp::getProjectName, permissions)
            .eq(BaseApp::getDeleteFlag, CommonConstants.DELETE_FLAG_NOT_DELETED)
            .eq(BaseApp::getStatus, CommonConstants.COMMON_STATUS_NORMAL)
            .orderByAsc(BaseApp::getSort);
        List<BaseApp> apps = appService.list(wrapper);
        //不在主数据权限表的外部应用直接增加
        wrapper.clear();
        wrapper.eq(BaseApp::getAppType, AppConstants.APP_TYPE_EXTERNAL)
            .eq(BaseApp::getStatus, CommonConstants.COMMON_STATUS_NORMAL)
            .eq(BaseAppUser::getDeleteFlag, CommonConstants.DELETE_FLAG_NOT_DELETED);
        List<BaseApp> list = appService.list(wrapper);
        apps.addAll(list);
        //排序
        List<BaseApp> baseAppList = apps.stream().sorted(Comparator.comparing(BaseApp::getSort)).collect(Collectors.toList());
        for (BaseApp app : baseAppList) {
            List<LoginMenuNode> menu = authService.getMenuForApp(app.getProjectName());
            for (LoginMenuNode node : menu) {
                node.setIdBaseApp(app.getIdBaseApp());
                node.setLanUrl(app.getLanUrl());
                node.setProjectName(app.getProjectName());
            }
            TreeNodeConfig config = new TreeNodeConfig();
            //config可以配置属性字段名和排序等等
            config.setIdKey("id");
            config.setParentIdKey("parent");
            config.setWeightKey("sort");
            config.setDeep(5);//最大递归深度  默认无限制
            config.setChildrenKey("children");
            List<Tree<String>> treeNodes = TreeUtil.build(menu, "0", config,
                (treeNode, tree) -> {
                    tree.setId(treeNode.getId());
                    tree.setParentId(treeNode.getParent());
                    tree.setWeight(treeNode.getSort());
                    tree.setName(treeNode.getName());
                    // 扩展属性 ...
                    tree.putExtra("name", treeNode.getName());
                    tree.putExtra("path", treeNode.getPath());
                    tree.putExtra("component", treeNode.getComponent());
                    tree.putExtra("title", treeNode.getTitle());
                    tree.putExtra("icon", treeNode.getIcon());
                    tree.putExtra("link", treeNode.getLink());
                    tree.putExtra("sort", treeNode.getSort());
                    tree.putExtra("menuType", treeNode.getMenuType());
                    tree.putExtra("openType", treeNode.getOpenType());
                    tree.putExtra("userDefaultFlag", treeNode.getUserDefaultFlag());
                    tree.putExtra("idAuth", treeNode.getIdAuth());
                    tree.putExtra("idBaseApp", treeNode.getIdBaseApp());
                    tree.putExtra("lanUrl", treeNode.getLanUrl());
                    tree.putExtra("projectName", treeNode.getProjectName());
                });
            if (ObjectUtil.isEmpty(treeNodes)) {
                List<Tree<String>> nodes = new ArrayList<>();
                Tree<String> tree = new Tree<>(config);
                tree.setId(app.getIdBaseApp());
                tree.setParentId("0");
                tree.setWeight(app.getSort());
                tree.setName(app.getName());
                // 扩展属性 ...
                tree.putExtra("name", app.getName());
                tree.putExtra("path", app.getIndexUrl());
                tree.putExtra("title", app.getName());
                tree.putExtra("icon", app.getIcon());
                tree.putExtra("sort", app.getSort());
                tree.putExtra("idBaseApp", app.getIdBaseApp());
                tree.putExtra("lanUrl", app.getLanUrl());
                tree.putExtra("projectName", app.getProjectName());
                nodes.add(tree);
                app.setAuths(nodes);
            } else {
                app.setAuths(treeNodes);
            }
        }
        return ResponseData.ok(apps);
    }

    /**
     * 应用列表(用户)
     */
    @GetMapping("/user-list")
    public ResponseData<Object> getUserList() {
        String idUser = LoginHelper.getLoginUser().getIdUser();
        MPJLambdaWrapper<BaseAppUser> wrapper = new MPJLambdaWrapper<>();
        wrapper.distinct().selectAll(BaseAppUser.class)
            .selectAll(BaseAuth.class)
            .selectAll(BaseApp.class)
            .innerJoin(BaseAuth.class, BaseAuth::getIdAuth, BaseAppUser::getIdAuth)
            .innerJoin(BaseApp.class, BaseApp::getIdBaseApp, BaseAppUser::getIdBaseApp)
            .innerJoin(BaseRoleauth.class, BaseRoleauth::getIdAuth, BaseAppUser::getIdAuth)
            .eq(BaseAppUser::getIdUser, idUser)
            .in(BaseRoleauth::getIdRole, LoginHelper.getLoginUser().getRoleIds())
            .eq(BaseAppUser::getDeleteFlag, CommonConstants.DELETE_FLAG_NOT_DELETED);
        List<AppUserVo> apps = appUserService.selectJoinList(AppUserVo.class, wrapper);
        List<LoginMenuNode> collect = new ArrayList<>();
        for (AppUserVo app : apps) {
            LoginMenuNode node = new LoginMenuNode();
            node.setId(app.getIdAuth());
            if (ObjectUtil.isEmpty(app.getIdParentauth())) {
                node.setParent("0");
            } else {
                node.setParent(app.getIdParentauth());
            }
            node.setComponent(app.getComponent());
            node.setPath(app.getHref());
            node.setName(app.getName());
            node.setSort(app.getSort());
            node.setIcon(app.getIcon());
            node.setMenuType(app.getMenuType());
            node.setLink(app.getLink());
            node.setTitle(app.getName());
            node.setOpenType(app.getOpenType());
            node.setHidden(true);
            node.setIdAuth(app.getIdAuth());
            node.setLabel(app.getLabel());
            node.setIdBaseApp(app.getIdBaseApp());
            node.setLanUrl(app.getLanUrl());
            node.setProjectName(app.getProjectName());
            node.setUserDefaultFlag(FlagEnum.YES.getCode());
            collect.add(node);
        }
        //添加应用收藏
        wrapper.clear();
        wrapper.distinct().selectAll(BaseAppUser.class)
            .selectAll(BaseApp.class)
            .innerJoin(BaseApp.class, BaseApp::getIdBaseApp, BaseAppUser::getIdBaseApp)
            .eq(BaseAppUser::getIdUser, idUser)
            .isNull(BaseAppUser::getIdAuth)
            .eq(BaseAppUser::getDeleteFlag, CommonConstants.DELETE_FLAG_NOT_DELETED);
        List<BaseApp> appsList = appUserService.selectJoinList(BaseApp.class, wrapper);
        for (BaseApp app : appsList) {
            LoginMenuNode node = new LoginMenuNode();
            node.setId(app.getIdBaseApp());
            node.setParent("0");
            node.setPath(app.getIndexUrl());
            node.setName(app.getName());
            node.setSort(app.getSort());
            node.setIcon(app.getIcon());
            node.setTitle(app.getName());
            node.setLabel(app.getLabel());
            node.setHidden(true);
            node.setIdBaseApp(app.getIdBaseApp());
            node.setLanUrl(app.getLanUrl());
            node.setProjectName(app.getProjectName());
            node.setUserDefaultFlag(FlagEnum.YES.getCode());
            collect.add(node);
        }
        return ResponseData.ok(collect);
    }

    @PostMapping("/add")
    public ResponseData<Boolean> add(@Validated @RequestBody BaseAppAddParam param) {
        return ResponseData.ok(appService.save(BeanUtil.copyProperties(param, BaseApp.class)));
    }

    @PostMapping("/edit")
    public ResponseData<Boolean> edit(@Validated @RequestBody BaseAppEditParam param) {
        return ResponseData.ok(appService.updateById(BeanUtil.copyProperties(param, BaseApp.class)));
    }

    @PostMapping("/del")
    public ResponseData<Boolean> del(@RequestBody Dict dict) {
        String id = dict.getStr("id");
        if (ObjectUtil.isEmpty(id)) {
            throw new ServiceException("id不能为空");
        }
        return ResponseData.ok(appService.removeById(id));
    }

    @PostMapping("/user-add")
    public ResponseData<Boolean> userAdd(@Validated @RequestBody BaseAppUser param) {
        if (ObjectUtil.isEmpty(param.getIdBaseApp())) {
            throw new ServiceException("缺少必填项[idBaseApp]");
        }
        String idUser = LoginHelper.getLoginUser().getIdUser();
        MPJLambdaWrapper<BaseAppUser> wrapper = new MPJLambdaWrapper<>();
        wrapper.eq(BaseAppUser::getIdUser, idUser)
            .eq(BaseAppUser::getIdBaseApp, param.getIdBaseApp())
            .eq(BaseAppUser::getDeleteFlag, FlagEnum.NOT.getCode());
        //如果是idAuth不为空说明是收藏菜单，用IdAuth查询，否则说明是收藏应用菜单为空
        if (ObjectUtil.isNotEmpty(param.getIdAuth())) {
            wrapper.eq(BaseAppUser::getIdAuth, param.getIdAuth());
        } else {
            wrapper.isNull(ObjectUtil.isEmpty(param.getIdAuth()), BaseAppUser::getIdAuth);
        }
        List<BaseAppUser> list = appUserService.list(wrapper);
        if (ObjectUtil.isNotEmpty(list)) {
            throw new ServiceException("已添加常用,无需重复添加");
        }
        List<BaseAppUser> alreadys = appUserService.list(new MPJLambdaWrapper<BaseAppUser>().eq(BaseAppUser::getIdUser, idUser).eq(BaseAppUser::getDeleteFlag, FlagEnum.NOT.getCode()));
        if (ObjectUtil.isNotEmpty(alreadys) && alreadys.size() >= userAppSize) {
            throw new ServiceException(String.format("每个用户最多添加%s个常用应用", userAppSize));
        }
        param.setIdUser(idUser);
        param.setCreator(idUser);
        return ResponseData.ok(appUserService.save(param));
    }

    @PostMapping("/user-del")
    public ResponseData<Boolean> userDel(@Validated @RequestBody BaseAppUser param) {
        if (ObjectUtil.isEmpty(param.getIdBaseApp())) {
            throw new ServiceException("缺少必填项[idBaseApp]");
        }
        MPJLambdaWrapper<BaseAppUser> wrapper = new MPJLambdaWrapper<>();
        if (ObjectUtil.isEmpty(param.getIdAuth())) {
            wrapper.eq(BaseAppUser::getIdUser, LoginHelper.getLoginUser().getIdUser())
                .eq(BaseAppUser::getIdBaseApp, param.getIdBaseApp())
                .isNull(BaseAppUser::getIdAuth);
        } else {
            wrapper.eq(BaseAppUser::getIdUser, LoginHelper.getLoginUser().getIdUser())
                .eq(BaseAppUser::getIdBaseApp, param.getIdBaseApp())
                .eq(BaseAppUser::getIdAuth, param.getIdAuth());
        }
        return ResponseData.ok(appUserService.deleteJoin(wrapper));
    }

    @GetMapping("/ihr-redirect")
    public void IHRRedirect(@RequestParam @Valid @NotEmpty(message = "手机号未注册，请补充手机号信息") String phone, HttpServletResponse response) throws IOException {
        response.sendRedirect(ihrResolver.getDefaultRedirectUrl(phone));
    }
}
