package com.fls.upms.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@Configuration
@RefreshScope
@ConfigurationProperties(prefix = "ihr.sso")
@Data
public class IHRSSOConfig {
    private String url;
    private String clientId;
    private String privateKey;
    private String licence;
}
