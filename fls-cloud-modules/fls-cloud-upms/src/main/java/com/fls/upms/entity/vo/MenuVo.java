package com.fls.upms.entity.vo;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class MenuVo {
    //菜单id
    private String id;
    //菜单code
    private String code;
    //父级菜单id
    private String parentId;
    //菜单名称
    private String name;
    //路径
    private String path;
    //组件
    private String component;
    //标题
    private String title;
    //图标
    private String icon;
    //菜单类型： M目录 C菜单 F按钮
    private String menuType;
    //打开方式： 0无 1组件 2内链 3外链
    private String openType;
    //链接
    private String link;
    //排序
    private Integer sort;
}
