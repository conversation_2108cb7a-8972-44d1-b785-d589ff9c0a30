package com.fls.upms.service.impl;

import cn.dev33.satoken.SaManager;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fls.common.core.constant.CommonConstants;
import com.fls.common.core.enums.UserStatus;
import com.fls.common.core.exception.ServiceException;
import com.fls.common.core.exception.user.UserException;
import com.fls.upms.api.model.LoginMenuNode;
import com.fls.upms.api.model.LoginPerson;
import com.fls.upms.api.model.LoginUser;
import com.fls.upms.entity.BaseAuth;
import com.fls.upms.entity.BaseBizunitship;
import com.fls.upms.entity.BasePerson;
import com.fls.upms.entity.BaseRoleDataBizUnit;
import com.fls.upms.entity.BaseRoledata;
import com.fls.upms.entity.BaseUser;
import com.fls.upms.entity.BaseUserRole;
import com.fls.upms.entity.dto.BizUnitResult;
import com.fls.upms.entity.vo.JobAndPostRecordVo;
import com.fls.upms.mapper.BaseUserMapper;
import com.fls.upms.model.LoginUserEx;
import com.fls.upms.param.LoginAuthParam;
import com.fls.upms.param.LoginParam;
import com.fls.upms.service.BaseRoleDataBizUnitService;
import com.fls.upms.service.IBaseAuthService;
import com.fls.upms.service.IBaseBizunitshipService;
import com.fls.upms.service.IBasePersonService;
import com.fls.upms.service.IBaseRoleauthService;
import com.fls.upms.service.IBaseRoledataService;
import com.fls.upms.service.IBaseUserService;
import com.fls.upms.service.IBaseUserroleService;
import com.fls.upms.utils.RedissonUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 用户表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @date 2022/05/18
 */
@Service
public class BaseUserServiceImpl extends ServiceImpl<BaseUserMapper, BaseUser> implements IBaseUserService {


    @Resource
    private IBasePersonService personService;

    @Resource
    private IBaseBizunitshipService bizUnitShipService;

    @Resource
    private IBaseUserroleService userRoleService;

    @Resource
    private IBaseRoleauthService roleAuthService;

    @Resource
    private BaseRoleDataBizUnitService roleDataBizUnitService;

    @Resource
    private IBaseRoledataService roleDataService;

    @Resource
    private IBaseAuthService authService;


    @Override
    public BaseUser getUserInfoByIdPerson(String idPerson) {
        return this.baseMapper.getUserInfoByIdPerson(idPerson);
    }

    @Override
    public BaseUser getUserInfoByUserCode(String userCode) {
        LambdaQueryWrapper<BaseUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BaseUser::getCode, userCode)
            .eq(BaseUser::getDeleteFlag, CommonConstants.DELETE_FLAG_NOT_DELETED)
            .last("limit 1");
        BaseUser baseUser = this.getOne(wrapper);
        if (ObjectUtil.isEmpty(baseUser)) {
            throw new UserException("用户查询失败");
        }
        return baseUser;
    }

    @Override
    public String getUserIdByNameAndPwd(String username, String password) {
        BaseUser baseUser = getUserInfoByUserCode(username);
        if (!UserStatus.NORMAL.getCode().equals(baseUser.getStatus())) {
            throw new UserException(String.format(
                "对不起，您的账号：%s 状态异常，请联系管理员", username
            ));
        }
        boolean check = baseUser.getPassword().equals(SecureUtil.md5(baseUser.getSalt() + password));
        return check ? baseUser.getIdUser() : null;
    }

    @Override
    public LoginUser buildLoginUser(String userId, String appcode) {
        LoginUserEx loginUser = buildBaseUserByUserId(userId);
        buildUserPerson(loginUser);
        buildUserPermissions(loginUser, appcode);
        Set<String> orgIds = buildUserOrgScope(loginUser);
        List<BizUnitResult> unitResultList = roleDataBizUnitService.getUnitByIdOrgs(orgIds);
        Map<String, Set<String>> orgUnitMap = unitResultList.stream().collect(Collectors.groupingBy(BizUnitResult::getIdOrg, Collectors.mapping(BizUnitResult::getIdUnit, Collectors.toSet())));
        buildUnitScope(loginUser, orgUnitMap);
        buildUserMenus(loginUser);
        return loginUser;
    }

    private LoginUserEx buildBaseUserByUserId(String userId) {
        BaseUser user = this.getById(userId);
        if (ObjectUtil.isNull(user)) {
            throw new ServiceException("用户不存在");
        }
        //可以转为Mapstruct
        LoginUserEx loginUser = new LoginUserEx();
        loginUser.setIdUser(user.getIdUser());
        loginUser.setIdIdentity(user.getIdIdentity());
        loginUser.setUsername(user.getCode());
        loginUser.setName(user.getName());
        loginUser.setNickName(user.getName());
        loginUser.setPassword(user.getPassword());
        loginUser.setSalt(user.getSalt());
        loginUser.setPkUser(user.getPkUser());
        return loginUser;
    }

    private void buildUserPerson(LoginUser loginUser) {
        //人员信息
        BasePerson basePerson = personService.getById(loginUser.getIdIdentity());
        if (ObjectUtil.isNotEmpty(basePerson)) {
            LoginPerson person = new LoginPerson();
            BeanUtil.copyProperties(basePerson, person);
            loginUser.setLoginPerson(person);
            loginUser.setPhone(person.getMobile());
            loginUser.setIdDepartment(person.getIdDepartment());
            loginUser.setIdOrg(person.getIdOrg());
            //查询人员所属的经营主体
            LambdaQueryWrapper<BaseBizunitship> qw = new LambdaQueryWrapper<>();
            qw.eq(BaseBizunitship::getIdOrg, loginUser.getIdOrg());
            Map<String, List<BaseBizunitship>> baseBizunitshipMap = bizUnitShipService.list(qw).stream().collect(Collectors.groupingBy(BaseBizunitship::getIdDepartment));
            String idUnit = null;
            if (ObjectUtil.isNotEmpty(baseBizunitshipMap.get(loginUser.getIdDepartment()))) {
                idUnit = baseBizunitshipMap.get(loginUser.getIdDepartment()).get(0).getIdBizunit();
            } else {
                idUnit = baseBizunitshipMap.get("~").get(0).getIdBizunit();
            }
            loginUser.setIdUnit(idUnit);
            //员工岗位和职务
            List<JobAndPostRecordVo> jobAndPostList = personService.getPersonJobAndPostList(loginUser.getIdIdentity());
            if (ObjectUtil.isNotEmpty(jobAndPostList)) {
                Set<String> jobSet = new HashSet<>();
                Set<String> postSet = new HashSet<>();
                jobAndPostList.forEach(item -> {
                    jobSet.add(item.getIdJob());
                    postSet.add(item.getIdPost());
                });
                loginUser.setJobIdSet(jobSet);
                loginUser.setPostIdSet(postSet);
            }
        }
    }


    private void buildUserPermissions(LoginUserEx loginUser, String appcode) {
        //封装权限信息
        LambdaQueryWrapper<BaseUserRole> userroleLambdaQueryWrapper = new LambdaQueryWrapper<>();
        userroleLambdaQueryWrapper.eq(BaseUserRole::getIdUser, loginUser.getIdUser());
        userroleLambdaQueryWrapper.eq(BaseUserRole::getStatus, CommonConstants.COMMON_STATUS_NORMAL);
        userroleLambdaQueryWrapper.eq(BaseUserRole::getDeleteFlag, CommonConstants.DELETE_FLAG_NOT_DELETED);
        List<BaseUserRole> userroleList = userRoleService.list(userroleLambdaQueryWrapper);
        if (ObjectUtil.isNotEmpty(userroleList)) {
            Set<String> roleIds = userroleList.stream().map(BaseUserRole::getIdRole).collect(Collectors.toSet());
            // 获取菜单id集合
            if (ObjectUtil.isNotEmpty(roleIds)) {
                //获取功能权限（crm）
                List<BaseAuth> menuList = roleAuthService.getMenuByRoleIdsAndApps(roleIds, Collections.singletonList(appcode));
                if (ObjectUtil.isNotEmpty(menuList)) {
                    loginUser.setPerms(menuList.stream().map(BaseAuth::getHref).collect(Collectors.toSet()));
                    loginUser.setOriginMenus(menuList);
                }
            } else {
                loginUser.setPerms(new HashSet<>());
            }
            loginUser.setRoleIds(roleIds);
        }
    }

    private void buildUnitScope(LoginUserEx loginUser, Map<String, Set<String>> orgUnitMap) {
        // 经营主体数据范围信息
        LambdaQueryWrapper<BaseRoleDataBizUnit> roledataBizunitLambdaQueryWrapper = new LambdaQueryWrapper<>();
        roledataBizunitLambdaQueryWrapper.in(BaseRoleDataBizUnit::getIdRole, loginUser.getRoleIds());
        List<BaseRoleDataBizUnit> baseRoledataBizunits = roleDataBizUnitService.list(roledataBizunitLambdaQueryWrapper);
        //配置了经营主体
        if (ObjectUtil.isNotEmpty(baseRoledataBizunits)) {
            //一个角色所对应的经营主体
            Map<String, Set<String>> roledataBizunitMap = baseRoledataBizunits.stream().collect(Collectors.groupingBy(BaseRoleDataBizUnit::getIdRole, Collectors.mapping(BaseRoleDataBizUnit::getIdBizunit, Collectors.toSet())));
            List<BaseAuth> menuList = loginUser.getOriginMenus();
            //一个权限所对应的角色
            Map<String, Set<String>> authdataRole = menuList.stream().collect(Collectors.groupingBy(BaseAuth::getIdAuth, Collectors.mapping(BaseAuth::getIdRole, Collectors.toSet())));
            //保存一个菜单url所对应的经营主体，取并集
            Map<String, Set<String>> dataScopes = new HashMap<>(32);
            menuList.stream().forEach(item -> {
                //获取一个权限对应的经营主体
                Set<String> unitSet = new HashSet<>();
                //获取菜单对应的角色，然后遍历获取角色对应的经营主体
                authdataRole.get(item.getIdAuth()).forEach(tem -> {
                    if (ObjectUtil.isNotEmpty(roledataBizunitMap.get(tem))) {
                        unitSet.addAll(roledataBizunitMap.get(tem));
                    }

                });
                if (ObjectUtil.isNotEmpty(dataScopes.get(item.getHref()))) {
                    dataScopes.get(item.getHref()).addAll(unitSet);
                } else {
                    dataScopes.put(item.getHref(), unitSet);
                }
            });
            //根据菜单的组织权限，再次封装菜单的经营主体
            if (ObjectUtil.isNotEmpty(loginUser.getOrgDataScopes())) {
                Map<String, Set<String>> orgScopes = loginUser.getOrgDataScopes();
                Map<String, Set<String>> finalOrgUnitMap = orgUnitMap;
                dataScopes.forEach((key, value) -> {
                    //如果当前url的经营主体配置为空，则获取组织的所有经营主体
                    if (ObjectUtil.isEmpty(value)) {
                        Set<String> orgs = orgScopes.get(key);
                        if (ObjectUtil.isNotEmpty(orgs)) {
                            orgs.forEach(i -> {
                                if (ObjectUtil.isNotEmpty(finalOrgUnitMap.get(i))) {
                                    value.addAll(finalOrgUnitMap.get(i));
                                }
                            });
                        }
                    } else {
                        //判断当前经营主体集合是否在该组织下，如果是则只有自己，如果不是则添加组织全部
                        Set<String> orgs = orgScopes.get(key);
                        if (ObjectUtil.isNotEmpty(orgs)) {
                            orgs.forEach(i -> {
                                Set<String> units = finalOrgUnitMap.get(i);
                                if (ObjectUtil.isNotEmpty(units)) {
                                    //取当前的配置和组织对应的经营主体匹配，有交集则不变，没有交集则添加全部
                                    if (ObjectUtil.isEmpty(units.stream().filter(value::contains).collect(Collectors.toSet()))) {
                                        value.addAll(units);
                                    }

                                }
                            });
                        }
                    }

                });
            }
            loginUser.setUnitDataScopes(dataScopes);
        } else {
            //没有配置经营主体则直接按照组织获取全部
            if (ObjectUtil.isNotEmpty(loginUser.getOrgDataScopes())) {
                Map<String, Set<String>> dataScopes = new HashMap<>(32);
                Map<String, Set<String>> orgDs = loginUser.getOrgDataScopes();
                Map<String, Set<String>> finalOrgUnitMap1 = orgUnitMap;
                orgDs.forEach((key, value) -> {
                    Set<String> units = new HashSet<>();
                    value.forEach(org -> {
                        if (ObjectUtil.isNotEmpty(finalOrgUnitMap1.get(org))) {
                            units.addAll(finalOrgUnitMap1.get(org));
                        }
                    });
                    dataScopes.put(key, units);
                });
                loginUser.setUnitDataScopes(dataScopes);
            }
        }
    }

    private Set<String> buildUserOrgScope(LoginUserEx loginUser) {
        LambdaQueryWrapper<BaseRoledata> roledataLambdaQueryWrapper = new LambdaQueryWrapper<>();
        roledataLambdaQueryWrapper.isNotNull(BaseRoledata::getIdOrg);
        roledataLambdaQueryWrapper.in(BaseRoledata::getIdRole, loginUser.getRoleIds());
        roledataLambdaQueryWrapper.eq(BaseRoledata::getStatus, "2");
        roledataLambdaQueryWrapper.eq(BaseRoledata::getDeleteFlag, CommonConstants.DELETE_FLAG_NOT_DELETED);
        List<BaseRoledata> roledataList = roleDataService.list(roledataLambdaQueryWrapper);
        if (ObjectUtil.isNotEmpty(roledataList)) {
            Map<String, Set<String>> roledataMap = roledataList.stream().collect(Collectors.groupingBy(BaseRoledata::getIdRole, Collectors.mapping(BaseRoledata::getIdOrg, Collectors.toSet())));
            //一个权限所对应的角色
            Map<String, Set<String>> authdataRole = loginUser.getOriginMenus().stream().collect(Collectors.groupingBy(BaseAuth::getIdAuth, Collectors.mapping(BaseAuth::getIdRole, Collectors.toSet())));
            //保存一个菜单url所对应的组织，取并集
            Map<String, Set<String>> dataScopes = new HashMap<>(32);
            List<BaseAuth> menuList = loginUser.getOriginMenus();
            menuList.stream().forEach(item -> {
                //获取一个权限对应的组织
                Set<String> orgSet = new HashSet<>();
                //获取菜单对应的角色，然后遍历获取角色对应的组织
                authdataRole.get(item.getIdAuth()).forEach(tem -> {
                    if (ObjectUtil.isNotEmpty(roledataMap.get(tem))) {
                        orgSet.addAll(roledataMap.get(tem));
                    }
                });
                if (ObjectUtil.isNotEmpty(dataScopes.get(item.getHref()))) {
                    dataScopes.get(item.getHref()).addAll(orgSet);
                } else {
                    dataScopes.put(item.getHref(), orgSet);
                }
            });
            loginUser.setOrgDataScopes(dataScopes);

            //获取所有角色的组织对应的经营主体
            return roledataList.stream().map(BaseRoledata::getIdOrg).collect(Collectors.toSet());
        }
        return CollectionUtil.newHashSet();
    }

    public static void buildUserMenus(LoginUserEx loginUser) {
        List<BaseAuth> loginMenus = loginUser.getOriginMenus();
        if (ObjectUtil.isNotEmpty(loginMenus)) {
            List<LoginMenuNode> nodeList = new ArrayList<>();
            loginMenus.forEach(item -> {
                LoginMenuNode node = new LoginMenuNode();
                node.setId(item.getIdAuth());
                if (ObjectUtil.isEmpty(item.getIdParentauth())) {
                    node.setParent("0");
                } else {
                    node.setParent(item.getIdParentauth());
                }
                node.setComponent(item.getComponent());
                node.setPath(item.getHref());
                node.setName(item.getName());
                node.setSort(item.getSort());
                node.setIcon(item.getIcon());
                node.setMenuType(item.getMenuType());
                node.setLink(item.getLink());
                //目前情况，title和name相同
                node.setTitle(item.getName());
                node.setOpenType(item.getOpenType());
                node.setHidden(false);
                nodeList.add(node);
            });
            loginUser.setMenus(nodeList);
        } else {
            loginUser.setMenus(new ArrayList<>());
        }
    }

    public LoginUser login(LoginParam params) {
        String account = params.getUsername();
        String password = params.getPassword();
        String userId = getUserIdByNameAndPwd(account, password);
        if (StrUtil.isEmpty(userId)) {
            throw new ServiceException("登录失败，用户名或密码错误");
        }
        LoginUser loginUser = buildBaseUserByUserId(userId);
        buildUserPerson(loginUser);
        return loginUser;
    }

    public void logout(String token) {
        //请求参数不携带appcode，能进到这个入口的请求都是底座开发带过来的token
        if (ObjectUtil.isNotNull(StpUtil.getLoginIdByToken(token))) {
            StpUtil.logoutByTokenValue(token);
        }
        //删除缓存
        RedissonUtils.deleteObject(token);
    }

    public void setLoginAuth(LoginAuthParam param) {
        String userId = param.getUserId();
        String appcode = param.getAppcode();
        String token = param.getToken();
        Dict baseCache = new Dict();
        baseCache.set("projectName", appcode);
        baseCache.set("idUser", userId);
        RedissonUtils.setCacheMap(token, baseCache);
        //暂时先使用nacos  sa-token的配置，86400秒，24小时，上层应用的token有效期需要小于等于服务层的token有效期
        long tokenTimeout = SaManager.getConfig().getTimeout();
        RedissonUtils.expire(token, Duration.ofSeconds(tokenTimeout));
        authService.setCacheAuths(token, appcode, userId);
    }

    public boolean checkLogin(String token) {
        return RedissonUtils.hasKey(token);
    }
}

