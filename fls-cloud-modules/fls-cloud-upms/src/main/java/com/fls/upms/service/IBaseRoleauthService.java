package com.fls.upms.service;

import com.fls.upms.entity.BaseAuth;
import com.fls.upms.entity.BaseRoleauth;
import com.baomidou.mybatisplus.extension.service.IService;
import com.github.yulichang.base.MPJBaseService;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 角色功能权限表 服务类
 * </p>
 *
 * <AUTHOR>
 * @date 2022/05/18
 */
public interface IBaseRoleauthService extends MPJBaseService<BaseRoleauth> {

    /**
     * 根据角色和应用列表获取菜单
     * @param roleIds 角色
     * @param apps 应用
     * @return List<BaseAuth>
     */
    List<BaseAuth> getMenuByRoleIdsAndApps(Set<String> roleIds, List<String> apps);
    List<BaseAuth> getMenuByRoleIdsAndApps(Set<String> roleIds, List<String> apps,String href);

    /**
     * 根据角色和应用列表获取所有权限
     * @param roleIds 角色
     * @param apps 应用
     * @return List<BaseAuth>
     */
    List<BaseAuth> getAuthByRoleIdsAndApps(Set<String> roleIds, List<String> apps,String menuType);

    /**
     * 根据角色id和URL查询权限
     * @param roleIds
     * @param urls
     * @return
     */
    List<BaseAuth> getAuthByRoleIdsAndUrls(Set<String> roleIds, List<String> urls);

    /**
     * 根据角色和父级查询子权限
     * @param roleIds
     * @param idParentauth
     * @return
     */
    List<BaseAuth> getAuthByRoleIdsAndIdParent(Set<String> roleIds, String idParentauth);

    /**
     * 根据角色查询应用
     * @param roleIds
     * @return
     */
    List<String> getAppByRoleIds(Set<String> roleIds);
}
