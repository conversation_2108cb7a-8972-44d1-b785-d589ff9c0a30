package com.fls.upms.extension;

import lombok.Getter;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@Getter
@NoArgsConstructor
public class ResolverStrategyFactory {
    private static final Map<String, MenuUrlResolver> RESOLVER_MAPS = new HashMap<>();

    public static MenuUrlResolver getResolverByCode(@NotEmpty String code) {
        return Optional.ofNullable(RESOLVER_MAPS.get(code)).orElse(RESOLVER_MAPS.get(ResolveStrategyEnum.DEFAULT.name()));
    }

    public static void registerResolver(String code, MenuUrlResolver resolver) {
        RESOLVER_MAPS.put(code, resolver);
    }
}
