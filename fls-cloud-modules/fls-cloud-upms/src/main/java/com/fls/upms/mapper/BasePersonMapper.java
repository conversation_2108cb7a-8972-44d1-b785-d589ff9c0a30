package com.fls.upms.mapper;

import com.fls.upms.entity.BasePerson;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fls.upms.entity.vo.JobAndPostRecordVo;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
 * <p>
 * 员工表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @date 2022/05/18
 */
public interface BasePersonMapper extends BaseMapper<BasePerson> {

    /**
     * 查询员工任职流水
     * @param idIdentity
     * @return
     */
    List<JobAndPostRecordVo> getPersonJobAndPostList(String idIdentity);

    BasePerson getUserInfoByPhoneNumber(@Param("phoneNumber") String phoneNumber);
}
