package com.fls.upms.service.impl;

import com.fls.common.core.service.SensitiveService;
import com.fls.common.satoken.utils.LoginHelper;
import org.springframework.stereotype.Service;

/**
 * 脱敏服务
 * 默认管理员不过滤
 * 需自行根据业务重写实现
 *
 * <AUTHOR>
 * @date 2022/05/19
 */
@Service
public class SysSensitiveServiceImpl implements SensitiveService {

    /**
     * 是否脱敏
     */
    @Override
    public boolean isSensitive() {
        return false;
    }

}
