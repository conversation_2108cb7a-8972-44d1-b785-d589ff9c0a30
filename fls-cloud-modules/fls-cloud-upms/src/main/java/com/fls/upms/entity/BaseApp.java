package com.fls.upms.entity;

import cn.hutool.core.lang.tree.Tree;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 应用表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-16
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_base_app")
public class BaseApp implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId("id_base_app")
    private String idBaseApp;

    /**
     * 应用名称
     */
    @TableField("name")
    private String name;

    /**
     * 应用标示
     */
    @TableField("label")
    private String label;

    /**
     * 应用类型:1=内部应用、2=外部应用
     */
    @TableField("app_type")
    private String appType;

    /**
     * 系统内网地址
     */
    @TableField("lan_url")
    private String lanUrl;

    /**
     * 系统外网地址
     */
    @TableField("wan_url")
    private String wanUrl;

    /**
     * 首页URL
     */
    @TableField("index_url")
    private String indexUrl;

    /**
     * 权限标识
     */
    @TableField("permission")
    private String permission;

    /**
     * 图标
     */
    @TableField("icon")
    private String icon;

    /**
     * 排序
     */
    @TableField("sort")
    private Integer sort;

    /**
     * 备注
     */
    @TableField("memo")
    private String memo;

    /**
     * 启用状态：1=未启用，2=已启用，3=已停用，默认1，参见basestatus
     */
    @TableField("status")
    private String status;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 失效时间
     */
    @TableField("disable_time")
    private Date disableTime;

    /**
     * 更新人
     */
    @TableField("updator")
    private String updator;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 是否删除：0=否，1=是，默认0，参见yesorno
     */
    @TableField("delete_flag")
    @TableLogic
    private String deleteFlag;

    /**
     * 时间戳
     */
    @TableField("ts")
    private Date ts;

    /**
     * 应用工程名
     */
    @TableField("project_name")
    private String projectName;

    @TableField(exist = false)
    private List<Tree<String>> auths;
}
