package com.fls.upms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 经营主体数据权限表
 *
 * <AUTHOR>
 */
@Data
@TableName("t_base_roledata_bizunit")
public class BaseRoleDataBizUnit {

    /**
     * 主键
     */
    @TableId(type = IdType.INPUT)
    private String idRoledataBizunit;

    /**
     * 经营主体ID
     */
    private String idBizunit;

    /**
     * 角色ID
     */
    private String idRole;

    /**
     * 启用状态 1=未启用，2=已启用，3=已停用 参见basestatus
     */
    private String status;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 失效时间
     */
    private Date disableTime;

    /**
     * 时间戳
     */
    private String ts;

    /**
     * 是否删除 0=否，1=是 参见yesorno
     */
    private String deleteFlag;

}
