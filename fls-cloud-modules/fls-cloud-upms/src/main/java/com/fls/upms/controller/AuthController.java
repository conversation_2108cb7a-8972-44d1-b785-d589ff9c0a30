package com.fls.upms.controller;

import cn.hutool.core.util.ObjectUtil;
import com.fls.common.core.domain.ResponseData;
import com.fls.upms.constants.AppConstants;
import com.fls.upms.param.GetAuthParam;
import com.fls.upms.param.GetBusinessAuthParam;
import com.fls.upms.service.IBaseAuthService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Set;

/**
 * 权限相关接口
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("auth/v1")
public class AuthController{
    private final IBaseAuthService authService;
    /**
     * 查询数据权限
     * @return 数据权限
     */
    @PostMapping("/getAuth")
    public ResponseData<Object> getAuth(@Validated @RequestBody GetBusinessAuthParam param){
        return ResponseData.ok(authService.getAuth(param.getToken()));
    }

    /**
     * 查询组织数据权限
     * @return 数据权限
     */
    @PostMapping("/setAuth")
    public ResponseData<Object> setAuth(@Validated @RequestBody GetAuthParam param){
        return ResponseData.ok(authService.setAuth(param));
    }


    /**
     * 查询组织数据权限
     * @return 数据权限
     */
    @PostMapping("/org")
    public ResponseData<Object> getAuthOrg(@Validated @RequestBody GetAuthParam param){
        return ResponseData.ok(authService.getAuthOrg(param));
    }

    /**
     * 查询部门数据权限
     * @return 数据权限
     */
    @PostMapping("/dept")
    public ResponseData<Object> getAuthDept(@Validated @RequestBody GetAuthParam param){
        return ResponseData.ok(authService.getAuthDept(param));
    }

    /**
     * 查询岗位数据权限
     * @return 数据权限
     */
    @PostMapping("/post")
    public ResponseData<Object> getAuthPost(@Validated @RequestBody GetAuthParam param){
        return ResponseData.ok(authService.getAuthPost(param));
    }

    /**
     * 查询档案数据权限
     * @return 数据权限
     */
    @PostMapping("/doc")
    public ResponseData<Object> getAuthDoc(@Validated @RequestBody GetAuthParam param){
        Set<String> authDoc = authService.getAuthDoc(param);
        if (ObjectUtil.isEmpty(authDoc)) {
            return ResponseData.ok("操作成功","all");
        }else{
            return ResponseData.ok(authService.getAuthDoc(param));
        }
    }

    /**
     * 查询菜单数据权限（包含按钮）
     * @return 数据权限
     */
    @PostMapping("/menu")
    public ResponseData<Object> getAuthMenu(@Validated @RequestBody GetAuthParam param){
        return ResponseData.ok(authService.getAuthMenu(param));
    }
}
