package com.fls.upms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 角色数据权限表
 * </p>
 *
 * <AUTHOR>
 * @date 2022/05/18
 */
@Data
@NoArgsConstructor
@TableName("t_base_roledata")
public class BaseRoledata implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id_roledata", type = IdType.INPUT)
    private String idRoledata;

    /**
     * 所属组织
     */
    private String idOrg;

    /**
     * 所属经营主体
     */
    @TableField(exist = false)
    private String idBizunit;

    /**
     * 所属部门
     */
    private String idDepartment;

    /**
     * 所属岗位序列
     */
    private String idPostseries;

    /**
     * 所属岗位
     */
    private String idPost;

    /**
     * 角色id
     */
    private String idRole;

    /**
     * 启用状态：1=未启用，2=已启用，3=已停用，默认1，参见basestatus
     */
    private String status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 失效时间
     */
    private Date disableTime;

    /**
     * 时间戳
     */
    private Date ts;

    /**
     * 是否删除：0=否，1=是，默认0，参见yesorno
     */
    private String deleteFlag;


}
