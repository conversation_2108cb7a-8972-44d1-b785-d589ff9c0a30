package com.fls.upms.mapper;

import com.fls.upms.entity.BaseRole;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.github.yulichang.base.MPJBaseMapper;

import java.util.List;

/**
 * <p>
 * 角色表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @date 2022/05/18
 */
public interface BaseRoleMapper extends MPJBaseMapper<BaseRole> {

    /**
     * 获取对应的角色信息
     * @param idUser 用户id
     * @return List<BaseRole>
     */
    List<BaseRole> getLoginRoles(String idUser);
}
