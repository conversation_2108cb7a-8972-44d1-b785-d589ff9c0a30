package com.fls.upms.controller;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.StrUtil;
import com.fls.common.core.domain.ResponseData;
import com.fls.upms.entity.vo.LoginMenuNode;
import com.fls.upms.service.IBaseAuthService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户权限控制器
 *
 * <AUTHOR>
 * @date 2022/09/06
 **/
@RestController
@RequiredArgsConstructor
@RequestMapping("menu")
public class BaseAuthController {
    private final IBaseAuthService baseAuthService;

    /**
     * 根据appCode获取菜单list
     *
     * @param appCode app的code
     * @return 菜单list列表
     */
    @GetMapping("/app")
    public ResponseData getUserMenuListWithApp(@RequestParam("appCode") String appCode) {
        return ResponseData.ok(baseAuthService.getMenuForApp(appCode));
    }

    @GetMapping("/auth")
    public ResponseData getUserAuthListWithApp(@RequestParam("appCode") String appCode) {
        return ResponseData.ok(baseAuthService.getAuthForApp(appCode));
    }

    /**
     * 根据appCode获取菜单tree
     *
     * @param appCode app的code
     * @return 菜单tree列表
     */
    public ResponseData getUserMenuTreeWithApp(@RequestParam("appCode") String appCode) {
        return null;
    }


    /**
     * v2版本获取指定用户指定项目的菜单列表
     *
     * @param params 请求参数，appcode & userId
     * @return 菜单列表
     */
    @PostMapping("/v2/app")
    public ResponseData<List<LoginMenuNode>> getUserMenusByAppAndUser(@RequestBody Dict params) {
        String appcode = params.getStr("appcode");
        String userId = params.getStr("userId");
        Assert.isTrue(StrUtil.isNotEmpty(appcode) && StrUtil.isNotEmpty(userId), "参数异常");
        return ResponseData.ok(baseAuthService.getMenusByAppAndUser(appcode, userId));
    }

}
