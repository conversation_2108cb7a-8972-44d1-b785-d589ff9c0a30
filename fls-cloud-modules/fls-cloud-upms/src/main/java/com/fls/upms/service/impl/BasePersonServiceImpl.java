package com.fls.upms.service.impl;

import com.fls.upms.entity.BasePerson;
import com.fls.upms.entity.vo.JobAndPostRecordVo;
import com.fls.upms.mapper.BasePersonMapper;
import com.fls.upms.service.IBasePersonService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 员工表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @date 2022/05/18
 */
@Service
public class BasePersonServiceImpl extends ServiceImpl<BasePersonMapper, BasePerson> implements IBasePersonService {

    @Override
    public List<JobAndPostRecordVo> getPersonJobAndPostList(String idIdentity) {
        return this.baseMapper.getPersonJobAndPostList(idIdentity);
    }

    @Override
    public BasePerson getUserInfoByPhoneNumber(String phoneNumber) {
        return this.baseMapper.getUserInfoByPhoneNumber(phoneNumber);
    }
}
