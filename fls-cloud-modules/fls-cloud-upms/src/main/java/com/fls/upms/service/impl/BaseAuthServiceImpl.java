package com.fls.upms.service.impl;

import cn.dev33.satoken.SaManager;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fls.common.core.enums.FlagEnum;
import com.fls.common.core.enums.StatusEnum;
import com.fls.common.core.exception.ServiceException;
import com.fls.common.redis.utils.RedisUtils;
import com.fls.common.satoken.utils.LoginHelper;
import com.fls.upms.api.model.LoginUser;
import com.fls.upms.constants.AppConstants;
import com.fls.upms.entity.BaseAppUser;
import com.fls.upms.entity.BaseAuth;
import com.fls.upms.entity.BaseBizunit;
import com.fls.upms.entity.BaseRole;
import com.fls.upms.entity.BaseRoleDocdata;
import com.fls.upms.entity.BaseRoleauth;
import com.fls.upms.entity.BaseRoledata;
import com.fls.upms.entity.BaseUserRole;
import com.fls.upms.entity.vo.LoginMenuNode;
import com.fls.upms.entity.vo.MenuVo;
import com.fls.upms.mapper.BaseAuthMapper;
import com.fls.upms.param.GetAuthParam;
import com.fls.upms.service.BaseAppUserService;
import com.fls.upms.service.BaseRoleDocdataService;
import com.fls.upms.service.IBaseAuthService;
import com.fls.upms.service.IBaseRoleService;
import com.fls.upms.service.IBaseRoleauthService;
import com.fls.upms.service.IBaseRoledataService;
import com.fls.upms.service.IBaseUserroleService;
import com.fls.upms.utils.RedissonUtils;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 权限表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @date 2022/05/18
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BaseAuthServiceImpl extends MPJBaseServiceImpl<BaseAuthMapper, BaseAuth> implements IBaseAuthService {

    private final IBaseRoleauthService roleauthService;
    private final IBaseRoleService baseRoleMapper;
    private final BaseAppUserService baseAppUserService;
    private final IBaseRoledataService roledataService;
    private final BaseRoleDocdataService roleDocdataService;
    private final IBaseUserroleService userroleService;

    @Override
    public List<LoginMenuNode> getMenuForApp(String appCode) {
        String loginId = (String) StpUtil.getLoginId();
        if (loginId.contains(":")) {
            loginId = loginId.split(":")[1];
        }
        return getMenusByAppAndUser(appCode, loginId);
    }

    @Override
    public List<LoginMenuNode> getMenusByAppAndUser(String appCode, String userId) {
        List<LoginMenuNode> menus = new ArrayList<>();
        List<BaseRole> roles = baseRoleMapper.getLoginRoles(userId);
        if (roles != null && roles.size() > 0) {
            Set<String> roleIdSet = roles.stream().map(BaseRole::getIdRole).collect(Collectors.toSet());
            if (roleIdSet != null && roleIdSet.size() > 0) {
                List<BaseAuth> loginMenus = roleauthService.getMenuByRoleIdsAndApps(roleIdSet, Collections.singletonList(appCode));

                if (ObjectUtil.isNotEmpty(loginMenus)) {
                    // 去重
                    Map<String, BaseAuth> distinctMenus = loginMenus.stream().collect(Collectors.toMap(BaseAuth::getIdAuth, r -> r, (key1, key2) -> key1));
                    distinctMenus.values().forEach(item -> {
                        LoginMenuNode node = new LoginMenuNode();
                        node.setId(item.getIdAuth());
                        if (ObjectUtil.isEmpty(item.getIdParentauth())) {
                            node.setParent("0");
                        } else {
                            node.setParent(item.getIdParentauth());
                        }
                        node.setComponent(item.getComponent());
                        node.setPath(item.getHref());
                        node.setName(item.getName());
                        node.setSort(item.getSort());
                        node.setIcon(item.getIcon());
                        node.setMenuType(item.getMenuType());
                        node.setLink(item.getLink());
                        //目前情况，title和name相同
                        node.setTitle(item.getName());
                        node.setOpenType(item.getOpenType());
                        node.setHidden(false);
                        node.setIdAuth(item.getIdAuth());
                        menus.add(node);
                    });
                }
            }
        }
        return menus;
    }

    @Override
    public List<String> getAuthForApp(String appCode) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        List<BaseAuth> authByRoleIdsAndApps = roleauthService.getAuthByRoleIdsAndApps(loginUser.getRoleIds(), Collections.singletonList(AppConstants.APP_PORTAL_JAVA), null);
        if (ObjectUtil.isEmpty(authByRoleIdsAndApps)) {
            return new ArrayList<>();
        }
        return authByRoleIdsAndApps.stream().map(BaseAuth::getHref).distinct().collect(Collectors.toList());
    }

    @Override
    public List<LoginMenuNode> getMenuForAppAndType(String appCode, String menuType) {
        List<LoginMenuNode> menus = new ArrayList<>();
        LoginUser loginUser = LoginHelper.getLoginUser();
        List<BaseAppUser> appUsers = baseAppUserService.list(new MPJLambdaWrapper<BaseAppUser>().eq(BaseAppUser::getIdUser, loginUser.getIdUser()).eq(BaseAppUser::getDeleteFlag, FlagEnum.NOT.getCode()));
        if (ObjectUtil.isNotEmpty(loginUser.getRoleIds())) {
            List<BaseAuth> auths = roleauthService.getAuthByRoleIdsAndApps(loginUser.getRoleIds(), Collections.singletonList(appCode), menuType);
            if (ObjectUtil.isNotEmpty(auths)) {
                Map<String, BaseAuth> distinctMenus = auths.stream().collect(Collectors.toMap(BaseAuth::getIdAuth, r -> r, (key1, key2) -> key1));
                distinctMenus.values().forEach(item -> {
                    LoginMenuNode node = new LoginMenuNode();
                    node.setId(item.getIdAuth());
                    if (ObjectUtil.isEmpty(item.getIdParentauth())) {
                        node.setParent("0");
                    } else {
                        node.setParent(item.getIdParentauth());
                    }
                    node.setComponent(item.getComponent());
                    node.setPath(item.getHref());
                    node.setName(item.getName());
                    node.setSort(item.getSort());
                    node.setIcon(item.getIcon());
                    node.setMenuType(item.getMenuType());
                    node.setLink(item.getLink());
                    //目前情况，title和name相同
                    node.setTitle(item.getName());
                    node.setOpenType(item.getOpenType());
                    node.setHidden(true);
                    for (BaseAppUser appUser : appUsers) {
                        if (ObjectUtils.notEqual(appUser.getIdAuth(), item.getIdAuth())) {
                            node.setUserDefaultFlag(FlagEnum.YES.getCode());
                        }
                    }
                    node.setIdAuth(item.getIdAuth());
                    menus.add(node);
                });
            }
        }
        return menus;
    }

    @Override
    public Map<String, Object> getAuth(String token) {
        return getCacheAuths(token, null);
    }

    @Override
    public Set<String> getAuthOrg(GetAuthParam param) {
        if (ObjectUtil.isEmpty(param.getHref())) {
            throw new ServiceException("缺少href参数！");
        }
        Map<String, Object> cacheAuths = getCacheAuths(param.getToken(), param.getProjectName());
        Set<String> orgs = new HashSet<>();
        cacheAuths.forEach((key, value) -> {
            JSONObject jsonObject = JSONArray.parseObject(JSON.toJSONString(value));
            List<BaseAuth> menuList = JSONArray.parseArray(jsonObject.getString("menu"), BaseAuth.class);
            Set<String> hrefs = menuList.stream().map(BaseAuth::getHref).collect(Collectors.toSet());
            if (hrefs.contains(param.getHref())) {
                JSONArray roleOrgs = JSONArray.parseArray(jsonObject.getString("org"));
                roleOrgs.forEach(item -> {
                    orgs.add(JSON.parseObject(JSON.toJSONString(item)).getString("id_org"));
                });
            }
        });
        return orgs;
    }

    @Override
    public Set<String> getAuthPost(GetAuthParam param) {
        if (ObjectUtil.isEmpty(param.getHref())) {
            throw new ServiceException("缺少href参数！");
        }
        Map<String, Object> cacheAuths = getCacheAuths(param.getToken(), param.getProjectName());
        Set<String> orgs = new HashSet<>();
        cacheAuths.forEach((key, value) -> {
            JSONObject jsonObject = JSONArray.parseObject(JSON.toJSONString(value));
            List<BaseAuth> menuList = JSONArray.parseArray(jsonObject.getString("menu"), BaseAuth.class);
            Set<String> hrefs = menuList.stream().map(BaseAuth::getHref).collect(Collectors.toSet());
            if (hrefs.contains(param.getHref())) {
                JSONArray roleOrgs = JSONArray.parseArray(jsonObject.getString("org"));
                roleOrgs.forEach(item -> {
                    orgs.add(JSON.parseObject(JSON.toJSONString(item)).getString("id_post"));
                });
            }
        });
        return orgs;
    }

    @Override
    public Set<String> getAuthDept(GetAuthParam param) {
        if (ObjectUtil.isEmpty(param.getHref())) {
            throw new ServiceException("缺少href参数！");
        }
        Map<String, Object> cacheAuths = getCacheAuths(param.getToken(), param.getProjectName());
        Set<String> orgs = new HashSet<>();
        cacheAuths.forEach((key, value) -> {
            JSONObject jsonObject = JSONArray.parseObject(JSON.toJSONString(value));
            List<BaseAuth> menuList = JSONArray.parseArray(jsonObject.getString("menu"), BaseAuth.class);
            Set<String> hrefs = menuList.stream().map(BaseAuth::getHref).collect(Collectors.toSet());
            if (hrefs.contains(param.getHref())) {
                JSONArray roleOrgs = JSONArray.parseArray(jsonObject.getString("org"));
                roleOrgs.forEach(item -> {
                    orgs.add(JSON.parseObject(JSON.toJSONString(item)).getString("id_department"));
                });
            }
        });
        return orgs;
    }

    @Override
    public Set<String> getAuthDoc(GetAuthParam param) {
        if (ObjectUtil.isEmpty(param.getIdResource())) {
            throw new ServiceException("缺少idResource参数！");
        }
        if (ObjectUtil.isEmpty(param.getHref())) {
            throw new ServiceException("缺少href参数！");
        }
        Map<String, Object> cacheAuths = getCacheAuths(param.getToken(), param.getProjectName());
        Set<String> datas = new HashSet<>();
        cacheAuths.forEach((key, value) -> {
            JSONObject jsonObject = JSONArray.parseObject(JSON.toJSONString(value));
            List<BaseAuth> menuList = JSONArray.parseArray(jsonObject.getString("menu"), BaseAuth.class);
            Set<String> hrefs = menuList.stream().map(BaseAuth::getHref).collect(Collectors.toSet());
            if (hrefs.contains(param.getHref())) {
                JSONArray roleDocs = JSONArray.parseArray(jsonObject.getString("data"));
                roleDocs.forEach(item -> {
                    String idResource = JSON.parseObject(JSON.toJSONString(item)).getString("id_resource");
                    if (ObjectUtil.equal(param.getIdResource(), idResource)) {
                        datas.add(JSON.parseObject(JSON.toJSONString(item)).getString("id_resdoc"));
                    }
                });
            }
        });
        return datas;
    }

    @Override
    public List<MenuVo> getAuthMenu(GetAuthParam param) {
        Map<String, Object> cacheAuths = getCacheAuths(param.getToken(), param.getProjectName());
        List<BaseAuth> menuList = new ArrayList<>();
        cacheAuths.forEach((key, value) -> {
            JSONObject jsonObject = JSONArray.parseObject(JSON.toJSONString(value));
            List<BaseAuth> menu = JSONArray.parseArray(jsonObject.getString("menu"), BaseAuth.class);
            menuList.addAll(menu);
        });
        //去重
        List<BaseAuth> auths = menuList.stream().distinct().collect(Collectors.toList());
        //封装菜单
        return auths.stream().map(item -> new MenuVo()
            .setId(item.getIdAuth())
            .setCode(item.getCode())
            .setComponent(item.getComponent())
            .setPath(item.getHref())
            .setName(item.getName())
            .setSort(item.getSort())
            .setIcon(item.getIcon())
            .setMenuType(item.getMenuType())
            .setLink(item.getLink())
            //目前情况，title和name相同
            .setTitle(item.getName())
            .setOpenType(item.getOpenType())
            .setParentId(ObjectUtil.isEmpty(item.getIdParentauth()) ? "0" : item.getIdParentauth())
        ).sorted(Comparator.comparing(MenuVo::getCode).thenComparing(MenuVo::getSort)).distinct().collect(Collectors.toList());
    }

    @Override
    public Boolean setAuth(GetAuthParam param) {
        Map<String, String> cacheToken;
        try {
            cacheToken = RedissonUtils.getCacheMap(param.getToken());
        } catch (Exception e) {
            throw new ServiceException("token信息获取失败");
        }
        if (ObjectUtil.isEmpty(cacheToken)) {
            throw new ServiceException("token信息验证失败");
        }
        String projectName = cacheToken.get("projectName");
        String idUser = cacheToken.get("idUser");
        if (ObjectUtil.isEmpty(projectName) || ObjectUtil.isEmpty(idUser)) {
            throw new ServiceException("token信息解析失败");
        }
        setCacheAuths(param.getToken(), projectName, idUser);
        return true;
    }

    @Override
    public Map<String, Object> setCacheAuths(String token, String projectName, String idUser) {
        Dict dict = new Dict();
        List<BaseUserRole> userRoles = userroleService.list(new MPJLambdaWrapper<BaseUserRole>().eq(BaseUserRole::getIdUser, idUser).eq(BaseUserRole::getDeleteFlag, FlagEnum.NOT.getCode()));
        if (ObjectUtil.isNotEmpty(userRoles)) {
            Set<String> roles = userRoles.stream().map(BaseUserRole::getIdRole).collect(Collectors.toSet());
            Map<String, Object> auths = getAuths(roles, projectName);
            dict.set(projectName, auths);
            RedissonUtils.setCacheMap(token, dict);
            long tokenTimeout = SaManager.getConfig().getTimeout();
            RedissonUtils.expire(token, Duration.ofSeconds(tokenTimeout));
            return auths;
        }
        return null;
    }

    /**
     * 根据token查询缓存权限，没有重新缓存
     *
     * @param token 票据凭证
     * @return 权限
     */
    public Map<String, Object> getCacheAuths(String token, String projectName) {
        Map<String, String> cacheMap;
        try {
            cacheMap = RedissonUtils.getCacheMap(token);
        } catch (Exception e) {
            throw new ServiceException("token信息获取失败");
        }
        if (ObjectUtil.isEmpty(cacheMap)) {
            throw new ServiceException("token信息验证失败");
        }
        String idUser = cacheMap.get("idUser");
        if (ObjectUtil.isEmpty(idUser)) {
            throw new ServiceException("token信息解析失败");
        }
        if (ObjectUtil.isEmpty(projectName)) {
            projectName = cacheMap.get("projectName");
        }
        Object cacheAuths = cacheMap.get(projectName);
        if (ObjectUtil.isEmpty(cacheAuths)) {
            return setCacheAuths(token, projectName, idUser);
        }
        return JSONObject.parseObject(JSON.toJSONString(cacheAuths));
    }

    /**
     * 封装权限
     *
     * @param roleIds     角色
     * @param projectName 工程名
     * @return 权限
     */
    public Map<String, Object> getAuths(Set<String> roleIds, String projectName) {
        Map<String, Object> auths = new HashMap<>();
        if (ObjectUtil.isNotEmpty(roleIds)) {
            for (String roleId : roleIds) {
                Map<String, Object> map = new HashMap<>();
                //读取用户角色功能权限
                MPJLambdaWrapper<BaseRoleauth> menuWrapper = new MPJLambdaWrapper<>();
                menuWrapper.selectAll(BaseAuth.class)
                    .select(BaseRoleauth::getIdRole)
                    .leftJoin(BaseAuth.class, BaseAuth::getIdAuth, BaseRoleauth::getIdAuth)
                    .eq(BaseRoleauth::getIdRole, roleId)
                    .eq(BaseRoleauth::getDeleteFlag, FlagEnum.NOT.getCode())
                    .eq(BaseRoleauth::getStatus, StatusEnum.ENABLE.getCode())
                    .eq(BaseAuth::getProjectName, projectName);
                List<BaseAuth> menuList = roleauthService.selectJoinList(BaseAuth.class, menuWrapper);
                if (CollUtil.isEmpty(menuList)) {
                    continue;
                }
                map.put("menu", menuList);
                //读取用户角色组织权限
                MPJLambdaWrapper<BaseRoledata> orgWrapper = new MPJLambdaWrapper<>();
                orgWrapper.select(BaseRoledata::getIdOrg, BaseRoledata::getIdDepartment, BaseRoledata::getIdPostseries, BaseRoledata::getIdPost)
                    .select(BaseBizunit::getIdBizunit)
                    .leftJoin(BaseBizunit.class, BaseBizunit::getIdOrg, BaseRoledata::getIdOrg)
                    .eq(BaseRoledata::getIdRole, roleId)
                    .eq(BaseRoledata::getDeleteFlag, FlagEnum.NOT.getCode())
                    .eq(BaseRoledata::getStatus, StatusEnum.ENABLE.getCode())
                    .eq(BaseBizunit::getDeleteFlag, FlagEnum.NOT.getCode())
                    .eq(BaseBizunit::getStatus, StatusEnum.ENABLE.getCode());
                List<Map<String, Object>> orgList = roledataService.selectJoinMaps(orgWrapper);
                map.put("org", orgList);
                //读取用户角色档案数据权限
                MPJLambdaWrapper<BaseRoleDocdata> dataWrapper = new MPJLambdaWrapper<>();
                dataWrapper.select(BaseRoleDocdata::getIdResource, BaseRoleDocdata::getIdResdoc)
                    .eq(BaseRoleDocdata::getIdRole, roleId)
                    .eq(BaseRoleDocdata::getDeleteFlag, FlagEnum.NOT.getCode())
                    .eq(BaseRoleDocdata::getStatus, StatusEnum.ENABLE.getCode());
                List<Map<String, Object>> dataList = roleDocdataService.selectJoinMaps(dataWrapper);
                map.put("data", dataList);
                auths.put(roleId, map);
            }
        }
        return auths;
    }

    /**
     * 校验ticket并返回用户上下文
     *
     * @param ticket 票据凭证
     * @return 用户上下文
     */
    public LoginUser getTicketByUser(String ticket) {
        if (ObjectUtil.isEmpty(ticket)) {
            log.error("访问失败，缺少ticket参数");
            throw new ServiceException("缺少ticket参数！");
        }
        String token = RedisUtils.getCacheObject(ticket);
        if (ObjectUtil.isEmpty(token)) {
            throw new ServiceException("ticket已过期，请重新申请！");
        }
        return LoginHelper.getLoginUser(token);
    }

    private Map<String, Object> getAuthsByCodeAndUser(String projectCode, String idUser) {
        List<BaseUserRole> userRoles = userroleService.list(new MPJLambdaWrapper<BaseUserRole>().eq(BaseUserRole::getIdUser, idUser).eq(BaseUserRole::getDeleteFlag, FlagEnum.NOT.getCode()));
        if (ObjectUtil.isNotEmpty(userRoles)) {
            Set<String> roles = userRoles.stream().map(BaseUserRole::getIdRole).collect(Collectors.toSet());
            return getAuths(roles, projectCode);
        }
        return null;
    }
}
