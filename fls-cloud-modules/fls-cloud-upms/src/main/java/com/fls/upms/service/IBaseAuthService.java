package com.fls.upms.service;

import com.fls.upms.entity.BaseAuth;
import com.fls.upms.entity.vo.LoginMenuNode;
import com.fls.upms.entity.vo.MenuVo;
import com.fls.upms.param.GetAuthParam;
import com.fls.upms.param.GetBusinessAuthParam;
import com.github.yulichang.base.MPJBaseService;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 权限表 服务类
 * </p>
 *
 * <AUTHOR>
 * @date 2022/05/18
 */
public interface IBaseAuthService extends MPJBaseService<BaseAuth> {

    /**
     * 根据appCode获取菜单list
     * @param appCode app的code
     * @return 菜单list列表
     */
    List<LoginMenuNode> getMenuForApp(String appCode);

    List<String>  getAuthForApp(String appCode);

    List<LoginMenuNode> getMenuForAppAndType(String appCode, String menuType);

    /**
     * 2024-10-17补充：新系统修改数据权限逻辑，通过upms获取数据权限列表，由业务系统自行缓存数据并进行管理
     * @param token
     * @return
     */
    Map<String, Object> getAuth(String token);

    Set<String> getAuthOrg(GetAuthParam param);

    Set<String> getAuthPost(GetAuthParam param);

    Set<String> getAuthDept(GetAuthParam param);

    Set<String> getAuthDoc(GetAuthParam param);

    List<MenuVo> getAuthMenu(GetAuthParam param);

    Boolean setAuth(GetAuthParam param);

    Map<String, Object> setCacheAuths(String token, String projectName, String idUser);

    /**
     * 根据appCode和userId获取菜单列表
     * @param appCode 应用编码
     * @param userId 用户id
     * @return 菜单列表
     */
    List<LoginMenuNode> getMenusByAppAndUser(String appCode,String userId);
}
