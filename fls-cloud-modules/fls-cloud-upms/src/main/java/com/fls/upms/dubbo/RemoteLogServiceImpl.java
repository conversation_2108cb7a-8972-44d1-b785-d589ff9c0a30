package com.fls.upms.dubbo;

import com.fls.upms.api.RemoteLogService;
import com.fls.upms.api.entity.SysOperLog;
import com.fls.upms.service.ISysOperLogService;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.dubbo.rpc.RpcContext;
import org.springframework.stereotype.Service;

/**
 * 操作日志记录
 *
 * <AUTHOR>
 * @date 2022/05/20
 */
@RequiredArgsConstructor
@Service
@DubboService
public class RemoteLogServiceImpl implements RemoteLogService {

    private final ISysOperLogService operLogService;

    @Override
    public Boolean saveLog(SysOperLog sysOperLog) {
        return operLogService.insertOperlog(sysOperLog) > 0;
    }


}
