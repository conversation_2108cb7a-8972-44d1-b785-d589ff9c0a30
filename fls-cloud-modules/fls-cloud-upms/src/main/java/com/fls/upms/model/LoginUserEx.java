package com.fls.upms.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fls.upms.api.model.LoginUser;
import com.fls.upms.entity.BaseAuth;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 扩展的LoginUser，不修改原有dubbo的api，扩展字段
 *
 * <AUTHOR>
 * @since 2024-10-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class LoginUserEx extends LoginUser {

    private static final long serialVersionUID = 1L;

    @JsonIgnore
    private List<BaseAuth> originMenus;
}
