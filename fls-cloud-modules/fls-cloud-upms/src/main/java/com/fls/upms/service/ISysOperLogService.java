package com.fls.upms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fls.common.mybatis.core.page.PageQuery;
import com.fls.common.mybatis.core.page.TableDataInfo;
import com.fls.upms.api.entity.SysOperLog;

import java.util.List;

/**
 * 操作日志 服务层
 *
 * <AUTHOR>
 * @date 2022/05/19
 */
public interface ISysOperLogService extends IService<SysOperLog> {

    /**
     * 新增操作日志
     *
     * @param operLog 操作日志对象
     * @return 结果
     */
    int insertOperlog(SysOperLog operLog);


}
