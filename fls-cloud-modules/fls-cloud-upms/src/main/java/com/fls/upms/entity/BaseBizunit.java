package com.fls.upms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 经营主体表
 * </p>
 *
 * <AUTHOR>
 * @date 2022/05/18
 */
@Data
@NoArgsConstructor
@TableName("t_base_bizunit")
public class BaseBizunit implements Serializable {


    /**
     * 主键
     */
    @TableId(value = "id_bizunit", type = IdType.INPUT)
    private String idBizunit;

    /**
     * 名称
     */
    private String name;

    /**
     * 显示顺序
     */
    private Integer displayOrder;

    /**
     * 内部编号
     */
    private String innercode;

    /**
     * 经营主体类型 1=终端，2=批发，3=外贸 参见bizunit_type
     */
    private String bizunitType;

    /**
     * 开始日期
     */
    private Date beginDate;

    /**
     * 结束日期
     */
    private Date endDate;

    /**
     * 所属集团
     */
    private String idGroup;

    /**
     * 启用状态：1=未启用，2=已启用，3=已停用，默认1，参见basestatus
     */
    private String status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 失效时间
     */
    private Date disableTime;

    /**
     * 时间戳
     */
    private Date ts;

    /**
     * 是否删除：0=否，1=是，默认0，参见yesorno
     */
    private String deleteFlag;

    /**
     * 核算归属主体
     */
    private String idAccunit;

    /**
     * 所属组织
     */
    private String idOrg;

    /**
     * 所属地区名称
     */
    private String areaName;

    /**
     * 经理
     */
    private String idManager;

    /**
     * 维修经理
     */
    private String idManagerMaintenance;

    /**
     * 自检登记机关 checking_city_dep
     */
    private String checkingCityDep;

    /**
     * 社会信用代码
     */
    private String appunitcode;

    /**
     * 资料关联id
     */
    private String idLinkDoc;

    /**
     * 单据编码，没用的值，仅用于资料上传不报错
     */
    private String billCode;

    /**
     * 使用单位邮政编码
     */
    private String usecompostcode;

    /**
     * 使用单位地址
     */
    private String usecomplace;

    /**
     * 使用单位固定电话
     */
    private String usecomtel;


}
