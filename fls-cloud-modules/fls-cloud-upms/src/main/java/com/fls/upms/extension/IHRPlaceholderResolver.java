package com.fls.upms.extension;

import cn.hutool.core.codec.Base64;
import com.fls.common.core.utils.StringUtils;
import com.fls.common.satoken.utils.LoginHelper;
import com.fls.upms.config.IHRSSOConfig;
import com.fls.upms.constants.AppConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.crypto.Cipher;
import java.nio.charset.StandardCharsets;
import java.security.Key;
import java.security.KeyFactory;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Supplier;

/**
 * IHRPlaceholderResolver
 *
 * <AUTHOR>
 * @since 2024-09-25
 */
@Component
@Slf4j
public class IHRPlaceholderResolver implements MenuUrlResolver {

    private static final String KEY_ALGORITHM = "RSA";

    @Resource
    private IHRSSOConfig ihrSSOSSOProperties;

    public static final ConcurrentHashMap<String, Supplier<String>> PLACEHOLDER_FUNCTIONS = new ConcurrentHashMap<>();

    public static void registerFunction(String placeholder, Supplier<String> function) {
        PLACEHOLDER_FUNCTIONS.put(placeholder, function);
    }

    @Override
    public String resolve(String url) {
        for (String placeholder : PLACEHOLDER_FUNCTIONS.keySet()) {
            url = url.replace(AppConstants.PLACEHOLDER_PREFIX + placeholder + AppConstants.PLACEHOLDER_SUFFIX, PLACEHOLDER_FUNCTIONS.get(placeholder).get());
        }
        return url;
    }

    /**
     * 获取默认的跳转地址,要从中间服务做一次重定向，因为IHR SSO的时间戳防盗链
     * @return IHR默认的单点登录跳转地址
     */
    public String getDefaultRedirectUrl(String phone) {
        String signature = generateSignatureByPhone(phone);
        return ihrSSOSSOProperties.getUrl() + "?loginfree_licence=" + ihrSSOSSOProperties.getLicence() + "&signature=" + signature + "&redirect_url=" + AppConstants.DEFAULT_PATH;
    }

    private String generateSignature() {
        return generateSignatureByPhone(LoginHelper.getLoginUser().getPhone());
    }

    private String getLicense() {
        return Optional.ofNullable(ihrSSOSSOProperties.getLicence()).orElse("");
    }

    private String getPhone() {
        return Optional.ofNullable(LoginHelper.getLoginUser().getPhone()).orElse("");
    }

    private String generateSignatureByPhone(String phone) {
        String content = StringUtils.format("company_id={}&mobile_no={}&timestamp={}", ihrSSOSSOProperties.getClientId(), phone, System.currentTimeMillis());
        try {
            PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(Base64.decode(ihrSSOSSOProperties.getPrivateKey()));
            KeyFactory keyFactory = KeyFactory.getInstance(KEY_ALGORITHM);
            Key key = keyFactory.generatePrivate(keySpec);
            Cipher cipher = Cipher.getInstance(keyFactory.getAlgorithm());
            cipher.init(Cipher.ENCRYPT_MODE, key);
            byte[] resultBytes = cipher.doFinal(content.getBytes(StandardCharsets.UTF_8));
            return Base64.encode(resultBytes).replaceAll("\\+", "%2B");
        } catch (Exception e) {
            log.error("generate ihr signature failed : {}", e.getMessage());
            return "";
        }
    }

    @Override
    public void afterPropertiesSet() {
        //这里保留占位符替换的扩展，因为IHR的单点登录有时间戳防盗链，做重定向中转处理
//        registerFunction("signature", this::generateSignature);
//        registerFunction("licence", this::getLicense);
        registerFunction("phone", this::getPhone);
//        registerFunction("userId", LoginHelper::getUserId);
        ResolverStrategyFactory.registerResolver(ResolveStrategyEnum.IHR.name(), this);
    }
}
