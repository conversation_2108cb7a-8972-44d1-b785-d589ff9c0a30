package com.fls.upms.entity.vo;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class AppUserVo {
    private String idAppUser;

    /**
     * 应用id
     */
    private String idBaseApp;

    /**
     * 权限id
     */
    private String idAuth;

    /**
     * 用户id
     */
    private String idUser;

    /**
     * 编码
     */
    private String code;

    /**
     * 名称
     */
    private String name;

    /**
     * 备注
     */
    private String memo;

    /**
     * 类型 1=系统管理，2=业务 参见auth_type
     */
    private String authType;

    /**
     * 启用状态：1=未启用，2=已启用，3=已停用，默认1，参见basestatus
     */
    private String status;

    /**
     * url地址
     */
    private String href;

    /**
     * 图标
     */
    private String icon;

    /**
     * 父级权限
     */
    private String idParentauth;

    /**
     * 应用工程名
     */
    private String projectName;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 菜单类型： M目录 C菜单 F按钮
     */
    private String menuType;

    /**
     * 组件地址
     */
    private String component;

    /**
     * 打开方式： 0无 1组件 2内链 3外链
     */
    private String openType;

    /**
     * 链接地址
     */
    private String link;

    /**
     * 内网地址
     */
    private String lanUrl;

    /**
     * 应用标识
     */
    private String label;

}
