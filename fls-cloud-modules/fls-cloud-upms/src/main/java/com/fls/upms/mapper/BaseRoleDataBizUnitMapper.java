package com.fls.upms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fls.upms.entity.BaseRoleDataBizUnit;
import com.fls.upms.entity.dto.BizUnitResult;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * 经营主体数据权限表
 *
 * <AUTHOR>
 */
public interface BaseRoleDataBizUnitMapper extends BaseMapper<BaseRoleDataBizUnit> {
    /**
     * 获取组织对应的经营主体
     * @param orgIds
     * @return
     */
    List<BizUnitResult> getUnitByIdOrgs(@Param("orgIds") Set<String> orgIds);
}
