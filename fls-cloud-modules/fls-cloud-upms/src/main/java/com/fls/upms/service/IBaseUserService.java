package com.fls.upms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fls.upms.api.model.LoginUser;
import com.fls.upms.entity.BaseUser;
import com.fls.upms.param.LoginAuthParam;
import com.fls.upms.param.LoginParam;

/**
 * <p>
 * 用户表 服务类
 * </p>
 *
 * <AUTHOR>
 * @date 2022/05/18
 */
public interface IBaseUserService extends IService<BaseUser> {

    BaseUser getUserInfoByIdPerson(String idPerson);

    BaseUser getUserInfoByUserCode(String userCode);

    /**
     * 通过登录用户名与密码获取用户id
     *
     * @param username 登录用户名称
     * @param password 登录用户密码
     * @return 用户id
     */
    String getUserIdByNameAndPwd(String username, String password);

    LoginUser buildLoginUser(String userId, String appcode);

    LoginUser login(LoginParam params);

    void logout(String token);

    void setLoginAuth(LoginAuthParam param);

    boolean checkLogin(String token);
}
