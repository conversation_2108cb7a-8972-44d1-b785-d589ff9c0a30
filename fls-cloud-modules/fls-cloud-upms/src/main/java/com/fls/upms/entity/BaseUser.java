package com.fls.upms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 用户表
 * </p>
 *
 * <AUTHOR>
 * @date 2022/05/18
 */
@Data
@NoArgsConstructor
@TableName("t_base_user")
public class BaseUser implements Serializable {


    /**
     * 主键
     */
    @TableId(value = "id_user", type = IdType.INPUT)
    private String idUser;

    /**
     * 用户名
     */
    private String code;

    /**
     * 昵称
     */
    private String name;

    /**
     * 密码
     */
    private String password;

    /**
     * 身份类型：0=员工，1=客户，2=供应商，3=审计，4=外部系统，5=开发者，6=合作伙伴，默认0，参见identity_type
     */
    private String identityType;

    /**
     * 身份id
     */
    private String idIdentity;

    /**
     * NC用户pk值
     */
    private String pkUser;

    /**
     * NC身份pk值
     */
    private String pkBaseDoc;

    /**
     * 启用状态：1=未启用，2=已启用，3=已停用，默认1，参见basestatus
     */
    private String status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 失效时间
     */
    private Date disableTime;

    /**
     * 时间戳
     */
    private Date ts;

    /**
     * 是否删除：0=否，1=是，默认0，参见yesorno
     */
    private String deleteFlag;

    /**
     * 盐值
     */
    private String salt;

    /**
     * 个人虚拟地址，将个人视为存放地址
     */
    private String idAddress;

    /**
     * 检保人员标识，0=否，1=是，默认0
     */
    private String maintenanceFlag;

    /**
     * 内部收货地址id
     */
    private String idInneraddress;

    /**
     * 手机端登录时返回的token
     */
    private String miopLoginToken;

    /**
     * 手机端token最新更新时间
     */
    private Date miopLoginTokenTs;


}
