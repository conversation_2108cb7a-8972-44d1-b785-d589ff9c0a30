package com.fls.upms.controller;

import com.fls.common.core.domain.ResponseData;
import com.fls.upms.api.model.LoginUser;
import com.fls.upms.param.BuildUserParam;
import com.fls.upms.param.CheckPwdParam;
import com.fls.upms.service.IBaseUserService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;

/**
 * 用户信息控制器
 *
 * <AUTHOR>
 * @date 2022/09/06
 **/
@RestController
@RequiredArgsConstructor
@RequestMapping("user")
public class BaseUserController {

    @Resource
    private IBaseUserService userService;

    @PostMapping("/checkPwd")
    public ResponseData checkPwd(@Validated @RequestBody CheckPwdParam params) {
        String userId = userService.getUserIdByNameAndPwd(params.getUsername(), params.getPassword());
        HashMap<String, String> result = new HashMap<>();
        result.put("userId", userId);
        return ResponseData.ok(result);
    }

    @PostMapping("/loginUser")
    public ResponseData<LoginUser> getLoginUser(@Validated @RequestBody BuildUserParam params) {
        return ResponseData.ok(userService.buildLoginUser(params.getUserId(), params.getAppcode()));
    }
}
