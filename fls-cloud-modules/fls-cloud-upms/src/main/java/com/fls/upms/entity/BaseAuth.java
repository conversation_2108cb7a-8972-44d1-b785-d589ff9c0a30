package com.fls.upms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 权限表
 * </p>
 *
 * <AUTHOR>
 * @date 2022/05/18
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("t_base_auth")
public class BaseAuth implements Serializable {


    /**
     * 主键
     */
    @TableId(value = "id_auth", type = IdType.INPUT)
    private String idAuth;

    /**
     * 编码
     */
    private String code;

    /**
     * 名称
     */
    private String name;

    /**
     * 备注
     */
    private String memo;

    /**
     * 类型 1=系统管理，2=业务 参见auth_type
     */
    private String authType;

    /**
     * 启用状态：1=未启用，2=已启用，3=已停用，默认1，参见basestatus
     */
    private String status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 失效时间
     */
    private Date disableTime;

    /**
     * 时间戳
     */
    private Date ts;

    /**
     * 是否删除：0=否，1=是，默认0，参见yesorno
     */
    private String deleteFlag;

    /**
     * url地址
     */
    private String href;

    /**
     * 图标
     */
    private String icon;

    /**
     * 父级权限
     */
    private String idParentauth;

    /**
     * 应用工程名
     */
    private String projectName;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 菜单类型： M目录 C菜单 F按钮
     */
    private String menuType;

    /**
     * 组件地址
     */
    private String component;

    /**
     * 打开方式： 0无 1组件 2内链 3外链
     */
    private String openType;

    /**
     * 链接地址
     */
    private String link;


    /**
     * 角色id
     */
    @TableField(exist = false)
    private String idRole;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        BaseAuth baseAuth = (BaseAuth) o;
        return Objects.equals(idAuth, baseAuth.idAuth) && Objects.equals(code, baseAuth.code) && Objects.equals(name, baseAuth.name) && Objects.equals(memo, baseAuth.memo) && Objects.equals(authType, baseAuth.authType) && Objects.equals(status, baseAuth.status) && Objects.equals(createTime, baseAuth.createTime) && Objects.equals(creator, baseAuth.creator) && Objects.equals(disableTime, baseAuth.disableTime) && Objects.equals(ts, baseAuth.ts) && Objects.equals(deleteFlag, baseAuth.deleteFlag) && Objects.equals(href, baseAuth.href) && Objects.equals(icon, baseAuth.icon) && Objects.equals(idParentauth, baseAuth.idParentauth) && Objects.equals(projectName, baseAuth.projectName) && Objects.equals(sort, baseAuth.sort) && Objects.equals(menuType, baseAuth.menuType) && Objects.equals(component, baseAuth.component) && Objects.equals(openType, baseAuth.openType) && Objects.equals(link, baseAuth.link);
    }

    @Override
    public int hashCode() {
        return Objects.hash(idAuth, code, name, memo, authType, status, createTime, creator, disableTime, ts, deleteFlag, href, icon, idParentauth, projectName, sort, menuType, component, openType, link);
    }
}
