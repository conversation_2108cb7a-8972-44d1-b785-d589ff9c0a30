package com.fls.upms.service.impl;

import cn.hutool.core.util.ArrayUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fls.upms.mapper.SysOperLogMapper;
import com.fls.upms.service.ISysOperLogService;
import com.fls.common.core.utils.StringUtils;
import com.fls.common.mybatis.core.page.PageQuery;
import com.fls.common.mybatis.core.page.TableDataInfo;
import com.fls.upms.api.entity.SysOperLog;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 操作日志 服务层处理
 *
 * <AUTHOR>
 * @date 2022/05/19
 */
@RequiredArgsConstructor
@Service
public class SysOperLogServiceImpl extends ServiceImpl<SysOperLogMapper, SysOperLog> implements ISysOperLogService {

    private final SysOperLogMapper baseMapper;


    /**
     * 新增操作日志
     *
     * @param operLog 操作日志对象
     * @return 结果
     */
    @Override
    public int insertOperlog(SysOperLog operLog) {
        operLog.setOperTime(new Date());
        return baseMapper.insert(operLog);
    }


}
