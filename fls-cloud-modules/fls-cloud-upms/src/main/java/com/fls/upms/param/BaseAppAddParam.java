package com.fls.upms.param;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Data
public class BaseAppAddParam {

    /**
     * 应用名称
     */
    @NotBlank(message = "应用名称不能为空")
    private String name;

    /**
     * 应用标示
     */
    @NotBlank(message = "应用名称标示不能为空")
    private String label;

    /**
     * 应用类型:1=内部应用、2=外部应用
     */
    @NotBlank(message = "应用名称标示不能为空")
    private String appType;

    /**
     * 系统内网URL
     */
    @NotBlank(message = "系统内网URL不能为空")
    private String lanUrl;

    /**
     * 系统外网URL
     */
    private String wanUrl;

    /**
     * 首页URL
     */
    @NotBlank(message = "首页URL不能为空")
    private String indexUrl;

    /**
     * 权限标识
     */
    private String permission;

    /**
     * 权限标识
     */
    @NotBlank(message = "应用工程名不能为空")
    private String projectName;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 备注
     */
    private String memo;

    /**
     * 启用状态：1=未启用，2=已启用，3=已停用，默认1，参见basestatus
     */
    private String status;

    /**
     * 图标
     */
    private String icon;
}
