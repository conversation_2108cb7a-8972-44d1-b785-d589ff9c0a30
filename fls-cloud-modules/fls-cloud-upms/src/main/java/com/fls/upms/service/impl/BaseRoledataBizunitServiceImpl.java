package com.fls.upms.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fls.upms.entity.BaseRoleDataBizUnit;
import com.fls.upms.entity.dto.BizUnitResult;
import com.fls.upms.mapper.BaseRoleDataBizUnitMapper;
import com.fls.upms.service.BaseRoleDataBizUnitService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;


/**
 * 经营主体数据权限表service接口实现类
 *
 * <AUTHOR>
 */
@Service
public class BaseRoledataBizunitServiceImpl extends ServiceImpl<BaseRoleDataBizUnitMapper, BaseRoleDataBizUnit> implements BaseRoleDataBizUnitService {

    @Override
    public List<BizUnitResult> getUnitByIdOrgs(Set<String> orgIds) {
        return this.baseMapper.getUnitByIdOrgs(orgIds);
    }
}
