package com.fls.upms.service;

import com.fls.upms.entity.BasePerson;
import com.baomidou.mybatisplus.extension.service.IService;
import com.fls.upms.entity.vo.JobAndPostRecordVo;

import java.util.List;

/**
 * <p>
 * 员工表 服务类
 * </p>
 *
 * <AUTHOR>
 * @date 2022/05/18
 */
public interface IBasePersonService extends IService<BasePerson> {

    /**
     * 查询员工任职流水
     * @param idIdentity
     * @return
     */
    List<JobAndPostRecordVo> getPersonJobAndPostList(String idIdentity);

    BasePerson getUserInfoByPhoneNumber(String phoneNumber);
}
