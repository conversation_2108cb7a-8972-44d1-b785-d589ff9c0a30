package com.fls.upms.service.impl;

import com.fls.upms.entity.BaseAuth;
import com.fls.upms.entity.BaseRoleauth;
import com.fls.upms.mapper.BaseRoleauthMapper;
import com.fls.upms.service.IBaseRoleauthService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.base.MPJBaseServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 角色功能权限表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @date 2022/05/18
 */
@Service
public class BaseRoleauthServiceImpl extends MPJBaseServiceImpl<BaseRoleauthMapper, BaseRoleauth> implements IBaseRoleauthService {

    @Override
    public List<BaseAuth> getMenuByRoleIdsAndApps(Set<String> roleIds, List<String> apps) {
        return this.getMenuByRoleIdsAndApps(roleIds, apps,null);
    }

    @Override
    public List<BaseAuth> getMenuByRoleIdsAndApps(Set<String> roleIds, List<String> apps, String href) {
        return this.baseMapper.getMenuByRoleIdsAndApps(roleIds, apps,href);
    }

    @Override
    public List<BaseAuth> getAuthByRoleIdsAndApps(Set<String> roleIds, List<String> apps,String menuType) {
        return this.baseMapper.getAuthByRoleIdsAndApps(roleIds, apps,menuType);
    }


    @Override
    public List<BaseAuth> getAuthByRoleIdsAndUrls(Set<String> roleIds, List<String> urls) {
        return this.baseMapper.getAuthByRoleIdsAndUrls(roleIds,urls);
    }

    @Override
    public List<BaseAuth> getAuthByRoleIdsAndIdParent(Set<String> roleIds, String idParentauth) {
        return this.baseMapper.getAuthByRoleIdsAndIdParent(roleIds,idParentauth);
    }
    @Override
    public List<String> getAppByRoleIds(Set<String> roleIds){
        return this.baseMapper.getAppByRoleIds(roleIds);
    }
}
