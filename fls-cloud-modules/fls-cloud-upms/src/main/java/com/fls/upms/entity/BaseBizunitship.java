package com.fls.upms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 经营主体对照表
 * </p>
 *
 * <AUTHOR>
 * @date 2022/05/18
 */
@Data
@NoArgsConstructor
@TableName("t_base_bizunitship")
public class BaseBizunitship implements Serializable {


    /**
     * 主键
     */
    @TableId(value = "id_bizunitship", type = IdType.INPUT)
    private String idBizunitship;

    /**
     * 组织id
     */
    private String idOrg;

    /**
     * 部门id
     */
    private String idDepartment;

    /**
     * 经营主体id
     */
    private String idBizunit;

    /**
     * 启用状态：1=未启用，2=已启用，3=已停用，默认1，参见basestatus
     */
    private String status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 失效时间
     */
    private Date disableTime;

    /**
     * 时间戳
     */
    private Date ts;

    /**
     * 是否删除：0=否，1=是，默认0，参见yesorno
     */
    private String deleteFlag;


}
