package com.fls.upms.dubbo;

import cn.dev33.satoken.SaManager;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fls.common.core.constant.CommonConstants;
import com.fls.common.core.enums.UserStatus;
import com.fls.common.core.exception.user.UserException;
import com.fls.common.core.utils.ip.AddressUtils;
import com.fls.common.core.web.entity.HttpRequestData;
import com.fls.common.dubbo.utils.RpcContextUtil;
import com.fls.upms.api.RemoteUserService;
import com.fls.upms.api.model.LoginUser;
import com.fls.upms.api.model.XcxLoginUser;
import com.fls.upms.entity.BaseAuth;
import com.fls.upms.entity.BaseBizunitship;
import com.fls.upms.entity.BasePerson;
import com.fls.upms.entity.BaseRole;
import com.fls.upms.entity.BaseUser;
import com.fls.upms.entity.BaseUserRole;
import com.fls.upms.entity.vo.JobAndPostRecordVo;
import com.fls.upms.service.IBaseBizunitshipService;
import com.fls.upms.service.IBasePersonService;
import com.fls.upms.service.IBaseRoleService;
import com.fls.upms.service.IBaseRoleauthService;
import com.fls.upms.service.IBaseUserService;
import com.fls.upms.service.IBaseUserroleService;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

/**
 * 操作日志记录
 *
 * <AUTHOR>
 * @date 2022/05/20
 */
@RequiredArgsConstructor
@Service
@DubboService
public class RemoteUserServiceImpl implements RemoteUserService {

    private final IBaseUserService baseUserService;

    private final IBasePersonService basePersonService;

    private final IBaseBizunitshipService baseBizunitshipService;

    private final IBaseUserroleService baseUserroleService;

    private final IBaseRoleauthService baseRoleauthService;

    private final IBaseRoleService baseRoleService;


    @Override
    public LoginUser getUserInfo(String username)  {
        LambdaQueryWrapper<BaseUser> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(BaseUser::getCode,username);
        lambdaQueryWrapper.eq(BaseUser::getDeleteFlag, CommonConstants.DELETE_FLAG_NOT_DELETED);
        List<BaseUser> userList = baseUserService.list(lambdaQueryWrapper);
        BaseUser baseUser=null;
        if(ObjectUtil.isNotEmpty(userList)){
            baseUser=userList.get(0);
        }
        if (ObjectUtil.isNull(baseUser)) {
            throw new UserException("账号或密码不匹配，请重新输入！", "");
        }
        if (CommonConstants.DELETE_FLAG_IS_DELETED.equals(baseUser.getDeleteFlag())) {
            throw new UserException("账号或密码不匹配，请重新输入！", "");
        }
        if (UserStatus.DISABLED.getCode().equals(baseUser.getStatus())) {
            throw new UserException("对不起，您的账号：{0} 已禁用，请联系管理员", username);
        }
        // 此处可根据登录用户的数据不同 自行创建 loginUser
        return buildLoginUser(baseUser);
    }

    @Override
    public LoginUser getUserInfoByPhoneNumber(String phoneNumber)  {
        //根据电话号码查找BasePersonal
        BasePerson basePerson=basePersonService.getUserInfoByPhoneNumber(phoneNumber);
        //BaseUser和BasePersonal关联字段位为身份ID
        BaseUser baseUser=null;
        if (!ObjectUtils.isEmpty(basePerson)) {
            baseUser = baseUserService.getUserInfoByIdPerson(basePerson.getIdPerson());
        }
        return buildLoginUser(baseUser);
    }

    @Override
    public XcxLoginUser getUserInfoByOpenid(String openid)  {

        return null;
    }

    @Override
    public Set<String> getAuthByUserAndUrl(String userCode, String url) {
        Set<String> urlAuths = new HashSet<>();
        String idUser = baseUserService.getUserInfoByUserCode(userCode).getIdUser();
        List<BaseRole> roles = baseRoleService.getLoginRoles(idUser);
        if (ObjectUtil.isNotEmpty(roles)) {
            Set<String> roleIdSet = roles.stream().map(BaseRole::getIdRole).collect(Collectors.toSet());
            if (ObjectUtil.isNotEmpty(roleIdSet)) {
                List<BaseAuth> auths = baseRoleauthService.getAuthByRoleIdsAndUrls(roleIdSet, Collections.singletonList(url));
                List<BaseAuth> authsLst=new ArrayList<>();
                for (BaseAuth auth : auths) {
                    authsLst.add(auth);
                    getAuth(roleIdSet,authsLst,auth.getIdAuth());
                }
                for (BaseAuth auth : authsLst) {
                    String code = auth.getCode();
                    urlAuths.add(code);
                }
            }
        }
        return urlAuths;
    }
    public void getAuth(Set<String> roleIdSet, List<BaseAuth> auths, String idParentauth){
        LambdaQueryWrapper<BaseAuth> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BaseAuth::getIdParentauth,idParentauth)
            .eq(BaseAuth::getDeleteFlag, CommonConstants.DELETE_FLAG_NOT_DELETED);
        List<BaseAuth> list = baseRoleauthService.getAuthByRoleIdsAndIdParent(roleIdSet,idParentauth);
        if (ObjectUtil.isNotEmpty(list)){
            auths.addAll(list);
            for (BaseAuth baseAuth : list) {
                getAuth(roleIdSet,auths,baseAuth.getIdAuth());
            }
        }
    }

    /**
     * 构建登录用户
     */
    private LoginUser buildLoginUser(BaseUser user) {
        BasePerson person = basePersonService.getById(user.getIdIdentity());
        if(ObjectUtil.isEmpty(person)){
            throw new UserException("用户信息不存在！");
        }
        LoginUser loginUser = new LoginUser();
        loginUser.setIdUser(user.getIdUser());
        loginUser.setIdIdentity(user.getIdIdentity());
        loginUser.setUsername(user.getCode());
        loginUser.setName(user.getName());
        loginUser.setNickName(user.getName());
        loginUser.setPassword(user.getPassword());
        loginUser.setSalt(user.getSalt());
        loginUser.setPhone(person.getMobile());
        loginUser.setPkUser(user.getPkUser());
        //封装登录信息
        HttpRequestData data = RpcContextUtil.getRpcHttpRequestData();
        if (ObjectUtil.isNotNull(data)) {
            String ip = data.getIpAddr();
            loginUser.setIpAddr(ip);
            loginUser.setLoginLocation(AddressUtils.getRealAddressByIP(ip));
            loginUser.setBrowser(data.getBrowser());
            loginUser.setOs(data.getOs());
            loginUser.setLoginTime(new Date());
            loginUser.setExpireTime(DateUtil.offsetSecond(new Date(), (int) SaManager.getConfig().getTimeout()));
        }
        //封装权限信息
         LambdaQueryWrapper<BaseUserRole> userroleLambdaQueryWrapper =new LambdaQueryWrapper<>();
        userroleLambdaQueryWrapper.eq(BaseUserRole::getIdUser,loginUser.getIdUser());
        userroleLambdaQueryWrapper.eq(BaseUserRole::getStatus,CommonConstants.COMMON_STATUS_NORMAL);
        userroleLambdaQueryWrapper.eq(BaseUserRole::getDeleteFlag, CommonConstants.DELETE_FLAG_NOT_DELETED);
        List<BaseUserRole> userroleList = baseUserroleService.list(userroleLambdaQueryWrapper);
        if(ObjectUtil.isNotEmpty(userroleList)){
            loginUser.setRoleIds(userroleList.stream().map(BaseUserRole::getIdRole).collect(Collectors.toSet()));
        }

        loginUser.setIdDepartment(person.getIdDepartment());
        loginUser.setIdOrg(person.getIdOrg());
         //查询人员所属的经营主体
        LambdaQueryWrapper<BaseBizunitship> qw = new LambdaQueryWrapper<>();
        qw.eq(BaseBizunitship::getIdOrg, loginUser.getIdOrg());
        Map<String, List<BaseBizunitship>> baseBizunitshipMap = baseBizunitshipService.list(qw).stream().collect(Collectors.groupingBy(BaseBizunitship::getIdDepartment));
        String idUnit = null;
        if (ObjectUtil.isNotEmpty(baseBizunitshipMap.get(loginUser.getIdDepartment()))) {
            idUnit = baseBizunitshipMap.get(loginUser.getIdDepartment()).get(0).getIdBizunit();
        } else {
            idUnit = baseBizunitshipMap.get("~").get(0).getIdBizunit();
        }
        loginUser.setIdUnit(idUnit);
        //员工岗位和职务
        List<JobAndPostRecordVo> jobAndPostList = basePersonService.getPersonJobAndPostList(loginUser.getIdIdentity());
        if(ObjectUtil.isNotEmpty(jobAndPostList)){
            Set<String> jobSet = new HashSet<>();
            Set<String> postSet = new HashSet<>();
            jobAndPostList.forEach(item->{
                jobSet.add(item.getIdJob());
                postSet.add(item.getIdPost());
            });
            loginUser.setJobIdSet(jobSet);
            loginUser.setPostIdSet(postSet);
        }

        return loginUser;
    }

    @Override
    public String getUserNameByUserId(String userId) {
        BaseUser baseUser = baseUserService.getById(userId);
        if (ObjectUtil.isEmpty(baseUser)) {
            throw new UserException("用户不存在");
        }
        return baseUser.getName();
    }
}
