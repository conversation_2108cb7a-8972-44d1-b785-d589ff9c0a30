package com.fls.upms.mapper;

import com.fls.upms.entity.BaseAuth;
import com.fls.upms.entity.BaseRoleauth;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.github.yulichang.base.MPJBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 角色功能权限表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @date 2022/05/18
 */
public interface BaseRoleauthMapper extends MPJBaseMapper<BaseRoleauth> {
    /**
     * 根据角色和应用名获取菜单权限
     * @param roleIds 角色ID集合
     * @param apps 应用集合
     * @return List<BaseAuth>
     */
    List<BaseAuth> getMenuByRoleIdsAndApps(@Param("roleIds")Set<String> roleIds, @Param("apps")List<String> apps, @Param("href")String href);

    /**
     * 根据角色和应用名获取功能权限
     * @param roleIds
     * @param apps
     * @return
     */
    List<BaseAuth> getAuthByRoleIdsAndApps(@Param("roleIds")Set<String> roleIds, @Param("apps")List<String> apps,@Param("menuType")String menuType);

    List<BaseAuth> getAuthByRoleIdsAndUrls(@Param("roleIds")Set<String> roleIds, @Param("urls")List<String> urls);

    List<BaseAuth> getAuthByRoleIdsAndIdParent(@Param("roleIds")Set<String> roleIds,@Param("idParentauth") String idParentauth);

    /**
     * 根据角色查询应用
     * @param roleIds
     * @return
     */
    List<String> getAppByRoleIds(@Param("roleIds")Set<String> roleIds);
}
