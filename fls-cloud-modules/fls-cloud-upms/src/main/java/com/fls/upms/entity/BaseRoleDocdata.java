package com.fls.upms.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 资源档案数据权限表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-17
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_base_role_docdata")
public class BaseRoleDocdata implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private String idRoleDocdata;

    /**
     * 资源主键，基础档案
     */
    private String idResource;

    /**
     * 基础档案数据表id
     */
    private String idResdoc;

    /**
     * 角色id
     */
    private String idRole;

    /**
     * 启用状态：1=未启用，2=已启用，3=已停用，默认1，参见basestatus
     */
    private String status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 作废时间
     */
    private Date invalidTime;

    /**
     * 作废人
     */
    private String invalider;

    /**
     * 时间戳
     */
    private Date ts;

    /**
     * 是否删除：0=否，1=是，默认0，参见yesorno
     */
    private String deleteFlag;
}
