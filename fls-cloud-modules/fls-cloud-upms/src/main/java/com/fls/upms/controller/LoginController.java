package com.fls.upms.controller;

import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.StrUtil;
import com.fls.common.core.domain.ResponseData;
import com.fls.upms.api.model.LoginUser;
import com.fls.upms.param.LoginAuthParam;
import com.fls.upms.param.LoginParam;
import com.fls.upms.service.IBaseUserService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 用户信息控制器
 *
 * <AUTHOR>
 * @date 2022/09/06
 **/
@RestController
@RequiredArgsConstructor
@RequestMapping("system")
public class LoginController {

    @Resource
    private IBaseUserService userService;

    @PostMapping("/login")
    public ResponseData<LoginUser> Login(@Validated @RequestBody LoginParam params) {
        LoginUser user = userService.login(params);
        return ResponseData.ok(user);
    }

    @PostMapping("/logout")
    public ResponseData<Void> LoginOut(@RequestBody Dict dict) {
        String token = dict.getStr("token");
        if (StrUtil.isEmpty(token)) {
            return ResponseData.fail("token不能为空");
        }
        userService.logout(token);
        return ResponseData.ok();
    }

    /**
     * 新增设置登录缓存逻辑，将原有的能力层生成Token的逻辑进行拆分（避免跨服务多会话管理带来的问题），由业务系统将会话token传递至服务层进行缓存处理
     *
     * @param param token缓存参数
     * @return void
     */
    @PostMapping("/setLoginAuth")
    public ResponseData<Void> setLoginAuth(@Validated @RequestBody LoginAuthParam param) {
        userService.setLoginAuth(param);
        return ResponseData.ok();
    }

    /**
     * 通过token判断用户是否登录
     *
     * @param param token缓存参数
     * @return void
     */
    @PostMapping("/check-login")
    public ResponseData<Void> checkLogin(@Validated @RequestBody Dict param) {
        String token = param.getStr("token");
        if (StrUtil.isEmpty(token)) {
            return ResponseData.fail("token不能为空");
        }
        return userService.checkLogin(token) ? ResponseData.ok() : ResponseData.fail("用户未登录");
    }
}
