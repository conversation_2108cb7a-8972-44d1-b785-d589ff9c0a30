package com.fls.workorder.enums;

import cn.hutool.core.util.StrUtil;
import lombok.Getter;

/**
 * 分配类型
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Getter
public enum AssigneeTypeEnum {
    /**
     * 系统默认
     */
    DEFAULT("0", "系统默认"),
    /**
     * 指派
     */
    ASSIGN("1", "指派"),
    /**
     * 认领
     */
    CLAIM("2", "认领"),
    /**
     * 协办
     */
    COOPERATE("3", "协办");

    private final String type;
    private final String name;

    // 构造方法
    AssigneeTypeEnum(String type, String name) {
        // 成员变量
        this.type = type;
        this.name = name;
    }

    public int getTypeInt() {
        return Integer.parseInt(this.getType());
    }

    public static String matchType(int type) {
        for (AssigneeTypeEnum assigneeTypeEnum : AssigneeTypeEnum.values()) {
            if (assigneeTypeEnum.getTypeInt() == type) {
                return assigneeTypeEnum.getType();
            }
        }
        return DEFAULT.getType();
    }

    public static String getName(String type) {
        for (AssigneeTypeEnum assigneeTypeEnum : AssigneeTypeEnum.values()) {
            if (assigneeTypeEnum.getType().equals(type)) {
                return assigneeTypeEnum.getName();
            }
        }
        return StrUtil.EMPTY;
    }
}
