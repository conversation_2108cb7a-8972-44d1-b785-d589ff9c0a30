package com.fls.todo.api.model;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * TodoTaskQuery
 *
 * <AUTHOR>
 * @since 2025-01-17
 */
@Data
public class TodoTaskQuery implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 单据资源id，单据资源id，精确匹配
     */
    private String idResource;
    /**
     * 待办人id
     */
    @NotBlank(message = "待办人id不能为空")
    private String idUser;
    /**
     * 来源系统标识，来源系统标识，精确匹配
     */
    private String sourceProjectName;
    /**
     * 待办任务状态，待办任务状态，0：待审批，1：待办理，2：进行中，3：已办理，4：已关闭
     */
    private String status;

    /**
     * 页码，校验规则：页码最小不能小于1
     */
    @Min(value = 1, message = "页码最小不能小于1")
    private Integer pageNo = 1;
    /**
     * 页记录大小，校验规则：非必填，默认20，取值范围1-5000
     */
    @Min(value = 1, message = "页记录大小最小不能小于1")
    @Max(value = 5000, message = "页记录大小最大不能超过5000")
    private Integer pageSize = 20;

    /**
     * 记录名称或者任务编号
     */
    private String keyword;

    /**
     * 记录编号
     */
    private String recordCode;

    /**
     * 经营主体id
     */
    private String idBizunit;

    private String beginDate;

    private String endDate;
}
