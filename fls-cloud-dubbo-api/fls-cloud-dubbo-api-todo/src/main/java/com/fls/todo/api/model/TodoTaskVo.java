package com.fls.todo.api.model;

import lombok.Data;

import java.io.Serializable;

/**
 * TodoTaskVo
 *
 * <AUTHOR>
 * @since 2025-01-17
 */
@Data
public class TodoTaskVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 小程序资源访问参数
     */
    private String appletVisitParam;
    /**
     * 小程序资源访问菜单路径
     */
    private String appletVisitPath;
    /**
     * 任务办理人
     */
    private String idAssignee;

    /**
     * 任务类型
     */
    private String taskType;

    /**
     * 任务类型描述
     */
    private String taskTypeDesc;

    /**
     * 分配类型
     */
    private String assigneeType;

    /**
     * 分配类型名称
     */
    private String assigneeTypeDesc;

    /**
     * 来源系统资源id
     */
    private String idResource;
    /**
     * 来源系统任务id
     */
    private String idSourceRecord;
    /**
     * 来源单据编码
     */
    private String sourceRecordCode;

    /**
     * 任务编号
     */
    private String taskCode;
    /**
     * 资源名称
     */
    private String resourceName;
    /**
     * 待办任务主键id
     */
    private String idTodoTask;
    /**
     * 来源任务名称
     */
    private String recordName;
    /**
     * 来源系统标识
     */
    private String sourceProjectCode;
    /**
     * 来源系统地址
     */
    private String sourceProjectUrl;
    /**
     * 待办任务状态
     */
    private String taskStatus;

    /**
     * 任务状态描述
     */
    private String taskStatusDesc;

    /**
     * 待办任务处理状态
     */
    private String handleStatus;

    /**
     * 待办任务处理状态描述
     */
    private String handleStatusDesc;

    /**
     * 资源访问参数
     */
    private String visitParam;
    /**
     * 资源访问菜单路径
     */
    private String visitPath;

    /**
     * 经营主体id
     */
    private String idBizunit;

    /**
     * 经营主体名称
     */
    private String bizunitName;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 发起人
     */
    private String initiator;

    /**
     * 发起人名称
     */
    private String initiatorName;

    /**
     * 创建时间
     */
    private String createTime;
}
