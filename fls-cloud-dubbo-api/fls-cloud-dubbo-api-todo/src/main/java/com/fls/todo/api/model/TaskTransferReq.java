package com.fls.todo.api.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 任务完成Req
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TaskTransferReq implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 来源单据id
     */
    private String idSourceBill;

    /**
     * 来源系统地址
     */
    @NotBlank(message = "操作人id不能为空")
    private String operator;

    /**
     * 转办人id
     */
    @NotBlank(message = "转办人用户id不能为空")
    private String idTransfer;
}
