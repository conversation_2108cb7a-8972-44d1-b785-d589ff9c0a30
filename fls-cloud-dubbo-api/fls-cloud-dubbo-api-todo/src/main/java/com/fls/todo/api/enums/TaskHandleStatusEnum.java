package com.fls.todo.api.enums;

import cn.hutool.core.util.StrUtil;
import lombok.Getter;

/**
 * 待办处理状态枚举
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@Getter
public enum TaskHandleStatusEnum {
    /**
     * 待审批
     */
    UN_READ("0", "未阅"),
    /**
     * 待办理
     */
    READ_ALREADY("1", "已阅"),
    /**
     * 进行中
     */
    WAIT_HANDLE("2", "待处理"),
    /**
     * 已完成
     */
    COMPLETED("3", "已处理");

    private final String code;
    private final String status;

    TaskHandleStatusEnum(String code, String status) {
        this.code = code;
        this.status = status;
    }

    public static String getName(String status) {
        for (TaskHandleStatusEnum statusEnum : values()) {
            if (statusEnum.getCode().equals(status)) {
                return statusEnum.getStatus();
            }
        }
        return StrUtil.EMPTY;
    }
}
