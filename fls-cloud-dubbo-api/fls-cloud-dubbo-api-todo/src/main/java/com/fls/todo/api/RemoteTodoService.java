package com.fls.todo.api;

import com.fls.todo.api.model.TaskCancelReq;
import com.fls.todo.api.model.TaskCompleteReq;
import com.fls.todo.api.model.TaskResumeReq;
import com.fls.todo.api.model.TaskTransferReq;

/**
 * RemoteTodoService
 *
 * <AUTHOR>
 * @since 2025/8/9
 */
public interface RemoteTodoService {

    /**
     * 完成任务请求
     *
     * @param completeReq 完成任务请求
     */
    void completeTask(TaskCompleteReq completeReq);

    /**
     * 取消任务请求
     *
     * @param cancelReq 取消任务请求
     */
    void cancelTask(TaskCancelReq cancelReq);

    /**
     * 转办任务请求
     *
     * @param transferReq 转办任务请求
     */
    void transfer(TaskTransferReq transferReq);

    /**
     * 恢复任务
     *
     * @param resumeReq 恢复任务请求
     */
    void resume(TaskResumeReq resumeReq);
}
