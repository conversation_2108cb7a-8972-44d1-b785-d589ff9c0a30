package com.fls.todo.api.enums;

import cn.hutool.core.util.StrUtil;
import lombok.Getter;

/**
 * 待办任务状态枚举
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@Getter
public enum TodoTaskStatusEnum {
    /**
     * 待审批
     */
    PENDING_APPROVAL("0", "待审批"),
    /**
     * 待办理
     */
    PENDING_HANDLE("1", "待办理"),
    /**
     * 进行中
     */
    IN_PROGRESS("2", "进行中"),
    /**
     * 已完成
     */
    COMPLETED("3", "已完成"),
    /**
     * 已终止
     */
    TERMINATED("4", "已终止"),
    /**
     * 待认领
     */
    UNCLAIM("5", "待认领"),
    /**
     * 已挂起
     */
    SUSPEND("6", "已挂起");

    private final String code;
    private final String status;

    TodoTaskStatusEnum(String code, String status) {
        this.code = code;
        this.status = status;
    }

    public static boolean isComplete(String status) {
        return StrUtil.equals(status, COMPLETED.getCode()) || StrUtil.equals(status, TERMINATED.getCode());
    }

    public static String getName(String status) {
        for (TodoTaskStatusEnum statusEnum : values()) {
            if (statusEnum.getCode().equals(status)) {
                return statusEnum.getStatus();
            }
        }
        return StrUtil.EMPTY;
    }
}
