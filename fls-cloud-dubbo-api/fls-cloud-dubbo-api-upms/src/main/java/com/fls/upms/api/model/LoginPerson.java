package com.fls.upms.api.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 员工表
 * </p>
 *
 * <AUTHOR>
 * @date 2022/05/18
 */
@Data
public class LoginPerson implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private String idPerson;

    /**
     * 编码
     */
    private String code;

    /**
     * 名称
     */
    private String name;

    /**
     * 曾用名
     */
    private String usedname;

    /**
     * 性别 1=男，2=女，3=不详 参见sex
     */
    private String sex;

    /**
     * 民族
     */
    private String nation;

    /**
     * 办公电话
     */
    private String officephone;

    /**
     * 手机
     */
    private String mobile;

    /**
     * 所属组织
     */
    private String idOrg;

    /**
     * 所属部门
     */
    private String idDepartment;

    /**
     * 所属岗位
     */
    private String idPost;

    /**
     * NC人员基本信息pk值
     */
    private String pkPsndoc;

    /**
     * 启用状态：1=未启用，2=已启用，3=已停用，默认1，参见basestatus
     */
    private String status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 失效时间
     */
    private Date disableTime;

    /**
     * 时间戳
     */
    private Date ts;

    /**
     * 是否删除：0=否，1=是，默认0，参见yesorno
     */
    private String deleteFlag;

    /**
     * 是否在岗，参见yesorno
     */
    private String postFlag;

    /**
     * 所属职务
     */
    private String idJob;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 钉钉识别码id
     */
    private String dingdid;


}
