package com.fls.upms.api.model;

import lombok.Data;

/**
 * 登录菜单
 *
 * <AUTHOR>
 * @date 2022/03/20
 */
@Data
public class LoginMenuNode {

    /**
     * id
     */

    private String id;

    /**
     * 父id
     */
    private String parent;

    /**
     * 路由名称, 必须设置,且不能重名
     */
    private String name;

    /**
     * 路由
     */
    private String path;

    /**
     * 组件
     */
    private String component;

    /**
     * 路由标题, 用于显示面包屑, 页面标题 *推荐设置
     */
    public String title;

    /**
     * 图标
     */
    public String icon;

    /**
     * 是否可见
     */
    public boolean hidden;


    /**
     * 内链打开http链接
     */
    public String link;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 菜单类型
     */
    private String menuType;

    /**
     * 打开方式： 0无 1组件 2内链 3外链
     */
    private String openType;



}
