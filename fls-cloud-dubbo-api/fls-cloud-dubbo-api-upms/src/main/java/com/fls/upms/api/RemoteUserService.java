package com.fls.upms.api;

import com.fls.upms.api.model.LoginUser;
import com.fls.upms.api.model.XcxLoginUser;
import java.util.Set;

/**
 * 用户服务
 *
 * <AUTHOR>
 * @date 2022/06/04
 */
public interface RemoteUserService {

    /**
     * 通过用户名查询用户信息
     *
     * @param username 用户名
     * @return 结果
     */
    LoginUser getUserInfo(String username) ;

    /**
     * 通过手机号查询用户信息
     *
     * @param phonenumber 手机号
     * @return 结果
     */
    LoginUser getUserInfoByPhoneNumber(String phonenumber) ;

    /**
     * 通过openid查询用户信息
     *
     * @param openid openid
     * @return 结果
     */
    XcxLoginUser getUserInfoByOpenid(String openid) ;

    /**
     * 根据用户编码和URL查询权限
     * @param userCode
     * @param url
     * @return
     */
    Set<String> getAuthByUserAndUrl(String userCode, String url);

    /**
     * 根据用户ID查询用户名
     * @param userId 用户ID
     * @return 用户名
     */
    String getUserNameByUserId(String userId);
}
