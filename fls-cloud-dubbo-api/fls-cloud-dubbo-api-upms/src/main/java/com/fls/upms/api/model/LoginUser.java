package com.fls.upms.api.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fls.common.core.constant.CacheConstants;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 用户信息
 *
 * <AUTHOR>
 * @date 2022/06/05
 */
@Data
@NoArgsConstructor
public class LoginUser implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    private String idUser;

    /**
     * 身份id
     */
    private String idIdentity;

    /**
     * 用户名
     */
    private String username;

    /**
     * 昵称
     */
    private String nickName;

    /**
     * 姓名
     */
    private String name;

    /**
     * 密码
     */
    @JsonIgnore
    private String password;

    /**
     * 盐
     */
    @JsonIgnore
    private String salt;


    /**
     * 手机
     */
    private String phone;



    /**
     * 用户唯一标识
     */
    @JsonIgnore
    private String token;

    /**
     * 用户类型
     */
    private String userType;

    /**
     * 登录时间
     */
    private Date loginTime;

    /**
     * 过期时间
     */
    private Date expireTime;

    /**
     * 登录IP地址
     */
    private String ipAddr;

    /**
     * 登录地点
     */
    private String loginLocation;

    /**
     * 浏览器类型
     */
    private String browser;

    /**
     * 操作系统
     */
    private String os;


    /**
     * 数据范围信息(经营主体),一个url对应的经营主体
     */
    private Map<String, Set<String>> unitDataScopes;

    /**
     * 数据范围信息(组织),一个url对应的组织
     */
    private Map<String, Set<String>> orgDataScopes;

    /**
     * 登录菜单信息，AntDesign版本菜单,动态菜单使用
     */
    private List<LoginMenuNode> menus;

    /**
     * 菜单code
     */
    private Set<String> perms;

    /**
     * 角色ids
     */
    private Set<String> roleIds;

    /**
     * 岗位id集合
     */
    private Set<String> postIdSet;

    /**
     * 职务id集合
     */
    private Set<String> jobIdSet;

    /**
     * 部门id
     */
    private String idDepartment;

    /**
     * 所属组织id
     */
    private String idOrg;

    /**
     * 所属经营主体id
     */
    private String idUnit;

    /**
     * NC用户id
     */
    private String pkUser;

    /**
     * 员工信息
     */
    private LoginPerson loginPerson;

    private String user;

    /**
     * 获取登录id
     */
    public String getLoginId() {
        return userType + CacheConstants.LOGINID_JOIN_CODE + idUser;
    }

}
