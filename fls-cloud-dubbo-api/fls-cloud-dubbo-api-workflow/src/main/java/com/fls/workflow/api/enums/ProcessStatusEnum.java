package com.fls.workflow.api.enums;

import lombok.Getter;

/**
 * 流程状态枚举
 *
 * <AUTHOR>
 * @since 2024-12-27
 */
@Getter
public enum ProcessStatusEnum {
    RUNNING("进行中", 1),
    END("已完成", 2);
    // 成员变量
    private final String status;
    private final int code;

    // 构造方法
    ProcessStatusEnum(String status, int code) {
        this.code = code;
        this.status = status;
    }

    // 普通方法
    public static String getStatus(int code) {
        for (ProcessStatusEnum c : ProcessStatusEnum.values()) {
            if (c.code == code) {
                return c.status;
            }
        }
        return null;
    }
}
