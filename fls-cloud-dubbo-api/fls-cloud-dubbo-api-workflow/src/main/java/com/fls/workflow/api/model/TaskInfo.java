package com.fls.workflow.api.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 任务信息
 *
 * <AUTHOR>
 * @since 2025-02-06
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TaskInfo implements Serializable {

    private static final long serialVersionUID = 4181723281551561657L;

    /**
     * 任务ID
     */
    private String id;

    /**
     * 任务名称
     */
    private String name;

    /**
     * 任务执行人（负责人）
     */
    private String assignee;

    /**
     * 任务候选人列表，通常为逗号分隔的用户ID字符串
     */
    private String candidates;

    /**
     * 执行实例ID，表示当前任务所属的执行实例
     */
    private String executionId;

    /**
     * 任务定义Key，用于标识流程定义中的任务节点
     */
    private String taskDefinitionKey;

    /**
     * 任务创建时间
     */
    private Date createTime;

    /**
     * 任务所属的流程定义ID
     */
    private String processDefinitionId;

    /**
     * 任务所属的流程实例ID
     */
    private String processInstanceId;

    /**
     * 流程定义的Key，通常对应流程的唯一标识
     */
    private String processDefKey;

    /**
     * 任务
     */
    private String taskDefId;
}
