package com.fls.workflow.api.model;

import java.io.Serializable;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * 发起流程请求参数
 *
 * <AUTHOR>
 * @since 2024-12-27
 */
@Data
public class InitiateProcessReq implements Serializable {

    private static final long serialVersionUID = 5257548402277323426L;

    @NotBlank(message = "发起人不能为空")
    private String initiator;

    @NotBlank(message = "资源编码不能为空")
    private String resourceCode;

    /**
     * 交易类型编码
     */
    private String transCode;

    private String title;

    @NotNull(message = "单据数据不能为空")
    private Object data;
}