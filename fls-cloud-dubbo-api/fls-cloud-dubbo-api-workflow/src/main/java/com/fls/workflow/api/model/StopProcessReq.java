package com.fls.workflow.api.model;

import java.io.Serializable;
import javax.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 发起流程请求参数
 *
 * <AUTHOR>
 * @since 2024-12-27
 */
@Data
public class StopProcessReq implements Serializable {

    private static final long serialVersionUID = 4451124476762961435L;

    @NotBlank(message = "操作人不能为空")
    private String operator;

    @NotBlank(message = "流程实例id不能为空")
    private String procInsId;
}