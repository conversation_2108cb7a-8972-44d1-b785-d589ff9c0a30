package com.fls.workflow.api;

import com.fls.workflow.api.model.ApproveStaticInfo;
import com.fls.workflow.api.model.ApproveStaticQuery;
import com.fls.workflow.api.model.BackNodeInfo;
import com.fls.workflow.api.model.BackNodeReq;
import com.fls.workflow.api.model.InitiateProcessReq;
import com.fls.workflow.api.model.InitiateProcessRsp;
import com.fls.workflow.api.model.StopProcessReq;
import com.fls.workflow.api.model.TaskInfo;

import java.util.List;
import java.util.Map;

/**
 * 工作班组服务
 *
 * <AUTHOR>
 * @since 2024-12-27
 */
public interface RemoteWorkflowService {
    /**
     * 发起流程
     *
     * @param req 发起流程请求
     * @return 发起流程响应
     */
    InitiateProcessRsp startProcess(InitiateProcessReq req);

    /**
     * 终止流程
     *
     * @param stopProcessReq 终止流程请求
     */
    void stopProcess(StopProcessReq stopProcessReq);

    /**
     * 任务认领
     *
     * @param taskId
     * @param userId
     */
    void claimTask(String taskId, String userId);

    /**
     * 任务跟进
     *
     * @param taskId 任务ID
     * @param data   跟进数据
     */
    void followUpTask(String taskId, Object data);

    /**
     * 任务转交
     *
     * @param taskId 任务ID
     * @param userId 用户ID
     */
    void transferTask(String taskId, String userId);

    /**
     * 挂起或激活流程
     *
     * @param state  active:挂起 suspend:激活
     * @param taskId 任务ID
     */
    void updateState(String state, String taskId);

    /**
     * 完成任务
     *
     * @param taskId 任务ID
     * @param userId 用户ID
     * @param data   完成数据
     */
    TaskInfo completeTask(String taskId, String userId, Object data);

    /**
     * 终止流程
     *
     * @param procInstanceId 流程实例ID
     * @param userId         用户ID
     */
    void stopProcess(String procInstanceId, String userId);

    /**
     * 更新任务执行人
     *
     * @param idTask        任务ID
     * @param idsAssignee   执行人ID
     * @param idsCoOperator 协办人ID
     */
    void updateAssignee(String idTask, String idsAssignee, List<String> idsCoOperator);

    /**
     * 更新协办人
     *
     * @param idTask        任务ID
     * @param idsCoOperator 协办人ID
     */
    void updateCoOperator(String idTask, String idsCoOperator);

    /**
     * 更新候选人
     *
     * @param idTask       任务ID
     * @param idsCandidate 候选人ID
     */
    void updateCandidates(String idTask, String idsCandidate);

    /**
     * 添加任务
     *
     * @param procInstanceId    流程实例ID
     * @param taskDefinitionKey 任务定义键
     * @return 任务信息
     */
    TaskInfo addTask(String procInstanceId, String taskDefinitionKey);

    /**
     * 获取可驳回节点列表
     *
     * @param taskId 流程任务id
     * @return 节点列表
     */
    List<BackNodeInfo> getBackNodes(String taskId);

    /**
     * 驳回至指定节点
     *
     * @param backNodeReq 驳回请求
     */
    String backNode(BackNodeReq backNodeReq);

    /**
     * 通过流程实例和任务定义，查询已完成任务id，因为在驳回的时候，BPMN引擎创建了新的流程任务
     *
     * @param idProcInst 流程实例id
     * @param taskDefKey 流程任务定义key
     * @return 已完成任务id
     */
    String getIdFinishedTaskByTaskDefKey(String idProcInst, String taskDefKey);

    /**
     * 统计待审任务
     *
     * @param query 统计查询条件
     * @return 待审任务统计结果
     */
    ApproveStaticInfo getApproveTaskStatic(ApproveStaticQuery query);

    /**
     * 统计待审任务月度数据
     *
     * @param year 年份
     * @return 月度统计结果 Map<月份, 数量>
     */
    Map<String, Integer> getApproveTaskMonthStatic(Integer year, String userId);
}
