package com.fls.workflow.api.model;

import java.io.Serializable;
import javax.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 驳回流程请求
 *
 * <AUTHOR>
 * @since 2025-06-11
 */
@Data
public class BackNodeReq implements Serializable {

    private static final long serialVersionUID = -193760305778745847L;

    @NotBlank(message = "发起人不能为空")
    private String taskId;

    @NotBlank(message = "驳回任务节点标识")
    private String backTaskDefKey;

    /**
     * 驳回备注
     */
    private String message;
}