package com.fls.workflow.api.model;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class InitiateProcessRsp implements Serializable {

    private static final long serialVersionUID = 1026191948821350658L;

    /**
     * 流程实例ID
     */
    private String processInstanceId;

    /**
     * 流程定义ID
     */
    private String processDefinitionId;

    /**
     * 当前流程状态 1-执行中   2-已完成
     */
    private Integer status;

    /**
     * 任务信息
     */
    private TaskInfo taskInfo;
}