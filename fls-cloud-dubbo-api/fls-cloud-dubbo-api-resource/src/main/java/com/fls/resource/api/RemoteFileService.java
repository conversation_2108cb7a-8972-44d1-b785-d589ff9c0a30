package com.fls.resource.api;

import com.fls.resource.api.domain.SysFile;
import com.fls.common.core.exception.ServiceException;

/**
 * 文件服务
 *
 * <AUTHOR>
 * @date 2022/06/26
 */
public interface RemoteFileService {

    /**
     * 上传文件
     *
     * @param file 文件信息
     * @return 结果
     */
    SysFile upload(String name, String originalFilename, String contentType, byte[] file) throws ServiceException;
}
