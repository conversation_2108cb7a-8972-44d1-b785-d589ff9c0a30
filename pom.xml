<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.fls</groupId>
    <artifactId>fls-cloud</artifactId>
    <version>1.0.0</version>

    <name>fls-cloud</name>
    <url>https://www.fls.com</url>
    <description>fls-cloud</description>

    <properties>
        <fls.cloud.version>1.0.0</fls.cloud.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <spring-boot.version>2.7.2</spring-boot.version>
        <spring-cloud.version>2021.0.3</spring-cloud.version>
        <spring-boot-admin.version>2.7.3</spring-boot-admin.version>
        <spring-boot.mybatis>2.2.2</spring-boot.mybatis>
        <mybatis.version>3.5.10</mybatis.version>
        <mybatis-plus.version>3.5.2</mybatis-plus.version>
        <p6spy.version>3.9.1</p6spy.version>
        <druid.version>1.2.11</druid.version>
        <dynamic-ds.version>3.5.1</dynamic-ds.version>
        <velocity.version>2.3</velocity.version>
        <fastjson.version>1.2.83</fastjson.version>
        <swagger.core.version>2.2.0</swagger.core.version>
        <springdoc.version>1.6.9</springdoc.version>
        <poi.version>5.2.2</poi.version>
        <easyexcel.version>3.1.1</easyexcel.version>
        <hutool.version>5.8.20</hutool.version>
        <redisson.version>3.17.5</redisson.version>
        <lock4j.version>2.2.2</lock4j.version>
        <xxl-job.version>2.3.1</xxl-job.version>
        <satoken.version>1.30.0</satoken.version>
        <lombok.version>1.18.24</lombok.version>
        <logstash.version>7.1.1</logstash.version>
        <easy-es.version>0.9.80</easy-es.version>
        <elasticsearch.version>7.14.0</elasticsearch.version>
        <guava.version>31.1-jre</guava.version>
        <aws-java-sdk-s3.version>1.12.264</aws-java-sdk-s3.version>
        <okhttp.version>4.10.0</okhttp.version>
        <aliyun.sms.version>2.0.16</aliyun.sms.version>
        <tencent.sms.version>3.1.555</tencent.sms.version>
        <hutool.version>5.8.10</hutool.version>

        <!-- docker 配置 -->
        <docker.registry.url>localhost</docker.registry.url>
        <docker.registry.host>http://${docker.registry.url}:2375</docker.registry.host>
        <docker.namespace>fls-coud</docker.namespace>
        <docker.plugin.version>1.2.2</docker.plugin.version>

        <!--Mybatis-plus-join插件-->
        <mybatis.join.version>1.4.5</mybatis.join.version>

        <commons.net.version>3.9.0</commons.net.version>
        <mapstruct.version>1.5.5.Final</mapstruct.version>
        <sqljdbc4.version>4.0</sqljdbc4.version>
        <!--oracle数据库-->
        <oracle.version>********</oracle.version>
    </properties>

    <modules>
        <module>fls-cloud-auth</module>
        <module>fls-cloud-gateway</module>
        <module>fls-cloud-visual</module>
        <module>fls-cloud-modules</module>
        <module>fls-cloud-dubbo-api</module>
        <module>fls-cloud-common</module>
    </modules>
    <packaging>pom</packaging>

    <dependencies>

    </dependencies>


    <profiles>
        <profile>
            <id>dev</id>
            <properties>
                <!-- 环境标识，需要与配置文件的名称相对应 -->
                <profiles.active>dev</profiles.active>
                <nacos.server>127.0.0.1:8848</nacos.server>
                <nacos.discovery.group>DEFAULT_GROUP</nacos.discovery.group>
                <nacos.config.group>DEFAULT_GROUP</nacos.config.group>
                <logstash.address>127.0.0.1:4560</logstash.address>
            </properties>
            <activation>
                <!-- 默认环境 -->
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>test</id>
            <properties>
                <!-- 环境标识，需要与配置文件的名称相对应 -->
                <profiles.active>test</profiles.active>
                <nacos.server>************:8848</nacos.server>
                <nacos.username>nacos</nacos.username>
                <nacos.password>nacos</nacos.password>
                <nacos.discovery.group>DEFAULT_GROUP</nacos.discovery.group>
                <nacos.config.group>DEFAULT_GROUP</nacos.config.group>
                <logstash.address>127.0.0.1:4560</logstash.address>
            </properties>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <profiles.active>prod</profiles.active>
                <nacos.server>127.0.0.1:8848</nacos.server>
                <nacos.discovery.group>DEFAULT_GROUP</nacos.discovery.group>
                <nacos.config.group>DEFAULT_GROUP</nacos.config.group>
                <logstash.address>127.0.0.1:4560</logstash.address>
            </properties>
        </profile>
    </profiles>

    <!-- 依赖声明 -->
    <dependencyManagement>
        <dependencies>

            <!-- SpringCloud 微服务 -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>


            <!-- SpringBoot 依赖配置 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- hutool 的依赖配置-->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-bom</artifactId>
                <version>${hutool.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- common 的依赖配置-->
            <dependency>
                <groupId>com.fls</groupId>
                <artifactId>fls-cloud-common-bom</artifactId>
                <version>${fls.cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- api 的依赖配置-->
            <dependency>
                <groupId>com.fls</groupId>
                <artifactId>fls-cloud-dubbo-api-upms</artifactId>
                <version>${fls.cloud.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fls</groupId>
                <artifactId>fls-cloud-dubbo-api-resource</artifactId>
                <version>${fls.cloud.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fls</groupId>
                <artifactId>fls-cloud-dubbo-api-master-data</artifactId>
                <version>${fls.cloud.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fls</groupId>
                <artifactId>fls-cloud-dubbo-api-workflow</artifactId>
                <version>${fls.cloud.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fls</groupId>
                <artifactId>fls-cloud-dubbo-api-todo</artifactId>
                <version>${fls.cloud.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fls</groupId>
                <artifactId>fls-cloud-dubbo-api-work-order</artifactId>
                <version>${fls.cloud.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-core</artifactId>
                <version>${satoken.version}</version>
            </dependency>

            <!-- Mybatis 依赖配置 -->
            <dependency>
                <groupId>org.mybatis.spring.boot</groupId>
                <artifactId>mybatis-spring-boot-starter</artifactId>
                <version>${spring-boot.mybatis}</version>
            </dependency>

            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis</artifactId>
                <version>${mybatis.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-annotation</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <!-- sql性能分析插件 -->
            <dependency>
                <groupId>p6spy</groupId>
                <artifactId>p6spy</artifactId>
                <version>${p6spy.version}</version>
            </dependency>

            <dependency>
                <groupId>io.swagger.core.v3</groupId>
                <artifactId>swagger-annotations</artifactId>
                <version>${swagger.core.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-webmvc-core</artifactId>
                <version>${springdoc.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-javadoc</artifactId>
                <version>${springdoc.version}</version>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.poi</groupId>
                        <artifactId>poi-ooxml-schemas</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!--redisson-->
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>lock4j-redisson-spring-boot-starter</artifactId>
                <version>${lock4j.version}</version>
            </dependency>

            <!-- xxl-job-core -->
            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>${xxl-job.version}</version>
            </dependency>

            <!-- logstash -->
            <dependency>
                <groupId>net.logstash.logback</groupId>
                <artifactId>logstash-logback-encoder</artifactId>
                <version>${logstash.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.easy-es</groupId>
                <artifactId>easy-es-boot-starter</artifactId>
                <version>${easy-es.version}</version>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch</groupId>
                <artifactId>elasticsearch</artifactId>
                <version>${elasticsearch.version}</version>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch.client</groupId>
                <artifactId>elasticsearch-rest-high-level-client</artifactId>
                <version>${elasticsearch.version}</version>
            </dependency>

            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${okhttp.version}</version>
            </dependency>

            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-java-sdk-s3</artifactId>
                <version>${aws-java-sdk-s3.version}</version>
            </dependency>

            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>dysmsapi20170525</artifactId>
                <version>${aliyun.sms.version}</version>
            </dependency>

            <dependency>
                <groupId>com.tencentcloudapi</groupId>
                <artifactId>tencentcloud-sdk-java</artifactId>
                <version>${tencent.sms.version}</version>
            </dependency>

            <!-- 统一 guava 版本 解决隐式漏洞问题 -->
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>

            <!-- 统一 fastjson 版本 解决alibaba组件序列化漏洞问题 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <!--Mybatis-plus-join插件-->
            <dependency>
                <groupId>com.github.yulichang</groupId>
                <artifactId>mybatis-plus-join-boot-starter</artifactId>
                <version>${mybatis.join.version}</version>
            </dependency>

            <dependency>
                <groupId>commons-net</groupId>
                <artifactId>commons-net</artifactId>
                <version>${commons.net.version}</version>
            </dependency>
            <!--mapstruct-->
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <!--sqlserver数据库-->
            <dependency>
                <groupId>com.microsoft.sqlserver</groupId>
                <artifactId>sqljdbc4</artifactId>
                <version>${sqljdbc4.version}</version>
            </dependency>
            <!--Oracle数据库-->
            <dependency>
                <groupId>com.oracle.database.jdbc</groupId>
                <artifactId>ojdbc6</artifactId>
                <version>${oracle.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>


    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.9.0</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>com.github.therapi</groupId>
                            <artifactId>therapi-runtime-javadoc-scribe</artifactId>
                            <version>0.13.0</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.springframework.boot</groupId>
                            <artifactId>spring-boot-configuration-processor</artifactId>
                            <version>${spring-boot.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
        </plugins>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>com.spotify</groupId>
                    <artifactId>docker-maven-plugin</artifactId>
                    <version>${docker.plugin.version}</version>
                    <configuration>
                        <imageName>${docker.namespace}/${project.artifactId}:${project.version}</imageName>
                        <dockerDirectory>${project.basedir}</dockerDirectory>
                        <dockerHost>${docker.registry.host}</dockerHost>
                        <registryUrl>${docker.registry.url}</registryUrl>
                        <serverId>${docker.registry.url}</serverId>
                        <resources>
                            <resource>
                                <targetPath>/</targetPath>
                                <directory>${project.build.directory}</directory>
                                <include>${project.build.finalName}.jar</include>
                            </resource>
                        </resources>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <!-- 关闭过滤 -->
                <filtering>false</filtering>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <!-- 引入所有 匹配文件进行过滤 -->
                <includes>
                    <include>application*</include>
                    <include>bootstrap*</include>
                    <include>logback*</include>
                </includes>
                <!-- 启用过滤 即该资源中的变量将会被过滤器中的值替换 -->
                <filtering>true</filtering>
            </resource>
        </resources>
    </build>

    <repositories>
        <repository>
            <id>public</id>
            <name>aliyun nexus</name>
            <url>https://maven.aliyun.com/repository/public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>public</id>
            <name>aliyun nexus</name>
            <url>https://maven.aliyun.com/repository/public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>

</project>

