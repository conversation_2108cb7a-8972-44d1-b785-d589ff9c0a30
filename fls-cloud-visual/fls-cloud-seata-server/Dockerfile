FROM anapsix/alpine-java:8_server-jre_unlimited

MAINTAINER zzh

RUN mkdir -p /fls-cloud/seata-server
RUN mkdir -p /fls-cloud/seata-server/logs

WORKDIR /fls-cloud/seata-server

ENV TZ=PRC
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

EXPOSE 7091
EXPOSE 8091

ADD ./target/fls-cloud-seata-server.jar ./app.jar

ENTRYPOINT ["java", "-Djava.security.egd=file:/dev/./urandom", "-jar", "app.jar"]
