FROM anapsix/alpine-java:8_server-jre_unlimited

MAINTAINER zzh

RUN mkdir -p /fls-cloud/xxl-job-admin
RUN mkdir -p /fls-cloud/xxl-job-admin/logs

WORKDIR /fls-cloud/xxl-job-admin

ENV TZ=PRC
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

EXPOSE 9900

ADD ./target/fls-cloud-xxl-job-admin.jar ./app.jar

ENTRYPOINT ["java", "-Djava.security.egd=file:/dev/./urandom", "-jar", "app.jar"]
