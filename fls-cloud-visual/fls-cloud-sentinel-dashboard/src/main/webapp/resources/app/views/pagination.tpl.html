<div class="pagination" ng-if="1 < pages.length || !autoHide">
  <a href="" class="btn btn-default btn-xs" ng-if="boundaryLinks" ng-class="{ disabled : pagination.current == 1 }" ng-click="setCurrent(1)">
    <i class="fa fa-angle-double-left"></i>
  </a>
  <a href="" class="btn btn-default btn-xs" ng-if="directionLinks" ng-class="{ disabled : pagination.current == 1 }" ng-click="setCurrent(pagination.current - 1)">
    <i class="fa fa-angle-left"></i>
  </a>
  <a href="" class="btn btn-default btn-xs" ng-repeat="pageNumber in pages track by tracker(pageNumber, $index)" ng-class="{ active : pagination.current == pageNumber, disabled : pageNumber == '...' }"
    ng-click="setCurrent(pageNumber)">{{ pageNumber }}</a>
  <a href="" class="btn btn-default btn-xs" ng-if="directionLinks" ng-class="{ disabled : pagination.current == pagination.last }"
    ng-click="setCurrent(pagination.current + 1)">
    <i class="fa fa-angle-right"></i>
  </a>
  <a href="" class="btn btn-default btn-xs" ng-if="boundaryLinks" ng-class="{ disabled : pagination.current == pagination.last }"
    ng-click="setCurrent(pagination.last)">
    <i class="fa fa-angle-double-right"></i>
  </a>
</div>
