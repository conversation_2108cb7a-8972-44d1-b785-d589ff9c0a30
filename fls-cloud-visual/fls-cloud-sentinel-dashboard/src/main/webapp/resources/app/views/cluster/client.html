<div class="row clearfix">
    <form role="form" class="form-horizontal">
        <div class="form-group" ng-if="stateVO.currentMode == 0">
            <label class="col-sm-2 control-label">连接状态</label>
            <div class="col-sm-4">
                <p class="form-control-static text-danger" ng-if="stateVO.client.clientConfig.clientState === 0">未连接</p>
                <p class="form-control-static" ng-if="stateVO.client.clientConfig.clientState === 1">连接中</p>
                <p class="form-control-static text-success" ng-if="stateVO.client.clientConfig.clientState === 2">已连接</p>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label">Token Server IP</label>
            <div class="col-sm-4">
                <input type="text" class="form-control highlight-border" ng-model='stateVO.client.clientConfig.serverHost' placeholder='请指定 Token Server IP' />
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label">Token Server 端口</label>
            <div class="col-sm-4">
                <input type="number" min="0" max="65535" required class="form-control highlight-border" ng-model='stateVO.client.clientConfig.serverPort' placeholder='请指定 Token Server 端口' />
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label">请求超时时间（ms）</label>
            <div class="col-sm-4">
                <input type="number" min="0" required class="form-control highlight-border" ng-model='stateVO.client.clientConfig.requestTimeout' placeholder='请指定请求超时时间（ms）' />
            </div>
        </div>
    </form>
</div>