<div class="row" style="margin-left: 1px; margin-top:10px; height: 50px;">
  <div class="col-md-6" style="margin-bottom: 10px;">
    <span style="font-size: 30px;font-weight: bold;">{{app}}</span>
  </div>
</div>

<!--<div>-->
<!--<span>实例总数 {{machines.length}}, 健康 {{healthyCount}}, 失联 {{machines.length-healthyCount}}</span>-->
<!--</div>-->

<div class="separator"></div>
<div class="container-fluid">
  <div class="row" style="margin-top: 20px; margin-bottom: 20px;">
    <div class="col-md-12">
      <div class="card">
        <div class="inputs-header">
          <span class="brand" style="font-size: 13px;">机器列表</span>
          <span>实例总数 {{machines.length}}, 健康 {{healthyCount}}, 失联 {{machines.length - healthyCount}}.</span>
          <input class="form-control width-300" placeholder="关键字" ng-model="searchKey">
        </div>

        <!--.tools-header -->
        <div class="card-body" style="padding: 0px 0px;">
          <table class="table" style="border-left: none; border-right:none;margin-top: 10px;">
            <thead>
              <tr style="background: #F3F5F7;">
                <td>机器名</td>
                <td>IP 地址</td>
                <td>端口号</td>
                <td>Sentinel 客户端版本</td>
                <td>健康状态</td>
                <td>心跳时间</td>
                <td>操作</td>
              </tr>
            </thead>
            <tbody>
              <tr dir-paginate="entry in machines | filter : searchKey | itemsPerPage: machinesPageConfig.pageSize " current-page="machinesPageConfig.currentPageIndex"
                pagination-id="entriesPagination">
                <td style="word-wrap:break-word;word-break:break-all;">{{entry.hostname}}</td>
                <td style="word-wrap:break-word;word-break:break-all;">{{entry.ip}}</td>
                <td> {{entry.port}} </td>
                <td> {{entry.version}} </td>
                <td ng-if="entry.healthy">健康</td>
                <td ng-if="!entry.healthy" style="color: red">失联</td>
                <td>{{entry.lastHeartbeat | date: 'yyyy/MM/dd HH:mm:ss'}}</td>
                <td>
                    <button ng-if="!entry.healthy" class="btn btn-xs btn-outline-danger" style="height: 25px; font-size: 12px;" ng-click="removeMachine(entry.ip, entry.port)">移除</button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        <!-- .card-body -->
        <div class="pagination-footer">
          <dir-pagination-controls boundary-links="true" template-url="app/views/pagination.tpl.html" pagination-id="entriesPagination"
            on-page-change="">
          </dir-pagination-controls>
          <div class="tools" style="">
            <span>共 {{machinesPageConfig.totalCount}} 条记录, </span>
            <span>
              每页
              <input class="form-control" ng-model="machinesPageConfig.pageSize"> 条记录,
            </span>
            <span>第 {{machinesPageConfig.currentPageIndex}} / {{machinesPageConfig.totalPage}} 页</span>
          </div>
          <!-- .tools -->
        </div>
        <!-- pagination-footer -->
      </div>
      <!-- .card -->
    </div>
    <!-- .col-md-12 -->
  </div>
  <!-- -->
</div>
<!-- .container-fluid -->
