<div>
    <span class="brand" style="font-weight:bold;">{{authorityRuleDialog.title}}</span>
    <div class="card" style="margin-top: 20px;margin-bottom: 10px;">
        <div class="panel-body">
            <div class="row">
                <form role="form" class="form-horizontal">
                    <div class="form-group">
                        <label class="col-sm-2 control-label">资源名</label>
                        <div class="col-sm-9">
                            <input type="text" ng-if="authorityRuleDialog.type == 'edit'" class="form-control" placeholder="资源名" ng-model='currentRule.rule.resource'
                                   disabled="" />
                            <input type="text" ng-if="authorityRuleDialog.type == 'add'" class="form-control highlight-border" placeholder="资源名称" ng-model='currentRule.rule.resource'
                            />
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-sm-2 control-label">流控应用</label>
                        <div class="col-sm-9">
                            <input type="text" class="form-control highlight-border" ng-model='currentRule.rule.limitApp' placeholder='指调用方，多个调用方名称用半角英文逗号（,）分隔'
                            />
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-sm-2 control-label">授权类型</label>
                        <div class="col-sm-4">
                            <div class="form-control highlight-border" align="center">
                                <input type="radio" name="strategy" value="0" checked ng-model='currentRule.rule.strategy' />&nbsp;白名单&nbsp;&nbsp;
                                <input type="radio" name="strategy" value="1" ng-model='currentRule.rule.strategy' />&nbsp;黑名单
                            </div>
                        </div>
                    </div>

                </form>
            </div>
            <div class="separator"></div>
            <div clss="row" style="margin-top: 20px;">
                <button class="btn btn-outline-danger" style="float:right; height: 30px;font-size: 12px;margin-left: 10px;" ng-click="closeThisDialog()">取消</button>
                <button class="btn btn-outline-success" style="float:right; height: 30px;font-size: 12px;margin-left: 10px;" ng-click="saveRule()">{{authorityRuleDialog.confirmBtnText}}</button>
                <button ng-if="authorityRuleDialog.saveAndContinueBtnText" class="btn btn-default" style="float:right; height: 30px;font-size: 12px;"
                        ng-click="saveRuleAndContinue()">{{authorityRuleDialog.saveAndContinueBtnText}}</button>
            </div>
        </div>
    </div>
</div>
