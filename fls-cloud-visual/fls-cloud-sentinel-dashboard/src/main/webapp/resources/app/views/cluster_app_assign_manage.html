<div class="row" style="margin-left: 1px; margin-top:10px; height: 50px;">
    <div class="col-md-6" style="margin-bottom: 10px;">
        <span style="font-size: 30px;font-weight: bold;">{{app}}</span>
    </div>
</div>

<div class="separator"></div>
<div class="container-fluid">
    <div class="row" style="margin-top: 20px; margin-bottom: 20px;">
        <div class="col-md-12">
            <div class="card">
                <div class="inputs-header">
                    <span class="brand" style="font-size: 13px;">集群限流 - 机器分配/管控</span>
                </div>

                <!-- error panel -->
                <div class="row clearfix" ng-if="loadError">
                    <div class="col-md-6 col-md-offset-3">
                        <div class="panel panel-default">
                            <div class="panel-body">
                                <center>
                                    <p>{{loadError.message}}</p>
                                </center>
                            </div>
                        </div>
                    </div>
                </div>

                <!--.tools-header -->
                <div class="card-body" style="padding: 0px 0px;" ng-if="!loadError">
                    <form role="form" class="form-horizontal">
                        <div class="form-group">
                            <label class="col-sm-2 control-label">Server 列表</label>
                            <div class="col-sm-4">
                                <select ng-model="tmp.curChosenServer" ng-change="onCurrentServerChange()" size="8"
                                        ng-options="serverGroup.machineId for serverGroup in clusterMap"
                                        class="form-control"></select>
                            </div>
                            <button type="button" class="btn btn-outline-warning" ng-click="removeFromServerList()">移除
                            </button>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">Token Server 端口</label>
                            <div class="col-sm-4">
                                <input type="number" class="form-control highlight-border"
                                       ng-disabled="!tmp.curChosenServer.belongToApp"
                                       ng-model='tmp.curChosenServer.port' placeholder='port' min="1" max="65535"/>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">命名空间集合</label>
                            <div class="col-sm-4">
                                <input type="text" required class="form-control highlight-border"
                                       ng-disabled="!tmp.curChosenServer.belongToApp"
                                       ng-model='tmp.curChosenServer.namespaceSetStr'
                                       placeholder='请指定服务端服务的命名空间集合（以,分隔）'/>
                            </div>
                        </div>
                    </form>
                    <form role="form" class="form-inline" style="margin-top: 30px; margin-left: 20px;">
                        <div>
                            <div class="form-group">
                                <div class="col-sm-12">
                                    <label class="control-label" style="width: 200px; text-align: center;">当前对应客户端列表</label>
                                    <select size="8" multiple="multiple" ng-model="tmp.curClientChosen"
                                            ng-options="ip for ip in tmp.curChosenServer.clientSet"
                                            class="form-control" style="width: 100%;"></select>
                                </div>

                            </div>
                            <div class="form-group">
                                <div class="col-sm-12">
                                    <button type="button" class="btn btn-outline-primary"
                                            ng-disabled="!tmp.curChosenServer || !tmp.curChosenServer.machineId"
                                            ng-click="moveToServerGroup()">←
                                    </button>
                                    <button type="button" class="btn btn-outline-primary"
                                            ng-click="moveToRemainingSharePool()">→
                                    </button>
                                </div>
                            </div>

                            <div class="form-group">
                                <div class="col-sm-12">
                                    <label class="control-label" style="width: 220px; text-align: center;">未分配机器列表</label>
                                    <div>
                                        <select size="8" multiple="multiple" ng-model="tmp.curRemainingClientChosen"
                                                ng-options="ip for ip in remainingClientAddressList"
                                                class="form-control" style="width: 100%;">
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-6">
                                    <button type="button" class="btn btn-outline-primary"
                                            ng-click="addToServerList()">添加为 server
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                    <div class="separator"></div>
                    <div style="margin-top: 20px;">
                        <button type="button" style="margin: 0 10px 10px 10px;" class="btn btn-outline-success"
                                ng-click="saveAndApplyAssign()">保存并执行分配
                        </button>
                    </div>
                </div>
                <!-- .card-body -->
            </div>
            <!-- .card -->
        </div>
        <!-- .col-md-12 -->
    </div>
    <!-- -->
</div>
<!-- .container-fluid -->
