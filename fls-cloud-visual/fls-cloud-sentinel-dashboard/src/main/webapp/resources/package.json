{"name": "sentinel-dashboard", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo no test case", "build": "gulp build", "start": "gulp"}, "author": "x-cold <<EMAIL>>", "license": "MIT", "dependencies": {"angular": "^1.4.8", "angular-animate": "^1.4.0", "angular-bootstrap": "^0.12.2", "angular-clipboard": "^1.6.2", "angular-cookies": "^1.4.0", "angular-date-time-input": "^1.2.1", "angular-loading-bar": "^0.9.0", "angular-mocks": "^1.4.0", "angular-resource": "^1.4.0", "angular-route": "^1.4.0", "angular-selectize2": "^v1.2.3", "angular-table-resize": "^2.0.1", "angular-touch": "^1.4.0", "angular-ui-notification": "^0.3.6", "angular-ui-router": "^1.0.18", "angular-utils-pagination": "^0.11.1", "angularjs-bootstrap-datetimepicker": "^1.1.4", "bootstrap-switch": "^3.3.4", "bootstrap-tagsinput": "~0.7.1", "lodash": "^4.17.15", "moment": "^2.12.0", "ng-dialog": "^0.6.6", "ng-tags-input": "~3.0.0", "oclazyload": "^1.1.0", "selectize": "^0.12.1"}, "devDependencies": {"gulp": "^3.9.1", "gulp-clean": "^0.4.0", "gulp-concat": "^2.6.1", "gulp-connect": "^5.7.0", "gulp-csscomb": "^3.0.8", "gulp-cssmin": "^0.2.0", "gulp-jshint": "^2.1.0", "gulp-load-plugins": "^1.6.0", "gulp-serv": "0.0.1", "gulp-uglify": "^3.0.0", "jshint": "^2.10.2", "open": "^6.3.0", "source-map": "^0.7.3"}}