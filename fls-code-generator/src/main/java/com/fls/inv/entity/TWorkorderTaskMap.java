package com.fls.inv.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 工单与工单任务定义映射
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_workorder_task_map")
public class TWorkorderTaskMap implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 工单与工单任务定义主键
     */
    @TableId(value = "id_workorder_task_map", type = IdType.ASSIGN_UUID)
    private String idWorkorderTaskMap;

    /**
     * 工单资源id
     */
    private String idWorkorderResource;

    /**
     * 工单任务定义id
     */
    private String idTaskDefinition;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 删除标记: 0-正常, 1-删除
     */
    private String deleteFlag;

    /**
     * 创建时间戳
     */
    private LocalDateTime ts;


}
