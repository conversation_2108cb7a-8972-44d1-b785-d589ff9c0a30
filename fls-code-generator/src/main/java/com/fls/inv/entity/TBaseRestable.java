package com.fls.inv.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 资源数据表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_base_restable")
public class TBaseRestable implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id_restable", type = IdType.ASSIGN_UUID)
    private String idRestable;

    /**
     * 资源主键
     */
    private String idResource;

    /**
     * 资源编码
     */
    private String resourceCode;

    /**
     * 数据库名
     */
    private String dbName;

    /**
     * 数据表名
     */
    private String tblName;

    /**
     * 数据表主键名
     */
    private String tblIdName;

    /**
     * 数据表编码字段名
     */
    private String tblColCode;

    /**
     * 数据表名称字段名
     */
    private String tblColName;

    /**
     * 时间戳
     */
    private LocalDateTime ts;

    /**
     * 是否删除：0=否，1=是，默认0，参见yesorno
     */
    private String deleteFlag;


}
