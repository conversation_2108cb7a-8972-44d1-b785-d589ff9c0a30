package com.fls.inv.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 待办任务
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_base_todo_task")
public class TBaseTodoTask implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 待办任务主键id
     */
    @TableId(value = "id_todo_task", type = IdType.ASSIGN_UUID)
    private String idTodoTask;

    /**
     * 来源系统标识
     */
    private String sourceProjectName;

    /**
     * 来源系统地址
     */
    private String sourceProjectUrl;

    /**
     * 资源访问菜单路径
     */
    private String visitPath;

    /**
     * 资源访问参数
     */
    private String visitParam;

    /**
     * 小程序资源访问菜单路径
     */
    private String appletVisitPath;

    /**
     * 小程序资源访问参数
     */
    private String appletVisitParam;

    /**
     * 来源系统任务id
     */
    private String idSourceRecord;

    /**
     * 来源任务名称
     */
    private String recordName;

    /**
     * 待办任务状态，0：带审批，1：待办理，2：进行中，3：已办理，4：已关闭
     */
    private String taskStatus;

    /**
     * 任务办理人
     */
    private String idAssignee;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 删除标记: 0-正常, 1-删除
     */
    private String deleteFlag;

    /**
     * 创建时间戳
     */
    private LocalDateTime ts;


}
