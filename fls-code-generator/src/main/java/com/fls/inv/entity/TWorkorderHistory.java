package com.fls.inv.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 工单流转历史记录
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_workorder_history")
public class TWorkorderHistory implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 工单历史记录id
     */
    @TableId(value = "id_workorder_history", type = IdType.ASSIGN_UUID)
    private String idWorkorderHistory;

    /**
     * 来源记录id
     */
    private String idSourceRecord;

    /**
     * 来源实例名称
     */
    private String recordName;

    /**
     * 来源系统标识
     */
    private String sourceProjectName;

    /**
     * 记录开始时间
     */
    private LocalDateTime startTime;

    /**
     * 记录结束时间
     */
    private LocalDateTime endTime;

    /**
     * 操作人
     */
    private String idOperator;

    /**
     * 操作人名称
     */
    private String operatorName;

    /**
     * 记录状态
     */
    private Integer recordStatus;

    /**
     * 记录类型，枚举值 0：工单，1：工单任务
     */
    private Integer recordType;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 删除标记: 0-正常, 1-删除
     */
    private String deleteFlag;

    /**
     * 创建时间戳
     */
    private LocalDateTime ts;


}
