package com.fls.inv.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 仓库表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_base_warehouse")
public class TBaseWarehouse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id_warehouse", type = IdType.ASSIGN_UUID)
    private String idWarehouse;

    /**
     * 编码
     */
    private String code;

    /**
     * 名称
     */
    private String name;

    /**
     * 所属组织
     */
    private String idOrg;

    /**
     * 所属主体
     */
    private String idBizunit;

    /**
     * NC仓库主键
     */
    private String pkStordoc;

    /**
     * 是否启用货位，参见yesorno
     */
    private String rackFlag;

    /**
     * 系统类型，01=NC，02=LABS
     */
    private String systemType;

    /**
     * 是否废品库，0=否，1=是
     */
    private String scrapFlag;

    /**
     * 是否代储仓，0=否，1=是
     */
    private String agentFlag;

    /**
     * 是否影响可用量，0=否，1=是
     */
    private String atpAffectedFlag;

    /**
     * 是否委外仓，0=否，1=是
     */
    private String commisionFlag;

    /**
     * 是否直运仓，0=否，1=是
     */
    private String directFlag;

    /**
     * 是否保税仓，0=否，1=是
     */
    private String bondedFlag;

    /**
     * 是否零售，0=否，1=是
     */
    private String retailFlag;

    /**
     * 是否门店，0=否，1=是
     */
    private String shopStoreFlag;

    /**
     * 是否在途仓，0=否，1=是
     */
    private String onthewayFlag;

    /**
     * 是否外贸仓，参见yesorno
     */
    private String foreignFlag;

    /**
     * 是否参与ROP，参见yesorno
     */
    private String ropFlag;

    /**
     * 是否启用WMS，参见yesorno
     */
    private String wmsFlag;

    /**
     * wms别名，gz=广州基地，tj=天津基地，hf=合肥基地
     */
    private String wmsAlias;

    /**
     * wms数据库名
     */
    private String wmsDbName;

    /**
     * ROP主体ID
     */
    private String idRopUnit;

    /**
     * 启用状态：1=未启用，2=已启用，3=已停用，默认1，参见basestatus
     */
    private String status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 失效时间
     */
    private LocalDateTime disableTime;

    /**
     * 时间戳
     */
    private LocalDateTime ts;

    /**
     * 是否删除：0=否，1=是，默认0，参见yesorno
     */
    private String deleteFlag;


}
