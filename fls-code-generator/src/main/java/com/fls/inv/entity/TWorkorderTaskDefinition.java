package com.fls.inv.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 工单任务定义
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_workorder_task_definition")
public class TWorkorderTaskDefinition implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 工单任务定义主键
     */
    @TableId(value = "id_task_definition", type = IdType.ASSIGN_UUID)
    private String idTaskDefinition;

    /**
     * 工单任务名称
     */
    private String workorderTaskName;

    /**
     * 工单任务资源id
     */
    private String idTaskResource;

    /**
     * 工单任务资源编码
     */
    private String taskResourceCode;

    /**
     * 工单任务描述信息（描述工单任务工作内容）
     */
    private String taskDescription;

    /**
     * 资源访问菜单路径
     */
    private String visitPath;

    /**
     * 资源访问参数
     */
    private String visitParam;

    /**
     * 小程序资源访问菜单路径
     */
    private String appletVisitPath;

    /**
     * 小程序资源访问参数
     */
    private String appletVisitParam;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 删除标记: 0-正常, 1-删除
     */
    private String deleteFlag;

    /**
     * 创建时间戳
     */
    private LocalDateTime ts;


}
