package com.fls.inv.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 待办消息
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_base_todo_message")
public class TBaseTodoMessage implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 待办消息主键id
     */
    @TableId(value = "id_todo_message", type = IdType.ASSIGN_UUID)
    private String idTodoMessage;

    /**
     * 来源系统标识
     */
    private String sourceProjectName;

    /**
     * 来源系统地址
     */
    private String sourceProjectUrl;

    /**
     * 资源访问菜单路径
     */
    private String visitPath;

    /**
     * 资源访问参数
     */
    private String visitParam;

    /**
     * 小程序资源访问菜单路径
     */
    private String appletVisitPath;

    /**
     * 小程序资源访问参数
     */
    private String appletVisitParam;

    /**
     * 来源系统任务id
     */
    private String idSourceRecord;

    /**
     * 待办消息标题
     */
    private String messageTitle;

    /**
     * 待办消息内容
     */
    private String messageContent;

    /**
     * 待办消息状态，0：未读，1：已读
     */
    private Integer messageStatus;

    /**
     * 消息通知人
     */
    private String idAssignee;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 删除标记: 0-正常, 1-删除
     */
    private String deleteFlag;

    /**
     * 创建时间戳
     */
    private LocalDateTime ts;


}
