package com.fls.inv.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 工单任务信息
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_workorder_task_record")
public class TWorkorderTaskRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 工单任务记录主键
     */
    @TableId(value = "id_workorder_task_record", type = IdType.ASSIGN_UUID)
    private String idWorkorderTaskRecord;

    /**
     * 工单记录id
     */
    private String idWorkorderRecord;

    /**
     * 工单任务定义id
     */
    private String idTaskDefinition;

    /**
     * 工单任务资源id
     */
    private String idTaskResource;

    /**
     * 来源系统标识
     */
    private String sourceProjectName;

    /**
     * 工单任务描述信息（描述工单任务工作内容）
     */
    private String taskDescription;

    /**
     * 工单任务实例id
     */
    private String idTaskInstance;

    /**
     * 工单任务实例名称
     */
    private String taskInstanceName;

    /**
     * 流程任务id
     */
    private String idProctask;

    /**
     * 流程实例id
     */
    private String idProcinst;

    /**
     * 工单状态，0：新提交，1：进行中，2：已完成，3：已关闭
     */
    private Boolean taskStatus;

    /**
     * 工单任务备注
     */
    private String taskRemarks;

    /**
     * 任务办理人
     */
    private String idsAssignee;

    /**
     * 任务候选人
     */
    private String idsCandidate;

    /**
     * 任务协办人
     */
    private String idsCoOperator;

    /**
     * 任务所有人
     */
    private String owner;

    /**
     * 工单任务提交数据
     */
    private String formJson;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 删除标记: 0-正常, 1-删除
     */
    private String deleteFlag;

    /**
     * 创建时间戳
     */
    private LocalDateTime ts;


}
