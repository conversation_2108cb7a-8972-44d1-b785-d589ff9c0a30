package com.fls.inv.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 入库单表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_workteam_inbound_bill")
public class TWorkteamInboundBill implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 入库单主键id
     */
    @TableId(value = "id_inbound_bill", type = IdType.ASSIGN_UUID)
    private String idInboundBill;

    /**
     * 单据编号
     */
    private String billCode;

    /**
     * 单据日期
     */
    private LocalDateTime billDate;

    /**
     * 业务来源:1：物料领取 2：组间调用
     */
    private Integer sourceBillType;

    /**
     * 来源单据编号
     */
    private String sourceBillCode;

    /**
     * 来源单据id
     */
    private String idSourceBill;

    /**
     * 来源描述
     */
    private String sourceDesc;

    /**
     * 源头来源单据id
     */
    private String idFirstSourceBill;

    /**
     * 源头来源单据编号
     */
    private String firstSourceBillCode;

    /**
     * 入库数量
     */
    private BigDecimal inboundCount;

    /**
     * 存储组织id(单据归属)-组织数据权限
     */
    private String idOrg;

    /**
     * 经营主体id(单据归属)--档案数据权限
     */
    private String idBizunit;

    /**
     * 资源id(单据归属)--档案数据权限
     */
    private String idResource;

    /**
     * 资源交易类型(单据归属)--档案数据权限
     */
    private String idResTrantype;

    /**
     * 1-启用审批流，0-未启用审批流
     */
    private String needprocFlag;

    /**
     * 单据审批状态 (0-待审批,1-审批通过,2-审批驳回)
     */
    private String status;

    /**
     * 作废标识:1-作废,0-有效
     */
    private String invalidFlag;

    /**
     * 审批时间
     */
    private LocalDateTime auditTime;

    /**
     * 审批人
     */
    private String auditor;

    /**
     * 作废时间
     */
    private LocalDateTime invalidTime;

    /**
     * 作废人
     */
    private String invalider;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 0-正常,1-删除
     */
    private String deleteFlag;


}
