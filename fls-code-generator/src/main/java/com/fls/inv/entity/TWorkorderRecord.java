package com.fls.inv.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 工单记录信息
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_workorder_record")
public class TWorkorderRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 工单记录主键
     */
    @TableId(value = "id_workorder_record", type = IdType.ASSIGN_UUID)
    private String idWorkorderRecord;

    /**
     * 工单资源id
     */
    private String idWorkorderResource;

    /**
     * 来源系统标识
     */
    private String sourceProjectName;

    /**
     * 流程定义id
     */
    private String idProcdef;

    /**
     * 流程实例id
     */
    private String idProcinst;

    /**
     * 工单实例id
     */
    private String idWorkorderInstance;

    /**
     * 工单实例名称
     */
    private String workorderInstanceName;

    /**
     * 工单状态，0：新提交，1：进行中，2：已完成，3：已关闭
     */
    private Boolean workorderStatus;

    /**
     * 工单备注
     */
    private String remarks;

    /**
     * 工单发起人id
     */
    private String initiator;

    /**
     * 工单发起时间
     */
    private LocalDateTime initiateTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 删除标记: 0-正常, 1-删除
     */
    private String deleteFlag;

    /**
     * 创建时间戳
     */
    private LocalDateTime ts;


}
