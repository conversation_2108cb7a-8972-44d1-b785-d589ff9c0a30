package com.fls.task.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 任务调度日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_task_log")
public class TTaskLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id_schedule_log", type = IdType.ASSIGN_UUID)
    private String idScheduleLog;

    /**
     * 调度信息id
     */
    private String idTaskSchedule;

    /**
     * 请求任务id
     */
    private String idApiTask;

    /**
     * 调度类型（定时|固定速率|执行一次|手动）
     */
    private String scheduleType;

    /**
     * 触发时间
     */
    private LocalDateTime triggerTime;

    /**
     * 请求内容
     */
    private String reqBody;

    /**
     * 响应http状态码
     */
    private String resHttpCode;

    /**
     * 响应码
     */
    private String resCode;

    /**
     * 响应描述
     */
    private String resMsg;

    /**
     * 响应文本
     */
    private String resText;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 删除标识
     */
    private String deleteFlag;

    /**
     * 时间戳
     */
    private LocalDateTime ts;


}
