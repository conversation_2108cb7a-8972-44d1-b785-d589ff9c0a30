package com.fls.task.service.impl;

import com.fls.task.entity.TApiTaskInfo;
import com.fls.task.mapper.TApiTaskInfoMapper;
import com.fls.task.service.ITApiTaskInfoService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 接口任务请求信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Service
public class TApiTaskInfoServiceImpl extends ServiceImpl<TApiTaskInfoMapper, TApiTaskInfo> implements ITApiTaskInfoService {

}
