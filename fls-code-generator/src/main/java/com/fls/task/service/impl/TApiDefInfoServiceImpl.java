package com.fls.task.service.impl;

import com.fls.task.entity.TApiDefInfo;
import com.fls.task.mapper.TApiDefInfoMapper;
import com.fls.task.service.ITApiDefInfoService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 接口定义表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Service
public class TApiDefInfoServiceImpl extends ServiceImpl<TApiDefInfoMapper, TApiDefInfo> implements ITApiDefInfoService {

}
