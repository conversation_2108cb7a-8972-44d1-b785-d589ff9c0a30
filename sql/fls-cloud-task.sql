-- MySQL 8.0 任务管理数据库表定义SQL文本

-- -----------------------------------------------------
-- Schema fls_task
-- 任务管理数据库
-- -----------------------------------------------------
CREATE SCHEMA IF NOT EXISTS `fls_task` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
USE `fls_task`;

-- -----------------------------------------------------
-- Table `fls_task`.`t_api_def_info`
-- 接口定义表
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `t_api_def_info` (
    `id_api_def` VARCHAR(36) NOT NULL COMMENT '主键',
    `api_type` CHAR(1) NOT NULL COMMENT '接口类型（服务总线|原生请求|链式任务）',
    `api_code` VARCHAR(20) NOT NULL UNIQUE COMMENT '接口编码（用于识别不同的操作）',
    `api_name` VARCHAR(64) NOT NULL COMMENT '接口名称',
    `api_path` VARCHAR(64) NOT NULL COMMENT '接口路径',
    `api_method` VARCHAR(16) NOT NULL COMMENT '请求方法',
    `remark` VARCHAR(256) NULL COMMENT '备注',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `creator` VARCHAR(36) NOT NULL COMMENT '创建人',
    `update_time` DATETIME NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间', -- 根据你的表格，此处update_time是可空，但通常会希望自动更新
    `updater` VARCHAR(36) NULL COMMENT '更新人', -- 根据你的表格，updater是可空
    `delete_flag` CHAR(1) NOT NULL DEFAULT '0' COMMENT '删除标识',
    `ts` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '时间戳', -- 保持与你表格一致，如果为NULL，则第一次插入时不会自动更新
    PRIMARY KEY (`id_api_def`),
    INDEX `idx_api_code` (`api_code` ASC) COMMENT '接口编码索引，加速查询'
)
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8mb4
COLLATE = utf8mb4_general_ci
COMMENT = '接口定义表';


-- -----------------------------------------------------
-- Table `fls_task`.`t_api_task_info`
-- 接口任务请求信息表
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `t_api_task_info` (
    `id_api_task` VARCHAR(36) NOT NULL COMMENT '主键',
    `id_api_def` VARCHAR(36) NOT NULL COMMENT '所属接口定义id',
    `task_status` CHAR(1) NOT NULL DEFAULT '0' COMMENT '任务状态（待调度|进行中|执行成功|执行失败）',
    `notify_status` CHAR(1) NOT NULL DEFAULT '0' COMMENT '任务回调处理状态（成功|失败）',
    `req_url` VARCHAR(128) NULL COMMENT '请求url',
    `req_method` VARCHAR(16) NULL COMMENT '请求方法',
    `req_body` TEXT NULL COMMENT '请求内容',
    `res_code` VARCHAR(16) NULL COMMENT '响应编码',
    `res_text` TEXT NULL COMMENT '响应内容',
    `task_cost` INT NULL COMMENT '任务耗时（单位：秒）',
    `task_handler` VARCHAR(128) NULL COMMENT '任务处理器名称',
    `id_sccc_task` VARCHAR(36) NULL COMMENT '供应链任务id',
    `remark` VARCHAR(256) NULL COMMENT '备注',
    `source_project_code` VARCHAR(36) NULL COMMENT '来源项目编码',
    `source_project_url` VARCHAR(256) NULL COMMENT '来源项目地址',
    `id_source_bill` VARCHAR(36) NULL COMMENT '来源单据主键',
    `source_bill_code` VARCHAR(20) NULL COMMENT '来源单据编号',
    `id_source_res` VARCHAR(36) NULL COMMENT '来源单据资源主键',
    `source_bill_name` VARCHAR(64) NULL COMMENT '来源单据名称',
    `source_bill_table` VARCHAR(64) NULL COMMENT '来源单据表',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `creator` VARCHAR(36) NOT NULL COMMENT '创建人',
    `update_time` DATETIME NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间', -- 同上
    `updater` VARCHAR(36) NULL COMMENT '更新人', -- 同上
    `delete_flag` CHAR(1) NOT NULL DEFAULT '0' COMMENT '删除标识',
    `ts` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '时间戳', -- 同上
    PRIMARY KEY (`id_api_task`),
    INDEX `idx_task_status` (`task_status` ASC) COMMENT '任务状态索引，加速查询',
    INDEX `idx_notify_status` (`notify_status` ASC) COMMENT '回调状态索引',
    INDEX `idx_source_bill_code` (`source_bill_code` ASC) COMMENT '来源单据编号索引'
)
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8mb4
COLLATE = utf8mb4_general_ci
COMMENT = '接口任务请求信息表';


-- -----------------------------------------------------
-- Table `fls_task`.`t_task_schedule_info`
-- 任务调度信息表
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `t_task_schedule_info` (
    `id_task_schedule` VARCHAR(36) NOT NULL COMMENT '主键',
    `id_api_task` VARCHAR(36) NOT NULL COMMENT '请求任务id',
    `schedule_type` CHAR(1) NULL COMMENT '调度类型（定时|固定速率|执行一次|手动）', -- 根据你表格，是可空
    `schedule_conf` VARCHAR(36) NULL COMMENT '调度配置',
    `trigger_status` CHAR(1) NULL COMMENT '调度状态（进行中|停止）',
    `trigger_last_time` DATETIME NULL COMMENT '上次触发时间',
    `trigger_next_time` DATETIME NULL COMMENT '下次触发时间',
    `remark` VARCHAR(256) NULL COMMENT '备注',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `creator` VARCHAR(36) NOT NULL COMMENT '创建人',
    `update_time` DATETIME NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间', -- 同上
    `updater` VARCHAR(36) NULL COMMENT '更新人', -- 同上
    `delete_flag` CHAR(1) NOT NULL DEFAULT '0' COMMENT '删除标识',
    `ts` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '时间戳', -- 同上
    PRIMARY KEY (`id_task_schedule`)
)
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8mb4
COLLATE = utf8mb4_general_ci
COMMENT = '任务调度信息表';


-- -----------------------------------------------------
-- Table `fls_task`.`t_task_log`
-- 任务调度日志表
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `t_task_log` (
    `id_schedule_log` VARCHAR(36) NOT NULL COMMENT '主键',
    `id_task_schedule` VARCHAR(36) NOT NULL COMMENT '调度信息id',
    `id_api_task` VARCHAR(36) NOT NULL COMMENT '请求任务id',
    `schedule_type` CHAR(1) NULL COMMENT '调度类型（定时|固定速率|执行一次|手动）', -- 根据你表格，是可空
    `trigger_time` DATETIME NULL COMMENT '触发时间',
    `req_body` TEXT NULL COMMENT '请求内容',
    `res_http_code` VARCHAR(16) NULL COMMENT '响应http状态码',
    `res_code` VARCHAR(16) NULL COMMENT '响应码',
    `res_msg` VARCHAR(64) NULL COMMENT '响应描述',
    `res_text` TEXT NULL COMMENT '响应文本',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `creator` VARCHAR(36) NOT NULL COMMENT '创建人',
    `delete_flag` CHAR(1) NOT NULL DEFAULT '0' COMMENT '删除标识',
    `ts` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '时间戳', -- 同上
    PRIMARY KEY (`id_schedule_log`),
    INDEX `idx_log_trigger_time` (`trigger_time` ASC) COMMENT '日志触发时间索引'
)
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8mb4
COLLATE = utf8mb4_general_ci
COMMENT = '任务调度日志表';