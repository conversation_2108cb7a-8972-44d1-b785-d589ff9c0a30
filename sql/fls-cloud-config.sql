/*
 Navicat Premium Data Transfer

 Source Server         : localhost
 Source Server Type    : MySQL
 Source Server Version : 50726
 Source Host           : localhost:3306
 Source Schema         : fls-cloud-config

 Target Server Type    : MySQL
 Target Server Version : 50726
 File Encoding         : 65001

 Date: 21/11/2022 08:35:15
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for config_info
-- ----------------------------
DROP TABLE IF EXISTS `config_info`;
CREATE TABLE `config_info`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `data_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT 'data_id',
  `group_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `content` longtext CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT 'content',
  `md5` varchar(32) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT 'md5',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `src_user` text CHARACTER SET utf8 COLLATE utf8_bin NULL COMMENT 'source user',
  `src_ip` varchar(50) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT 'source ip',
  `app_name` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `tenant_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '' COMMENT '租户字段',
  `c_desc` varchar(256) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `c_use` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `effect` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `type` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `c_schema` text CHARACTER SET utf8 COLLATE utf8_bin NULL,
  `encrypted_data_key` text CHARACTER SET utf8 COLLATE utf8_bin NULL COMMENT '秘钥',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_configinfo_datagrouptenant`(`data_id`, `group_id`, `tenant_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin COMMENT = 'config_info' ROW_FORMAT = Dynamic;


-- ----------------------------
-- Table structure for config_info_aggr
-- ----------------------------
DROP TABLE IF EXISTS `config_info_aggr`;
CREATE TABLE `config_info_aggr`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `data_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT 'data_id',
  `group_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT 'group_id',
  `datum_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT 'datum_id',
  `content` longtext CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '内容',
  `gmt_modified` datetime NOT NULL COMMENT '修改时间',
  `app_name` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `tenant_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '' COMMENT '租户字段',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_configinfoaggr_datagrouptenantdatum`(`data_id`, `group_id`, `tenant_id`, `datum_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin COMMENT = '增加租户字段' ROW_FORMAT = Dynamic;



-- ----------------------------
-- Table structure for config_info_beta
-- ----------------------------
DROP TABLE IF EXISTS `config_info_beta`;
CREATE TABLE `config_info_beta`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `data_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT 'data_id',
  `group_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT 'group_id',
  `app_name` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT 'app_name',
  `content` longtext CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT 'content',
  `beta_ips` varchar(1024) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT 'betaIps',
  `md5` varchar(32) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT 'md5',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `src_user` text CHARACTER SET utf8 COLLATE utf8_bin NULL COMMENT 'source user',
  `src_ip` varchar(50) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT 'source ip',
  `tenant_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '' COMMENT '租户字段',
  `encrypted_data_key` text CHARACTER SET utf8 COLLATE utf8_bin NULL COMMENT '秘钥',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_configinfobeta_datagrouptenant`(`data_id`, `group_id`, `tenant_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin COMMENT = 'config_info_beta' ROW_FORMAT = Dynamic;



-- ----------------------------
-- Table structure for config_info_tag
-- ----------------------------
DROP TABLE IF EXISTS `config_info_tag`;
CREATE TABLE `config_info_tag`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `data_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT 'data_id',
  `group_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT 'group_id',
  `tenant_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '' COMMENT 'tenant_id',
  `tag_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT 'tag_id',
  `app_name` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT 'app_name',
  `content` longtext CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT 'content',
  `md5` varchar(32) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT 'md5',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `src_user` text CHARACTER SET utf8 COLLATE utf8_bin NULL COMMENT 'source user',
  `src_ip` varchar(50) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT 'source ip',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_configinfotag_datagrouptenanttag`(`data_id`, `group_id`, `tenant_id`, `tag_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin COMMENT = 'config_info_tag' ROW_FORMAT = Dynamic;



-- ----------------------------
-- Table structure for config_tags_relation
-- ----------------------------
DROP TABLE IF EXISTS `config_tags_relation`;
CREATE TABLE `config_tags_relation`  (
  `id` bigint(20) NOT NULL COMMENT 'id',
  `tag_name` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT 'tag_name',
  `tag_type` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT 'tag_type',
  `data_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT 'data_id',
  `group_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT 'group_id',
  `tenant_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '' COMMENT 'tenant_id',
  `nid` bigint(20) NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`nid`) USING BTREE,
  UNIQUE INDEX `uk_configtagrelation_configidtag`(`id`, `tag_name`, `tag_type`) USING BTREE,
  INDEX `idx_tenant_id`(`tenant_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin COMMENT = 'config_tag_relation' ROW_FORMAT = Dynamic;



-- ----------------------------
-- Table structure for group_capacity
-- ----------------------------
DROP TABLE IF EXISTS `group_capacity`;
CREATE TABLE `group_capacity`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `group_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT '' COMMENT 'Group ID，空字符表示整个集群',
  `quota` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '配额，0表示使用默认值',
  `usage` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '使用量',
  `max_size` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '单个配置大小上限，单位为字节，0表示使用默认值',
  `max_aggr_count` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '聚合子配置最大个数，，0表示使用默认值',
  `max_aggr_size` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '单个聚合数据的子配置大小上限，单位为字节，0表示使用默认值',
  `max_history_count` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '最大变更历史数量',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_group_id`(`group_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin COMMENT = '集群、各Group容量信息表' ROW_FORMAT = Dynamic;



-- ----------------------------
-- Table structure for his_config_info
-- ----------------------------
DROP TABLE IF EXISTS `his_config_info`;
CREATE TABLE `his_config_info`  (
  `id` bigint(64) UNSIGNED NOT NULL,
  `nid` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `data_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `group_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `app_name` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT 'app_name',
  `content` longtext CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `md5` varchar(32) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `src_user` text CHARACTER SET utf8 COLLATE utf8_bin NULL,
  `src_ip` varchar(50) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `op_type` char(10) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `tenant_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '' COMMENT '租户字段',
  `encrypted_data_key` text CHARACTER SET utf8 COLLATE utf8_bin NULL COMMENT '秘钥',
  PRIMARY KEY (`nid`) USING BTREE,
  INDEX `idx_gmt_create`(`gmt_create`) USING BTREE,
  INDEX `idx_gmt_modified`(`gmt_modified`) USING BTREE,
  INDEX `idx_did`(`data_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin COMMENT = '多租户改造' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for permissions
-- ----------------------------
DROP TABLE IF EXISTS `permissions`;
CREATE TABLE `permissions`  (
  `role` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `resource` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `action` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  UNIQUE INDEX `uk_role_permission`(`role`, `resource`, `action`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;



-- ----------------------------
-- Table structure for roles
-- ----------------------------
DROP TABLE IF EXISTS `roles`;
CREATE TABLE `roles`  (
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `role` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  UNIQUE INDEX `idx_user_role`(`username`, `role`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;



-- ----------------------------
-- Table structure for tenant_capacity
-- ----------------------------
DROP TABLE IF EXISTS `tenant_capacity`;
CREATE TABLE `tenant_capacity`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `tenant_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT '' COMMENT 'Tenant ID',
  `quota` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '配额，0表示使用默认值',
  `usage` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '使用量',
  `max_size` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '单个配置大小上限，单位为字节，0表示使用默认值',
  `max_aggr_count` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '聚合子配置最大个数',
  `max_aggr_size` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '单个聚合数据的子配置大小上限，单位为字节，0表示使用默认值',
  `max_history_count` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '最大变更历史数量',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_tenant_id`(`tenant_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin COMMENT = '租户容量信息表' ROW_FORMAT = Dynamic;


-- ----------------------------
-- Table structure for tenant_info
-- ----------------------------
DROP TABLE IF EXISTS `tenant_info`;
CREATE TABLE `tenant_info`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `kp` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT 'kp',
  `tenant_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '' COMMENT 'tenant_id',
  `tenant_name` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '' COMMENT 'tenant_name',
  `tenant_desc` varchar(256) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT 'tenant_desc',
  `create_source` varchar(32) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT 'create_source',
  `gmt_create` bigint(20) NOT NULL COMMENT '创建时间',
  `gmt_modified` bigint(20) NOT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_tenant_info_kptenantid`(`kp`, `tenant_id`) USING BTREE,
  INDEX `idx_tenant_id`(`tenant_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin COMMENT = 'tenant_info' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for users
-- ----------------------------
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users`  (
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `password` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `enabled` tinyint(1) NOT NULL,
  PRIMARY KEY (`username`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of roles
-- ----------------------------
INSERT INTO `roles` VALUES ('nacos', 'ROLE_ADMIN');

-- ----------------------------
-- Records of users
-- ----------------------------
INSERT INTO `users` VALUES ('nacos', '$2a$10$EuWPZHzz32dJN7jexM34MOeYirDdFAZm2kuWj7VEOJhhZkDrxfvUu', 1);



-- ----------------------------
-- Records of tenant_info
-- ----------------------------
INSERT INTO `tenant_info` VALUES (1, '1', 'dev', 'dev', '开发环境', NULL, 1641741261189, 1641741261189);
INSERT INTO `tenant_info` VALUES (2, '1', 'prod', 'prod', '生产环境', NULL, 1641741270448, 1641741287236);
INSERT INTO `tenant_info` VALUES (3, '1', 'dev-dubbo', 'dev-dubbo', '开发环境dubbo注册', 'nacos', 1662196423782, 1662196423782);


-- ----------------------------
-- Records of config_info
-- ----------------------------
INSERT INTO `config_info` VALUES (1, 'application-common.yml', 'DEFAULT_GROUP', 'server:\n  # undertow 配置\n  undertow:\n    # HTTP post内容的最大大小。当值为-1时，默认值为大小是无限的\n    max-http-post-size: -1\n    # 以下的配置会影响buffer,这些buffer会用于服务器连接的IO操作,有点类似netty的池化内存管理\n    # 每块buffer的空间大小,越小的空间被利用越充分\n    buffer-size: 512\n    # 是否分配的直接内存\n    direct-buffers: true\n    threads:\n      # 设置IO线程数, 它主要执行非阻塞的任务,它们会负责多个连接, 默认设置每个CPU核心一个线程\n      io: 8\n      # 阻塞任务线程池, 当执行类似servlet请求阻塞操作, undertow会从这个线程池中取得线程,它的值设置取决于系统的负载\n      worker: 256\n\ndubbo:\n  application:\n    logger: slf4j\n    # 元数据中心 local 本地 remote 远程 这里使用远程便于其他服务获取\n    metadataType: remote\n    # 可选值 interface、instance、all，默认是 all，即接口级地址、应用级地址都注册\n    register-mode: instance\n    service-discovery:\n      # FORCE_INTERFACE，只消费接口级地址，如无地址则报错，单订阅 2.x 地址\n      # APPLICATION_FIRST，智能决策接口级/应用级地址，双订阅\n      # FORCE_APPLICATION，只消费应用级地址，如无地址则报错，单订阅 3.x 地址\n      migration: FORCE_APPLICATION\n  protocol:\n    # 设置为 tri 即可使用 Triple 3.0 新协议\n    # 性能对比 dubbo 协议并没有提升 但基于 http2 用于多语言异构等 http 交互场景\n    # 使用 dubbo 协议通信\n    name: dubbo\n    # dubbo 协议端口(-1表示自增端口,从20880开始)\n    port: -1\n    # 指定dubbo协议注册ip\n    # host: 127.0.0.1\n  # 注册中心配置\n  registry:\n    use-as-config-center: false\n    # use-as-metadata-center: false\n    address: nacos://${spring.cloud.nacos.server-addr}\n    group: DUBBO_GROUP\n    parameters:\n      namespace: ${spring.profiles.active}-dubbo\n  # 消费者相关配置\n  consumer:\n    # 结果缓存(LRU算法)\n    # 会有数据不一致问题 建议在注解局部开启\n    cache: false\n    # 支持校验注解\n    validation: true\n    # 超时时间\n    timeout: 60000\n    # 初始化检查\n    check: false\n  scan:\n    # 接口实现类扫描\n    base-packages: com.fls.**.dubbo\n  #禁止provicer处理自定义异常 \n  provider:\n    filter: -exception\nspring:\n  # 资源信息\n  messages:\n    # 国际化资源文件路径\n    basename: i18n/messages\n  servlet:\n    multipart:\n      # 整个请求大小限制\n      max-request-size: 20MB\n      # 上传单个文件大小限制\n      max-file-size: 10MB\n  #jackson配置\n  jackson:\n    # 日期格式化\n    date-format: yyyy-MM-dd HH:mm:ss\n    serialization:\n      # 格式化输出\n      INDENT_OUTPUT: false\n      # 忽略无法转换的对象\n      fail_on_empty_beans: false\n    deserialization:\n      # 允许对象忽略json中不存在的属性\n      fail_on_unknown_properties: false\n  cloud:\n    # sentinel 配置\n    sentinel:\n      # 取消控制台懒加载\n      eager: true\n      transport:\n        # 控制台地址\n        dashboard: localhost:8718\n\n  # redis通用配置 子服务可以自行配置进行覆盖\n  redis:\n    host: localhost\n    port: 6379\n    # 密码\n    # password:\n    database: 0\n    timeout: 10s\n    ssl: false\n\n# redisson 配置\nredisson:\n  # redis key前缀\n  keyPrefix:\n  # 线程池数量\n  threads: 4\n  # Netty线程池数量\n  nettyThreads: 8\n  # 单节点配置\n  singleServerConfig:\n    # 客户端名称\n    clientName: ${spring.application.name}\n    # 最小空闲连接数\n    connectionMinimumIdleSize: 8\n    # 连接池大小\n    connectionPoolSize: 32\n    # 连接空闲超时，单位：毫秒\n    idleConnectionTimeout: 10000\n    # 命令等待超时，单位：毫秒\n    timeout: 3000\n    # 发布和订阅连接池大小\n    subscriptionConnectionPoolSize: 50\n  # redisson 缓存配置\n  cacheGroup:\n    # 用例: @Cacheable(cacheNames=\"groupId\", key=\"#XXX\") 方可使用缓存组配置\n    - groupId: redissonCacheMap\n      # 组过期时间(脚本监控)\n      ttl: 60000\n      # 组最大空闲时间(脚本监控)\n      maxIdleTime: 60000\n      # 组最大长度\n      maxSize: 0\n\n# 分布式锁 lock4j 全局配置\nlock4j:\n  # 获取分布式锁超时时间，默认为 3000 毫秒\n  acquire-timeout: 3000\n  # 分布式锁的超时时间，默认为 30 毫秒\n  expire: 30000\n\n# 暴露监控端点\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: \'*\'\n  endpoint:\n    health:\n      show-details: ALWAYS\n    logfile:\n      external-file: ./logs/${spring.application.name}/console.log\n\n# 日志配置\nlogging:\n  level:\n    com.fls: debug\n    org.springframework: warn\n    org.apache.dubbo: warn\n    com.alibaba.nacos: warn\n  config: classpath:logback.xml\n\n# Sa-Token配置\nsa-token:\n  # token名称 (同时也是cookie名称)\n  token-name: Authorization\n  # token有效期  单位: 秒\n  timeout: 86400\n  # token临时有效期 (指定时间无操作就过期) 单位: 秒\n  activity-timeout: -1\n  # 开启内网服务调用鉴权\n  check-id-token: true\n  # Id-Token的有效期 (单位: 秒)\n  id-token-timeout: 600\n  # 是否允许同一账号并发登录 (为true时允许一起登录, 为false时新登录挤掉旧登录)\n  is-concurrent: true\n  # 在多人登录同一账号时，是否共用一个token (为true时所有登录共用一个token, 为false时每次登录新建一个token)\n  is-share: false\n  # 是否尝试从header里读取token\n  is-read-head: true\n  # 是否尝试从cookie里读取token\n  is-read-cookie: false\n  # token前缀\n  token-prefix: \"Bearer\"\n  # jwt秘钥\n  jwt-secret-key: fls-cloud@2022\n  # 是否输出操作日志\n  is-log: true\n\n# MyBatisPlus配置\nmybatis-plus:\n  # 不支持多包, 如有需要可在注解配置 或 提升扫包等级\n  # 例如 com.**.**.mapper\n  mapperPackage: com.fls.**.mapper\n  # 对应的 XML 文件位置\n  mapperLocations: classpath*:mapper/**/*Mapper.xml\n  # 实体扫描，多个package用逗号或者分号分隔\n  typeAliasesPackage: com.fls.**.entity\n  # 启动时是否检查 MyBatis XML 文件的存在，默认不检查\n  checkConfigLocation: false\n  configuration:\n    # 自动驼峰命名规则（camel case）映射\n    mapUnderscoreToCamelCase: true\n    # MyBatis 自动映射策略\n    # NONE：不启用 PARTIAL：只对非嵌套 resultMap 自动映射 FULL：对所有 resultMap 自动映射\n    autoMappingBehavior: PARTIAL\n    # MyBatis 自动映射时未知列或未知属性处理策\n    # NONE：不做处理 WARNING：打印相关警告 FAILING：抛出异常和详细信息\n    autoMappingUnknownColumnBehavior: NONE\n    # 更详细的日志输出 会有性能损耗 org.apache.ibatis.logging.stdout.StdOutImpl\n    # 关闭日志记录 (可单纯使用 p6spy 分析) org.apache.ibatis.logging.nologging.NoLoggingImpl\n    # 默认日志输出 org.apache.ibatis.logging.slf4j.Slf4jImpl\n    logImpl: org.apache.ibatis.logging.slf4j.Slf4jImpl\n  global-config:\n    # 是否打印 Logo banner\n    banner: false\n    dbConfig:\n      # 主键类型\n      # AUTO 自增 NONE 空 INPUT 用户输入 ASSIGN_ID 雪花 ASSIGN_UUID 唯一 UUID\n      idType: ASSIGN_ID\n      # 逻辑已删除值\n      logicDeleteValue: 2\n      # 逻辑未删除值\n      logicNotDeleteValue: 0\n      insertStrategy: NOT_NULL\n      updateStrategy: NOT_NULL\n      where-strategy: NOT_NULL\n\n# swagger配置\nswagger:\n  # 是否开启swagger\n  enabled: true\n  info:\n    # 标题\n    title: \'佛朗斯业务管理系统_接口文档\'\n    # 描述\n    description: \'描述：。。。。。。。\'\n    # 版本\n    version: \'版本号：V1.0.0\'\n    # 作者信息\n    contact:\n      name: zzh\n      email: <EMAIL>\n      url: https://www.fls.com\n  components:\n    # 鉴权方式配置\n    security-schemes:\n      apiKey:\n        type: APIKEY\n        in: HEADER\n        name: ${sa-token.token-name}\n  # 服务文档路径映射 参考 gateway router 配置\n  # 默认为服务名去除前缀转换为path 此处填特殊的配置\n  service-mapping:\n    fls-cloud-gen: /code\n\n# seata配置\nseata:\n  # 默认关闭，如需启用spring.datasource.dynami.seata需要同时开启\n  enabled: true\n  # Seata 应用编号，默认为 ${spring.application.name}\n  application-id: ${spring.application.name}\n  # Seata 事务组编号，用于 TC 集群名\n  tx-service-group: ${spring.application.name}-group\n  config:\n    type: nacos\n    nacos:\n      server-addr: ${spring.cloud.nacos.server-addr}\n      group: ${spring.cloud.nacos.config.group}\n      namespace: ${spring.profiles.active}\n      data-id: seata-server.properties\n  registry:\n    type: nacos\n    nacos:\n      application: fls-cloud-seata-server\n      server-addr: ${spring.cloud.nacos.server-addr}\n      group: ${spring.cloud.nacos.discovery.group}\n      namespace: ${spring.profiles.active}\n', 'd654bb6d35554d0d4b60791358724bee', '2022-01-09 15:18:55', '2022-09-03 09:30:19', 'nacos', '0:0:0:0:0:0:0:1', '', 'dev', '通用配置基础配置', '', '', 'yaml', '', '');
INSERT INTO `config_info` VALUES (2, 'datasource.yml', 'DEFAULT_GROUP', 'datasource:\n  upms-master:\n    # rewriteBatchedStatements=true 批处理优化 大幅提升批量插入更新删除性能\n    url: **********************************************************************************************************************************************************************************    username: root\n    password: root\n  job:\n    url: **************************************************************************************************************************************************************************************    username: root\n    password: root\n\nspring:\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    dynamic:\n      # 性能分析插件\n      p6spy: false\n      # 开启seata代理，开启后默认每个数据源都代理，如果某个不需要代理可单独关闭\n      seata: true\n      # 严格模式 匹配不到数据源则报错\n      strict: true\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 30\n        maxWait: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n\n# seata配置\nseata:\n  # 关闭自动代理\n  enable-auto-data-source-proxy: false\n', 'fa1d0267893d9ae8d180072ca52a0920', '2022-01-09 15:19:07', '2022-11-21 00:31:49', 'nacos', '0:0:0:0:0:0:0:1', '', 'dev', '数据源配置', '', '', 'yaml', '', '');
INSERT INTO `config_info` VALUES (3, 'fls-cloud-gateway.yml', 'DEFAULT_GROUP', '# 安全配置\nsecurity:\n  # 验证码\n  captcha:\n    # 是否开启验证码\n    enabled: false\n    # 验证码类型 math 数组计算 char 字符验证\n    type: MATH\n    # line 线段干扰 circle 圆圈干扰 shear 扭曲干扰\n    category: CIRCLE\n    # 数字验证码位数\n    numberLength: 1\n    # 字符验证码长度\n    charLength: 4\n  # 防止XSS攻击\n  xss:\n    enabled: true\n    excludeUrls:\n      - /system/notice\n  # 不校验白名单\n  ignore:\n    whites:\n      - /code\n      - /auth/token/login\n      - /auth/check-token\n      - /auth/token/smsLogin\n      - /auth/token/xcxLogin\n      - /auth/sso/**\n      - /resource/sms/code\n      - /*/v3/api-docs\n      - /csrf\n\nspring:\n  cloud:\n    # 网关配置\n    gateway:\n      # 打印请求日志\n      requestLog: true\n      discovery:\n        locator:\n          lowerCaseServiceId: true\n          enabled: true\n      routes:\n        # 认证中心\n        - id: fls-cloud-auth\n          uri: lb://fls-cloud-auth\n          predicates:\n            - Path=/auth/**\n          filters:\n            # 验证码处理\n            - ValidateCodeFilter\n            - StripPrefix=1\n        # 代码生成\n        - id: fls-cloud-gen\n          uri: lb://fls-cloud-gen\n          predicates:\n            - Path=/code/**\n          filters:\n            - StripPrefix=1\n        # 系统模块\n        - id: fls-cloud-upms\n          uri: lb://fls-cloud-upms\n          predicates:\n            - Path=/system/**\n          filters:\n            - StripPrefix=1\n        # 资源服务\n        - id: fls-cloud-resource\n          uri: lb://fls-cloud-resource\n          predicates:\n            - Path=/resource/**\n          filters:\n            - StripPrefix=1\n        # 测试例子\n        - id: fls-cloud-demo\n          uri: lb://fls-cloud-demo\n          predicates:\n            - Path=/demo/**\n          filters:\n            - StripPrefix=1\n        # MQ\n        - id: fls-cloud-stream-mq\n          uri: lb://fls-cloud-stream-mq\n          predicates:\n            - Path=/stream-mq/**\n          filters:\n            - StripPrefix=1\n\n    # sentinel 配置\n    sentinel:\n      # nacos配置持久化\n      datasource:\n        ds1:\n          nacos:\n            server-addr: ${spring.cloud.nacos.server-addr}\n            dataId: sentinel-${spring.application.name}.json\n            groupId: ${spring.cloud.nacos.config.group}\n            namespace: ${spring.profiles.active}\n            data-type: json\n            rule-type: flow\n', 'd55bd70da9f13d0fb6497d88b8f959e4', '2022-01-09 15:19:43', '2022-09-02 08:03:09', 'nacos', '0:0:0:0:0:0:0:1', '', 'dev', '网关模块', '', '', 'yaml', '', '');
INSERT INTO `config_info` VALUES (4, 'fls-cloud-auth.yml', 'DEFAULT_GROUP', '# 用户配置\nuser:\n  password:\n    # 密码最大错误次数\n    maxRetryCount: 5\n    # 密码锁定时间（默认10分钟）\n    lockTime: 10\n', '1fd08de287662e81f0ced8808d7b19e6', '2022-01-09 15:19:43', '2022-08-26 03:31:20', 'nacos', '0:0:0:0:0:0:0:1', '', 'dev', '认证中心', '', '', 'yaml', '', '');
INSERT INTO `config_info` VALUES (5, 'fls-cloud-monitor.yml', 'DEFAULT_GROUP', '# 监控中心配置\nspring:\n  security:\n    user:\n      name: fls-cloud\n      password: fls@2022\n  boot:\n    admin:\n      ui:\n        title: 服务监控中心\n      discovery:\n        # seata 不具有健康检查的能力 防止报错排除掉\n        ignored-services: fls-cloud-seata-server\n', '165d2d01a6cd87551499738f8077b651', '2022-01-09 15:20:18', '2022-08-26 03:31:59', 'nacos', '0:0:0:0:0:0:0:1', '', 'dev', '监控中心', '', '', 'yaml', '', '');
INSERT INTO `config_info` VALUES (6, 'fls-cloud-upms.yml', 'DEFAULT_GROUP', 'spring:\n  datasource:\n    dynamic:\n      # 设置默认的数据源或者数据源组,默认值即为 master\n      primary: master\n      datasource:\n        # 主库数据源\n        master:\n          driver-class-name: com.mysql.cj.jdbc.Driver\n          url: ${datasource.upms-master.url}\n          username: ${datasource.upms-master.username}\n          password: ${datasource.upms-master.password}\n        # 从库数据源\n        # slave:\n        #  driver-class-name:\n        #  url:\n        #  username:\n        #  password:\n', '14ee8ffac8f1ba2f06f0d74006daf669', '2022-01-09 15:20:18', '2022-08-29 10:04:29', 'nacos', '0:0:0:0:0:0:0:1', '', 'dev', '系统模块', '', '', 'yaml', '', '');
INSERT INTO `config_info` VALUES (8, 'fls-cloud-job.yml', 'DEFAULT_GROUP', 'spring:\n  datasource:\n    dynamic:\n      # 设置默认的数据源或者数据源组,默认值即为 master\n      primary: master\n      datasource:\n        # 主库数据源\n        master:\n          driver-class-name: com.mysql.cj.jdbc.Driver\n          url: ${datasource.job.url}\n          username: ${datasource.job.username}\n          password: ${datasource.job.password}\n\nxxl:\n  job:\n    # 执行器开关\n    enabled: true\n    # 调度中心地址：如调度中心集群部署存在多个地址则用逗号分隔。\n    admin-addresses: http://localhost:9900\n    # 执行器通讯TOKEN：非空时启用\n    access-token: xxl-job\n    # 执行器配置\n    executor:\n      # 执行器AppName：执行器心跳注册分组依据；为空则关闭自动注册\n      appname: ${spring.application.name}-executor\n      # 执行器端口号 执行器从19901开始往后写\n      port: 9901\n      # 执行器注册：默认IP:PORT\n      address:\n      # 执行器IP：默认自动获取IP\n      ip:\n      # 执行器运行日志文件存储磁盘路径\n      logpath: ./logs/${spring.application.name}/xxl-job\n      # 执行器日志文件保存天数：大于3生效\n      logretentiondays: 30\n', '3c951a136dbc9989b9184f05e08b5961', '2022-01-09 15:20:18', '2022-08-26 03:35:50', 'nacos', '0:0:0:0:0:0:0:1', '', 'dev', '定时任务', '', '', 'yaml', '', '');
INSERT INTO `config_info` VALUES (9, 'fls-cloud-resource.yml', 'DEFAULT_GROUP', 'spring:\n  datasource:\n    dynamic:\n      # 设置默认的数据源或者数据源组,默认值即为 master\n      primary: master\n      datasource:\n        # 主库数据源\n        master:\n          driver-class-name: com.mysql.cj.jdbc.Driver\n          url: ${datasource.upms-master.url}\n          username: ${datasource.upms-master.username}\n          password: ${datasource.upms-master.password}\n        # 从库数据源\n        # slave:\n        #  driver-class-name:\n        #  url:\n        #  username:\n        #  password:\n\nmail:\n  enabled: false\n  host: smtp.163.com\n  port: 465\n  # 是否需要用户名密码验证\n  auth: true\n  # 发送方，遵循RFC-822标准\n  from: <EMAIL>\n  # 用户名（注意：如果使用foxmail邮箱，此处user为qq号）\n  user: <EMAIL>\n  # 密码（注意，某些邮箱需要为SMTP服务单独设置密码，详情查看相关帮助）\n  pass: xxxxxxxxxx\n  # 使用 STARTTLS安全连接，STARTTLS是对纯文本通信协议的扩展。\n  starttlsEnable: true\n  # 使用SSL安全连接\n  sslEnable: true\n  # SMTP超时时长，单位毫秒，缺省值不超时\n  timeout: 0\n  # Socket连接超时值，单位毫秒，缺省值不超时\n  connectionTimeout: 0\n\nsms:\n  enabled: false\n  # 阿里云 dysmsapi.aliyuncs.com\n  # 腾讯云 sms.tencentcloudapi.com\n  endpoint: \"dysmsapi.aliyuncs.com\"\n  accessKeyId: xxxxxxx\n  accessKeySecret: xxxxxx\n  signName: 测试\n  # 腾讯专用\n  sdkAppId:\n', '63ef9211d48193bc399cd58e0723b905', '2022-01-09 15:20:35', '2022-08-29 09:45:42', 'nacos', '0:0:0:0:0:0:0:1', '', 'dev', '文件服务', '', '', 'yaml', '', '');
INSERT INTO `config_info` VALUES (10, 'sentinel-fls-cloud-gateway.json', 'DEFAULT_GROUP', '[\n  {\n    \"resource\": \"fls-cloud-auth\",\n    \"count\": 500,\n    \"grade\": 1,\n    \"limitApp\": \"default\",\n    \"strategy\": 0,\n    \"controlBehavior\": 0\n  },\n  {\n    \"resource\": \"fls-cloud-upms\",\n    \"count\": 1000,\n    \"grade\": 1,\n    \"limitApp\": \"default\",\n    \"strategy\": 0,\n    \"controlBehavior\": 0\n  },\n  {\n    \"resource\": \"fls-cloud-resource\",\n    \"count\": 500,\n    \"grade\": 1,\n    \"limitApp\": \"default\",\n    \"strategy\": 0,\n    \"controlBehavior\": 0\n  }\n]\n', '12db9985a176184729741b204cfcd5ed', '2022-01-09 15:21:02', '2022-08-29 09:40:36', 'nacos', '0:0:0:0:0:0:0:1', '', 'dev', '限流策略', '', '', 'json', '', '');
INSERT INTO `config_info` VALUES (11, 'fls-cloud-xxl-job-admin.yml', 'DEFAULT_GROUP', '# server 配置\nspring:\n  datasource:\n    type: com.zaxxer.hikari.HikariDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: ${datasource.job.url}\n    username: ${datasource.job.username}\n    password: ${datasource.job.password}\n    hikari:\n      auto-commit: true\n      connection-test-query: SELECT 1\n      connection-timeout: 10000\n      idle-timeout: 30000\n      max-lifetime: 900000\n      maximum-pool-size: 30\n      minimum-idle: 10\n      pool-name: HikariCP\n      validation-timeout: 1000\n  mail:\n    from: <EMAIL>\n    host: smtp.qq.com\n    username: <EMAIL>\n    password: xxx\n    port: 25\n    properties:\n      mail:\n        smtp:\n          auth: true\n          socketFactory:\n            class: javax.net.ssl.SSLSocketFactory\n          starttls:\n            enable: true\n            required: true\n\n# mybatis 配置\nmybatis:\n  mapper-locations: classpath:/mybatis-mapper/*Mapper.xml\n\n# Actuator 监控端点的配置项\nmanagement:\n  health:\n    mail:\n      enabled: false\n\n# xxljob系统配置\nxxl:\n  job:\n    # 鉴权token\n    accessToken: xxl-job\n    # 国际化\n    i18n: zh_CN\n    # 日志清理\n    logretentiondays: 30\n    triggerpool:\n      fast:\n        max: 200\n      slow:\n        max: 100\n', '71f881b9b27d4be062971515f6fbbeb3', '2022-01-09 15:21:02', '2022-08-26 03:37:30', 'nacos', '0:0:0:0:0:0:0:1', '', 'dev', '定时任务控制台', '', '', 'yaml', '', '');
INSERT INTO `config_info` VALUES (12, 'seata-server.properties', 'DEFAULT_GROUP', 'service.vgroupMapping.fls-cloud-auth-group=default\nservice.vgroupMapping.fls-cloud-upms-group=default\nservice.vgroupMapping.fls-cloud-resource-group=default\nservice.vgroupMapping.fls-cloud-gen-group=default\nservice.vgroupMapping.fls-cloud-job-group=default\n\nservice.enableDegrade=false\nservice.disableGlobalTransaction=false\n\n#Transaction storage configuration, only for the server. The file, DB, and redis configuration values are optional.\nstore.mode=db\nstore.lock.mode=db\nstore.session.mode=db\n#Used for password encryption\nstore.publicKey=\n\n#These configurations are required if the `store mode` is `db`. If `store.mode,store.lock.mode,store.session.mode` are not equal to `db`, you can remove the configuration block.\nstore.db.datasource=druid\nstore.db.dbType=mysql\nstore.db.driverClassName=com.mysql.cj.jdbc.Driver\nstore.db.url=*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************** rule configuration, only for the server\nserver.recovery.committingRetryPeriod=1000\nserver.recovery.asynCommittingRetryPeriod=1000\nserver.recovery.rollbackingRetryPeriod=1000\nserver.recovery.timeoutRetryPeriod=1000\nserver.maxCommitRetryTimeout=-1\nserver.maxRollbackRetryTimeout=-1\nserver.rollbackRetryTimeoutUnlockEnable=false\nserver.distributedLockExpireTime=10000\nserver.xaerNotaRetryTimeout=60000\nserver.session.branchAsyncQueueSize=5000\nserver.session.enableBranchAsyncRemove=false\n\n#Transaction rule configuration, only for the client\nclient.rm.asyncCommitBufferLimit=10000\nclient.rm.lock.retryInterval=10\nclient.rm.lock.retryTimes=30\nclient.rm.lock.retryPolicyBranchRollbackOnConflict=true\nclient.rm.reportRetryCount=5\nclient.rm.tableMetaCheckEnable=true\nclient.rm.tableMetaCheckerInterval=60000\nclient.rm.sqlParserType=druid\nclient.rm.reportSuccessEnable=false\nclient.rm.sagaBranchRegisterEnable=false\nclient.rm.sagaJsonParser=fastjson\nclient.rm.tccActionInterceptorOrder=-2147482648\nclient.tm.commitRetryCount=5\nclient.tm.rollbackRetryCount=5\nclient.tm.defaultGlobalTransactionTimeout=60000\nclient.tm.degradeCheck=false\nclient.tm.degradeCheckAllowTimes=10\nclient.tm.degradeCheckPeriod=2000\nclient.tm.interceptorOrder=-2147482648\nclient.undo.dataValidation=true\nclient.undo.logSerialization=jackson\nclient.undo.onlyCareUpdateColumns=true\nserver.undo.logSaveDays=7\nserver.undo.logDeletePeriod=86400000\nclient.undo.logTable=undo_log\nclient.undo.compress.enable=true\nclient.undo.compress.type=zip\nclient.undo.compress.threshold=64k\n\n#For TCC transaction mode\ntcc.fence.logTableName=tcc_fence_log\ntcc.fence.cleanPeriod=1h\n\n#Log rule configuration, for client and server\nlog.exceptionRate=100\n\n#Metrics configuration, only for the server\nmetrics.enabled=false\nmetrics.registryType=compact\nmetrics.exporterList=prometheus\nmetrics.exporterPrometheusPort=9898\n\n#For details about configuration items, see https://seata.io/zh-cn/docs/user/configurations.html\n#Transport configuration, for client and server\ntransport.type=TCP\ntransport.server=NIO\ntransport.heartbeat=true\ntransport.enableTmClientBatchSendRequest=false\ntransport.enableRmClientBatchSendRequest=true\ntransport.enableTcServerBatchSendResponse=false\ntransport.rpcRmRequestTimeout=30000\ntransport.rpcTmRequestTimeout=30000\ntransport.rpcTcRequestTimeout=30000\ntransport.threadFactory.bossThreadPrefix=NettyBoss\ntransport.threadFactory.workerThreadPrefix=NettyServerNIOWorker\ntransport.threadFactory.serverExecutorThreadPrefix=NettyServerBizHandler\ntransport.threadFactory.shareBossWorker=false\ntransport.threadFactory.clientSelectorThreadPrefix=NettyClientSelector\ntransport.threadFactory.clientSelectorThreadSize=1\ntransport.threadFactory.clientWorkerThreadPrefix=NettyClientWorkerThread\ntransport.threadFactory.bossThreadSize=1\ntransport.threadFactory.workerThreadSize=default\ntransport.shutdown.wait=3\ntransport.serialization=seata\ntransport.compressor=none\n', '505b318c9130995fa99538f94c45bb48', '2022-01-09 15:21:02', '2022-09-01 01:20:51', 'nacos', '0:0:0:0:0:0:0:1', '', 'dev', 'seata配置文件', '', '', 'properties', '', '');
INSERT INTO `config_info` VALUES (13, 'fls-cloud-sentinel-dashboard.yml', 'DEFAULT_GROUP', 'spring:\n  mvc:\n    pathmatch:\n      # 新路由方式\n      matching-strategy: ANT_PATH_MATCHER\n\nserver:\n  servlet:\n    encoding:\n      force: true\n      charset: UTF-8\n      enabled: true\n    session:\n      cookie:\n        name: sentinel_dashboard_cookie\n\nlogging:\n  level:\n    org.springframework.web: INFO\n\nauth:\n  enabled: true\n  filter:\n    exclude-urls: /,/auth/login,/auth/logout,/registry/machine,/version,/actuator,/actuator/**\n    exclude-url-suffixes: htm,html,js,css,map,ico,ttf,woff,png\n  username: sentinel\n  password: sentinel\n', 'c22832e431e62479f733f920d61c080b', '2022-01-09 15:21:02', '2022-08-27 07:38:26', 'nacos', '0:0:0:0:0:0:0:1', '', 'dev', 'sentinel控制台配置文件', '', '', 'yaml', '', '');
INSERT INTO `config_info` VALUES (101, 'application-common.yml', 'DEFAULT_GROUP', '# config', '2944a25cb97926efcaa43b3ad7a64cf0', '2022-01-09 15:23:00', '2022-01-09 15:23:00', NULL, '0:0:0:0:0:0:0:1', '', 'prod', '通用配置基础配置', NULL, NULL, 'yaml', NULL, '');
INSERT INTO `config_info` VALUES (102, 'datasource.yml', 'DEFAULT_GROUP', '# config', '2944a25cb97926efcaa43b3ad7a64cf0', '2022-01-09 15:23:00', '2022-01-09 15:23:00', NULL, '0:0:0:0:0:0:0:1', '', 'prod', '数据源配置', NULL, NULL, 'yaml', NULL, '');
INSERT INTO `config_info` VALUES (103, 'fls-cloud-gateway.yml', 'DEFAULT_GROUP', '# config', '2944a25cb97926efcaa43b3ad7a64cf0', '2022-01-09 15:23:00', '2022-01-09 15:23:00', NULL, '0:0:0:0:0:0:0:1', '', 'prod', '网关模块', NULL, NULL, 'yaml', NULL, '');
INSERT INTO `config_info` VALUES (104, 'fls-cloud-auth.yml', 'DEFAULT_GROUP', '# config', '2944a25cb97926efcaa43b3ad7a64cf0', '2022-01-09 15:23:00', '2022-01-09 15:23:00', NULL, '0:0:0:0:0:0:0:1', '', 'prod', '认证中心', NULL, NULL, 'yaml', NULL, '');
INSERT INTO `config_info` VALUES (105, 'fls-cloud-monitor.yml', 'DEFAULT_GROUP', '# config', '2944a25cb97926efcaa43b3ad7a64cf0', '2022-01-09 15:23:00', '2022-01-09 15:23:00', NULL, '0:0:0:0:0:0:0:1', '', 'prod', '监控中心', NULL, NULL, 'yaml', NULL, '');
INSERT INTO `config_info` VALUES (106, 'fls-cloud-upms.yml', 'DEFAULT_GROUP', '# config', '2944a25cb97926efcaa43b3ad7a64cf0', '2022-01-09 15:23:00', '2022-01-09 15:23:00', NULL, '0:0:0:0:0:0:0:1', '', 'prod', '系统模块', NULL, NULL, 'yaml', NULL, '');
INSERT INTO `config_info` VALUES (107, 'fls-cloud-gen.yml', 'DEFAULT_GROUP', '# config', '2944a25cb97926efcaa43b3ad7a64cf0', '2022-01-09 15:23:00', '2022-01-09 15:23:00', NULL, '0:0:0:0:0:0:0:1', '', 'prod', '代码生成', NULL, NULL, 'yaml', NULL, '');
INSERT INTO `config_info` VALUES (108, 'fls-cloud-job.yml', 'DEFAULT_GROUP', '# config', '2944a25cb97926efcaa43b3ad7a64cf0', '2022-01-09 15:23:00', '2022-01-09 15:23:00', NULL, '0:0:0:0:0:0:0:1', '', 'prod', '定时任务', NULL, NULL, 'yaml', NULL, '');
INSERT INTO `config_info` VALUES (109, 'fls-cloud-resource.yml', 'DEFAULT_GROUP', '# config', '2944a25cb97926efcaa43b3ad7a64cf0', '2022-01-09 15:23:00', '2022-01-09 15:23:00', NULL, '0:0:0:0:0:0:0:1', '', 'prod', '文件服务', NULL, NULL, 'yaml', NULL, '');
INSERT INTO `config_info` VALUES (110, 'sentinel-fls-cloud-gateway.json', 'DEFAULT_GROUP', '# config', '2944a25cb97926efcaa43b3ad7a64cf0', '2022-01-09 15:23:00', '2022-01-09 15:23:00', NULL, '0:0:0:0:0:0:0:1', '', 'prod', '限流策略', NULL, NULL, 'json', NULL, '');
INSERT INTO `config_info` VALUES (111, 'fls-cloud-xxl-job-admin.yml', 'DEFAULT_GROUP', '# config', '2944a25cb97926efcaa43b3ad7a64cf0', '2022-01-09 15:21:02', '2022-01-09 15:21:02', NULL, '0:0:0:0:0:0:0:1', '', 'prod', '定时任务控制台', NULL, NULL, 'yaml', NULL, '');
INSERT INTO `config_info` VALUES (112, 'seata-server.properties', 'DEFAULT_GROUP', '# config', '2944a25cb97926efcaa43b3ad7a64cf0', '2022-01-09 15:21:02', '2022-01-09 15:21:02', NULL, '0:0:0:0:0:0:0:1', '', 'prod', 'seata配置文件', NULL, NULL, 'properties', NULL, '');
INSERT INTO `config_info` VALUES (113, 'fls-cloud-sentinel-dashboard.yml', 'DEFAULT_GROUP', '# config', '2944a25cb97926efcaa43b3ad7a64cf0', '2022-01-09 15:21:02', '2022-01-09 15:21:02', NULL, '0:0:0:0:0:0:0:1', '', 'prod', 'sentinel控制台配置文件', NULL, NULL, 'yaml', NULL, '');



SET FOREIGN_KEY_CHECKS = 1;
