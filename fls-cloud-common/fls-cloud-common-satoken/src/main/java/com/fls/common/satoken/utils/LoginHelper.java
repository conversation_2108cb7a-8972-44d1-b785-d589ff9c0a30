package com.fls.common.satoken.utils;

import cn.dev33.satoken.SaManager;
import cn.dev33.satoken.context.SaHolder;
import cn.dev33.satoken.stp.SaLoginConfig;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.fls.common.core.constant.UserConstants;
import com.fls.common.core.enums.DeviceType;
import com.fls.common.core.enums.UserType;
import com.fls.common.core.exception.UtilException;
import com.fls.common.core.utils.StringUtils;
import com.fls.upms.api.model.LoginUser;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * 登录鉴权助手
 *
 * user_type 为 用户类型 同一个用户表 可以有多种用户类型 例如 pc,app
 * deivce 为 设备类型 同一个用户类型 可以有 多种设备类型 例如 web,ios
 * 可以组成 用户类型与设备类型多对多的 权限灵活控制
 *
 *
 * <AUTHOR>
 * @date 2022/04/10
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class LoginHelper {

    public static final String JOIN_CODE = ":";
    public static final String LOGIN_USER_KEY = "loginUser";

    /**
     * 登录系统
     *
     * @param loginUser 登录用户信息
     */
    public static void login(LoginUser loginUser) {
        SaHolder.getStorage().set(LOGIN_USER_KEY, loginUser);
        StpUtil.login(loginUser.getLoginId(),SaLoginConfig
            .setExtra("userId", loginUser.getIdUser())
            .setExtra("account",loginUser.getUsername())
            .setExtra("uuid", IdUtil.fastUUID())
            .setExtra("sub", loginUser.getIdUser())
            .setExtra("iat", loginUser.getLoginTime().getTime())
            .setExtra("exp", loginUser.getExpireTime().getTime()));
        setLoginUser(loginUser);
    }

    /**
     * 登录系统 基于 设备类型
     * 针对相同用户体系不同设备
     *
     * @param loginUser 登录用户信息
     */
    public static void loginByDevice(LoginUser loginUser, DeviceType deviceType) {
        SaHolder.getStorage().set(LOGIN_USER_KEY, loginUser);
        StpUtil.login(loginUser.getLoginId(), SaLoginConfig
            .setDevice(deviceType.getDevice())
            .setExtra("userId", loginUser.getIdUser())
            .setExtra("account",loginUser.getUsername())
            .setExtra("uuid", IdUtil.fastUUID())
            .setExtra("sub", loginUser.getIdUser())
            .setExtra("iat", loginUser.getLoginTime().getTime())
            .setExtra("exp", loginUser.getExpireTime()));
        setLoginUser(loginUser);
    }

    /**
     * 设置用户数据(多级缓存)
     */
    public static void setLoginUser(LoginUser loginUser) {
        StpUtil.getTokenSession().set(LOGIN_USER_KEY, loginUser);
        StpUtil.getSession().set(LOGIN_USER_KEY, loginUser);
    }

    /**
     * 获取用户(多级缓存)
     */
    public static LoginUser getLoginUser() {
        LoginUser loginUser = (LoginUser) SaHolder.getStorage().get(LOGIN_USER_KEY);
        if (loginUser != null) {
            return loginUser;
        }
        loginUser = (LoginUser) StpUtil.getTokenSession().get(LOGIN_USER_KEY);
        SaHolder.getStorage().set(LOGIN_USER_KEY, loginUser);
        return loginUser;
    }

    public static LoginUser getLoginUser(String token) {
        Object loginUser =  StpUtil.getTokenSessionByToken(token).get(LOGIN_USER_KEY);
        if(ObjectUtil.isNotEmpty(loginUser)){
            return (LoginUser) loginUser;
        }
        return null;
    }

    /**
     * 获取用户id
     */
    public static String getUserId() {
        LoginUser loginUser = getLoginUser();
        if (ObjectUtil.isNull(loginUser)) {
            String loginId = StpUtil.getLoginIdAsString();
            String userId = null;
            for (UserType value : UserType.values()) {
                if (StringUtils.contains(loginId, value.getUserType())) {
                    String[] strs = StringUtils.split(loginId, JOIN_CODE);
                    // 用户id在总是在最后
                    userId = strs[strs.length - 1];
                }
            }
            if (StringUtils.isBlank(userId)) {
                throw new UtilException("登录用户: LoginId异常 => " + loginId);
            }
            return userId;
        }
        return loginUser.getIdUser();
    }



    /**
     * 获取用户账户
     */
    public static String getUsername() {
        return getLoginUser().getUsername();
    }

    /**
     * 获取用户类型
     */
    public static UserType getUserType() {
        String loginId = StpUtil.getLoginIdAsString();
        return UserType.getUserType(loginId);
    }



}
