<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.fls</groupId>
        <artifactId>fls-cloud-common</artifactId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>fls-cloud-common-satoken</artifactId>

    <dependencies>

        <dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-core</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-jwt</artifactId>
            <version>${satoken.version}</version>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-jwt</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fls</groupId>
            <artifactId>fls-cloud-dubbo-api-upms</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fls</groupId>
            <artifactId>fls-cloud-common-redis</artifactId>
        </dependency>

    </dependencies>

</project>
