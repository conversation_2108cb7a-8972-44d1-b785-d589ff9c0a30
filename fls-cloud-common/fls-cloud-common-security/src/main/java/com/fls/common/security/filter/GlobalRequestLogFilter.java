package com.fls.common.security.filter;

import cn.hutool.core.util.ObjectUtil;
import com.fls.common.satoken.utils.LoginHelper;
import com.fls.common.security.wrapper.RequestWrapper;
import com.fls.upms.api.model.LoginUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;

/**
 * 请求日志打印
 *
 * <AUTHOR>
 * @date 2022/08/27
 **/
@Slf4j
@Component
public class GlobalRequestLogFilter extends OncePerRequestFilter {

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, Fi<PERSON><PERSON>hain filterChain) throws ServletException, IOException {
        String url = request.getRequestURI();
        //健康检查不打印
        if(url.contains("actuator")){
            filterChain.doFilter(request, response);
            return;
        }

        RequestWrapper requestWrapper = null;
        StringBuilder sb = new StringBuilder();
        if (request instanceof HttpServletRequest) {
            requestWrapper = new RequestWrapper(request);
            BufferedReader bufferedReader = requestWrapper.getReader();
            String line;
            while ((line = bufferedReader.readLine()) != null) {
                sb.append(line);
            }
        }
        String id = null, username = null;
        LoginUser loginUser = null;
        try {
            loginUser = LoginHelper.getLoginUser();
        } catch (Exception e) {

        }
        if (ObjectUtil.isNotEmpty(loginUser)) {
            id = loginUser.getIdUser();
            username = loginUser.getUsername();
        }
        log.info("==============》url = {} , method = {} , userId = {}  , username = {}  , params = {}  , requestBody = {}  "
            , url, request.getMethod(), id, username, request.getQueryString(), sb.toString());

        //继续执行链条
        filterChain.doFilter(requestWrapper, response);
    }
}
