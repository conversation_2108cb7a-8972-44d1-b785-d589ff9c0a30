package com.fls.common.security.config;

import cn.dev33.satoken.filter.SaServletFilter;
import cn.dev33.satoken.id.SaIdUtil;
import cn.dev33.satoken.interceptor.SaAnnotationInterceptor;
import cn.dev33.satoken.util.SaResult;
import com.fls.common.core.constant.HttpStatus;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * 权限安全配置，只允许内部调用（从网关转发）
 *
 * <AUTHOR>
 * @date 2022/04/10
 */
@AutoConfiguration
public class SecurityConfiguration implements WebMvcConfigurer {


    /**
     * 注册sa-token的拦截器
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {

        // 非法请求拦截器
        registry.addInterceptor(new SaAnnotationInterceptor()).addPathPatterns("/**");

    }

    /**
     * 校验是否从网关转发
     */
    public SaServletFilter getSaServletFilter() {
        return new SaServletFilter()
            .addInclude("/**")
            .addExclude("/actuator/**")
            .setAuth(obj ->{
                SaIdUtil.checkCurrentRequestToken();})
            .setError(e -> SaResult.error("非法请求，请从网关进入").setCode(HttpStatus.UNAUTHORIZED));
    }

}
