package com.fls.common.security.handler;

import cn.dev33.satoken.exception.IdTokenInvalidException;
import cn.dev33.satoken.exception.NotLoginException;
import cn.dev33.satoken.exception.NotPermissionException;
import cn.dev33.satoken.exception.NotRoleException;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.fls.common.core.constant.HttpStatus;
import com.fls.common.core.constant.SymbolConstant;
import com.fls.common.core.domain.ResponseData;
import com.fls.common.core.exception.ServiceException;
import com.fls.common.core.exception.user.UserException;
import com.fls.common.core.utils.HttpServletUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.ObjectError;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.NoHandlerFoundException;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 全局异常处理器
 *
 * <AUTHOR>
 * @date 2022/04/10
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 权限码异常
     */
    @ExceptionHandler(NotPermissionException.class)
    public ResponseData handleNotPermissionException(NotPermissionException e, HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        log.error("请求地址'{}',权限码校验失败'{}'", requestURI, e.getMessage());
        return ResponseData.fail(HttpStatus.FORBIDDEN, "没有访问权限，请联系管理员授权");
    }

    /**
     * 角色权限异常
     */
    @ExceptionHandler(NotRoleException.class)
    public ResponseData handleNotRoleException(NotRoleException e, HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        log.error("请求地址'{}',角色权限校验失败'{}'", requestURI, e.getMessage());
        return ResponseData.fail(HttpStatus.FORBIDDEN, "没有访问权限，请联系管理员授权");
    }

    /**
     * 认证失败
     */
    @ExceptionHandler(NotLoginException.class)
    public ResponseData handleNotLoginException(NotLoginException e, HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        log.error("请求地址'{}',认证失败'{}',无法访问系统资源", requestURI, e.getMessage());
        return ResponseData.fail(HttpStatus.UNAUTHORIZED, "认证失败，无法访问系统资源");
    }

    /**
     * 无效认证
     */
    @ExceptionHandler(IdTokenInvalidException.class)
    public ResponseData handleIdTokenInvalidException(IdTokenInvalidException e, HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        log.error("请求地址'{}',内网认证失败'{}',无法访问系统资源", requestURI, e.getMessage());
        return ResponseData.fail(HttpStatus.UNAUTHORIZED, "认证失败，无法访问系统资源");
    }

    /**
     * 请求方式不支持
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public ResponseData handleHttpRequestMethodNotSupported(HttpRequestMethodNotSupportedException e,
                                                                  HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        log.error("请求地址'{}',不支持'{}'请求", requestURI, e.getMethod());
        return ResponseData.fail("请求方式不支持");
    }


    /**
     * 拦截资源找不到的运行时异常
     *
     */
    @ExceptionHandler(NoHandlerFoundException.class)
    public ResponseData handleNoHandlerFoundException(NoHandlerFoundException e) {
        log.error("资源不存在异常，具体信息为：{}", e.getMessage() +"，请求地址为:" + HttpServletUtil.getRequest().getRequestURI());
        return ResponseData.fail("资源不存在异常");
    }

    /**
     * 拦截不支持媒体类型异常
     *
     */
    @ExceptionHandler(HttpMediaTypeNotSupportedException.class)
    public ResponseData httpMediaTypeNotSupport(HttpMediaTypeNotSupportedException e) {
        log.error("参数格式传递异常，具体信息为：{}",  e.getMessage());
        return ResponseData.fail("请求方法不支持该请求媒体类型");
    }

    /**
     * 拦截参数格式传递异常
     *
     */
    @ExceptionHandler(HttpMessageNotReadableException.class)
    public ResponseData httpMessageNotReadable(HttpMessageNotReadableException e) {
        log.error("参数格式传递异常，具体信息为：{}",  e.getMessage());
        return ResponseData.fail("参数格式传递异常,请求参数为空或格式不对");
    }

    /**
     * 请求参数缺失异常
     *
     */
    @ExceptionHandler(MissingServletRequestParameterException.class)
    public ResponseData missParamException(MissingServletRequestParameterException e) {
        log.error("请求参数异常，具体信息为：{}",  e.getMessage());
        String parameterType = e.getParameterType();
        String parameterName = e.getParameterName();
        String message = StrUtil.format(" 缺少请求的参数{}，类型为{}", parameterName, parameterType);
        return ResponseData.fail(message);
    }


    /**
     * 业务异常
     */
    @ExceptionHandler(ServiceException.class)
    public ResponseData handleServiceException(ServiceException e) {
        return  ResponseData.fail(e.getMessage());
    }

    /**
     * 用户异常
     */
    @ExceptionHandler(UserException.class)
    public ResponseData handleUserException(UserException e) {
        return  ResponseData.fail(e.getMessage());
    }


    /**
     * 自定义验证异常
     */
    @ExceptionHandler(BindException.class)
    public ResponseData handleBindException(BindException e) {
        log.error(e.getMessage(), e);
        String message = e.getAllErrors().get(0).getDefaultMessage();
        return ResponseData.fail(message);
    }

    /**
     * 自定义验证异常
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseData handleMethodArgumentNotValidException(MethodArgumentNotValidException e) {
        String argNotValidMessage = getArgNotValidMessage(e.getBindingResult());
        log.error(">>> 参数校验错误异常，具体信息为：{}", argNotValidMessage);
        return ResponseData.fail(argNotValidMessage);
    }



    /**
     * 系统异常
     */
    @ExceptionHandler(Exception.class)
    public ResponseData handleException(Exception e, HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        log.error("请求地址'{}',发生系统异常.", requestURI, e);
        return ResponseData.fail("系统异常，请联系管理员");
    }

    /**
     * 获取请求参数不正确的提示信息
     * <p>
     * 多个信息，拼接成用逗号分隔的形式
     *
     */
    private String getArgNotValidMessage(BindingResult bindingResult) {
        if (bindingResult == null) {
            return "";
        }
        StringBuilder stringBuilder = new StringBuilder();

        //多个错误用逗号分隔
        List<ObjectError> allErrorInfos = bindingResult.getAllErrors();
        for (ObjectError error : allErrorInfos) {
            stringBuilder.append(SymbolConstant.COMMA).append(error.getDefaultMessage());
        }

        //最终把首部的逗号去掉
        return CharSequenceUtil.removePrefix(stringBuilder.toString(), SymbolConstant.COMMA);
    }

}
