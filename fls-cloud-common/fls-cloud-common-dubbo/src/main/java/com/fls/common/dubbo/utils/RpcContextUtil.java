package com.fls.common.dubbo.utils;

import com.fls.common.core.constant.CommonConstants;
import com.fls.common.core.web.entity.HttpRequestData;
import org.apache.dubbo.rpc.RpcContext;

/**
 * rpc工具类
 *
 * <AUTHOR>
 * @date 2022/09/01
 **/
public class RpcContextUtil {
    public static HttpRequestData getRpcHttpRequestData(){
        try {
            return (HttpRequestData) RpcContext.getServiceContext().getObjectAttachment(CommonConstants.RPC_HTTP_REQUEST_DATA);
        }catch (Exception e){
            return null;
        }
    }
}
