package com.fls.common.dubbo.filter;

import cn.hutool.core.util.ObjectUtil;
import com.fls.common.core.utils.HttpServletUtil;
import com.fls.common.core.utils.IpAddressUtil;
import com.fls.common.core.utils.JsonUtils;
import com.fls.common.core.utils.UaUtil;
import com.fls.common.core.web.entity.HttpRequestData;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.constants.CommonConstants;
import org.apache.dubbo.common.extension.Activate;
import org.apache.dubbo.rpc.*;
import org.apache.dubbo.rpc.service.GenericService;

import javax.servlet.http.HttpServletRequest;

/**
 * dubbo日志过滤器
 *
 * <AUTHOR>
 * @date 2022/04/18
 */
@Slf4j
@Activate(group = {CommonConstants.PROVIDER, CommonConstants.CONSUMER})
public class DubboRequestFilter implements Filter {

    @Override
    public Result invoke(Invoker<?> invoker, Invocation invocation) throws RpcException {

        String client = CommonConstants.PROVIDER;
        if (RpcContext.getServiceContext().isConsumerSide()) {
            client = CommonConstants.CONSUMER;
            HttpServletRequest request=null;
            try{
                request = HttpServletUtil.getRequest();
            }catch (Exception e){

            }
            if(ObjectUtil.isNotEmpty(request)){
                //消费端，如果是http请求的，需要封装参数
                HttpRequestData data = new HttpRequestData();
                data.setIpAddr(IpAddressUtil.getIp(request));
                data.setLoginLocation(IpAddressUtil.getAddress(request));
                data.setBrowser(UaUtil.getBrowser(request));
                data.setOs(UaUtil.getOs(request));
                RpcContext.getServiceContext().setObjectAttachment(com.fls.common.core.constant.CommonConstants.RPC_HTTP_REQUEST_DATA,data);
            }
        }
        String baselog = "Client [" + client + "] , InterfaceName =  " + invocation.getInvoker().getInterface().getSimpleName() + "  , MethodName = " + invocation.getMethodName() ;
        log.info("DUBBO - 服务调用: {} , Parameter = {}", baselog, invocation.getArguments());

        long startTime = System.currentTimeMillis();
        // 执行接口调用逻辑
        Result result = invoker.invoke(invocation);
        // 调用耗时
        long elapsed = System.currentTimeMillis() - startTime;
        // 如果发生异常 则打印异常日志
        if (result.hasException() && invoker.getInterface().equals(GenericService.class)) {
            log.error("DUBBO - 服务异常: {} , Exception = {}", baselog, result.getException());
        } else {
            log.info("DUBBO - 服务响应: {} , SpendTime = {} ms ,Response = {}", baselog, elapsed, JsonUtils.toJsonString(new Object[]{result.getValue()}));
        }
        return result;
    }

}
