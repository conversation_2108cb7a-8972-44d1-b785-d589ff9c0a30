package com.fls.common.core.web.entity;

import lombok.Data;

import java.io.Serializable;

/**
 * dubbo请求基础参数转发类
 *
 * <AUTHOR>
 * @date 2022/09/01
 **/
@Data
public class HttpRequestData implements Serializable {
    /**
     *  IP地址
     */
    private String ipAddr;

    /**
     * 访问地点
     */
    private String loginLocation;

    /**
     * 浏览器类型
     */
    private String browser;

    /**
     * 操作系统
     */
    private String os;
}
