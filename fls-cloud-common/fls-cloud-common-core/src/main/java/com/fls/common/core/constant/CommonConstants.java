package com.fls.common.core.constant;

/**
 * 通用常量信息
 *
 * <AUTHOR>
 * @date 2022/05/17
 */
public interface CommonConstants {
    /**
     * UTF-8 字符集
     */
    String UTF8 = "UTF-8";

    /**
     * GBK 字符集
     */
    String GBK = "GBK";

    /**
     * http请求
     */
    String HTTP = "http://";

    /**
     * https请求
     */
    String HTTPS = "https://";

    /**
     * 成功标记
     */
    Integer SUCCESS = 200;

    /**
     * 失败标记
     */
    Integer FAIL = 500;

    /**
     * 登录成功状态
     */
    String LOGIN_SUCCESS_STATUS = "0";

    /**
     * 登录失败状态
     */
    String LOGIN_FAIL_STATUS = "1";

    /**
     * 登录成功
     */
    String LOGIN_SUCCESS = "Success";

    /**
     * 注销
     */
    String LOGOUT = "Logout";

    /**
     * 注册
     */
    String REGISTER = "Register";

    /**
     * 登录失败
     */
    String LOGIN_FAIL = "Error";

    /**
     * 验证码有效期（分钟）
     */
    long CAPTCHA_EXPIRATION = 2;

    /**
     * 防重提交 redis key
     */
    String REPEAT_SUBMIT_KEY = "repeat_submit:";


    /**
     * 删除标志
     */
    String DELETE_FLAG_IS_DELETED="1";
    String DELETE_FLAG_NOT_DELETED="0";

    /**
     * 公用状态
     */
    String COMMON_STATUS_NORMAL="2";
    String COMMON_STATUS_DISABLED="1";

    /**
     * 未知标识
     */
    String UNKNOWN = "Unknown";

    /**
     * 用户代理
     */
    String USER_AGENT = "User-Agent";

    /**
     * 服务前缀
     */
    String CLOUD_START_KEY="fls-cloud";

    /**
     * rpc请求，封装请求基础数据的data的key
     */
    String RPC_HTTP_REQUEST_DATA="httpServletRequestData";

        /**
     * 是/否
     */
    String YES = "1";
    String NO = "0";
}
