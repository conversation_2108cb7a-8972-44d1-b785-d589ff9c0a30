package com.fls.common.core.web.controller;

import com.fls.common.core.domain.ResponseData;
import lombok.extern.slf4j.Slf4j;

/**
 * web层通用数据处理
 *
 * <AUTHOR>
 * @date 2022/03/25
 */
@Slf4j
public class BaseController {

    /**
     * 响应返回结果
     *
     * @param rows 影响行数
     * @return 操作结果
     */
    protected ResponseData<Void> toAjax(int rows) {
        return rows > 0 ? ResponseData.ok() : ResponseData.fail();
    }

    /**
     * 响应返回结果
     *
     * @param result 结果
     * @return 操作结果
     */
    protected ResponseData<Void> toAjax(boolean result) {
        return result ? ResponseData.ok() : ResponseData.fail();
    }

}
