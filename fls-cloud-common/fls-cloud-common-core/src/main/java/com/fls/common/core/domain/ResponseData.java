package com.fls.common.core.domain;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fls.common.core.constant.CommonConstants;
import java.io.Serializable;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 响应信息主体
 *
 * <AUTHOR>
 * @date 2022/05/17
 */
@Data
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ResponseData<T> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 成功
     */
    public static final int SUCCESS = CommonConstants.SUCCESS;

    /**
     * 失败
     */
    public static final int FAIL = CommonConstants.FAIL;

    /**
     * 执行状态
     */
    private boolean success = true;

    /**
     * 消息状态码
     */
    private int code;

    /**
     * 消息内容
     */
    private String msg;

    /**
     * 数据对象
     */
    private T data;

    @JsonIgnore
    private transient static Object defaultData = new Object();

    public static <T> ResponseData<T> ok() {
        return restResult(null, SUCCESS, "操作成功",true);
    }

    public static <T> ResponseData<T> ok(T data) {
        return restResult(data, SUCCESS, "操作成功",true);
    }

    public static <T> ResponseData<T> ok(String msg) {
        return restResult(null, SUCCESS, msg,true);
    }

    public static <T> ResponseData<T> ok(String msg, T data) {
        return restResult(data, SUCCESS, msg, true);
    }

    public static <T> ResponseData<T> fail() {
        return restResult(null, FAIL, "操作失败", false);
    }

    public static <T> ResponseData<T> fail(String msg) {
        return restResult(null, FAIL, msg, false);
    }

    public static <T> ResponseData<T> fail(T data) {
        return restResult(data, FAIL, "操作失败", false);
    }

    public static <T> ResponseData<T> fail(String msg, T data) {
        return restResult(data, FAIL, msg, false);
    }

    public static <T> ResponseData<T> fail(int code, String msg) {
        return restResult(null, code, msg, false);
    }

    private static <T> ResponseData<T> restResult(T data, int code, String msg,boolean success) {
        if (data == null) {
            data = (T) defaultData;
        }
        ResponseData<T> responseData = new ResponseData<>();
        responseData.setCode(code);
        responseData.setData(data);
        responseData.setMsg(msg);
        responseData.setSuccess(success);
        return responseData;
    }

}
