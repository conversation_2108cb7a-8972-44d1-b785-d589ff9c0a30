<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.fls</groupId>
        <artifactId>fls-cloud</artifactId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>fls-cloud-common</artifactId>
    <description>通用工程</description>
    <packaging>pom</packaging>

    <modules>
        <module>fls-cloud-common-bom</module>
        <module>fls-cloud-common-log</module>
        <module>fls-cloud-common-core</module>
        <module>fls-cloud-common-redis</module>
        <module>fls-cloud-common-doc</module>
        <module>fls-cloud-common-security</module>
        <module>fls-cloud-common-satoken</module>
        <module>fls-cloud-common-web</module>
        <module>fls-cloud-common-mybatis</module>
        <module>fls-cloud-common-job</module>
        <module>fls-cloud-common-dubbo</module>
        <module>fls-cloud-common-seata</module>
        <module>fls-cloud-common-loadbalancer</module>
        <module>fls-cloud-common-oss</module>
        <module>fls-cloud-common-idempotent</module>
        <module>fls-cloud-common-message</module>
        <module>fls-cloud-common-elasticsearch</module>
    </modules>


</project>
