package com.fls.common.mybatis.service;

import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

/**
 * 数据权限 实现
 * <p>
 * 注意: 此Service内不允许调用标注`数据权限`注解的方法
 * 例如: deptMapper.selectList 此 selectList 方法标注了`数据权限`注解 会出现循环解析的问题
 *
 * <AUTHOR>
 * @date 2022/03/19
 */
@Service("sdss")
public class SysDataScopeService {


    public String getRoleCustom(Long roleId) {
        return null;
    }

    public String getDeptAndChild(Long deptId) {

        return null;
    }
}
