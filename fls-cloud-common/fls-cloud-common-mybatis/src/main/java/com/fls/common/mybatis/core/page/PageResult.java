package com.fls.common.mybatis.core.page;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.Data;

import java.util.List;

@Data
public class PageResult<T> {

    private int current;

    private int size;

    private int total;

    private List<T> records;

    public PageResult(Page page, List<T> records) {
        this.current = (int) page.getCurrent();
        this.size = (int) page.getSize();
        this.total = (int) page.getTotal();
        this.records = records;
    }
}
