<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.fls</groupId>
    <artifactId>fls-cloud-common-bom</artifactId>
    <version>1.0.0</version>
    <packaging>pom</packaging>
    <properties>
        <spring-cloud-alibaba.version>2021.0.1.0</spring-cloud-alibaba.version>
        <maven-javadoc-plugin.version>3.1.1</maven-javadoc-plugin.version>
        <sentinel.version>1.8.4</sentinel.version>
        <maven-gpg-plugin.version>1.6</maven-gpg-plugin.version>
        <nacos.config.version>0.8.0</nacos.config.version>
        <maven-source-plugin.version>2.2.1</maven-source-plugin.version>
        <flatten-maven-plugin.version>1.1.0</flatten-maven-plugin.version>
        <nacos.client.version>2.0.2</nacos.client.version>
        <seata.version>1.5.2</seata.version>
        <dubbo.version>3.0.10</dubbo.version>
        <spring.context.support.version>1.0.11</spring.context.support.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>com.alibaba.nacos</groupId>
                <artifactId>nacos-client</artifactId>
                <version>${nacos.client.version}</version>
            </dependency>

            <!--<dependency>
                <groupId>com.alibaba.nacos</groupId>
                <artifactId>nacos-config</artifactId>
                <version>${nacos.config.version}</version>
            </dependency>-->

            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-core</artifactId>
                <version>${sentinel.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-parameter-flow-control</artifactId>
                <version>${sentinel.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-datasource-extension</artifactId>
                <version>${sentinel.version}</version>
            </dependency>

            <!--<dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-datasource-apollo</artifactId>
                <version>${sentinel.version}</version>
            </dependency>-->

            <!--<dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-datasource-zookeeper</artifactId>
                <version>${sentinel.version}</version>
            </dependency>-->

            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-datasource-nacos</artifactId>
                <version>${sentinel.version}</version>
            </dependency>

            <!--<dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-datasource-redis</artifactId>
                <version>${sentinel.version}</version>
            </dependency>-->

            <!--<dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-datasource-consul</artifactId>
                <version>${sentinel.version}</version>
            </dependency>-->

            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-web-servlet</artifactId>
                <version>${sentinel.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-spring-cloud-gateway-adapter</artifactId>
                <version>${sentinel.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-transport-simple-http</artifactId>
                <version>${sentinel.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-annotation-aspectj</artifactId>
                <version>${sentinel.version}</version>
            </dependency>

            <!--<dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-dubbo-adapter</artifactId>
                <version>${sentinel.version}</version>
            </dependency>-->

            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-apache-dubbo-adapter</artifactId>
                <version>${sentinel.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-reactor-adapter</artifactId>
                <version>${sentinel.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-cluster-server-default</artifactId>
                <version>${sentinel.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-cluster-client-default</artifactId>
                <version>${sentinel.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-spring-webflux-adapter</artifactId>
                <version>${sentinel.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-api-gateway-adapter-common</artifactId>
                <version>${sentinel.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-spring-webmvc-adapter</artifactId>
                <version>${sentinel.version}</version>
            </dependency>

            <dependency>
                <groupId>io.seata</groupId>
                <artifactId>seata-spring-boot-starter</artifactId>
                <version>${seata.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-spring-boot-starter</artifactId>
                <version>${dubbo.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-spring-boot-actuator</artifactId>
                <version>${dubbo.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo</artifactId>
                <version>${dubbo.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.spring</groupId>
                <artifactId>spring-context-support</artifactId>
                <version>${spring.context.support.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fls</groupId>
                <artifactId>fls-cloud-common-core</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fls</groupId>
                <artifactId>fls-cloud-common-doc</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fls</groupId>
                <artifactId>fls-cloud-common-security</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fls</groupId>
                <artifactId>fls-cloud-common-satoken</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fls</groupId>
                <artifactId>fls-cloud-common-log</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fls</groupId>
                <artifactId>fls-cloud-common-redis</artifactId>
                <version>${project.version}</version>
            </dependency>


            <dependency>
                <groupId>com.fls</groupId>
                <artifactId>fls-cloud-common-web</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fls</groupId>
                <artifactId>fls-cloud-common-mybatis</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fls</groupId>
                <artifactId>fls-cloud-common-job</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fls</groupId>
                <artifactId>fls-cloud-common-dubbo</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fls</groupId>
                <artifactId>fls-cloud-common-seata</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fls</groupId>
                <artifactId>fls-cloud-common-loadbalancer</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fls</groupId>
                <artifactId>fls-cloud-common-oss</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fls</groupId>
                <artifactId>fls-cloud-common-idempotent</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fls</groupId>
                <artifactId>fls-cloud-common-message</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fls</groupId>
                <artifactId>fls-cloud-common-elasticsearch</artifactId>
                <version>${project.version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>
</project>
